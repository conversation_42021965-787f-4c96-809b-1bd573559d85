/**
 * 進度條組件
 * @param {string} width - 寬度 (預設: '0%')
 * @param {string} bgColor - 背景顏色 (預設: 'bg-white')
 * @param {boolean} striped - 是否顯示斜紋效果 (預設: false)
 * @param {string} className - 自定義類名，將完全替換原有的類名 (預設: '')
 * @param {string} extraClassName - 額外附加的類名，將追加在原有的類名後面 (預設: '')
 * @returns {JSX.Element} - React 元素
 */
const ProgressBar = ({ width = '0%', bgColor = 'bg-white', striped = false, className = '', extraClassName = '' }) => {
    return (
        <div className="progress">
            <div className={className ? className : `progress-bar ${bgColor} ${striped ? "progress-bar-striped" : ""} fs-10px fw-bold ${extraClassName}`}
                style={{ width: width }}>
            </div>
        </div>
    );
};

export default ProgressBar;
