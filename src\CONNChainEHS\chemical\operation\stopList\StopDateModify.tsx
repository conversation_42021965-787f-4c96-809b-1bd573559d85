import { getStopOperDate } from "ehs/utils/chemicalUtil";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { ChemicalAPI } from "../../../../api/chemicalAPI";
import useLoginUser from "../../../hooks/useLoginUser";
import { EhsChemicalCon } from "../../../models/EhsChemicalCon";
import { getBasicLoginUserInfo } from "../../../utils/authUtil";
import { isApiCallSuccess } from "../../../utils/resultUtil";
import { getFormatDateSlash } from "../../../utils/stringUtil";

function StopDateModify(props: {
    onClose: () => void;
    onActionSuccess: (conList: EhsChemicalCon[]) => void;
    setLoadingBlock: (block: boolean) => void;
    serverDate: Date;
    monthNum: number;
    setMonthNum: (num: number) => void;
    dayOfMonth: number;
    conList: EhsChemicalCon[];
    mode: boolean | null;
}) {
    const { onClose, onActionSuccess, setLoadingBlock, serverDate, monthNum, setMonthNum, dayOfMonth, conList, mode } = props;
    const { loginUser } = useLoginUser();
    const { t } = useTranslation();
    const maxMonthNum = 4;//最多往前四個月
    const stopMonthDate = getStopOperDate(serverDate, monthNum, dayOfMonth);
    const displayStopOperDate = stopMonthDate ? getFormatDateSlash(stopMonthDate) : "";

    useEffect(() => {
    }, [loginUser, mode]);

    const onAction = () => {
        if (!loginUser) {
            return;
        }
        setLoadingBlock(true);
        const newConList: EhsChemicalCon[] = conList.map(item => ({
            ...item,
            stopOperMonths: monthNum // 可選，根據需要是否要刪除原始的 stopOperMonths 屬性
        }));
        ChemicalAPI.editChemicalStopOperMonth({
            ...getBasicLoginUserInfo(loginUser),
            conList: newConList,
        }).then((res) => {
            if (isApiCallSuccess(res)) {
                onActionSuccess(newConList);
            }
            setLoadingBlock(false);
        })
    };

    return (
        <StyledModifyStopDate>
            <div className="modifyStopDate">
                <div className="modifyStopDate-header">
                    <h4 className="modal-title">{t('func.edit')}</h4>
                    <button
                        type="button"
                        className="btn-close"
                        aria-hidden="true"
                        onClick={onClose}
                    ></button>
                </div>
                <div className="modifyStopDate-body">
                    <div className="row mb-4 justify-content-center">
                        <div className="col-md-10 ms-md-0 ms-3 mb-3">
                            <label className="fw-bold mb-3 h4">
                                {t('text.chemical.operation_stop_date')}：{displayStopOperDate}
                            </label><br />
                            <div className="d-md-flex align-items-center">
                                <label className="fw-bold h5 my-0">
                                    {t('text.chemical.stop_oper_edit_desc_pre')}
                                </label>
                                <select className="form-select form-select-lg mx-md-1 w-auto" onChange={(e) => setMonthNum(Number(e.target.value))}>
                                    {Array.from({ length: maxMonthNum }, (_, index) => (
                                        <option key={`option_${index + 1}`} value={index + 1}>
                                            {index + 1}
                                        </option>
                                    ))}
                                </select>
                                <label className="fw-bold h5 my-0">
                                    {t('text.chemical.stop_oper_edit_desc_suf')}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                {<div className="modifyStopDate-footer">
                    <div className="btn btn-white" aria-hidden="true" onClick={onClose}>
                        <i className="fas fa-times me-1" />
                        {t("button.close")}
                    </div>
                    <div className="btn btn-purple" onClick={onAction}>
                        <i className="fas fa-pen" /> {t('func.edit')}
                    </div>
                </div>}
            </div>
        </StyledModifyStopDate>
    );
}

const StyledModifyStopDate = styled.div`
  background: white;
  width: 500px;
  .modifyStopDate-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  .modifyStopDate-body {
    padding: 15px;
  }
  .modifyStopDate-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-purple {
      margin-left: 10px;
    }
  }
`;

export default StopDateModify;