import GhsImage from "ehs/common/GhsImage";
import { scrollToElement } from "ehs/utils/scrollUtil";
import React from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { CONFIG_TYPE_GHS_CLASS, CONFIG_TYPE_GHS_IMG, CONFIG_TYPE_PACKMAL, CONFIG_TYPE_PACKTYPE, CONFIG_TYPE_PUBLIC_HAZARD } from "../../constant/constants";
import useLoginUser from "../../hooks/useLoginUser";
import { EhsConfigParam } from "../../models/EhsConfigParam";
import { EhsManufacturer } from "../../models/EhsManufacturer";
import { EhsProduct } from "../../models/EhsProduct";
import { isArrayEmpty } from "../../utils/arrayUtil";
import { isChineseLang, splitChemNameListByLang } from "../../utils/langUtil";
import SubStanceDetail from "./SubStanceDetail";
import DownloadButton from "ehs/common/button/DownloadButton";
import { getBasicLoginUserInfo } from "ehs/utils/authUtil";
import { FileAPI } from "api/fileAPI";
import { isApiCallSuccess } from "ehs/utils/resultUtil";


function ProductDetail(props: {
  onClose: () => void;
  productInfo: EhsProduct;
  configMap: { [configId: string]: EhsConfigParam };
  productCategoryMap: { [productId: string]: string[] };
  vendorMap: { [manufacturerId: string]: EhsManufacturer };
}) {
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const { onClose, productInfo, configMap, vendorMap, productCategoryMap } = props;
  const { productId, productNo, productName, brand,
    chemClassLv, chemCtrlNo, concentration, weight, capacity, density, phaseState, manufacturerId,
    productLevel, packing, meltingPoint, boilingPoint, productDescription, isFutures, futuresDay,
    packingLong, packingWidth, packingHigh, packingDiameter, fullKilogram, fullPressure,
    sdsId, sdsDate, sdsName, casnoList, chemNameList, containsMix, substanceList } = productInfo;
  const firstNameList = chemNameList.filter(chemName => chemName.chemNameSeq === 1);
  const { currentLangNames, enNames } = splitChemNameListByLang(firstNameList, i18n.language);
  const firstName = (!isArrayEmpty(currentLangNames) && currentLangNames[0].chemName) || "";
  const firstEnName = (!isArrayEmpty(enNames) && enNames[0].chemName) || "";
  const packingConfigIds = productCategoryMap[productId] || [];
  const packingConfig = isArrayEmpty(packingConfigIds)
    ? []
    : packingConfigIds.map(configId => configMap[configId]).filter(Boolean);
  const configTypeMap = {
    packType: CONFIG_TYPE_PACKTYPE,
    packMaterial: CONFIG_TYPE_PACKMAL,
    publicHazard: CONFIG_TYPE_PUBLIC_HAZARD,
    ghsImg: CONFIG_TYPE_GHS_IMG,
    ghsClass: CONFIG_TYPE_GHS_CLASS
  };
  const filteredConfigs = Object.entries(configTypeMap).reduce((result, [key, configType]) => {
    result[key] = packingConfig.filter(config => config?.configType === configType);
    return result;
  }, {} as Record<string, EhsConfigParam[]>);
  const { packType: packTypeList, packMaterial: packMaterialList, publicHazard: publicHazardList,
    ghsImg: ghsImgList, ghsClass: ghsClassList } = filteredConfigs;
  const { mainGhsClass, subGhsClass } = ghsClassList.reduce((acc, item) => {
    if (item.configSubType) {
      acc.subGhsClass.push(item);
    } else {
      acc.mainGhsClass.push(item);
    }
    return acc;
  }, { mainGhsClass: [] as EhsConfigParam[], subGhsClass: [] as EhsConfigParam[] });
  const splitFlag = isChineseLang(i18n.language) ? "、" : ", ";
  const mixSectionId = "mix-section";

  const fetchSdsFile = async (sdsId: string, loginUser: any) => {
    const result = await FileAPI.getProductSdsFile({
      ...getBasicLoginUserInfo(loginUser)!,
      sdsId: sdsId
    });
    if (isApiCallSuccess(result)) {
      const { fileContentBase64, fileName } = result.results;
      return {
        content: fileContentBase64,
        fileName: fileName || ''
      };
    }
    return {
      content: '',
      fileName: ''
    };
  };


  return (
    <StlyedProductDetail >
      <div className="product-detail-div">
        <div className="modal-header">
          <h4 className="modal-title">{t('button.detail')}</h4>
          <button type="button" className="btn-close" aria-hidden="true" onClick={onClose}></button>
        </div>
        <div className="modal-body">
          <div className="row">
            <div className="col-xl-6 text-center align-self-center">
              <div className="mb-5">
                {substanceList && Boolean(containsMix) &&
                  !isArrayEmpty(substanceList.filter(subst => !subst.isPrimary)) && (
                    <h5 className="text-start ps-4">
                      <button className="btn btn-success btn-icon"
                        onClick={() => { scrollToElement(`#${mixSectionId}`); }}>
                        <i className="fas fa-chevron-down" />
                      </button>
                      {t('text.product.is_mixture')}
                    </h5>
                  )}
                {chemCtrlNo && <h5 className="text-danger mt-1">{t('text.ctrl_no')}：{chemCtrlNo}</h5>}
                <h5 className="text-primary">{t('text.chemical.casno')}：{casnoList.find(casno => casno.casnoSeq === 1)?.casno}</h5>
                <h3>{firstName ? firstName : firstEnName} </h3>
                {firstName && <div className="fs-5">{firstEnName}</div>}
                <div className="fs-5 mt-3">{vendorMap[manufacturerId]?.manufacturerName}</div>
              </div>
              {publicHazardList && !isArrayEmpty(publicHazardList) && (
                <div className="ms-3 me-1 mb-5 text-start">
                  <h4>{t('text.chemical.public_hazard_classify')}</h4>
                  <ul className="fs-5">{publicHazardList.map((data, idx) => (<li key={idx}>{data.configName}</li>))}</ul>
                </div>
              )}
              {ghsImgList && !isArrayEmpty(ghsImgList) && (
                <div className="ms-3 me-1 mb-5 text-start">
                  <h4>{t('text.chemical.ghs_img')}</h4>
                  <div>
                    {ghsImgList.map((data, idx) => (
                      <React.Fragment key={idx}>
                        <GhsImage src={data.configValue} alt={data.configName} title={data.configName} />
                      </React.Fragment>
                    ))}
                  </div>
                </div>
              )}
              {mainGhsClass && subGhsClass && !isArrayEmpty(mainGhsClass) && !isArrayEmpty(subGhsClass) && (
                <div className="ms-3 mb-5 text-start">
                  <h4>{t('text.chemical.ghs_classification')}</h4>
                  <table className="table table-bordered fs-5">
                    <tbody>
                      {mainGhsClass.map((mainItem) => (
                        <tr key={mainItem.configId}>
                          <td>{mainItem.configName}</td>
                          <td>
                            {subGhsClass
                              .filter((subItem) => subItem.configSubType === mainItem.configId)
                              .map((subData) => subData.configName)
                              .join(splitFlag)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
            <div className="col-xl-6 pe-3">
              <ul className="fs-5">
                <li>{t('text.product_item_number')}：{productNo}</li>
                <li>{t('text.product.name')}：{productName}</li>
                <li>{t('text.brand')}：{brand}</li>
                <li>{t('text.concentration')}(%)：{concentration}</li>
                <li>{t('text.weight')}(kg)：{weight}</li>
                <li>{t('text.chemical.phase_state')}：{configMap[phaseState]?.configName}</li>
                <li>{t('text.chemical.melt_point')}：{meltingPoint}</li>
                <li>{t('text.chemical.boil_point')}：{boilingPoint}</li>
                <li>{t('text.capacity')}：{capacity}</li>
                <li>{t('text.density')}：{density}</li>
                <li>{t('text.level')}：{productLevel}</li>
                <li>{t('text.packing')}：{packing}</li>
                <li>{t('text.product_description')}：{productDescription}</li>
                <li>{t('table.title.sds_file')}：
                  <DownloadButton
                    fileName={sdsName || ''}
                    onDownload={() => fetchSdsFile(sdsId || '', loginUser)}
                  />
                </li>
                <li>{t('text.chemical.sds_file_exp_date')}：{sdsDate}</li>
                <li>{t('text.futures')}：{isFutures} / {futuresDay}</li>
                <li>
                  {t('text.product.packing_type')}：
                  {packTypeList?.map((config, index) => (
                    <React.Fragment key={'packType' + config.configId}>
                      {config.configName}
                      {index < packTypeList.length - 1 && splitFlag}
                    </React.Fragment>
                  ))}
                </li>
                <li>
                  {t('text.product.packing_material')}：
                  {packMaterialList?.map((config, index) => (
                    <React.Fragment key={'packMaterial' + config.configId}>
                      {config.configName}
                      {index < packMaterialList.length - 1 && splitFlag}
                    </React.Fragment>
                  ))}
                </li>
              </ul>
              <table className="table fs-5">
                <tbody>
                  <tr>
                    <td rowSpan={6} className={`${isChineseLang(i18n.language) ? 'item-width-25' : 'item-width-35'} text-center`}>
                      {t('table.title.product.packing_format')}
                    </td>
                    <td> {t('table.title.product.packing_length')}</td>
                    <td>{packingLong}</td>
                  </tr>
                  <tr>
                    <td> {t('table.title.product.packing_width')}</td>
                    <td>{packingWidth}</td>
                  </tr>
                  <tr>
                    <td>{t('table.title.product.packing_height')}</td>
                    <td>{packingHigh}</td>
                  </tr>
                  <tr>
                    <td>{t('table.title.product.packing_diameter')}</td>
                    <td>{packingDiameter}</td>
                  </tr>
                  <tr>
                    <td>{t('table.title.product.packing_full_capacity')}</td>
                    <td>{fullKilogram}</td>
                  </tr>
                  <tr>
                    <td>{t('table.title.product.packing_full_pressure')}</td>
                    <td>{fullPressure}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div className="ms-3 me-1 mt-3 mb-5">
            <h3 id={mixSectionId} className="text-center">
              {t('text.chemical.substance')}
            </h3>
            <div className="mix-container row">
              {substanceList?.map((data, idx) => (
                <div key={idx} className="col-6 mb-2">
                  <SubStanceDetail subStance={data} configMap={configMap} />
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="modal-footer">
          <button className="btn btn-white" aria-hidden="true" onClick={onClose} title={t("button.close")}>
            <i className="fas fa-times me-1" />
            {t("button.close")}
          </button>
        </div>
      </div>
    </StlyedProductDetail>
  );
}


const StlyedProductDetail = styled.div`
  font-size: 1.2em;
  background: white;
  width: 1100px; 
  height: 90vh;
  position: relative;
  display: flex;
  
  .product-detail-div {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  
  .modal-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
    flex-shrink: 0;
  }
  
  .modal-body {
    padding: 15px; 
    overflow-y: auto;
    flex-grow: 1;
  }
  
  .modal-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end; 
    flex-shrink: 0;
  }
`;

export default ProductDetail;
