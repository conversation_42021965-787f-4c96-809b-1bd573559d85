import { useState } from "react";
import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { UserAPI } from "../../../../api/userAPI";
import NamesSplitFragment from "../../../common/NamesSplitFragment";
import { showWarnToast, showWarnToastWithId } from "../../../common/Toast";
import { ROU_TYPE_LAB, ORG_SPLIT_FLAG } from "../../../constant/constants";
import { ActionMode } from "../../../enums/ActionMode";
import useLoginUser from "../../../hooks/useLoginUser";
import { EhsRou, initEhsRou } from "../../../models/EhsRou";
import { isArrayEmpty } from "../../../utils/arrayUtil";
import { isApiCallSuccess } from "../../../utils/resultUtil";
import { getBasicLoginUserInfo } from "../../../utils/authUtil";
import { getLabTextObj, getUserUnitTextObj } from "../../../utils/langUtil";
import { isEnterKey } from "../../../utils/stringUtil";

interface SearchUserResult extends EhsRou {
  checked: boolean;
}

function LabModifyUser(props: {
  onClose: () => void;
  onActionSuccess: () => void;
  mode: ActionMode | null;
  modifyData: {};
  labUsers: EhsRou[];
  setLabUsers: React.Dispatch<React.SetStateAction<EhsRou[]>>;
}) {
  const { onClose, onActionSuccess, mode, modifyData, labUsers, setLabUsers } = props;
  const { t } = useTranslation();
  const { loginUser } = useLoginUser();
  const { userIdText } = getUserUnitTextObj(loginUser, t);
  const { msgLabUserNoChecked } = getLabTextObj(loginUser, t);
  const [checkAllUser, setCheckAllUser] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState<string>("");
  const [hasSearchUser, setHasSearchUser] = useState<boolean>(false);
  const [searchUserResult, setSearchUserResult] = useState<{ [key: string]: SearchUserResult }>({});
  const labUserList = Object.values(searchUserResult);

  const fetchData = () => {
    UserAPI.getUserSearchList({
      ...getBasicLoginUserInfo(loginUser),
      keyword: searchKeyword!.trim(),
      pageSize: 50,
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        if (!isArrayEmpty(result.results)) {
          setHasSearchUser(true);
          const resultArray = result.results; // 假设 result 是一个包含用户信息的数组
          const resultObject = resultArray.reduce((acc: any, user: SearchUserResult) => {
            acc[user.userId] = { ...user, checked: false }; // 将每个用户对象添加到结果对象中，并设置 checked 初始值为 false
            return acc;
          }, {});
          setSearchUserResult(resultObject);
        }
      }
    });
  };

  const doSearchUser = () => {
    if (searchKeyword.trim() === "") {
      showWarnToastWithId(t('message.labuser.search_nokeyword'), "search_labuser_nokeyword");
      return false;
    }
    setCheckAllUser(false);
    fetchData();
  }

  const handleSelectAllChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const checked = event.target.checked;
    setCheckAllUser(checked);
    setSearchUserResult(prevState => {
      const updatedResult: { [key: string]: SearchUserResult } = {};
      Object.keys(prevState).forEach((userId: string) => {
        updatedResult[userId] = {
          ...prevState[userId],
          checked: checked
        };
      });
      return updatedResult;
    });
  };

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>, userId: string) => {
    setSearchUserResult(prevState => {
      const updatedUsers = { ...prevState }; // 克隆原始状态对象
      const userToUpdate = updatedUsers[userId]; // 通过 userId 获取要更新的用户对象
      if (userToUpdate) {
        userToUpdate.checked = event.target.checked; // 更新用户的 checked 属性
      }
      // 檢查所有用戶是否全選
      const allSelected = Object.values(updatedUsers).every(user => user.checked === true);

      // 更新全選框的狀態
      if (allSelected !== checkAllUser) {
        setCheckAllUser(allSelected);
      }
      return updatedUsers;
    });
  };

  const modifyFinish = () => {
    const isAnyUserChecked = labUserList.some(user => user.checked);
    if (!isAnyUserChecked) {
      showWarnToastWithId(msgLabUserNoChecked, "modify_labuser_nocheckuser");
    } else {
      const checkedUsers = labUserList.filter(user => user.checked);
      const usersInLabUsers = checkedUsers.filter(user => labUsers.some(labUser => labUser.userId === user.userId));
      const usersNotInLabUsers = checkedUsers.filter(user => !labUsers.some(labUser => labUser.userId === user.userId));
      const addLabUsers: EhsRou[] = usersNotInLabUsers.map(user => ({
        ...initEhsRou,
        roleId: user.roleId,
        userId: user.userId,
        userName: user.userName,
        userEmail: user.userEmail,
        jobTitle: user.jobTitle,
        orgId: user.orgId,
        orgNames: user.orgNames,
        rouType: ROU_TYPE_LAB
      }));

      setLabUsers((prevLabUsers) => {
        return [
          ...prevLabUsers, // 保留原本的人員
          ...addLabUsers, //新加入的人員
        ];
      });

      if (!isArrayEmpty(usersInLabUsers)) {
        showWarnToast(
          <div>
            {usersInLabUsers.map(data => (
              <div key={data.userId}>
                {data.userId} {data.userName}
                <br />
              </div>
            ))}
            {t('message.labuser.joined')}
          </div>
        );
      }
      onActionSuccess();
    }
  }

  return (
    <StlyedLabModifyUser >
      <div className="modifyLabUser">
        <div className="modal-header">
          <h4 className="modal-title"><i className="fas fa-user-plus me-1" />{t('button.edit')}</h4>
          <button type="button" className="btn-close" onClick={onClose}></button>
        </div>
        <div className="modal-body">
          <div className="row justify-content-center align-items-center my-3">
            <div className="col-4 col-xl-2">
              <h5>{t('text.search.keyword')}</h5>
            </div>
            <div className="col-8 col-xl-4">
              <input type="text" className="form-control" placeholder={`${userIdText}/${t('text.user_name')}`} onChange={(e) => setSearchKeyword(e.target.value)}
                onKeyDown={(e: any) => { if (isEnterKey(e)) { doSearchUser(); } }} />
            </div>
            <div className="col-12 col-xl-1 m-1 d-flex justify-content-end justify-content-xl-start">
              <button type="button" className="btn btn-primary" title={t('text.search.item')}
                onClick={doSearchUser}><i className="fas fa-magnifying-glass me-1" />{t('text.search.item')}</button>
            </div>
          </div>
          {hasSearchUser && <div className="table-responsive mt-3 mx-3">
            <table className="table table-hover align-middle">
              <thead className="text-center fs-4 fw-bold">
                <tr>
                  <th><input type="checkbox" className="form-check-input" checked={checkAllUser} onChange={(e) => handleSelectAllChange(e)} /></th>
                  <th>{userIdText}</th>
                  <th className="text-start">{t('table.title.lab.username_jobtitle')}</th>
                  <th className="text-start">{t('table.title.org.item')}</th>
                </tr>
              </thead>
              <tbody className="text-center fs-5">
                {labUserList.map((user) => {
                  return (
                    <tr key={'searchUserResult-' + user.userId}>
                      <td><input type="checkbox" className="form-check-input" checked={user.checked} onChange={(e) => handleCheckboxChange(e, user.userId)} /></td>
                      <td data-title={userIdText}>{user.userId}</td>
                      <td className="text-start" data-title={t('table.title.lab.username_jobtitle')}>{user.userName} / {user.jobTitle}</td>
                      <td className="text-start" data-title={t('table.title.org.item')}><NamesSplitFragment names={user.orgNames} separator={ORG_SPLIT_FLAG} /></td>
                    </tr>);
                })}
              </tbody>
            </table>
          </div>
          }
        </div>
        <div className="modal-footer">
          <button className="btn btn-white" onClick={onClose} title={t("button.close")}>
            <i className="fas fa-times me-1" />
            {t("button.close")}
          </button>
          {hasSearchUser && <button className="btn btn-warning ms-2" onClick={modifyFinish} title={t('button.edit')}>
            <i className="fas fa-user-plus me-1" /> {t('button.edit')}
          </button>}
        </div>
      </div>
    </StlyedLabModifyUser>
  );
}


const StlyedLabModifyUser = styled.div`
  font-size: 1.2em;
  background: white;
  width: 1100px;
  .modal-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  .modal-body {
    padding: 15px;
    .table-responsive{
      max-height: 650px; /* 设置容器的最大高度 */
      overflow-y: auto;
    }
    thead {
      /* 将标题行固定在顶部 */
      position: sticky;
      top: 0;
      background:rgb(251, 205, 165);
      z-index: 2; /* 设置标题行的层级高于其他内容 */
    }
  }
  .modal-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-purple {
      margin-left: 10px;
    }
  }

  @media (max-width: 1024px){
    width: 100%;
  }
  
  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 100px;
        text-align:left;
        min-height:50px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
  
  .search-label {
    text-align: end;  // 大螢幕靠右

    @media (max-width: 1200px) {  // xl breakpoint
      text-align: start;  // 小螢幕靠左
      margin-bottom: 0.5rem;  // 加一點底部間距
    }
  }
  
  @media (max-width: 1024px) {
      width: 100%;  // 在小螢幕時使用 100% 寬度
      max-width: 100%;
      height: 100%;
      
      .modal-body {
          flex: 1;
          overflow-y: auto;
          padding-bottom: 20px; // 添加底部間距，避免內容被 footer 遮擋
          max-height: 700px;
      }
      
      .modal-footer {
          position: sticky;
          bottom: 0;
          background: white;
          padding: 15px;
          border-top: 1px solid #ced4da;
          display: flex;
          justify-content: center;
      }
  }
`;

export default LabModifyUser;
