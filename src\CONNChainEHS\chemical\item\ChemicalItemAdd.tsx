import BlockUi from "@availity/block-ui";
import { ChemicalAPI } from "api/chemicalAPI";
import { ConfigAPI } from "api/configAPI";
import { LabAPI } from "api/labAPI";
import { ManufacturerAPI } from "api/manufacturerAPI";
import { OptionAPI } from "api/optionAPI";
import { SelectListAPI } from "api/selectListAPI";
import { Panel, PanelBody } from "components/panel/panel";
import BlockuiMsg from "ehs/common/BlockuiMsg";
import YesNoRadioButtons from "ehs/common/button/YesNoRadioButtons";
import ChemicalClassificationBadge from "ehs/common/chemical/ChemicalClassificationBadge";
import ChemicalTemperatureObj from "ehs/common/chemical/ChemicalTemperatureObj";
import Dialog from "ehs/common/Dialog";
import GhsImage from "ehs/common/GhsImage";
import InputDate from "ehs/common/input/InputDate";
import { InputMixConCenObj, initInputMixConCenObj } from "ehs/common/input/inputMixConcen";
import InputNumFloat from "ehs/common/input/InputNumFloat";
import InputFileUpload from "ehs/common/InputFileUpload";
import MixChemical from "ehs/common/MixChemical";
import PdfViewer from "ehs/common/PdfViewer";
import SearchMethod from "ehs/common/search/SearchMethod";
import { confirmMsg, errorMsg } from "ehs/common/SwalMsg";
import { showSuccessToast, showWarnToast, showWarnToastWithId } from "ehs/common/Toast";
import ToggleDisplayDiv from "ehs/common/ToggleDisplayDiv";
import { CHEMICAL_PHASE_LIQUID, CHEMICAL_PHASE_SOLID, CHEMICAL_TEMPERATURE_BOILING, CHEMICAL_TEMPERATURE_MELTING, CONFIG_ID_START_PUB_HAZ, CONFIG_TYPE_CCB_PROCESS_TEMP, CONFIG_TYPE_CHEM, CONFIG_TYPE_CHEM_MIXTURE_TYPE, CONFIG_TYPE_CHEM_STORAGE_LOCATION, CONFIG_TYPE_CHEM_TEMPRATURE_TYPE, CONFIG_TYPE_GHS_CLASS, CONFIG_TYPE_GHS_IMG, CONFIG_TYPE_MIX_CONCEN_TYPE, CONFIG_TYPE_MIX_CONCEN_TYPE_RANGE, CONFIG_TYPE_MIX_CONCEN_TYPE_SINGLE, CONFIG_TYPE_OPER_TYPE, CONFIG_TYPE_PACKMAL, CONFIG_TYPE_PACKTYPE, CONFIG_TYPE_POWDER_LEVEL, CONFIG_TYPE_PUBLIC_HAZARD, CONFIG_TYPE_STATE, CONFIG_VAL_OTHER, OPTION_CHEM_ADD_SUBSTANCE_MAX_LIMIT, OPTION_CHEM_ITEM_ADD_INCREASE, OPTION_CHEM_ITEM_ADD_INITIAL, OPTION_CHEM_ITEM_ADD_PURCHASE, SEARCH_LIMIT_QTY_CHEM_ITEM_ADD, SEARCH_METHOD_FUZZY, UPLOAD_ACCEPT_TYPE_PDF } from "ehs/constant/constants";
import InputMixConCenContext from "ehs/context/InputMixConCenContext";
import { ChemicalTemperatureType, getChemicalTemperatureType } from "ehs/enums/ChemicalTempratureType";
import { OperateType } from "ehs/enums/OperateType";
import useGhsMapping from "ehs/hooks/useGhsMapping";
import useLoginUser from "ehs/hooks/useLoginUser";
import useServerNowDate from "ehs/hooks/useServerNowDate";
import Breadcrumbs from "ehs/layout/Breadcrumbs";
import Footer from "ehs/layout/Footer";
import { ChemicalClassMatch, initChemicalClassMatch } from "ehs/models/ChemicalClassMatch";
import { EhsChemical } from "ehs/models/EhsChemical";
import { EhsChemicalCon } from "ehs/models/EhsChemicalCon";
import { EhsChemicalSubstance, initEhsChemicalSubstance } from "ehs/models/EhsChemicalSubstance";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsLab, initEhsLab } from "ehs/models/EhsLab";
import { EhsManufacturer } from "ehs/models/EhsManufacturer";
import { EhsOptions } from "ehs/models/EhsOptions";
import { EhsOtherInfo, initEhsOtherInfo } from "ehs/models/EhsOtherInfo";
import { EhsPurchase, initEhsPurchase } from "ehs/models/EhsPurchase";
import { EhsPurchaseDetail, initEhsPurchaseDetail } from "ehs/models/EhsPurchaseDetail";
import { EhsPurchaseDetailCategory, initEhsPurchaseDetailCategory } from "ehs/models/EhsPurchaseDetailCategory";
import { EhsPurchaseSubst, initEhsPurchaseSubst } from "ehs/models/EhsPurchaseSubst";
import { EhsPurchaseSubstCategory, initEhsPurchaseSubstCategory } from "ehs/models/EhsPurchaseSubstCategory";
import { ReactSelectOption } from "ehs/models/ReactSelectOption";
import { SelectItem, initSelectItem } from "ehs/models/SelectItem";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { checkTimeoutAction, getBasicLoginUserInfo, getLoginUserRoleLevel } from "ehs/utils/authUtil";
import { getFirstChemNames } from "ehs/utils/chemicalUtil";
import { getLabTextObj, notChineseLang, notEnglishLang } from "ehs/utils/langUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { getFormatDateSlash, isEnterKey } from "ehs/utils/stringUtil";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import Select from "react-select";
import styled from "styled-components";
import AddSubstanceDiv from "./components/AddSubstanceDiv";
import EditWeight from "./components/EditWeight";
import OtherInfoPanel from "./components/OtherInfoPanel";
import RequiredInfoPanel from './components/RequiredInfoPanel';
import { scrollToFirstError } from "ehs/utils/scrollUtil";

interface AddChemItem {
  labId: string;
  purchaseDetail: EhsPurchaseDetail;
  quantity: number;
  phaseState: string;
  boilingPoint: number | undefined;
  boilingPointMax: number | undefined;
  meltingPoint: number | undefined;
  meltingPointMax: number | undefined;
  weightList: (number | null)[];
}
const initAddChemItem: AddChemItem = {
  labId: "",
  purchaseDetail: initEhsPurchaseDetail,
  quantity: 1,
  phaseState: "",
  boilingPoint: undefined,
  boilingPointMax: undefined,
  meltingPoint: undefined,
  meltingPointMax: undefined,
  weightList: [],
};

function ChemicalItemAdd() {
  const { t, i18n } = useTranslation();
  const isNotEnglish = notEnglishLang(i18n.language);
  const { loginUser } = useLoginUser();
  const userRoleLevel = getLoginUserRoleLevel(loginUser);
  const { labText, msgNoOperChemLab } = getLabTextObj(loginUser, t);
  const [nowDate] = useServerNowDate(loginUser);
  const ghsMappingObj = useGhsMapping(loginUser);//ghs分類勾選對應
  const navigate = useNavigate();
  const { control, register, handleSubmit, formState: { errors }, setValue, watch, setError, clearErrors } = useForm({
    mode: "all" // 所有變更時驗證
  });
  const registerObj = {//名稱需與後端對應
    manufacturerId: 'manufacturerId',
    sdsFile: 'sdsFile',
    sdsExpDate: 'sdsExpDate',
    addDate: 'addDate',
    inventoryLocation: 'inventoryLocation',
    inventoryNote: 'inventoryNote',
    density: 'density',
    capacity: 'capacity',
    productName: 'productName',
    productNo: 'productNo',
    brand: 'brand',
    productLevel: 'productLevel',
    packing: 'packing',
    productDescription: 'productDescription',
    packingLong: 'packingLong',
    packingWidth: 'packingWidth',
    packingHigh: 'packingHigh',
    packingDiameter: 'packingDiameter',
    fullKilogram: 'fullKilogram',
    fullPressure: 'fullPressure',
    estMaxStorageQty: 'estMaxStorageQty',
    estMaxUseQty: 'estMaxUseQty',
    processTemperature: 'processTemperature',
    respiratoryProtection: 'respiratoryProtection',
    handProtection: 'handProtection',
    eyeProtection: 'eyeProtection',
    skinBodyProtection: 'skinBodyProtection'
  }
  const allOptionValue = "all"
  const allSelectOption = useMemo(() => ({ value: allOptionValue, label: t('text.all') }), [t]);
  const chemTableRef = useRef<HTMLDivElement>(null);
  const [tableHeight, setTableHeight] = useState(0); //搜尋表格高度
  const [operateType, setOperateType] = useState(OperateType.PURCHASE); //運作類型 1:採購 2:盤增 5:起始
  const [mixCounter, setMixCounter] = useState(0); //混合物數量
  const [containMixture, setContainMixture] = useState(1); //包含混合物
  const [mixtureMaxLimit, setMixtureMaxLimit] = useState(0); //混合物數量上限
  const [boilType, setBoilType] = useState(ChemicalTemperatureType.Single); //沸點類型 1:單一 2:區間
  const [meltType, setMeltType] = useState(ChemicalTemperatureType.Single); //熔點類型 1:單一 2:區間
  const [isQuery, setIsQuery] = useState<boolean>(false); //判斷是否已搜尋成功
  const [isAdd, setIsAdd] = useState<boolean>(false); //判斷是否已加入
  const [fetchLabOptionSuccess, setFetchLabOptionSuccess] = useState<boolean>(false); //判斷是否抓取實驗室選項完成
  const [loadingBlock, setLoadingBlock] = useState(false);
  const [showEditWeights, setShowEditWeights] = useState<boolean>(false); //顯示重量編輯視窗
  const [hasChangeSearchCondition, setHasChangeSearchCondition] = useState<boolean>(false); //有改變搜尋條件
  const [hasSubmit, setHasSubmit] = useState<boolean>(false);//有提交
  const [inputSubstanceExpandIdx, setInputSubstanceExpandIdx] = useState(""); //展開輸入物質的 index 
  const [substancePhaseState, setSubstancePhaseState] = useState(""); //輸入物質的相態
  const [casno, setCasno] = useState('');
  const [chemName, setChemName] = useState('');
  const [searchMethod, setSearchMethod] = useState(SEARCH_METHOD_FUZZY);
  const [filterSearchResult, setFilterSearchResult] = useState(''); //篩選搜尋結果
  const [filterGhsOptionText, setFilterGhsOptionText] = useState(''); //篩選 ghs option
  const [selectLabOption, setSelectLabOption] = useState<EhsLab>(initEhsLab); // 選中的實驗室選項
  const [labOption, setLabOption] = useState<EhsLab[]>([]); // 實驗室選項
  const [vendorOption, setVendorOption] = useState<SelectItem[]>([]); // 供應商選項
  const [ctrlNoOptionList, setCtrlNoOptionList] = useState<SelectItem[]>([]); // 列管編號選項
  const [ctrlNoCache, setCtrlNoCache] = useState<{ [areaId: string]: SelectItem[] }>({});
  const [selectedCtrlNoOption, setSelectedCtrlNoOption] = useState<ReactSelectOption>(allSelectOption);
  const [phaseStateOptions, setPhaseStateOptions] = useState<EhsConfigParam[]>([]);
  const [tempratureTypeOptions, setTempratureTypeOptions] = useState<EhsConfigParam[]>([]);
  const [mixtureTypeOptions, setMixtureTypeOptions] = useState<EhsConfigParam[]>([]);
  const [storageLocationOptions, setStorageLocationOptions] = useState<EhsConfigParam[]>([]);
  const [packTypeOptions, setPackTypeOptions] = useState<EhsConfigParam[]>([]);
  const [packMalOptions, setPackMalOptions] = useState<EhsConfigParam[]>([]);
  const [ghsClassOptions, setGhsClassOptions] = useState<EhsConfigParam[]>([]);
  const [ghsImageOptions, setGhsImageOptions] = useState<EhsConfigParam[]>([]);
  const [pubHazClassOptions, setPubHazClassOptions] = useState<EhsConfigParam[]>([]);
  const [chemClassOptions, setChemClassOptions] = useState<EhsConfigParam[]>([]);
  const [mixConcenTypeOptions, setMixConcenTypeOptions] = useState<EhsConfigParam[]>([]);
  const [powderLevelOptions, setPowderLevelOptions] = useState<EhsConfigParam[]>([]);
  const [ccbProcessTempOptions, setCcbProcessTempOptions] = useState<EhsConfigParam[]>([]);
  const [selectedGhsImg, setSelectedGhsImg] = useState<EhsConfigParam[]>([]); //GHS危害圖示 已選取顯示
  const [selectedPubHazClass, setSelectedPubHazClass] = useState<EhsConfigParam[]>([]); //公共危險分類 已選取顯示
  const [selectedGhsClass, setSelectedGhsClass] = useState<string[]>([]); //Ghs健康危害分類 已選取顯示 
  const [searchResult, setSearchResult] = useState<EhsChemical[]>([]); // 搜尋結果 
  const [substances, setSubstances] = useState<EhsChemicalSubstance[]>([]);
  const [activeOperTypes, setActiveOperTypes] = useState<OperateType[]>([]);
  const [inputMixConCenObj, setInputMixConCenObj] = useState<InputMixConCenObj>({ ...initInputMixConCenObj, concenType: CONFIG_TYPE_MIX_CONCEN_TYPE_SINGLE });
  const [addItemChemicalInfo, setAddItemChemicalInfo] = useState<AddChemItem>(initAddChemItem);
  const [configMap, setConfigMap] = useState<{
    [configId: string]: EhsConfigParam;
  }>({}); // config對應
  const [detailCategoryMap, setDetailCategoryMap] = useState<{
    [configId: string]: EhsConfigParam;
  }>({});
  const [otherInfoMap, setOtherInfoMap] = useState<{
    [type: string]: string;
  }>({});
  const selectedChemClass = useMemo(() => {
    // 獲取不重複的 configId
    const configIds = new Set(
      substances.flatMap(mixture =>
        (mixture.categoryList || []).map(category => category.configId).filter(Boolean) // 確保 configId 存在
      )
    );

    // 從 chemClassOptions 中篩選出對應的選項
    const selectedOptions = chemClassOptions.filter(option => configIds.has(option.configId));

    return selectedOptions;
  }, [substances, chemClassOptions]);
  const hasSelectedPhaseStateOptions = useMemo(() => {
    // 從 mixture 中獲取唯一的 configId 列表
    const uniqueConfigIds = Array.from(new Set((substances || []).map(item => item.phaseState)));

    // 根據 uniqueConfigIds 過濾 phaseStateOptions 中符合條件的選項
    return (phaseStateOptions || []).filter(option => uniqueConfigIds.includes(option.configId));
  }, [substances, phaseStateOptions]);
  const phaseStateValue = useMemo(() => {
    return configMap[addItemChemicalInfo.phaseState]?.configValue || "";
  }, [addItemChemicalInfo.phaseState, configMap]);

  const phaseStateConditions = useMemo(() => {
    return {
      isChooseLiquid: phaseStateValue === CHEMICAL_PHASE_LIQUID,
      isChooseSolid: phaseStateValue === CHEMICAL_PHASE_SOLID,
    };
  }, [phaseStateValue]);
  const { isChooseLiquid, isChooseSolid } = phaseStateConditions;
  const hasAddSubstance = !isArrayEmpty(substances);
  const hasMultipleSubstance = (substances || []).length > 1;
  const { areaId, labId } = selectLabOption;
  const notChinese = notChineseLang(i18n.language);
  const labTitle = t('text.chemical.item') + (notChinese ? " " : "") + labText
  const hasChemLabOption = isArrayEmpty(labOption) && fetchLabOptionSuccess;
  const rowHeight = 60; // 假設每行的高度為 xx px
  const maxRows = 10; // 最多顯示行數
  const expandSubstanceHeight = inputSubstanceExpandIdx ? 220 : 125;
  const filteredResults = (searchResult || []).filter(({
    chemCtrlNo,
    casno,
    nameList
  }) => {
    const {
      firstName,
      firstEnName
    } = getFirstChemNames(nameList, i18n.language);
    const searchText = filterSearchResult?.toLowerCase();
    return chemCtrlNo?.toLowerCase().includes(searchText) || casno?.toLowerCase().includes(searchText) || firstName?.toLowerCase().includes(searchText) || firstEnName?.toLowerCase().includes(searchText);
  });
  const operTypeName = configMap[operateType]?.configName || "";
  const sdsFile = watch(registerObj.sdsFile);
  const mainGhsClass = (ghsClassOptions || []).filter(
    (item) => !item.configSubType
  ); ////GHS大標題
  const subGhsClass = (ghsClassOptions || []).filter(
    (item) => item.configSubType
  ); /////GHS選項
  const subGhsClassObject: { [key: string]: EhsConfigParam[] } = subGhsClass.reduce((acc, curr) => {
    const { configSubType } = curr;
    if (!acc[configSubType]) {
      acc[configSubType] = [];
    }
    acc[configSubType].push(curr);
    return acc;
  }, {} as { [key: string]: EhsConfigParam[] });
  const isMixture = containMixture === 1;
  const isOperPurch = operateType === OperateType.PURCHASE;
  const hasOperTypeOption = !isArrayEmpty(activeOperTypes);

  // 使用 useCallback 記憶化 fetchOption 函數
  const fetchOption = useCallback(() => {
    const optionIds = [OPTION_CHEM_ADD_SUBSTANCE_MAX_LIMIT, OPTION_CHEM_ITEM_ADD_PURCHASE, OPTION_CHEM_ITEM_ADD_INCREASE, OPTION_CHEM_ITEM_ADD_INITIAL];
    OptionAPI.getOptionListByIds({
      ...getBasicLoginUserInfo(loginUser),
      optionIdList: optionIds
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const options = result.results || [];
        const optionsMap: {
          [key: string]: EhsOptions;
        } = {};
        options.forEach((item: any) => {
          optionsMap[item.optionId] = item;
        });
        setMixtureMaxLimit(optionsMap[OPTION_CHEM_ADD_SUBSTANCE_MAX_LIMIT].optionIntValue);
        const operTypeMapping = {
          [OPTION_CHEM_ITEM_ADD_PURCHASE]: OperateType.PURCHASE,
          [OPTION_CHEM_ITEM_ADD_INCREASE]: OperateType.INVENTORY_ADD,
          [OPTION_CHEM_ITEM_ADD_INITIAL]: OperateType.INITIAL
        };
        const operTypeList = Object.keys(operTypeMapping) as Array<keyof typeof operTypeMapping>;
        // 根據選項值過濾可用操作類型
        const activeOperTypes = operTypeList.filter(optionId => {
          const optionValue = optionsMap[optionId]?.optionIntValue; // 使用選項 ID 查找選項值
          return userRoleLevel <= optionValue; // 根據角色等級檢查可用性
        }).map(optionId => operTypeMapping[optionId]); // 將符合條件的選項 ID 轉換為操作類型
        setActiveOperTypes(activeOperTypes);
      }
    });
  }, [loginUser, userRoleLevel]); // 加入所有函數內部使用的依賴項

  // 使用 useCallback 記憶化 fetchCtrlNoList 函數
  const fetchCtrlNoList = useCallback(() => {
    // 如果該區域的數據已經在快取中，直接使用快取數據
    if (ctrlNoCache[areaId]) {
      setCtrlNoOptionList(ctrlNoCache[areaId]);
      return;
    }

    // 否則發起 API 請求
    SelectListAPI.getSelectChemicalCtrlnoListByAreaId({
      ...getBasicLoginUserInfo(loginUser),
      areaId: areaId
    }).then(result => {
      if (isApiCallSuccess(result)) {
        // 將 "全部" 選項添加到結果中
        const updatedResults = [
          allSelectOption,
          ...(result.results || []) // 將現有的結果展開到新的陣列中
        ];

        // 更新快取
        setCtrlNoCache(prevCache => ({
          ...prevCache,
          [areaId]: updatedResults
        }));

        // 更新當前選項列表
        setCtrlNoOptionList(updatedResults);
      }
    });
  }, [loginUser, areaId, allSelectOption, ctrlNoCache]); // 加入所有函數內部使用的依賴項

  // 使用 useCallback 包裝 fetchLabOption 函數
  const fetchLabOption = useCallback(() => {
    LabAPI.getChemicalLabList({
      ...getBasicLoginUserInfo(loginUser)!
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const labOptionResult = result.results || [];

        if (!isArrayEmpty(labOptionResult)) {
          setSelectLabOption(labOptionResult[0]);
        }

        setLabOption(labOptionResult);
        setFetchLabOptionSuccess(true);
      }
    }).catch(error => {
      checkTimeoutAction(error, navigate, t);
    });
  }, [loginUser, navigate, t]); // 加入所有函數內部使用的依賴項

  useEffect(() => {
    if (loginUser) {
      fetchOption();
    }
  }, [loginUser, fetchOption]);

  useEffect(() => {
    if (loginUser && areaId) {
      fetchCtrlNoList();
    }
  }, [loginUser, areaId, fetchCtrlNoList]);

  // 然後在 useEffect 中使用
  useEffect(() => {
    if (loginUser) {
      fetchLabOption();
    }
    setSelectedCtrlNoOption(allSelectOption);
  }, [loginUser, allSelectOption, fetchLabOption]);

  useEffect(() => {
    // if (loginUser && isQuery) {
    if (loginUser) {
      fetchConfig();
    }
  }, [loginUser, isQuery, i18n.language]);
  useEffect(() => {
    if (loginUser && isAdd) {
      fetchVendorOption();
    }
  }, [loginUser, isAdd, i18n.language]);
  useEffect(() => {
    if (isAdd) {
      scrollToElementChemTable();
    }
  }, [isAdd]);

  useEffect(() => {
    changeAddItemChemInfo({
      labId
    });
  }, [selectLabOption.labId])

  useEffect(() => {
    const newWeightList = Array(addItemChemicalInfo.quantity).fill(null).slice(0, addItemChemicalInfo.quantity); // 確保長度為 quantity
    // 如果 quantity 減少,則截斷 weightList

    const updatedWeightList = newWeightList.map((_, index) => (addItemChemicalInfo.weightList || [])[index] || null);
    changeAddItemChemInfo({
      weightList: updatedWeightList
    });
  }, [addItemChemicalInfo.quantity]); // 動態設定表格的高度

  useEffect(() => {
    const visibleRows = Math.min((filteredResults || []).length, maxRows);
    const paddingHeight = visibleRows * visibleRows;
    setTableHeight(visibleRows * rowHeight + expandSubstanceHeight + paddingHeight);
  }, [filteredResults]);

  /**
   * 勾選ghs 健康危害分類 變更時
   */
  useEffect(() => {
    //透過key取得對應的value且過濾重複後
    //再透過選項去篩選對應的放入
    if (isArrayEmpty(selectedGhsClass)) {
      setSelectedGhsImg([]);
      setSelectedPubHazClass([]);
    } else {

      let allValues = (selectedGhsClass || []).reduce<string[]>((acc, key) => {
        if (ghsMappingObj[key]) {
          return acc.concat(ghsMappingObj[key]);
        }
        return acc;
      }, []);
      // 去除重複的值
      allValues = Array.from(new Set(allValues));
      // 將值分成兩個 array，根據開頭來分類
      const imageOptions = allValues.filter(value => value.startsWith(CONFIG_TYPE_GHS_IMG));
      const classOptions = allValues.filter(value => value.startsWith(CONFIG_ID_START_PUB_HAZ));
      // 根據 configId 來篩選 ghsImageOptions 和 pubHazClassOptions
      const selectedGhsImg = ghsImageOptions.filter(option => imageOptions.includes(option.configId));
      const selectedPubHazClass = pubHazClassOptions.filter(option => classOptions.includes(option.configId));
      // 更新選中的 GHS 圖示和公共危險分類
      setSelectedGhsImg(selectedGhsImg);
      setSelectedPubHazClass(selectedPubHazClass);
    }
  }, [selectedGhsClass])

  //物質選項 預設液態
  useEffect(() => {
    if (!isArrayEmpty(phaseStateOptions) && !addItemChemicalInfo.phaseState) {
      setSubstancePhaseState(phaseStateOptions.find(item => item.configValue === CHEMICAL_PHASE_LIQUID)?.configId || "");
    }
  }, [phaseStateOptions])

  // 商品相態預設第一個
  useEffect(() => {
    if (isArrayEmpty(hasSelectedPhaseStateOptions)) {
      return;
    }
    const existPhaseState = hasSelectedPhaseStateOptions.some(
      (option) => option.configId === addItemChemicalInfo.phaseState
    );
    if (!existPhaseState) {
      const firstPhaseStateId = hasSelectedPhaseStateOptions[0].configId;
      changeAddItemChemInfo({ phaseState: firstPhaseStateId });
    }
  }, [hasSelectedPhaseStateOptions]);

  const fetchVendorOption = () => {
    ManufacturerAPI.getEnableManufacturerList({
      ...getBasicLoginUserInfo(loginUser)!
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const getEnableManufacturerList = result.results || [];
        const selectItems: SelectItem[] = getEnableManufacturerList.map((item: EhsManufacturer) => ({
          ...initSelectItem,
          label: item.manufacturerName,
          value: item.manufacturerId
        }));
        setVendorOption(selectItems); // setVendorMap(getEnableManufacturerList.reduce((acc: { [configId: string]: EhsManufacturer }, config: EhsManufacturer) => {
        //   acc[config.manufacturerId] = config;
        //   return acc;
        // }, {}));
      }
    });
  };

  const initialConfigAcc = {
    phaseStateOptions: [] as EhsConfigParam[],
    tempratureTypeOptions: [] as EhsConfigParam[],
    mixtureTypeOptions: [] as EhsConfigParam[],
    storageLocationOptions: [] as EhsConfigParam[],
    packTypeOptions: [] as EhsConfigParam[],
    packMalOptions: [] as EhsConfigParam[],
    ghsClassOptions: [] as EhsConfigParam[],
    ghsImageOptions: [] as EhsConfigParam[],
    pubHazClassOptions: [] as EhsConfigParam[],
    chemClassOptions: [] as EhsConfigParam[],
    mixConcenTypeOptions: [] as EhsConfigParam[],
    powderLevelOptions: [] as EhsConfigParam[],
    ccbProcessTempOptions: [] as EhsConfigParam[]
  };

  const fetchConfig = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser)!,
      configTypeList: [CONFIG_TYPE_OPER_TYPE, CONFIG_TYPE_STATE, CONFIG_TYPE_CHEM_TEMPRATURE_TYPE, CONFIG_TYPE_CHEM_MIXTURE_TYPE, CONFIG_TYPE_CHEM_STORAGE_LOCATION,
        CONFIG_TYPE_CHEM, CONFIG_TYPE_PACKTYPE, CONFIG_TYPE_PACKMAL, CONFIG_TYPE_GHS_CLASS, CONFIG_TYPE_GHS_IMG, CONFIG_TYPE_PUBLIC_HAZARD, CONFIG_TYPE_CHEM,
        CONFIG_TYPE_MIX_CONCEN_TYPE, CONFIG_TYPE_POWDER_LEVEL, CONFIG_TYPE_CCB_PROCESS_TEMP]
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const rs = result.results || [];
        const configMap: { [key: string]: keyof typeof initialConfigAcc } = {
          [CONFIG_TYPE_STATE]: 'phaseStateOptions',
          [CONFIG_TYPE_CHEM_TEMPRATURE_TYPE]: 'tempratureTypeOptions',
          [CONFIG_TYPE_CHEM_MIXTURE_TYPE]: 'mixtureTypeOptions',
          [CONFIG_TYPE_CHEM_STORAGE_LOCATION]: 'storageLocationOptions',
          [CONFIG_TYPE_PACKTYPE]: 'packTypeOptions',
          [CONFIG_TYPE_PACKMAL]: 'packMalOptions',
          [CONFIG_TYPE_GHS_CLASS]: 'ghsClassOptions',
          [CONFIG_TYPE_GHS_IMG]: 'ghsImageOptions',
          [CONFIG_TYPE_PUBLIC_HAZARD]: 'pubHazClassOptions',
          [CONFIG_TYPE_CHEM]: 'chemClassOptions',
          [CONFIG_TYPE_MIX_CONCEN_TYPE]: 'mixConcenTypeOptions',
          [CONFIG_TYPE_POWDER_LEVEL]: 'powderLevelOptions',
          [CONFIG_TYPE_CCB_PROCESS_TEMP]: 'ccbProcessTempOptions',
        };

        const {
          phaseStateOptions,
          tempratureTypeOptions,
          mixtureTypeOptions,
          storageLocationOptions,
          packTypeOptions,
          packMalOptions,
          ghsClassOptions,
          ghsImageOptions,
          pubHazClassOptions,
          chemClassOptions,
          mixConcenTypeOptions,
          powderLevelOptions,
          ccbProcessTempOptions
        } = rs.reduce((acc: typeof initialConfigAcc, config: EhsConfigParam) => {
          const key = configMap[config.configType];
          if (key) acc[key].push(config);
          return acc;
        }, initialConfigAcc);

        setPhaseStateOptions(phaseStateOptions);
        setTempratureTypeOptions(tempratureTypeOptions);
        setMixtureTypeOptions(mixtureTypeOptions);
        setStorageLocationOptions(storageLocationOptions);
        setPackTypeOptions(packTypeOptions);
        setPackMalOptions(packMalOptions);
        setGhsClassOptions(ghsClassOptions);
        setGhsImageOptions(ghsImageOptions);
        setPubHazClassOptions(pubHazClassOptions);
        setChemClassOptions(chemClassOptions);
        setMixConcenTypeOptions(mixConcenTypeOptions);
        setPowderLevelOptions(powderLevelOptions);
        setCcbProcessTempOptions(ccbProcessTempOptions);
        setConfigMap(rs.reduce((acc: {
          [configId: string]: EhsConfigParam;
        }, config: EhsConfigParam) => {
          if (config.configType === CONFIG_TYPE_OPER_TYPE) {
            acc[config.configIvalue] = config;
          } else {
            acc[config.configId] = config;
          }

          return acc;
        }, {}));
      }
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setHasChangeSearchCondition(true);

    const handlers: { [key: string]: () => void } = {
      casno: () => setCasno(value),
      chemName: () => setChemName(value),
    };

    // 使用 name 作為索引
    const handler = handlers[name as keyof typeof handlers];
    if (handler) {
      handler();
    }
  };



  const handleSearchMethodChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchMethod(e.target.value);
    setHasChangeSearchCondition(true);
  };

  const handleSearch = () => {
    if (hasChangeSearchCondition) {
      setLoadingBlock(true); // 將輸入值去除前後空格
      const trimmedCtrlNo = selectedCtrlNoOption.value === allOptionValue ? '' : selectedCtrlNoOption.value
      const trimmedCasno = casno.trim();
      const trimmedChemName = chemName.trim(); // 檢查是否所有的輸入值都是空字串

      if (!trimmedCtrlNo && !trimmedCasno && !trimmedChemName) {
        showWarnToast(t('message.search.condition_required'));
        setLoadingBlock(false);
        return;
      } else {
        ChemicalAPI.getChemicalSearch({
          ...getBasicLoginUserInfo(loginUser)!,
          areaId,
          chemCtrlNo: trimmedCtrlNo,
          casNo: trimmedCasno,
          chemName: trimmedChemName,
          searchMethod: searchMethod,
          limitQty: SEARCH_LIMIT_QTY_CHEM_ITEM_ADD
        }).then(result => {
          if (isApiCallSuccess(result)) {
            const rs = result.results || [];
            setSearchResult(rs);

            if (isArrayEmpty(rs)) {
              showWarnToast(t('message.no_data'));
              setIsQuery(false);
            } else {
              setIsQuery(true);
            }

            setHasChangeSearchCondition(false);
          }

        }).catch(error => {
          checkTimeoutAction(error, navigate, t);
          console.error(error);
        }).finally(() => {
          setLoadingBlock(false);
        }); // 在這裡執行你的搜尋邏輯
      }
    }
  };

  const handleKeyDown = (e: any) => {
    if (isEnterKey(e)) {
      handleSearch();
    }
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilterSearchResult(e.target.value);
  };

  const handleCheckAddInfo = (item: EhsChemical) => {

    if (!validateAddInputs(item)) {
      return; // 若有錯誤則提前返回
    }

    if (needConfirmAddMixConcenMaxValue(item)) {
      confirmMsg(t('message.chemical.mixture_concen_exceeds_limit'), t).then((value) => {
        if (value) {
          getMatchConcenAndAddSubstance(item);
        }
      })
    } else {
      getMatchConcenAndAddSubstance(item);
    }

  };

  const validateAddInputs = (item: EhsChemical) => {
    const inputConcenType = inputMixConCenObj.concenType;
    const inputConcen = inputMixConCenObj.concenMin;

    // 檢查濃度輸入
    if (inputConcen === null) {
      return showWarnToastWithId(t('message.enter_concen'), "enter_concen_min");
    }
    if (inputConcen === 0) {
      return showWarnToastWithId(t('message.chemical.error.enter_zero_concen'), "enter_concen_zero");
    }
    if (inputConcenType === CONFIG_TYPE_MIX_CONCEN_TYPE_RANGE && !inputMixConCenObj.concenMax) {
      return showWarnToastWithId(t('message.enter_concen_max'), "enter_concen_max");
    }

    // 檢查是否為混合物
    if (isMixture) {
      if ((substances || []).length === mixtureMaxLimit) {
        return showWarnToastWithId(t('message.chemical.mixture_max_limit', { limit: mixtureMaxLimit }), "mixture_max_limit");
      }

      const existSubstance = (substances || []).some(substance => substance.chemId === item.chemId);
      if (existSubstance) {
        return showWarnToastWithId(t('message.chemical.duplicate_substance'), "duplicate_substance");
      }

    } else { // 如果不是混合物
      if (!isArrayEmpty(substances)) {
        return showWarnToastWithId(t('message.chemical.non_mixture_multiple_substances'), "non_mixture_multiple_substances");
      }
    }

    // 檢查物質相位狀態
    if (!substancePhaseState) {
      return showWarnToastWithId(t('message.chemical.choose_substance_phase'), "substance_phase");
    }

    return true; // 所有檢查都通過
  }

  const needConfirmAddMixConcenMaxValue = (item: EhsChemical) => {
    const inputConcen = inputMixConCenObj.concenMin;
    if (!isMixture || !inputConcen) {
      return false;
    }
    const totalConcentration = (substances || []).reduce((sum, mixture) => sum + (mixture.concentration || 0), 0);
    return (totalConcentration + inputConcen) > 100;
  }

  const getMatchConcenAndAddSubstance = (item: EhsChemical) => {
    ChemicalAPI.getChemConInfoByConcen({
      ...getBasicLoginUserInfo(loginUser!),
      chemId: item.chemId,
      concen: inputMixConCenObj.concenMin,
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const chemConInfo: EhsChemicalCon = result.results || {};
        const chemConId = chemConInfo.chemConId;
        if (chemConId) {
          addNewMixture(item, chemConInfo);
          setIsAdd(true);
        } else {
          showWarnToastWithId(t('message.chemical.error.add_product_concen'), "add_error_prod_concen");
        }
      }
    })
  }

  const scrollToElementChemTable = () => {
    if (chemTableRef.current) {
      window.scrollTo({
        top: chemTableRef.current.offsetTop + 20,
        behavior: 'smooth' // 平滑滾動
      });
    }
  };

  const handleErrorScroll = () => {
    scrollToFirstError();
  };

  const changeAddItemChemInfo = (info: object) => {
    setAddItemChemicalInfo({
      ...addItemChemicalInfo,
      ...info
    });
  };

  const handleGhsCheckBoxChange = (e: React.ChangeEvent<HTMLInputElement>, configId: string) => {
    const checked = e.target.checked;
    if (checked) {
      setSelectedGhsClass((prevState) => [...(prevState || []), configId]); // 將id添加到陣列中
    } else {
      setSelectedGhsClass((prevState) =>
        (prevState || []).filter((item) => item !== configId)
      ); // 從陣列中移除id
    }
  };
  const handleCheckedCategoryChange = (configId: string, config: EhsConfigParam, isChecked: boolean) => {
    setDetailCategoryMap(prevState => {
      const newState = { ...prevState };
      if (isChecked) {
        newState[configId] = config; // 勾選時加入 config
      } else {
        delete newState[configId]; // 取消勾選時移除該鍵
      }
      return newState;
    });
  };

  const handleOtherInfoChange = (type: string, content: string) => {
    setOtherInfoMap({ ...otherInfoMap, [type]: content });
  }

  const addNewMixture = async (item: EhsChemical, chemConInfo: EhsChemicalCon) => {
    let newMixture: EhsChemicalSubstance = {
      ...initEhsChemicalSubstance,
      mixId: `mix${mixCounter}`,
      chemId: item.chemId,
      chemConId: chemConInfo.chemConId,
      chemCtrlNo: item.chemCtrlNo,
      casNo: item.casno,
      nameList: item.nameList,
      phaseState: substancePhaseState,
      concentration: inputMixConCenObj.concenMin,
      concentrationMax: inputMixConCenObj.concenMax,
      categoryList: [] // 先設為空，稍後再更新
    };
    // 根據 isMixture 判斷 categoryList 的來源
    newMixture.categoryList = isMixture
      ? await matchMixtureChemClass(newMixture)
      : (chemConInfo.categoryList || []).map(category => ({ ...initEhsPurchaseSubstCategory, ...category }));
    setSubstances([...(substances || []), newMixture]);
    setMixCounter(mixCounter + 1); // 增加計數器
  };

  const updateMixture = (info: EhsChemicalSubstance) => {
    setSubstances(prevMixtures => {
      const updatedMixtures = [...(prevMixtures || [])];
      const index = updatedMixtures.findIndex(item => item.mixId === info.mixId);

      if (index !== -1) {
        updatedMixtures[index] = Object.assign({}, updatedMixtures[index], info);
      }

      return updatedMixtures;
    });
  };

  const deleteMixture = (mixture: EhsChemicalSubstance) => {
    setSubstances((substances || []).filter(item => item.mixId !== mixture.mixId));
  };

  const matchMixtureChemClass = async (mixture: EhsChemicalSubstance): Promise<EhsPurchaseSubstCategory[]> => {
    const { firstName, firstEnName } = getFirstChemNames(mixture.nameList, i18n.language);

    // 獲取 casno 和其他變數
    const {
      casNo: mixtureCasno = '',
      concentration: mixtureConcen = 0,
    } = mixture;

    // 創建 ChemicalClassMatch 物件
    const chemicalClassMatch: ChemicalClassMatch = {
      ...initChemicalClassMatch,
      casno: mixtureCasno,
      chName: firstName,
      enName: firstEnName,
      ruleConcen: mixtureConcen !== null ? mixtureConcen : 0,
    };

    try {
      const rs = await ChemicalAPI.getChemicalClassMatch({
        ...getBasicLoginUserInfo(loginUser),
        mixtureFlag: true,
        matchConcenFlag: true,
        matchList: [chemicalClassMatch],
      });

      if (isApiCallSuccess(rs)) {
        const categoryList: EhsPurchaseSubstCategory[] = [];
        const matchRs: ChemicalClassMatch[] = rs.results || [];

        matchRs.forEach((match) => {
          // 根據 match.configId 找到對應的 chemClassify
          const matchedClass = configMap[match.configId];
          if (matchedClass) {
            categoryList.push({ ...initEhsPurchaseSubstCategory, ...matchedClass });
          }
        });
        return categoryList; // 返回 categoryList
      }
    } catch (err) {
      console.error(err);
    }

    return []; // 若發生錯誤或 API 呼叫不成功，返回空陣列
  };

  const validateSubmitData = () => {
    const { weightList, boilingPoint, boilingPointMax } = addItemChemicalInfo;
    const hasNullWeight = (weightList || []).includes(null);
    const labId = selectLabOption.labId;
    if (!labId) {
      showWarnToast(t('message.chemical.no_choose_lab', { lab: labTitle }));
      return;
    }
    if (hasNullWeight) {
      showWarnToast(t('message.chemical.no_product_weight'));
      return;
    }
    if (isArrayEmpty(substances)) {
      showWarnToast(t('message.chemical.error.no_mixture'));
      return;
    }
    const isRange = boilType === ChemicalTemperatureType.Range;
    if (isChooseLiquid) {
      if (!boilingPoint) {
        showWarnToast(t('message.chemical.liquid_enter_boiling_point'));
        return;
      }
      if (isRange && (boilingPointMax === undefined || boilingPoint >= boilingPointMax)) {
        showWarnToast(t('message.chemical.error.boiling_point_range'));
        return;
      }
    }
    if (isArrayEmpty(selectedGhsClass)) {
      showWarnToast(t('message.chemical.no_checked_ghs_classify'));
      return;
    }
    if (isArrayEmpty(selectedChemClass)) {
      showWarnToast(t('message.chemical.no_checked_classify'));
      return;
    }
    return true; // 所有檢查都通過
  };

  const onSubmit = async (data: any) => {
    if (!validateSubmitData()) {
      return;
    }

    const isConfirm = await confirmMsg(t('message.confirm.chemical_item_add', { operType: operTypeName }), t);
    if (!isConfirm) {
      return;
    }

    setLoadingBlock(true);
    const finalInfo = {
      ...addItemChemicalInfo,
      ...data,
    }
    const newPurchase: EhsPurchase = {
      ...initEhsPurchase,
      ...finalInfo,
      labId: selectLabOption.labId
    }
    const newDetail: EhsPurchaseDetail = {
      ...initEhsPurchaseDetail,
      ...finalInfo,
      containsMix: Number(isMixture),
      arrivalDate: data.addDate,
      inspDate: data.addDate,
    }

    const allConfigIds = [
      ...selectedGhsClass,
      ...selectedGhsImg.map(img => img.configId),
      ...selectedPubHazClass.map(hazClass => hazClass.configId),
      ...Object.keys(detailCategoryMap)
    ];
    const detailCategoryList: EhsPurchaseDetailCategory[] = [];
    // 遍歷所有 configId 並推入 detailCategoryList
    allConfigIds.forEach((configId) => {
      detailCategoryList.push({
        ...initEhsPurchaseDetailCategory,
        configId
      });
    });

    const otherInfoList: EhsOtherInfo[] = Object.entries(otherInfoMap)
      .filter(([_, value]) => value) // 過濾掉值為空的屬性
      .map(([key, value]) => ({ ...initEhsOtherInfo, otherType: key, otherContent: value }));
    const substanceList: EhsPurchaseSubst[] = (substances || []).map((substance) => {
      const { concentration, concentrationMax, phaseState } = substance;
      return {
        ...initEhsPurchaseSubst,
        ...substance,
        conType: concentrationMax ? CONFIG_TYPE_MIX_CONCEN_TYPE_RANGE : CONFIG_TYPE_MIX_CONCEN_TYPE_SINGLE,
        conLower: concentration,
        conUpper: concentrationMax,
        substPhaseState: phaseState
      };
    });

    ChemicalAPI.addChemicalItem({
      ...getBasicLoginUserInfo(loginUser)!,
      ...finalInfo,
      operType: operateType,
      purchase: newPurchase,
      purchaseDetail: newDetail,
      substanceList: substanceList,
      detailCategoryList,
      otherInfoList
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
      }
    }).catch(err => {
      errorMsg(t('message.error'));
    }).finally(() => {
      setLoadingBlock(false);
    })
  };

  return <StlyedChemicalItemAdd $tableHeight={tableHeight}>
    <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
      <div className="d-flex flex-column p-0" id="content">
        {<Dialog content={<EditWeight onClose={() => setShowEditWeights(false)} weights={addItemChemicalInfo.weightList} setWeights={weights => changeAddItemChemInfo({
          weightList: weights
        })} />} show={showEditWeights} />}
        {
          /* BEGIN scrollbar */
        }
        <div className="app-content-padding flex-grow-1">
          {
            /* BEGIN breadcrumb */
          }
          <Breadcrumbs items={[{
            label: t("func.chemical.manage")
          }, {
            label: t("func.chemical.item_add")
          }]} />
          {
            /* END breadcrumb */
          }
          {
            /* BEGIN page-header */
          }
          <h1 className="page-header">{t("func.chemical.item_add")} </h1>
          {
            /* END page-header */
          }

          <div className="card">
            <div className="card-body">
              {hasChemLabOption ? <div className="row">
                <label className="text-center h2">{msgNoOperChemLab}</label>
              </div> : <div className="row align-items-start">
                <div className="col-xl-4 mb-2" title={`${hasAddSubstance ? t('text.disabled.has_add_substance') : ''}`}>
                  <label className="h4 mt-sm-2 mt-md-0">{labTitle}</label>
                  <select className="form-select mt-sm-1 mt-md-0"
                    disabled={hasAddSubstance}
                    onChange={e => {
                      setSelectLabOption(labOption.find(item => item.labId === e.target.value) || initEhsLab);
                      setSelectedCtrlNoOption(allSelectOption); // 重置列管編號選項為「全部」
                      setHasChangeSearchCondition(true);
                    }}>
                    {(labOption || []).map(item => {
                      return <option value={item.labId} key={item.labId}>{item.labName}</option>;
                    })}
                  </select>
                </div>
                <div className="col-sm-12 col-md-6 col-xl-2 mb-2" title={`${hasAddSubstance ? t('text.disabled.has_add_substance') : ''}`}>
                  <label className="h4 mt-sm-2 mt-md-0">{t('text.chemical.operation_item')}</label><br />
                  <select className="form-select mt-sm-1 mt-md-0"
                    disabled={hasAddSubstance}
                    onChange={e => {
                      setOperateType(parseInt(e.target.value));
                    }}>
                    {(activeOperTypes || []).map(item => {
                      return <option value={item} key={'opertTypeOption' + item}>{configMap[item]?.configName}</option>;
                    })}
                  </select>
                </div>
                <div className="col-sm-12 col-md-6 col-xl-2" title={`${hasAddSubstance ? t('text.disabled.has_add_substance') : ''}`}>
                  <label className="h4 mt-sm-2 mt-md-0">{t('text.chemical.item_add_contain_mixture_title')}</label><br />
                  <YesNoRadioButtons
                    isChecked={isMixture}
                    disabled={hasAddSubstance}
                    idPrefix="containsMix"  //  唯一 idPrefix
                    onSelectionChange={(value) => {
                      setContainMixture(value)
                      if (!value && hasMultipleSubstance) {
                        setSubstances([(substances || [])[0]].filter(Boolean));
                      }
                    }}  // 通用的選擇變更處理邏輯
                  />
                </div>
              </div>
              }
            </div>
            {
              /* <!-- 查詢化學品 --> */
            }
            {!hasChemLabOption && <div className="card card-body mt-3">
              <h4>{t('text.chemical.search')}</h4>
              <div className="row">
                <div className="col-xl-2">
                  <label>{t('text.ctrl_no')}</label>
                  <Select options={ctrlNoOptionList || []} isSearchable menuPosition="fixed" placeholder={t('text.all')} className="w-100"
                    menuPortalTarget={document.body}
                    onChange={(selectedOption: any) => {
                      setSelectedCtrlNoOption(selectedOption);
                      setHasChangeSearchCondition(true);
                    }} value={selectedCtrlNoOption}
                    noOptionsMessage={() => t('message.search.non_ctrl_no')}
                  />
                </div>
                <div className="col-xl-2">
                  <label>{t('text.chemical.casno')}</label>
                  <input type="text" name="casno" className="form-control mt-1" value={casno} onKeyDown={handleKeyDown} onChange={handleChange} />
                </div>
                <div className="col-xl-2">
                  <label>{t('text.chemical.name')}</label>
                  <input type="text" name="chemName" className="form-control mt-1" value={chemName} onKeyDown={handleKeyDown} onChange={handleChange} />
                </div>
                <SearchMethod className="col-xl-2" searchMethod={searchMethod} handleSearchMethodChange={handleSearchMethodChange} />
                <div className="col-xl-2 align-self-center">
                  <div className="d-grid">
                    <button type="button" className="btn btn-primary" id="search-chemicals-btn" onClick={handleSearch}>
                      <i className="fas fa-magnifying-glass me-1 fs-5" />
                      {t('button.search.item')}
                    </button>
                  </div>
                </div>
              </div>
              {isQuery && <div className="row mt-3 d-flex align-items-center" id="show-chemicals">
                <hr />
                <div className="col-12 d-flex justify-content-between">
                  <label className="h4">{t('text.search.result')}</label>
                  {<div className="col-3">
                    <input type="text" placeholder={t('message.filter_table_data')} value={filterSearchResult} onChange={handleFilterChange} className="form-control mb-3" />
                  </div>}
                </div>
                <div className="col-12 table-responsive" id="search-result">
                  <table className="table table-hover fs-5">
                    <thead className="bg-lime-200">
                      <tr>
                        <th className="text-start item-width-10">{t('table.title.ctrl_no')}</th>
                        <th className="text-start item-width-10">{t('table.title.casno')}</th>
                        <th className="text-start item-width-35">{t('table.title.name')}</th>
                        <th className="text-start item-width-35">{t('table.title.action')}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {(filteredResults || []).map((item, index) => {
                        const {
                          chemId,
                          chemCtrlNo,
                          casno,
                          nameList
                        } = item;
                        const {
                          firstName,
                          firstEnName
                        } = getFirstChemNames(nameList, i18n.language);
                        return <tr key={"search-result-tr-" + index}>
                          <td className="align-middle" data-title={t('table.title.ctrl_no')}>{chemCtrlNo}</td>
                          <td className="align-middle" data-title={t('table.title.casno')}>{casno}</td>
                          <td className="align-middle" data-title={t('table.title.name')}>{isNotEnglish && firstName && <>{firstName}<br /></>}{firstEnName}</td>
                          <td className="align-middle" data-title={t('table.title.action')}>
                            <InputMixConCenContext.Provider value={{ inputMixConCenObj, setInputMixConCenObj }}>
                              <AddSubstanceDiv chemInfo={item}
                                mixConcenTypeOptions={mixConcenTypeOptions} phaseStateOptions={phaseStateOptions}
                                selectedPhaseState={substancePhaseState}
                                defaultExpanded={inputSubstanceExpandIdx === chemId}
                                setExpendId={(id: string) => { setInputSubstanceExpandIdx(id) }}
                                setPhaseState={(phaseState: string) => { setSubstancePhaseState(phaseState) }}
                                handleAdd={() => {
                                  handleCheckAddInfo(item);
                                  // setQueryResultExpandIdx("");
                                }} />
                            </InputMixConCenContext.Provider>
                          </td>
                        </tr>;
                      })}
                    </tbody>
                  </table>
                </div>
              </div>}
            </div>}
            {
              /* <!-- 化學品新增 --> */
            }
            {isAdd && <div className="card card-body" id="chemical-table" ref={chemTableRef}>
              <label className="h4">{t('text.chemical.item_add_title')}<span className="text-danger ms-3">{t('text.chemical.operation_item')}({operTypeName})</span></label>
              {isOperPurch && <span className="text-danger">*{t('text.chemical.item_add_no_mail_title')}</span>}
              <form
                onSubmit={handleSubmit(onSubmit, (errors) => {
                  handleErrorScroll(); // 如果有錯誤，滾動到錯誤位置
                })}
              >

                <Panel>
                  {
                    /* 混合物資訊 */
                  }
                  {<PanelBody>
                    <div className="row border border-2 border-info py-3">
                      <>
                        <h3>
                          <label className="d-flex align-items-center fw-bold">
                            {t('text.chemical.substance')}<span className="text-danger ms-1">*</span>
                            {hasMultipleSubstance && <button type="button" className="btn btn-danger ms-3" onClick={(e) => {
                              setSubstances([]);
                              setMixCounter(0);
                            }}>
                              <i className="fas fa-trash"></i>{t('button.clear_all')}
                            </button>}
                          </label>
                        </h3>
                      </>
                      <div className="col-12 mb-4">
                        <div className="col-12 row mix-chem-container">
                          {isMixture && <label className="text-danger">*{t('message.chemical.item_add_class_match_desc')}</label>}
                          {(substances || []).map((data, index) => {
                            return <div className="col-12 col-md-4 col-xl-4" key={`normalMixListdiv-${index}`}>
                              <div className="rounded-rectangle p-1 ms-1 mt-2"
                                key={`normalMixList-${index}`}>
                                <button type="button" className="btn btn-danger btn-icon float-end me-1 mt-1" onClick={() => {
                                  deleteMixture(data);
                                }}>
                                  <i className="fas fa-times"></i>
                                </button>
                                <MixChemical key={index + `normal` + data.mixId}
                                  hasChemInfo
                                  mixInfo={data} mixtureTypeOptions={mixtureTypeOptions}
                                  hasPubHazClass={!isArrayEmpty(selectedPubHazClass)}
                                  phaseStateOptions={phaseStateOptions}
                                  chemClassOptions={chemClassOptions}
                                  onChange={info => {
                                    updateMixture(info);
                                  }}
                                />
                              </div>
                            </div>;
                          })}
                        </div>
                      </div>
                    </div>
                  </PanelBody>
                  }
                  {/* ghs危害分類 */}
                  <PanelBody className="ghs-panel-body" >
                    <div className="row border border-2 border-info py-3">
                      <ToggleDisplayDiv
                        className="row"
                        header={(isVisible) => (
                          <h3>
                            <label
                              className="d-flex align-items-center fw-bold cursor-pointer" // 加上指標樣式讓它看起來像是可以點擊的
                            >
                              <i className={`ms-2 me-1 fas ${isVisible ? "fa-chevron-up" : "fa-chevron-down"}`} />
                              {t('text.chemical.ghs_and_info')}<span className="text-danger ms-1">*</span>
                            </label>
                          </h3>)}
                        content={<div className="row">
                          <PdfViewer pdfFile={sdsFile} styledClassName="col-sm-12 col-md-6 col-xl-6" noFileMsg={t('message.upload_sds_file')} />
                          <div className="col-sm-12 col-md-5 col-xl-5  mb-4">
                            <div className="row">
                              <div className="col-sm-12 col-md-6 mb-4">
                                <label className="fw-bold">{t('table.title.sds_file')}<span className="text-danger">*</span></label>
                                <Controller
                                  control={control}
                                  name={registerObj.sdsFile}
                                  rules={{ required: t("message.select") }}
                                  render={({ field: { onChange, onBlur, value, ref } }) => (
                                    <InputFileUpload acceptedFileTypes={UPLOAD_ACCEPT_TYPE_PDF} onFileChange={(file) => {
                                      setValue(registerObj.sdsFile, file, { shouldValidate: true });
                                    }} />
                                  )}
                                />
                                {errors[registerObj.sdsFile] && <div className="mt-1"><span className="text-danger">{errors[registerObj.sdsFile]?.message?.toString()}</span></div>}
                              </div>
                              <div className="col-sm-12 col-md-6 mb-4">
                                <label className="fw-bold">{t('text.chemical.sds_file_exp_date')}<span className="text-danger">*</span></label>
                                <Controller
                                  control={control}
                                  name={registerObj.sdsExpDate}
                                  rules={{ required: t("message.select") }}
                                  render={({ field: { onChange, onBlur, value, ref } }) => (
                                    <InputDate className={`form-control ${errors[registerObj.sdsExpDate] && 'is-invalid error-field'}`}
                                      defaultValue={getFormatDateSlash(value)}
                                      minDate={getFormatDateSlash(nowDate)}
                                      onChange={date => {
                                        setValue(registerObj.sdsExpDate, date, { shouldValidate: true });
                                      }}
                                    />
                                  )}
                                />
                                {errors[registerObj.sdsExpDate] && <div className="mt-1"><span className="text-danger">{errors[registerObj.sdsExpDate]?.message?.toString()}</span></div>}
                              </div>
                              <div className="rounded-rectangle-normal p-1 ms-1 mt-2">
                                <ToggleDisplayDiv
                                  header={(isVisible) => (
                                    <h3 className="mt-2">
                                      <label
                                        className="d-flex align-items-center fw-bold cursor-pointer" // 加上指標樣式讓它看起來像是可以點擊的
                                      >
                                        <i className={`ms-2 me-1 fas ${isVisible ? "fa-chevron-up" : "fa-chevron-down"}`} />
                                        {t('text.chemical.phy_and_chem_prop')}
                                        {isChooseLiquid && <span className="text-danger ms-1">*</span>}
                                      </label>
                                    </h3>
                                  )}
                                  content={
                                    <div className="ms-2">
                                      <h5 className="text-primary">
                                        {t('text.chemical.phy_and_chem_prop_ref_sds')}
                                      </h5>
                                      <ChemicalTemperatureObj extClassName="mt-3" tempratureTypeOptions={tempratureTypeOptions} temperatureType={CHEMICAL_TEMPERATURE_BOILING} checked={boilType} checkedRadioOnChange={e => {
                                        const tempType = getChemicalTemperatureType(parseInt(e.currentTarget.value))!!;
                                        setBoilType(tempType);

                                        if (tempType === ChemicalTemperatureType.Single) {
                                          changeAddItemChemInfo({
                                            boilingPointMax: ""
                                          });
                                        }
                                      }} minValue={addItemChemicalInfo.boilingPoint} maxValue={addItemChemicalInfo.boilingPointMax} minOnChange={num => {
                                        changeAddItemChemInfo({
                                          boilingPoint: num
                                        }); // if (e.target.value !== "") {
                                        //   setBoilCheck(false);
                                        //   setBoilIntervalCheck(false);
                                        // }
                                      }} maxOnChange={num => {
                                        changeAddItemChemInfo({
                                          boilingPointMax: num
                                        }); // setBoilIntervalCheck(false);
                                      }} phaseStateValue={phaseStateValue} />
                                      <ChemicalTemperatureObj extClassName="mt-3" tempratureTypeOptions={tempratureTypeOptions} temperatureType={CHEMICAL_TEMPERATURE_MELTING} checked={meltType} // intervalCheck={true}
                                        checkedRadioOnChange={e => {
                                          const tempType = getChemicalTemperatureType(parseInt(e.currentTarget.value))!!;
                                          setMeltType(tempType);

                                          if (tempType === ChemicalTemperatureType.Single) {
                                            changeAddItemChemInfo({
                                              meltingPointMax: ""
                                            });
                                          }
                                        }} minValue={addItemChemicalInfo.meltingPoint} maxValue={addItemChemicalInfo.meltingPointMax} minOnChange={num => {
                                          changeAddItemChemInfo({
                                            meltingPoint: num
                                          }); // if (e.target.value !== "") {
                                          //   setBoilCheck(false);
                                          //   setBoilIntervalCheck(false);
                                          // }
                                        }} maxOnChange={num => {
                                          changeAddItemChemInfo({
                                            meltingPointMax: num
                                          }); // setBoilIntervalCheck(false);
                                        }} />
                                      <div className="col-sm-12 col-md-4 col-xl-4 my-3">
                                        <label className="fw-bold">{t('table.title.chemical.density')}</label>
                                        <Controller
                                          control={control}
                                          name={registerObj.density}
                                          render={({ field: { onChange, onBlur, value } }) => (
                                            <InputNumFloat value={value} className={`form-control`} minValue={0}
                                              allowEmptyUndefine
                                              placeholder=""
                                              onBlur={(num) => onBlur} />
                                          )}
                                        />
                                      </div>
                                    </div>
                                  }
                                />
                              </div>
                              <>
                                {!isArrayEmpty(selectedGhsImg) && <div className="rounded-rectangle-normal p-1 ms-1 mt-2 ghs-rad-box">
                                  <ToggleDisplayDiv
                                    header={(isVisible) => (
                                      <h3 className="mt-2">
                                        <label
                                          className="d-flex align-items-center fw-bold cursor-pointer" // 加上指標樣式讓它看起來像是可以點擊的
                                        >
                                          <i className={`ms-2 me-1 fas ${isVisible ? "fa-chevron-up" : "fa-chevron-down"}`} />
                                          {t('text.chemical.ghs_img')}
                                        </label>
                                      </h3>
                                    )}
                                    content={(
                                      <>
                                        <h5 className="text-danger ms-2">{t('message.chemical.ghs_class_system_auto')}</h5>
                                        {(
                                          <div className="col-xl-12 mb-3">
                                            {(selectedGhsImg || []).map((filteredItem) => {
                                              return (
                                                <React.Fragment key={'ghs-img-' + filteredItem.configId}>
                                                  <GhsImage
                                                    src={filteredItem.configValue}
                                                    alt={filteredItem.configName}
                                                    title={filteredItem.configName}
                                                  />
                                                </React.Fragment>
                                              );
                                            })}
                                          </div>
                                        )}
                                      </>
                                    )}
                                    externalVisible={!isArrayEmpty(selectedGhsImg)}
                                  />
                                </div>}
                                {!isArrayEmpty(selectedPubHazClass) && <div className="rounded-rectangle-normal p-1 ms-1 mt-2 ghs-rad-box">
                                  <ToggleDisplayDiv
                                    header={(isVisible) => (
                                      <h3 className="mt-2">
                                        <label
                                          className="d-flex align-items-center fw-bold cursor-pointer" // 加上指標樣式讓它看起來像是可以點擊的
                                        >
                                          <i className={`ms-2 me-1 fas ${isVisible ? "fa-chevron-up" : "fa-chevron-down"}`} />
                                          {t('text.chemical.public_hazard_classify')}
                                        </label>
                                      </h3>
                                    )}
                                    content={(
                                      <>
                                        <h5 className="text-danger ms-2">{t('message.chemical.ghs_class_system_auto')}</h5>
                                        {(
                                          <div className="col-xl-12 mb-3 ms-3">
                                            {(selectedPubHazClass || []).map((filteredItem) => (
                                              <React.Fragment key={'pub-haz-class-' + filteredItem.configId}>
                                                <label className="mb-2">{filteredItem.configName}</label>
                                                <br />
                                              </React.Fragment>
                                            ))}
                                          </div>
                                        )}
                                      </>
                                    )}
                                    externalVisible={!isArrayEmpty(selectedPubHazClass)}
                                  />
                                </div>}
                                {!isArrayEmpty(selectedGhsClass) && <div className="rounded-rectangle-normal p-1 ms-1  mt-2 ghs-rad-box">
                                  <ToggleDisplayDiv
                                    header={(isVisible) => (
                                      <h3 className="mt-2">
                                        <label
                                          className="d-flex align-items-center fw-bold cursor-pointer"// 加上指標樣式讓它看起來像是可以點擊的
                                        >
                                          <i className={`ms-2 me-1 fas ${isVisible ? "fa-chevron-up" : "fa-chevron-down"}`} />
                                          {t('text.chemical.ghs_classification')} <span className="mx-1">{t('text.preview')}</span>
                                          <span className="text-danger ms-1">*</span>
                                        </label>
                                      </h3>
                                    )}
                                    content={(
                                      <>
                                        {/* 顯示已勾選的主標題和副標題 */}
                                        <div className="mb-3 ms-2 selected-items-container">
                                          {(mainGhsClass || []).map((ghs) => (
                                            (subGhsClassObject[ghs.configId] || []).some(subGhs => (selectedGhsClass || []).includes(subGhs.configId)) && (
                                              <div key={'ghs-class-selected-' + ghs.configId} className="selected-item">
                                                <strong>{ghs.configName}</strong>
                                                <ul>
                                                  {(subGhsClassObject[ghs.configId] || []).map((subGhs) => (
                                                    (selectedGhsClass || []).includes(subGhs.configId) && (
                                                      <li key={'ghs-class-selected-sub-' + subGhs.configId}>{subGhs.configName}</li>
                                                    )
                                                  ))}
                                                </ul>
                                              </div>
                                            )
                                          ))}
                                        </div>
                                      </>
                                    )}
                                    externalVisible={!isArrayEmpty(selectedGhsClass)}
                                  />
                                </div>}
                                <div className="rounded-rectangle-normal p-1 ms-1  mt-2 ghs-rad-box">
                                  <ToggleDisplayDiv
                                    header={(isVisible) => (
                                      <h3 className="mt-2">
                                        <label
                                          className="d-flex align-items-center fw-bold cursor-pointer" // 加上指標樣式讓它看起來像是可以點擊的
                                        >
                                          <i className={`ms-2 me-1 fas ${isVisible ? "fa-chevron-up" : "fa-chevron-down"}`} />
                                          {t('text.chemical.ghs_classification')} {t('text.edit')}
                                          <span className="text-danger ms-1">*</span>
                                        </label>
                                      </h3>
                                    )}
                                    content={(
                                      <>
                                        <h5 className="text-danger ms-2">{t('message.checked_least_one')}</h5>
                                        <h5 className="text-primary ms-2">{t('text.chemical.haz_and_identify_ref_sds')}</h5>
                                        <input
                                          type="text"
                                          className="form-control w-50 mb-3 ms-2"
                                          placeholder={t('message.filter_option_data')}
                                          value={filterGhsOptionText}
                                          onChange={(e) => setFilterGhsOptionText(e.target.value)}
                                        />
                                        {(mainGhsClass || [])
                                          .filter(ghs =>
                                            ghs.configName.toLowerCase().includes(filterGhsOptionText.toLowerCase()) ||
                                            (subGhsClassObject[ghs.configId] || []).some(subGhs => subGhs.configName.toLowerCase().includes(filterGhsOptionText.toLowerCase()))
                                          )
                                          .map((ghs, index) => {
                                            return (
                                              <React.Fragment key={ghs.configId}>
                                                <div className="col-12 ms-3">
                                                  <h4>
                                                    <label className="d-flex align-items-center fw-bold">
                                                      {ghs.configName}
                                                    </label>
                                                  </h4>
                                                </div>
                                                <div className="d-md-flex align-items-center ms-3 me-3 mb-3">
                                                  {(subGhsClassObject[ghs.configId] || []).map((subGhs) => (
                                                    <div
                                                      className="form-check form-check-inline mt-3"
                                                      key={"check-ghs-class-" + subGhs.configId}
                                                    >
                                                      <input
                                                        className="form-check-input"
                                                        type="checkbox"
                                                        value={subGhs.configId}
                                                        data-parsley-mincheck="1"
                                                        id={"check" + subGhs.configId}
                                                        checked={(selectedGhsClass || []).includes(subGhs.configId)}
                                                        onChange={(e) => {
                                                          handleGhsCheckBoxChange(e, subGhs.configId);
                                                        }}
                                                      />
                                                      <label
                                                        className="form-check-label"
                                                        htmlFor={"check" + subGhs.configId}
                                                      >
                                                        {subGhs.configName}
                                                      </label>
                                                    </div>
                                                  ))}
                                                </div>
                                                <hr />
                                              </React.Fragment>
                                            );
                                          })}
                                      </>
                                    )}
                                  />
                                </div>
                              </>
                            </div>
                          </div>
                        </div>} />
                    </div>
                  </PanelBody>
                  {/* 化學品分類 */}
                  {!isArrayEmpty(selectedChemClass) && <PanelBody>
                    <div className="row border border-2 border-info py-3">
                      <div>
                        <h3>
                          <label className="d-flex align-items-center fw-bold">
                            {/* <i className={`ms-2 me-1 fas ${isVisible ? "fa-chevron-up" : "fa-chevron-down"}`} /> */}
                            {t("text.chemical.product_chem_class")}<span className="text-danger ms-1">*</span>
                          </label>
                        </h3>
                      </div>
                      <div className="col-12 mb-4">
                        {(selectedChemClass || []).map((chemClass: EhsConfigParam) =>
                          <React.Fragment key={'category_' + chemClass.configId}>
                            <span className="d-inline-block mb-1">
                              <ChemicalClassificationBadge item={chemClass} />
                            </span>
                          </React.Fragment>
                        )}
                      </div>
                    </div>
                  </PanelBody>}
                  {/* 新增必填 */}
                  <PanelBody>
                    <RequiredInfoPanel
                      register={register}
                      control={control}
                      setValue={setValue}
                      errors={errors}
                      registerObj={registerObj}
                      isOperPurch={isOperPurch}
                      operTypeName={operTypeName}
                      vendorOption={vendorOption}
                      storageLocationOptions={storageLocationOptions}
                      hasSelectedPhaseStateOptions={hasSelectedPhaseStateOptions}
                      powderLevelOptions={powderLevelOptions}
                      ccbProcessTempOptions={ccbProcessTempOptions}
                      isChooseSolid={isChooseSolid}
                      isChooseLiquid={isChooseLiquid}
                      addItemChemicalInfo={addItemChemicalInfo}
                      changeAddItemChemInfo={changeAddItemChemInfo}
                      hasSubmit={hasSubmit}
                      setShowEditWeights={setShowEditWeights}
                    />
                  </PanelBody>
                  <PanelBody>
                    <OtherInfoPanel
                      register={register}
                      control={control}
                      registerObj={registerObj}
                      packTypeOptions={packTypeOptions}
                      packMalOptions={packMalOptions}
                      detailCategoryMap={detailCategoryMap}
                      handleCheckedCategoryChange={handleCheckedCategoryChange}
                      handleOtherInfoChange={handleOtherInfoChange}
                      CONFIG_VAL_OTHER={CONFIG_VAL_OTHER}
                      CONFIG_TYPE_PACKTYPE={CONFIG_TYPE_PACKTYPE}
                      CONFIG_TYPE_PACKMAL={CONFIG_TYPE_PACKMAL}
                    />
                  </PanelBody>
                </Panel>
                <div className="text-center">
                  {hasOperTypeOption && <button type="submit" className="btn btn-success my-3 item-width-30 fs-5" onClick={() => {
                    setHasSubmit(true);
                  }}><i className="fas fa-plus me-1" />{t('button.submit_confirm')} ({operTypeName})</button>}
                </div>
              </form>
            </div>}
          </div>
        </div>
        {  /* BEGIN #footer */}
        <Footer />
        {  /* END #footer */}
      </div>
    </BlockUi>
  </StlyedChemicalItemAdd>;
}

const StlyedChemicalItemAdd = styled.div<{
  $tableHeight?: number;
}>`
  padding-bottom:150px;
  
  .ghs-rad-box {
    max-height: 400px; /* 設置面板最大高度 */
    overflow-y: auto; /* 當內容超過最大高度時顯示垂直滾動條 */
  }
  
  /* GHS 區塊選項預覽：美化長文字為可換行徽章 chip，並以響應式網格排列 */
  .ghs-panel-body .selected-items-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
  }
  @media (min-width: 768px) {
    .ghs-panel-body .selected-items-container {
      grid-template-columns: 1fr 1fr;
    }
  }
  .ghs-panel-body .selected-item {
    background: #fafafa;
    border: 1px solid #e5e7eb;
    border-left: 4px solid #60a5fa;
    border-radius: 6px;
    padding: 10px 12px;
  }
  .ghs-panel-body .selected-item > strong {
    display: inline-block;
    margin-bottom: 6px;
    color: #111827;
  }
  .ghs-panel-body .selected-item ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  .ghs-panel-body .selected-item ul li {
    display: inline-flex;
    align-items: center;
    max-width: 100%;
    padding: 4px 8px;
    border-radius: 999px;
    background: #eef2ff; /* 淡紫藍 */
    color: #1f2937;
    font-size: 12px;
    line-height: 1.3;
    white-space: normal;
    word-break: break-word;
    hyphens: auto;
  }

  /* GHS 編輯區 checkbox 長文字優化（允許換行、不擠壓） */
  .ghs-panel-body .form-check.form-check-inline {
    display: inline-flex;
    align-items: flex-start;
    margin-right: 16px;
    max-width: 100%;
    white-space: normal;
  }
  .ghs-panel-body .d-md-flex {
    flex-wrap: wrap;
    gap: 8px 16px;
  }
  .ghs-panel-body .form-check-label {
    margin-left: 6px;
    overflow-wrap: anywhere;
    word-break: break-word;
    line-height: 1.25;
  }
  
  .rounded-rectangle-normal {
    border: 1px solid gray;
    border-radius: 5px;
  }

  .mix-chem-container{
    display: flex; /* 啟用 Flexbox */
    flex-wrap: wrap; /* 允許換行 */
  }

  .rounded-rectangle { 
    border: 1px solid gray;
    border-radius: 5px;
    padding: 10px;
    margin: 10px; /* 可以根據需要調整 */
    flex: 1 0 200px; /* 設定彈性寬度，最小寬度為 200px */
  }

  .rounded-rectangle:hover {
      box-shadow: 0 0 10px #87CEEB; /* 添加蓝色阴影 */
  }

  #search-result {
    // height: ${props => props.$tableHeight}px;
  }

  // #search-result thead {
      // position: sticky;
      // top: 0;
      // z-index: 2; /* 设置标题行的层级高于其他内容 */
      // margin: 0px;
      // height: 45px;
  // }

        th, td {
            padding: 8px;
            text-align: left;
        }

  label {
    user-select: none;
  }

  /* 商品明細包裝表格垂直文字 */
  table tr td.w-25 {
      -webkit-writing-mode: vertical-lr;
      writing-mode: vertical-lr;
      vertical-align: middle;
      letter-spacing: .5rem;
  }

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }  
  table {
    position:relative;
    th {
      text-align: center;
    }
    tbody tr:nth-of-type(odd){
      // background:#e9ecef;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 110px;
        text-align:left;
        min-height:50px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 15px;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }
    }
  }
`;
export default ChemicalItemAdd;