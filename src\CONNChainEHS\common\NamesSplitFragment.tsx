import React from 'react';

interface Props {
    names: string;
    separator?: string;
    lineBreak?: React.ReactNode; // 將換行符號定義為可選的 React 元素
}

const NamesSplitFragment: React.FC<Props> = ({ names = "", separator = ",", lineBreak = <br /> }) => {
    return (
        <>
            {names?.split(separator).map((orgName, index) => (
                <React.Fragment key={index}>
                    {orgName}
                    {index !== names.split(separator).length - 1 && lineBreak} {/* 換行或替換的分開條件 */}
                </React.Fragment>
            ))}
        </>
    );
}

export default NamesSplitFragment;
