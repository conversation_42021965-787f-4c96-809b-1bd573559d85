import { useState, useEffect } from "react";
import { ConfigAPI } from "../../api/configAPI";
import {
  CONFIG_TYPE_PUBLIC_HAZARD,
  CONFIG_TYPE_GHS_IMG,
} from "../constant/constants";
import { EhsConfigMapping } from "../models/EhsConfigParamMapping";
import { getBasicLoginUserInfo } from "../utils/authUtil";
import { isApiCallSuccess } from "../utils/resultUtil";

const useGhsMapping = (loginUser: any) => {
  const [ghsMappingObj, setGhsMappingObj] = useState<{
    [key: string]: string[];
  }>({});

  const fetchGhsMappingData = () => {
    ConfigAPI.getConfigMapping({
      ...getBasicLoginUserInfo(loginUser),
      configTypeList: [CONFIG_TYPE_PUBLIC_HAZARD, CONFIG_TYPE_GHS_IMG],
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const ghsMapping: EhsConfigMapping[] = result.results;
        const resultGhsMappingObj = ghsMapping.reduce((acc, mapping) => {
          // 如果 key 已存在，將 configId 加入現有陣列；否則創建新的陣列
          acc[mapping.mappingId] = acc[mapping.mappingId]
            ? [...acc[mapping.mappingId], mapping.configId]
            : [mapping.configId];
          return acc;
        }, {} as { [key: string]: string[] });
        setGhsMappingObj(resultGhsMappingObj);
      }
    });
  };

  useEffect(() => {
    if (loginUser) {
      fetchGhsMappingData();
    }
  }, [loginUser]); // 根據需要設置依賴

  return ghsMappingObj;
};

export default useGhsMapping;
