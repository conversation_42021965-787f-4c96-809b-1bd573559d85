import { WEB_IS_TEST_API_BASE_URL, WEB_IS_TEST_CUSTOMER } from "ehs/constant/constants";
import { getUserPreferences } from "../CONNChainEHS/utils/storageUtil";
import { TokenService } from "../services/tokenService";
import { HttpInterceptor } from "../services/httpInterceptor";
import { shouldIncludeBearerToken } from "./apiEndpoints";

export const Config = {
  // apiAddress:"https://ehs.connchain.net/ehsapi/api/34567890/" //外面
  apiBaseUrl: WEB_IS_TEST_CUSTOMER ? WEB_IS_TEST_API_BASE_URL : "https://localhost:8443/ehsapi/api/", // API 本機
  // apiBaseUrl: WEB_IS_TEST_CUSTOMER ? WEB_IS_TEST_API_BASE_URL : "https://iehsapi.connchain.net/ehsapi/api/", // API 基本位址
  getApiEndpoint: () => {
    const userPreferences = getUserPreferences();
    return userPreferences?.uniformNum || ""; //  
  },

  getFullApiAddress: (uniformNum: string) => {
    const endPoint = uniformNum ? uniformNum : Config.getApiEndpoint();
    return `${Config.apiBaseUrl}${endPoint}/`;
  },
};
export const baseRequestConfig: RequestInit = {
  credentials: 'include',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
};

// 創建 POST 請求配置的工具函數（自動添加 Bearer token）
export const createAuthorPostJsonConfig = (data: unknown, endpoint?: string): RequestInit => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // 自動添加 Bearer token（如果端點需要認證）
  if (shouldIncludeBearerToken(endpoint)) {
    const tokenService = TokenService.getInstance();
    const token = tokenService.getAccessToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
  }

  return {
    ...baseRequestConfig,
    method: 'POST',
    headers,
    body: JSON.stringify(data)
  };
};

export const createLoginPostJsonConfig = (params: unknown, endpoint?: string): RequestInit => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // 自動添加 Bearer token（如果端點需要認證）
  if (shouldIncludeBearerToken(endpoint)) {
    const tokenService = TokenService.getInstance();
    const token = tokenService.getAccessToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
  }

  return {
    credentials: 'include',
    headers,
    method: 'POST',
    body: JSON.stringify(params)
  };
};

export const createLoginPostFormDataConfig = (params: unknown, formData: FormData, endpoint?: string): RequestInit => {
  const headers: Record<string, string> = {
    'Accept': 'application/json',
  };

  // 自動添加 Bearer token（如果端點需要認證）
  if (shouldIncludeBearerToken(endpoint)) {
    const tokenService = TokenService.getInstance();
    const token = tokenService.getAccessToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
  }

  return {
    credentials: 'include',
    headers,
    method: 'POST',
    body: formData
  };
};

export const getApiAddress = Config.getFullApiAddress("");
export const getApiAddressByUniformNum = (uniformNum: string) =>
  Config.getFullApiAddress(uniformNum);

// 統一的 API 請求函數，使用 HttpInterceptor
export const apiRequest = async (url: string, options: RequestInit): Promise<Response> => {
  return HttpInterceptor.interceptRequest(url, options);
};

// utils/fetchUtils.js
export const handleFetchResponse = async (response: Response) => {
  if (!response.ok) {
    if (response.status === 401) {
      // 嘗試解析後端的JSON錯誤訊息，但保持拋出 UNAUTHORIZED 讓外層能正確處理
      try {
        const errorData = await response.json();
        console.log('後端錯誤訊息:', errorData.message); // 記錄詳細錯誤訊息
      } catch {
        // 解析失敗就忽略，直接拋出 UNAUTHORIZED
      }
      throw new Error("UNAUTHORIZED");
    }
    if (response.status === 403) {
      // 統一映射 403，供上層顯示權限不足訊息
      throw new Error("FORBIDDEN");
    }
    // 其它錯誤統一丟出帶狀態碼字串
    throw new Error(`HTTP_${response.status}`);
  }
  return response.json();
};
