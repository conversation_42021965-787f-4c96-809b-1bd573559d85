import { useTranslation } from "react-i18next";
import { OrgAPI } from "../../../../api/orgAPI";
import { confirmMsg, errorMsg } from "../../../common/SwalMsg";
import { showSuccessToast } from "../../../common/Toast";
import { ActionMode } from "../../../enums/ActionMode";
import { BtnType } from "../../../enums/BtnType";
import useLoginUser from "../../../hooks/useLoginUser";
import { EhsOrg } from "../../../models/EhsOrg";
import { EhsUser } from "../../../models/EhsUser";
import { checkBtnAuth, getBasicLoginUserInfo } from "../../../utils/authUtil";
import { isApiCallSuccess } from "../../../utils/resultUtil";
import { useExpandedRwdTableRow } from "ehs/hooks/useExpandedRwdTableRow";
import ExpandRwdButton from "ehs/common/ExpandRwdButton";

const OrgRow = (props: {
    index: number; org: EhsOrg; orgManager: EhsUser[];
    onDeleteSuccess: () => void;
    setPopupMode: (mode: ActionMode | null) => void;
    setPopupDetail: (isPopup: boolean) => void;
    setModifyData: React.Dispatch<React.SetStateAction<EhsOrg>>;
    setLoadingBlock: (block: boolean) => void;
}) => {
    const { isExpanded, toggleExpanded } = useExpandedRwdTableRow();
    const { loginUser } = useLoginUser();
    const { t } = useTranslation();
    const { index, org, orgManager, onDeleteSuccess, setPopupMode, setPopupDetail, setModifyData, setLoadingBlock } = props;
    const isEditOrgRole = checkBtnAuth(BtnType.EDIT_ORG)
    const isDeleteOrgRole = checkBtnAuth(BtnType.DELETE_ORG)

    const clickDetail = () => {
        setModifyData({ ...org })
        setPopupDetail(true);
    }
    const clickEdit = () => {
        setModifyData({ ...org })
        setPopupMode(ActionMode.EDIT);
    }
    const clickDelete = () => {
        if (loginUser) {
            confirmMsg(t('message.confirm.delete'), t).then((value) => {
                if (value) {
                    setLoadingBlock(true);
                    OrgAPI.deleteOrg(
                        {
                            ...getBasicLoginUserInfo(loginUser)!,
                            orgId: org.orgId
                        }
                    ).then(result => {
                        if (isApiCallSuccess(result)) {
                            showSuccessToast(t('message.success'));
                            onDeleteSuccess()
                        } else {
                            errorMsg(result.message);
                        }
                        setLoadingBlock(false);
                    }).catch(err => {
                        setLoadingBlock(false);
                    })
                }
            });
        }
    }

    const showOrgManager = orgManager ? orgManager.map((user, index) => {
        return (
            <div key={index} className='mb-1'>
                {user.userName}
            </div>
        );
    }) : "";

    return (
        <>
            <tr>
                <td data-title={t('table.title.item')}>{index}</td>
                <td data-title={t('table.title.name')} className='text-start'>{org.orgName}</td>
                <td data-title={t('table.title.org.manager')} className='text-start responsive-cell'>{showOrgManager}</td>
                <td data-title={t('table.title.building.item')} className='text-start'>{org.buildName}</td>
                <td data-title={t('table.title.building.floor.item')} className='responsive-cell'>{org.orgFloor}</td>
                <td data-title={t('table.title.org.house_number')} className='responsive-cell'>{org.orgHousenum}</td>
                <td data-title={t('table.title.ext')} className='responsive-cell'>{org.orgPhone}</td>
                <td data-title={t('table.title.action')} className='action-cell'>
                    <button type="button" className="btn btn-secondary fs-5 me-3 my-2" title={t("button.detail")} onClick={clickDetail}>
                        <i className="fas fa-file-alt"></i>  {t("button.detail")}
                    </button>
                    {isEditOrgRole && <button
                        type="button"
                        className="btn btn-warning fs-5 me-3 my-2"
                        title={t("button.edit")}
                        onClick={clickEdit}
                    >
                        <i className="fas fa-pen"></i> {t("button.edit")}
                    </button>}
                    {isDeleteOrgRole && <button
                        type="button"
                        className="btn btn-danger fs-5 me-3 my-2"
                        title={t("button.delete")}
                        onClick={clickDelete}
                    >
                        <i className="fas fa-trash-can fa-lg"></i> {t("button.delete")}
                    </button>}
                    <ExpandRwdButton isExpanded={isExpanded} onClick={toggleExpanded} />
                </td>
            </tr>
            {isExpanded && (
                <tr className="expanded-row">
                    <td colSpan={4} className="p-0">
                        <div className="expanded-content">
                            <table className="expanded-table">
                                <tbody>
                                    <tr>
                                        <td className="expanded-label">{t("table.title.org.manager")}</td>
                                        <td className="expanded-value"> {showOrgManager} </td>
                                    </tr>
                                    <tr>
                                        <td className="expanded-label">{t("table.title.building.floor.item")}</td>
                                        <td className="expanded-value"> {org.orgFloor} </td>
                                    </tr>
                                    <tr>
                                        <td className="expanded-label">{t("table.title.org.house_number")}</td>
                                        <td className="expanded-value"> {org.orgHousenum} </td>
                                    </tr>
                                    <tr>
                                        <td className="expanded-label">{t("table.title.ext")}</td>
                                        <td className="expanded-value"> {org.orgPhone} </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tr>
            )}
        </>
    )
}

export default OrgRow;