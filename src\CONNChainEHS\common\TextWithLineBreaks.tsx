import React from 'react';

interface TextWithLineBreaksProps {
    text?: string; // 設置為可選，避免傳遞 `undefined` 時出現錯誤
}

/**
 * 顯示換行文字 textarea 需使用
 * @param param0 
 * @returns 
 */

const TextWithLineBreaks: React.FC<TextWithLineBreaksProps> = ({ text }) => {
    return (
        <>
            {text?.split('\n').map((line, index) => (
                <React.Fragment key={index}>
                    {line}
                    <br />
                </React.Fragment>
            ))}
        </>
    );
};

export default TextWithLineBreaks;
