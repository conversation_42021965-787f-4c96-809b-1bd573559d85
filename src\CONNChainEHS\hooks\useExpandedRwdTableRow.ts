import { useState, useEffect } from 'react';

// 全局的 resize 監聽器管理
const resizeListeners = new Set<() => void>();
let isListenerAttached = false;

const attachGlobalResizeListener = () => {
    if (isListenerAttached) return;

    const handleResize = () => {
        resizeListeners.forEach(listener => listener());
    };

    window.addEventListener('resize', handleResize);
    isListenerAttached = true;
};

export const useExpandedRwdTableRow = (initialState: boolean = false) => {
    const [isExpanded, setIsExpanded] = useState(initialState);

    useEffect(() => {
        // 建立這個實例的 resize 處理函數
        const handleResize = () => setIsExpanded(false);

        // 添加到全局監聽器集合
        resizeListeners.add(handleResize);
        attachGlobalResizeListener();

        // cleanup：從集合中移除這個監聽器
        return () => {
            resizeListeners.delete(handleResize);
        };
    }, []);

    const toggleExpanded = () => setIsExpanded(!isExpanded);

    return {
        isExpanded,
        setIsExpanded,
        toggleExpanded
    };
};