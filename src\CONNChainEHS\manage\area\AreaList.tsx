import BlockUi from "@availity/block-ui";
import ExpandRwdButton from "ehs/common/ExpandRwdButton";
import ExpandableRwdTable from "ehs/common/ExpandableRwdTable";
import { useExpandedRwdTableRow } from "ehs/hooks/useExpandedRwdTableRow";
import React, { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { AreaAPI } from "../../../api/areaAPI";
import { ConfigAPI } from "../../../api/configAPI";
import { FileAPI } from "../../../api/fileAPI";
import { OptionAPI } from "../../../api/optionAPI";
import BlockuiMsg from "../../common/BlockuiMsg";
import Dialog from '../../common/Dialog';
import ImageView from "../../common/ImageView";
import Loader from "../../common/Loader";
import SortIcon from "../../common/SortIcon";
import { errorMsg } from "../../common/SwalMsg";
import { showSuccessToast } from "../../common/Toast";
import { CONFIG_TYPE_CHEM_AREA_APPROVE_TYPE, CONFIG_TYPE_CITY, FILE_TYPE_AREA_ALL_BUILDING_IMAGE, OPTION_USE_AREA } from "../../constant/constants";
import { ActionMode } from "../../enums/ActionMode";
import { BtnType } from "../../enums/BtnType";
import useLoginUser from "../../hooks/useLoginUser";
import Breadcrumbs from "../../layout/Breadcrumbs";
import Footer from "../../layout/Footer";
import { EhsArea, initEhsArea } from "../../models/EhsArea";
import { EhsConfigParam } from "../../models/EhsConfigParam";
import { EhsFile, initEhsFile } from "../../models/EhsFile";
import { EhsOptions } from "../../models/EhsOptions";
import { checkBtnAuth, getBasicLoginUserInfo, getLoginUserRoleLevel } from "../../utils/authUtil";
import { isApiCallSuccess } from "../../utils/resultUtil";
import AreaModify from "./AreaModify";

function AreaList() {
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const [areaList, setAreaList] = useState<EhsArea[]>([]);
  const [modifyData, setModifyData] = useState<EhsArea>(initEhsArea);
  const [localSearchResult, setLocalSearchResult] = useState<EhsArea[]>([]);
  const [popupMode, setPopupMode] = useState<ActionMode | null>(null);
  const [localSearchKey, setLocalSearchKey] = useState("");
  const [loading, setLoading] = useState(false);
  const [loadingBlock, setLoadingBlock] = useState(false);
  const [popupImageView, setPopupImageView] = useState<boolean>(false);
  const [imageViewFile, setImageViewFile] = useState<EhsFile>(initEhsFile);
  const [areaNumLimit, setAreaNumLimit] = useState(0);
  const [configMap, setConfigMap] = useState<{ [key: string]: EhsConfigParam[] }>({});
  const [areaAllBuildImg, setAreaAllBuildImg] = useState<{ [type: string]: EhsFile | null }>({});
  const isEditAreaRole = checkBtnAuth(BtnType.EDIT_AREA);
  const isEnableAreaRole = checkBtnAuth(BtnType.ENABLE_AREA);

  useEffect(() => {
    if (loginUser) {
      fetchOption();
      fetchFiles();
    }
  }, [loginUser]);

  useEffect(() => {
    if (loginUser) {
      fetchData();
      fetchConfig();
    }
  }, [loginUser, i18n.language]);

  useEffect(() => {
    if (localSearchKey) {
      let resultList = areaList?.filter((data: EhsArea) => {
        return [data.areaNo, data.areaName, data.areaAddress, data.areaCno].some(property => property && property.includes(localSearchKey));
      });
      setLocalSearchResult(resultList);
    }

  }, [localSearchKey])

  useEffect(() => {
    if (popupMode === null && loginUser) {
      if (modifyData.areaId) {
        fetchFiles(modifyData.areaId);
      }
      setModifyData(initEhsArea);
    }
  }, [popupMode]);

  const fetchData = () => {
    setLoading(true)
    AreaAPI.getAreaList({
      ...getBasicLoginUserInfo(loginUser)!,
    }).then((result) => {
      if (!isApiCallSuccess(result)) {
        errorMsg(result.message);
      }
      setAreaList(result.results);
    }).catch(error => {
      errorMsg(t('message.system_error'));
    }).finally(() => {
      setLoading(false);
    });
  };

  const fetchOption = () => {
    const optionIds = [OPTION_USE_AREA];
    OptionAPI.getOptionListByIds({
      ...getBasicLoginUserInfo(loginUser)!,
      optionIdList: optionIds
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const options = result.results;
        const optionsMap: {
          [key: string]: EhsOptions;
        } = {};
        options.forEach((item: any) => {
          optionsMap[item.optionId] = item;
        });
        setAreaNumLimit(optionsMap[OPTION_USE_AREA].optionIntValue);
      }
    });
  };

  const fetchConfig = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser)!,
      configTypeList: [CONFIG_TYPE_CHEM_AREA_APPROVE_TYPE, CONFIG_TYPE_CITY]
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const newConfigMap = result.results.reduce((acc: { [key: string]: EhsConfigParam[] }, config: EhsConfigParam) => {
          if (!acc[config.configType]) {
            acc[config.configType] = [];
          }
          acc[config.configType].push(config);
          return acc;
        }, {});
        setConfigMap(newConfigMap);
      }
    });
  };

  const fetchFiles = (fileMappingId: string | null | undefined = null) => {
    const params = {
      ...getBasicLoginUserInfo(loginUser),
      fileTypeList: [FILE_TYPE_AREA_ALL_BUILDING_IMAGE],
    };

    if (fileMappingId) {
      params.fileMappingIdList = [fileMappingId];
    }

    FileAPI.getFileList(params).then((result) => {
      if (isApiCallSuccess(result)) {
        const resultFileList: EhsFile[] = result.results;
        const fileInfos: Record<string, EhsFile> = { ...areaAllBuildImg } as Record<string, EhsFile>;;

        // 如果有 fileMappingId，刪除該檔案
        if (fileMappingId) {
          // 使用 delete 操作，這會讓該項目變成 undefined，而不是 null
          delete fileInfos[fileMappingId];
        }

        // 更新新的檔案資訊
        resultFileList.forEach((file: EhsFile) => {
          if (file.fileType === FILE_TYPE_AREA_ALL_BUILDING_IMAGE) {
            // 新增或更新檔案
            fileInfos[file.fileMappingId] = file;
          }
        });

        // 確保最終狀態不會包含 null 或 undefined
        setAreaAllBuildImg(fileInfos);
      }
    });
  };

  const getShowList = () => {
    if (!localSearchKey) {
      return areaList;
    }
    return localSearchResult;
  };
  const showList = useMemo(() => getShowList(), [areaList, localSearchKey]);

  const editAreaStatus = (areaId: string, activated: boolean) => {
    const areaStatus = activated ? 1 : 0;
    AreaAPI.editAreaStatus({
      ...getBasicLoginUserInfo(loginUser)!,
      areaId: areaId,
      areaStatus: areaStatus
    }).then(result => {
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
        let cloneList = [...areaList];
        let updateData = cloneList.filter(updateData => updateData.areaId === areaId);
        if (updateData.length) {
          updateData[0].areaStatus = areaStatus;
          setAreaList(cloneList);
        }
      } else {
        errorMsg(result.message);
      }
    }).catch(error => {
      errorMsg(t('message.system_error'));
    })

  }

  const deleteSuccess = () => {
    fetchData();
  }

  return (
    <StlyedAreaList $loading={loading}>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {
            <Dialog
              content={
                <AreaModify
                  onClose={() => {
                    setPopupMode(null);
                  }}
                  onActionSuccess={() => {
                    setPopupMode(null);
                    fetchData();
                    showSuccessToast(t('message.success'));
                  }}
                  setLoadingBlock={setLoadingBlock}
                  mode={popupMode}
                  modifyData={modifyData}
                  configMap={configMap}
                />
              }
              show={popupMode !== null}
            />
          }
          <Dialog
            content={
              <ImageView imageFile={imageViewFile} onClose={() => { setPopupImageView(false) }} />
            }
            show={popupImageView}
          />
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.system_manage_setting") },
                { label: t("func.area_manage") },
              ]}
            />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{t("func.area_manage")}</h1>
            {/* END page-header */}
            <div className="row">
              <div className="col-xl-12">
                {(areaList?.length < areaNumLimit) && checkBtnAuth(BtnType.ADD_AREA) && <div className="card mt-3">
                  <div className="card-body">
                    {/* 新增區域 button  之後跟A站串後 需判定買幾個區域 最多能新增幾個*/}
                    <button
                      type="button"
                      className="btn btn-purple fs-5"
                      title={t("button.add")}
                      onClick={() => {
                        setModifyData(initEhsArea)
                        setPopupMode(ActionMode.ADD);
                      }}
                    >
                      <i className="fas fa-plus"></i> {t("button.add")}
                    </button>
                  </div>
                </div>}

                <div className="card py-3">
                  {/* <div className="row topFunctionRow">
              <div className="col-sm-12 col-md-6 left">
                <PageSizeSelector pageSize={condition.pageSize} setCondition={setCondition} />
              </div>
              <div className="col-sm-12 col-md-6 right">
                <div className="dataTables_filter d-flex">
                  search:
                  <input
                    value={localSearchKey}
                    onChange={(e) => {
                      setLocalSearchKey(e.target.value);
                    }}
                    type="search"
                    className="form-control form-control-sm"
                    placeholder=""
                    aria-controls="data-table-default"
                  />
                </div>
              </div>
            </div> */}
                  <div className="card-body">
                    {loading && <Loader />}
                    <ExpandableRwdTable>
                      <div className="table-container">
                        <table
                          id="data-table-default"
                          className={
                            "table table-hover align-middle dt-responsive"
                          }
                        >
                          <thead className="text-center fs-4 fw-bold">
                            <tr>
                              <th>{t("table.title.item")} </th>
                              <th>
                                {t("table.title.no")}
                                <SortIcon
                                  dataList={showList}
                                  dataField={"areaNo"}
                                  setFunction={setAreaList}
                                />
                              </th>
                              <th className='text-start'>
                                {t("table.title.name")}
                                <SortIcon
                                  dataList={showList}
                                  dataField={"areaName"}
                                  setFunction={setAreaList}
                                />
                              </th>
                              <th className='text-start responsive-header'>
                                {t("table.title.area.address")}
                                <SortIcon
                                  dataList={areaList}
                                  dataField={"areaAddress"}
                                  setFunction={setAreaList}
                                />
                              </th>
                              <th className="responsive-header">
                                {t("table.title.phone")}
                                <SortIcon
                                  dataList={areaList}
                                  dataField={"areaPhone"}
                                  setFunction={setAreaList}
                                />
                              </th>
                              <th className="responsive-header">
                                {t("table.title.area.ctrl_no")}
                                <SortIcon
                                  dataList={showList}
                                  dataField={"areaCno"}
                                  setFunction={setAreaList}
                                />
                              </th>
                              <th className="responsive-header">
                                {t("table.title.area.use")}
                              </th>
                              <th className="responsive-header">
                                {t("table.title.area.resp_name")}
                              </th>
                              <th className="responsive-header" >
                                {t("table.title.area.image")}
                              </th>
                              {isEnableAreaRole && <th data-orderable="false">{t("table.title.enable")}</th>}
                              {(isEditAreaRole || checkBtnAuth(BtnType.DELETE_AREA)) && <th data-orderable="false">{t("table.title.action")}</th>}
                            </tr>
                          </thead>
                          <tbody className="text-center fs-5">
                            {!loading &&
                              showList.map((data, idx) => {
                                return <Row key={data.areaId} index={idx + 1} area={data} configMap={configMap}
                                  areaImageFile={areaAllBuildImg[data.areaId]}
                                  onEditStatus={editAreaStatus} onDeleteSuccess={deleteSuccess} setPopupMode={setPopupMode}
                                  setModifyData={setModifyData}
                                  setPopupImageView={setPopupImageView} setImageViewFile={setImageViewFile}
                                />;
                              })}
                          </tbody>
                        </table>
                      </div>
                      {/* <Pagination pageInfo={pageInfo} setCondition={setCondition} /> */}
                    </ExpandableRwdTable>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </BlockUi >
    </StlyedAreaList>
  );
}

const Row = (props: {
  index: number; area: EhsArea;
  configMap: { [key: string]: EhsConfigParam[] };
  areaImageFile: EhsFile | null;
  onEditStatus: (areaId: string, activated: boolean) => void; onDeleteSuccess: () => void;
  setPopupMode: (mode: ActionMode | null) => void;
  setModifyData: React.Dispatch<React.SetStateAction<EhsArea>>;
  setPopupImageView: (show: boolean) => void;
  setImageViewFile: (file: EhsFile) => void;
}) => {
  const { isExpanded, toggleExpanded } = useExpandedRwdTableRow();
  const { loginUser } = useLoginUser();
  const roleLevel = getLoginUserRoleLevel(loginUser);
  const { t } = useTranslation();
  const { index, area, configMap, areaImageFile, onEditStatus, onDeleteSuccess, setPopupMode, setModifyData,
    setImageViewFile, setPopupImageView } = props;
  const cityList = configMap[CONFIG_TYPE_CITY] || [];
  const cityMap = cityList.reduce((acc, city) => {
    acc[city.configId] = city;
    return acc;
  }, {} as { [key: string]: EhsConfigParam });
  const { areaId, areaNo, areaName, areaCity, areaAddress, areaCno, areaPhone, areaStatus, areaUse, areaRespName } = area;
  const isEditAreaRole = checkBtnAuth(BtnType.EDIT_AREA);
  const isEnableAreaRole = checkBtnAuth(BtnType.ENABLE_AREA);
  const isDeleteAreaRole = checkBtnAuth(BtnType.DELETE_AREA);

  const clickEdit = () => {
    setModifyData({ ...area });
    setPopupMode(ActionMode.EDIT);
  };

  // const clickDelete = () => {
  //   if (loginUser) {
  //     confirmMsg(t('message.confirm.delete'), t).then((value) => {
  //       if (value) {
  //         AreaAPI.deleteArea(
  //           {
  //             loginUserId: loginUser.loginUserId,
  //             loginRoleLevel: loginUser.loginRoleLevel,
  //             loginRoleId: loginUser.loginRoleId,
  //             langType: loginUser.langType,
  //             areaId: areaId
  //           }
  //         ).then(result => {
  //           console.log(result);
  //           if (isApiCallSuccess(result)) {
  //             showSuccessToast(t('message.success'));
  //             onDeleteSuccess()
  //           } else {
  //             errorMsg(result.message);
  //           }
  //         }).catch(err => {
  //         })
  //       }
  //     });
  //   }
  // };

  const areaImageFileDiv = areaImageFile &&
    <div key={'rescueImage' + areaImageFile.fileId} style={{ marginRight: '10px' }}>
      <img
        src={`data:image/png;base64,${areaImageFile.fileContent}`}
        alt={areaImageFile.fileName} title={areaImageFile.fileName}
        className="my-1"
        style={{ width: '100px', height: '100px' }}
        onClick={() => {
          setImageViewFile(areaImageFile);
          setPopupImageView(true);
        }}
      />
    </div>


  return (<>
    <tr>
      <td data-title={t("table.title.item")}>{index}</td>
      <td data-title={t("table.title.no")}>{areaNo}</td>
      <td data-title={t("table.title.name")} className='text-start'>{areaName}</td>
      <td data-title={t("table.title.area.address")} className='text-start responsive-cell'>{cityMap[areaCity]?.configName}{areaAddress} </td>
      <td data-title={t("table.title.phone")} className="responsive-cell">{areaPhone}</td>
      <td data-title={t("table.title.ctrl_no")} className="responsive-cell" >{areaCno}</td>
      <td data-title={t("table.title.area.use")} className="responsive-cell">{areaUse}</td>
      <td data-title={t("table.title.area.resp_name")} className="responsive-cell">{areaRespName}</td>
      <td data-title={t("table.title.area.image")} className="responsive-cell">{areaImageFileDiv}</td>
      {isEnableAreaRole && <td data-title={t("table.title.enable")}>
        <div className="form-check form-switch d-flex align-items-center">
          <input
            className="form-check-input"
            type="checkbox"
            checked={areaStatus === 1}
            onChange={(e) => {
              onEditStatus(areaId, e.target.checked)
            }}
          />
        </div>
      </td>}
      {(isEditAreaRole || isDeleteAreaRole) && <td data-title={t("table.title.action")} className="action-cell">
        <div className="d-flex flex-wrap action-buttons">
          {/* <button type="button" className="btn btn-secondary me-3 fs-5 my-2" title={t("button.detail")} onClick={() => { }}>
          <i className="fas fa-pen"></i>  {t("button.detail")}
        </button> */}
          {isEditAreaRole && <button
            type="button"
            className="btn btn-warning mx-1 fs-5"
            title={t("button.edit")}
            onClick={clickEdit}
          >
            <i className="fas fa-pen"></i> {t("button.edit")}
          </button>}
          {
          //By Mark:區域不給user刪除 只能新增 修改
        /*isDeleteAreaRole && <button
          type="button"
          className="btn btn-danger me-3 fs-5"
          title={t("button.delete")}
          onClick={clickDelete}
        >
          <i className="fas fa-trash-can fa-lg"></i> {t("button.delete")}
        </button> */}
          <ExpandRwdButton isExpanded={isExpanded} onClick={toggleExpanded} />
        </div>
      </td>}
    </tr>
    {isExpanded &&
      <tr className="expanded-row">
        <td colSpan={4} className="p-0">
          <div className="expanded-content">
            <table className="expanded-table">
              <tbody>
                <tr>
                  <td className="expanded-label">{t("table.title.area.address")}</td>
                  <td className="expanded-value"> {cityMap[areaCity]?.configName}{areaAddress} </td>
                </tr>
                <tr>
                  <td className="expanded-label">{t("table.title.phone")}</td>
                  <td className="expanded-value"> {areaPhone} </td>
                </tr>
                <tr>
                  <td className="expanded-label">{t("table.title.ctrl_no")}</td>
                  <td className="expanded-value"> {areaCno} </td>
                </tr>
                <tr>
                  <td className="expanded-label">{t("table.title.area.use")}</td>
                  <td className="expanded-value"> {areaUse} </td>
                </tr>
                <tr>
                  <td className="expanded-label">{t("table.title.area.resp_name")}</td>
                  <td className="expanded-value"> {areaRespName} </td>
                </tr>
                <tr>
                  <td className="expanded-label">{t("table.title.area.image")}</td>
                  <td className="expanded-value"> {areaImageFileDiv}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </td>
      </tr>
    }
  </>
  );
};

const StlyedAreaList = styled.div<{ $loading?: boolean }>`
  padding-bottom:150px;

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }
  table {
    position:relative;
    min-height:${props => props.$loading ? "200px" : "auto"};
    thead {
      background:rgb(251, 205, 165);
    }
    th {
      text-align: center;
    } 
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 100px;
        text-align:left;
        min-height:39.5px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
`;

export default AreaList;
