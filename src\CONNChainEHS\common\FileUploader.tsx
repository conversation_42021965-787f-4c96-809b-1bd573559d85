import BlockUi from '@availity/block-ui';
import { useEffect, useRef, useState } from 'react';
import Dropzone, { IFileWithMeta, IMeta } from 'react-dropzone-uploader';
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { FileAPI } from '../../api/fileAPI';
import { UPLOAD_ACCEPT_TYPE_IMAGE, UPLOAD_ACCEPT_TYPE_IMAGE_PDF, UPLOAD_ACCEPT_TYPE_PDF, UPLOAD_FILE_Size_IMAGE, UPLOAD_FILE_Size_PDF } from '../constant/constants';
import useLoginUser from '../hooks/useLoginUser';
import { EhsFile, initEhsFile } from '../models/EhsFile';
import { isArrayEmpty } from '../utils/arrayUtil';
import { isApiCallSuccess } from '../utils/resultUtil';
import BlockuiMsg from './BlockuiMsg';
import { showSuccessToast, showWarnToast, showWarnToastWithId } from './Toast';
import { getBasicLoginUserInfo } from '../utils/authUtil';

/**
 * dropzone上傳 component (單或多檔案皆可使用)
 */

interface Props {
    fileInfo: EhsFile,
    accept?: string;
    initialFiles?: File[];
    initialFileInfos?: EhsFile[];
    initKey?: number; //刷新用
    initStrKey?: string; //平時不刷新用
    needSubmit?: boolean;
    setFile?: (file: File, status: boolean, id: string) => void;//非即時上傳使用
}

interface AcceptFileObj {
    [key: string]: { msg: string, maxSize: number };
}
const acceptFileObjMap: AcceptFileObj = {
    [UPLOAD_ACCEPT_TYPE_IMAGE]: {
        msg: " jpg, jpeg, png",
        maxSize: UPLOAD_FILE_Size_IMAGE
    },
    [UPLOAD_ACCEPT_TYPE_PDF]: {
        msg: " pdf",
        maxSize: UPLOAD_FILE_Size_PDF
    },
    [UPLOAD_ACCEPT_TYPE_IMAGE_PDF]: {
        msg: " jpg, jpeg, png, pdf",
        maxSize: UPLOAD_FILE_Size_PDF
    }
};

const FileUploader: React.FC<Props> = ({ fileInfo, accept = "", initialFiles, initialFileInfos, initKey, initStrKey, needSubmit, setFile = () => { } }) => {
    const { t } = useTranslation();
    const { loginUser } = useLoginUser();
    const [loadingBlock, setLoadingBlock] = useState(false);
    const [canSubmit, setCanSubmit] = useState(needSubmit);
    const [submitting, setSubmitting] = useState(false);
    const [initFileCount, setInitFileCount] = useState(0);//初始檔案計算
    const acceptFileObj = acceptFileObjMap[accept];
    const isImageAndPdf = accept === UPLOAD_ACCEPT_TYPE_IMAGE_PDF;
    const showMsg = t('text.upload_file_type_title') + (acceptFileObj && acceptFileObj.msg) || " all";

    useEffect(() => {
        // setCanSubmit(false);
    }, [initKey]);

    const changeStatus = {
        preparing: "preparing",
        done: "done",
        rejectedFileSize: "error_file_size",
        rejectedFileType: "rejected_file_type",
        removed: "removed"
    }
    const maxSize = (acceptFileObj && acceptFileObj.maxSize) || UPLOAD_FILE_Size_PDF;

    const maxSizeBytes = maxSize * 1024 * 1024;// 10MB

    // // specify upload params and url for your files
    // const getUploadParams = ({ meta }) => { return { url: 'https://httpbin.org/post' } }

    // // called every time a file's `status` changes
    const handleChangeStatus = ({ meta, file, remove }: { meta: IMeta; file: File, remove: () => void }, status: string, allFiles: IFileWithMeta[]) => {
        // console.log(status, meta, file);
        // console.log(allFiles);
        const hasInit = initialFiles && !isArrayEmpty(initialFiles) && initialFileInfos && !isArrayEmpty(initialFileInfos);

        if (status === changeStatus.rejectedFileSize) {
            showWarnToastWithId(t('message.upload_filesize_error', { size: maxSize }), "upload_rejected_file_size");
            remove();// size error 無法上传
        }
        if (status === changeStatus.rejectedFileType) {
            showWarnToastWithId(t('message.upload_filetype_error'), "upload_rejected_file_type");
        }
        if (status === changeStatus.done) {

            if (hasInit) {
                //初始化完才正常動作
                if (initFileCount === initialFiles.length) {
                    setFile && setFile(file, true, meta.id);
                }
                setInitFileCount(prevCount => prevCount + 1);
            } else {
                setFile && setFile(file, true, meta.id);
            }
        }
        if (status === changeStatus.removed) {
            if (setFile) {
                if (hasInit) {
                    const index = allFiles.findIndex((item) => item.meta.id === meta.id);
                    const initFileInfo = initialFileInfos[index];
                    const fileId = initFileInfo ? initFileInfo.fileId : "";
                    setFile(file, false, fileId || meta.id);//有初始要給檔案流水號 沒有就維持原本的流水號
                } else {
                    setFile(file, false, meta.id);
                }
            }
            if (needSubmit && !submitting && hasInit) {
                setLoadingBlock(true);
                const index = allFiles.findIndex((item) => item.meta.id === meta.id);
                const initFileInfo = initialFileInfos[index];
                const fileId = initFileInfo ? initFileInfo.fileId : "";
                const newAllFiles = allFiles.slice(); // 複製數組 避免影響到原本刪除行為
                newAllFiles.splice(index, 1);
                if (fileId) {
                    FileAPI.deleteFile({
                        ...getBasicLoginUserInfo(loginUser)!,
                        fileId: fileId,
                    }).then((result) => {
                        if (isApiCallSuccess(result)) {
                            showSuccessToast(t('message.success'));
                            initialFiles.splice(index, 1);
                            initialFileInfos.splice(index, 1);
                        }
                        setLoadingBlock(false);
                    }).catch((error) => {
                        console.error('Error uploading files:', error);
                        setLoadingBlock(false);
                    });
                } else {
                    setLoadingBlock(false);
                }
            }
        }
    }
    // // receives array of files that are done uploading when submit button is clicked
    const handleSubmit = (files: IFileWithMeta[]) => {
        // console.log(files.map((f ) => f.meta))
        setLoadingBlock(true);
        setSubmitting(true);

        const filteredFiles = initialFileInfos ? files.slice(initialFileInfos.length) : files;
        const fileList = filteredFiles.map((file) => {
            // 在這裡，對每個檔案做一些處理，並建立 EhsFile 物件 
            const ehsFile = { ...initEhsFile, ...fileInfo, fileName: file.file.name };
            return ehsFile;
        });

        const fileContentList = filteredFiles.map(file => file.file);
        if (isArrayEmpty(fileList) || isArrayEmpty(fileContentList)) {
            setLoadingBlock(false);
            setSubmitting(false);
            showWarnToast(t('message.upload_choose_file'));
            return;
        }
        FileAPI.addFiles({
            ...getBasicLoginUserInfo(loginUser)!,
            fileList: fileList,
            fileContentList: fileContentList
        }).then((result) => {
            if (isApiCallSuccess(result)) {
                showSuccessToast(t('message.success'));
                files.forEach((file) => {
                    file.remove();
                });
            }
            setLoadingBlock(false);
            setSubmitting(false);
        }).catch((error) => {
            console.error('Error uploading files:', error);
            setLoadingBlock(false);
            setSubmitting(false);
        });
    }

    const dropzoneObj =
        <Dropzone
            key={'Dropzone' + (initStrKey || initKey)}
            onChangeStatus={handleChangeStatus}
            onSubmit={needSubmit ? handleSubmit : undefined}
            accept={accept}
            maxSizeBytes={maxSizeBytes}
            inputContent={<div className='text-center h5'> {t('text.browse_file_title_click')}<br />{t('text.browse_file_title_or')}<br />{t('text.browse_file_title_drag')}</div>}
            inputWithFilesContent={<><i className='fas fa-plus me-1' />{t('button.add_file')}</>}
            submitButtonContent={<><i className='fas fa-upload me-1' />{t('button.upload')}</>}
            initialFiles={initialFiles}
        />;

    const showLimitSize = isImageAndPdf ? t('text.upload_file_size_img_pdf_title', { imgSize: UPLOAD_FILE_Size_IMAGE, pdfSize: UPLOAD_FILE_Size_PDF }) : maxSize + ' MB';

    return (
        <StyledFileUploader key={'Dropzone-div' + (initStrKey || initKey)}>
            {accept && showMsg && <div className='text-danger m-1 pb-1 ps-2 h5'>{showMsg}</div>}
            {<div className='text-danger m-1 pb-1 ps-2 h5'>{t('text.upload_file_size_title') + showLimitSize}</div>}
            {canSubmit ? <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
                {dropzoneObj}
            </BlockUi> : dropzoneObj}
        </StyledFileUploader>
    )
}

const StyledFileUploader = styled.div` 
    .dzu-dropzone{
        padding: 10px !important;
        overflow: auto;
    }

`
export default FileUploader;