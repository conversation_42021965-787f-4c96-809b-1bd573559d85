import { useState, useEffect, useCallback } from "react";
import { AuthorAPI } from "../../api/authorAPI";
import { getBasicLoginUserInfo } from "../utils/authUtil";
import { isApiCallSuccess } from "../utils/resultUtil";
/**
 * 取得伺服器時間
 * @param loginUser 登入者
 * @returns nowDate:伺服器時間 fetchNowDate:重新取得新的時間用
 */
const useServerNowDate = (loginUser: any) => {
  const [nowDate, setNowDate] = useState(new Date());

  const fetchNowDate = useCallback(() => {
    AuthorAPI.getNowTime({
      ...getBasicLoginUserInfo(loginUser),
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const serverDate = new Date(result.results);
        setNowDate(serverDate);
      }
    });
  }, [loginUser]);

  useEffect(() => {
    if (loginUser) {
      fetchNowDate(); // 組件掛載時自動呼叫一次
    }
  }, [fetchNowDate, loginUser]); // loginUser 改變時重新取伺服器時間

  return [nowDate, fetchNowDate] as [Date, () => void];
};

export default useServerNowDate;
