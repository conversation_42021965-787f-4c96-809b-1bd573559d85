import { useQuery } from '@tanstack/react-query';
import { AuthorAPI } from 'api/authorAPI';
import { REACT_QUERY_CURRENT_USER, TIMEOUT_MINUTES } from 'ehs/constant/constants';
import { isAgent } from 'ehs/utils/authUtil';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { BaseParams } from '../models/BaseParams';
import { EhsUser } from '../models/EhsUser';
import { isPublicRoute } from '../utils/funcUtil';
import { getUserPreferences, setUserPreferences } from '../utils/storageUtil';
import { useLocation } from 'react-router-dom';

interface LoginUser extends BaseParams {
    userInfo: EhsUser;
}

export default function useLoginUser() {
    const { i18n } = useTranslation();
    const location = useLocation();
    const currentPath = location.pathname.replace(/^\/+/, "");
    const isEnabled = isPublicRoute(currentPath);
    // 使用 React Query 來管理用戶資訊
    const { data: loginUser, isLoading: isLoadingLoginUser } = useQuery<LoginUser>({
        queryKey: [REACT_QUERY_CURRENT_USER],
        queryFn: async (): Promise<LoginUser> => {
            const userPreferences = getUserPreferences();
            if (!userPreferences || Object.keys(userPreferences).length === 0) {
                throw new Error('找不到用戶偏好設定');
            }

            const langType = i18n.language;

            if (!userPreferences.uniformNum) {
                throw new Error('找不到統編');
            }

            const response = await AuthorAPI.getCurrentUser({
                uniformNum: userPreferences.uniformNum,
                langType: langType
            });

            if (!response.results) {
                throw new Error('無法獲取用戶資訊');
            }
            const userInfo = response.results;

            // 確保一定有返回值
            return {
                loginUserId: userInfo.userId,
                loginRoleLevel: userInfo.roleLevel,
                loginRoleId: userInfo.roleId,
                userInfo: userInfo,
                langType: langType
            } as LoginUser;
        },
        staleTime: 1000 * 60 * 5, // 5分鐘內不重新請求
        gcTime: 1000 * 60 * TIMEOUT_MINUTES,   // 120分鐘後清除快取
        refetchOnWindowFocus: false, // 視窗重新獲得焦點時不重新請求
        retry: 3,
        enabled: !isEnabled, // 在公開路由上不執行查詢
    });

    // 使用 useMemo 來避免不必要的重新計算
    const localizedLoginUser = useMemo(() =>
        loginUser ? {
            ...loginUser,
            langType: i18n.language,
        } : undefined
        , [loginUser, i18n.language]);

    // 當語言改變時更新用戶偏好
    useEffect(() => {
        if (loginUser) {
            setUserPreferences({
                langType: i18n.language,
            })
        }
    }, [i18n.language, loginUser]);

    return {
        loginUser: localizedLoginUser, isLoadingLoginUser, isAgent: isAgent(localizedLoginUser) };
}


