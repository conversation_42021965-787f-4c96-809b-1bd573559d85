import { Dispatch, SetStateAction, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import styled from "styled-components";
import { BuildAPI } from "../../../../api/buildAPI";
import { ConfigAPI } from "../../../../api/configAPI";
import { FileAPI } from "../../../../api/fileAPI";
import { AppPaths } from "../../../../config/app-paths";
import BackButton from "../../../common/button/BackButton";
import Dialog from "../../../common/Dialog";
import ImageView from "../../../common/ImageView";
import { CONFIG_TYPE_FLOOR_DESC_TYPE, FILE_TYPE_BUILD_FLOOR_HAZARD_CARD, FILE_TYPE_BUILD_FLOOR_PLAN, FILE_TYPE_BUILD_FLOOR_RESCUE_IMAGE } from "../../../constant/constants";
import { ActionMode } from "../../../enums/ActionMode";
import useLoginUser from "../../../hooks/useLoginUser";
import Breadcrumbs from "../../../layout/Breadcrumbs";
import Footer from "../../../layout/Footer";
import { EhsBuild } from "../../../models/EhsBuild";
import { EhsBuildingFloorInfo, initEhsBuildingFloorInfo } from "../../../models/EhsBuildingFloorInfo";
import { EhsConfigParam } from "../../../models/EhsConfigParam";
import { EhsFile, initEhsFile } from "../../../models/EhsFile";
import { checkTimeoutAction, getBasicLoginUserInfo, navigateToHome } from "../../../utils/authUtil";
import { isApiCallSuccess } from "../../../utils/resultUtil";
import BuildingFloorModify from "./BuildingFloorModify";
import React from "react";
import TextWithLineBreaks from "../../../common/TextWithLineBreaks";
import BlockUi from "@availity/block-ui";
import BlockuiMsg from "../../../common/BlockuiMsg";
import BuildingFloorDetail from "./BuildingFloorDetail";
import { isArrayEmpty } from "../../../utils/arrayUtil";
import { CommonAPI } from "../../../../api/commonAPI";
import { MultipleDetailLinkType } from "../../../enums/MultipleDetailLinkType";
import { EhsMultipleDetail } from "../../../models/EhsMultipleDetail";
import ExpandableRwdTable from "ehs/common/ExpandableRwdTable";
import { useExpandedRwdTableRow } from "ehs/hooks/useExpandedRwdTableRow";
import ExpandRwdButton from "ehs/common/ExpandRwdButton";

function BuildingFloorList() {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const { state } = useLocation();
  const { building, isEditBuildingRole } = state || { building: {}, isEditBuildingRole: false }; // 或者提供其他的默認值
  const [popupImageView, setPopupImageView] = useState<boolean>(false);
  const [loadingBlock, setLoadingBlock] = useState(false);
  const [popupMode, setPopupMode] = useState<ActionMode | null>(null);
  const [popupDetailMode, setPopupDetailMode] = useState<ActionMode | null>(null);
  const [imageViewFile, setImageViewFile] = useState<EhsFile>(initEhsFile);
  const [modifyFloor, setModifyFloor] = useState<EhsBuildingFloorInfo>(initEhsBuildingFloorInfo);
  const [modifyfloorFiles, setModifyFloorFiles] = useState<EhsFile[]>([]);
  const [floorFiles, setFloorFiles] = useState<Record<string, EhsFile[]>>({});
  const [floorInfos, setFloorInfos] = useState<{ [key: string]: EhsBuildingFloorInfo }>({});
  const [floorMultiDetails, setFloorMultiDetails] = useState<Record<string, EhsMultipleDetail[]>>({});
  const [configData, setConfigData] = useState<{ [configType: string]: EhsConfigParam[] }>({})
  const floorIds = useMemo(() => {
    return Object.values(floorInfos).map(info => info.floorId);
  }, [floorInfos]);

  useEffect(() => {
    if (loginUser) {
      if (!building.buildId) {
        navigateToHome(navigate);
        return;
      }
      fetchBuildFloorInfoList();
      fetchBuildingFloorFiles();
    }
  }, [loginUser]);

  useEffect(() => {
    if (loginUser) {
      if (!building.buildId) {
        navigateToHome(navigate);
        return;
      }
      fetchConfig();
    }
  }, [loginUser, i18n.language]);

  useEffect(() => {
    if (isArrayEmpty(floorIds)) {
      return;
    }
    fetchFloorMultipleDetail(floorIds);
  }, [floorIds])

  useEffect(() => {
    resetModifyFloorAndFile(popupMode, ActionMode.EDIT);
  }, [popupMode]);

  useEffect(() => {
    resetModifyFloorAndFile(popupDetailMode, ActionMode.DETAIL);
  }, [popupDetailMode])

  const fetchBuildFloorInfoList = () => {
    BuildAPI.getBuildFloorInfoList({
      ...getBasicLoginUserInfo(loginUser)!,
      buildId: building.buildId
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const floorInfoObj = result.results.reduce(
          (acc: { [key: string]: EhsBuildingFloorInfo }, floor: EhsBuildingFloorInfo) => {
            acc[floor.floorNo] = floor; // 假設 floor_no 是每層樓的唯一標識
            return acc;
          },
          {} as { [key: string]: EhsBuildingFloorInfo }
        );
        setFloorInfos(floorInfoObj);
      }
    }).catch(error => {
      checkTimeoutAction(error, navigate, t);
    });
  }

  const fetchBuildingFloorFiles = () => {
    FileAPI.getBuildFloorFileList({
      ...getBasicLoginUserInfo(loginUser)!,
      buildId: building.buildId,
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const fileList = result.results;
        const fileObj: Record<string, EhsFile[]> = {};
        fileList.forEach((file: EhsFile) => {
          const floor = file.fileMappingId;
          if (fileObj[floor]) {
            fileObj[floor].push(file);
          } else {
            fileObj[floor] = [file];
          }
        });
        setFloorFiles(fileObj);
      }
    });
  };

  const fetchUpdateBuildFlooFile = (mappingId: string) => {
    FileAPI.getFileList({
      ...getBasicLoginUserInfo(loginUser)!,
      fileTypeList: [FILE_TYPE_BUILD_FLOOR_PLAN, FILE_TYPE_BUILD_FLOOR_RESCUE_IMAGE, FILE_TYPE_BUILD_FLOOR_HAZARD_CARD],
      fileMappingIdList: [modifyFloor.floorId]
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const fileList = result.results;
        setFloorFiles((prevFiles) => {
          return { ...prevFiles, [mappingId]: fileList };
        });
      }
    })
  }

  const fetchFloorMultipleDetail = (floorIds: string[]) => {
    CommonAPI.getMultiDetailList({
      ...getBasicLoginUserInfo(loginUser)!,
      linkIdList: floorIds,
      linkTypeList: [MultipleDetailLinkType.FLOOR]
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const detailList = result.results;
        const groupedFloorDetails = detailList.reduce((acc: Record<string, EhsMultipleDetail[]>, item: EhsMultipleDetail) => {
          const { linkId } = item;
          if (!acc[linkId]) {
            acc[linkId] = [];
          }
          acc[linkId].push(item);
          return acc;
        }, {});
        setFloorMultiDetails(groupedFloorDetails);
      }
    })
  }

  const fetchConfig = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser)!,
      configTypeList: [CONFIG_TYPE_FLOOR_DESC_TYPE],
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        // 使用 reduce 將 EhsConfigParam[] 轉換為以 configType 為鍵的物件
        const configObject = result.results.reduce((acc: { [key: string]: EhsConfigParam[] }, item: EhsConfigParam) => {
          // 如果 configType 尚未存在於 acc 中，則初始化為一個空陣列
          if (!acc[item.configType]) {
            acc[item.configType] = [];
          }
          // 將該項目加入對應的 configType 陣列中
          acc[item.configType].push(item);
          return acc;
        }, {});
        setConfigData(configObject);
      }
    });
  }

  const resetModifyFloorAndFile = (popMode: ActionMode | null, type: ActionMode) => {
    if (popMode === null && loginUser) {
      if (type === ActionMode.EDIT && modifyFloor.floorId) {
        fetchUpdateBuildFlooFile(modifyFloor.floorId);
      }
      setModifyFloor(initEhsBuildingFloorInfo);
      setModifyFloorFiles([]);
    }
  }

  const generateFloorRows = (building: EhsBuild, isBasement: boolean) => {
    const floorRows = [];
    const totalFloors = isBasement ? building.buildUnfloors : building.buildFloors;
    const prefix = isBasement ? "B" : "";

    for (let i = 1; i <= totalFloors; i++) {
      floorRows.push(
        <Row
          key={i}
          index={i}
          building={building}
          floorInfos={floorInfos}
          configData={configData}
          showFloorNo={prefix + (isBasement ? i : i + "F")}
          isEditBuildingRole={isEditBuildingRole}
          setPopupMode={setPopupMode}
          setPopupDetailMode={setPopupDetailMode}
          setPopupImageView={setPopupImageView}
          setModifyFloor={setModifyFloor}
          floorAllFiles={floorFiles}
          setImageViewFile={setImageViewFile}
          setModifyFloorFiles={setModifyFloorFiles}
        />
      );
    }

    return floorRows;
  };

  const floorRows = generateFloorRows(building, false); // Floors
  const basementRows = generateFloorRows(building, true); // Basements

  return (
    <StlyedBuildingFloorList>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.system_manage_setting") },
                { label: t("func.building_manage"), path: AppPaths.manage.buildingList },
                { label: t("func.building_floor_manage") },
              ]}
            />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{t("func.building_floor_manage")} </h1>
            {/* END page-header */}
            <BackButton />
            <Dialog
              content={
                <BuildingFloorModify building={building}
                  floorInfo={modifyFloor}
                  floorMultiDetails={floorMultiDetails}
                  floorFiles={modifyfloorFiles}
                  configData={configData}
                  onClose={() => {
                    setPopupMode(null);
                  }}
                  setLoadingBlock={(loading) => {
                    setLoadingBlock(loading);
                  }}
                  onActionSuccess={(floorInfo) => {
                    setFloorInfos({ ...floorInfos, [floorInfo.floorNo]: floorInfo });
                  }}
                  setFloorInfo={(floorInfo) => { setModifyFloor(floorInfo) }}
                  setFloorFiles={(floorFiles) => {
                    setModifyFloorFiles(floorFiles)
                  }}
                />
              }
              show={popupMode !== null}
            />
            <Dialog
              content={
                <BuildingFloorDetail building={building}
                  floorInfo={modifyFloor}
                  floorMultiDetails={floorMultiDetails}
                  floorFiles={modifyfloorFiles}
                  configData={configData}
                  onClose={() => {
                    setPopupDetailMode(null);
                  }}
                />
              }
              show={popupDetailMode !== null}
            />
            <Dialog
              content={
                <ImageView imageFile={imageViewFile} onClose={() => { setPopupImageView(false) }} />
              }
              show={popupImageView}
            />
            <div className="card mt-3 pt-3">
              <div className="row topFunctionRow">
                <div className="col-sm-12 col-md-6 left h2">{building.buildName}</div>
                <div className="col-sm-12 col-md-6 right"></div>
              </div>
              <div className="card-body">
                <ExpandableRwdTable>
                  <div className="table-container">
                    <table
                      id="data-table-default"
                      className={
                        "table table-hover align-middle dt-responsive"
                      }
                    >
                      <thead className="text-center fs-4 fw-bold">
                        <tr>
                          <th>{t("table.title.building.floor.item")}</th>
                          <th className="text-start">{t("table.title.building.floor.description")}</th>
                          <th className="text-start">{t("table.title.building.floor.note")}</th>
                          <th className="text-start responsive-header">{t("table.title.building.floor.plan")} </th>
                          <th className="text-start responsive-header">{t('table.title.building.rescue_image_class_b')}</th>
                          <th className="text-start responsive-header">{t("table.title.building.hazard_identification_card")}</th>
                          {<th data-orderable="false">{t("table.title.action")}</th>}
                        </tr>
                      </thead>
                      <tbody className="text-center fs-5">
                        {floorRows}
                        {basementRows}
                      </tbody>
                    </table>
                  </div>
                </ExpandableRwdTable>
              </div>
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </BlockUi>
    </StlyedBuildingFloorList >
  );
}

//樓層列表 樓層修改用
export interface FloorFileAccumulator {
  floorPlanFiles: EhsFile[];
  rescueImageFiles: EhsFile[];
  hazardCardFiles: EhsFile[];
}

const Row = (props: {
  index: number; building: EhsBuild; showFloorNo: string;
  floorInfos: { [key: string]: EhsBuildingFloorInfo };
  configData: { [key: string]: EhsConfigParam[] };
  isEditBuildingRole: boolean;
  setPopupMode: (mode: ActionMode | null) => void;
  setPopupDetailMode: (mode: ActionMode | null) => void;
  setPopupImageView: (show: boolean) => void;
  setModifyFloor: (floorInfo: EhsBuildingFloorInfo) => void;
  floorAllFiles: Record<string, EhsFile[]>;
  setImageViewFile: Dispatch<SetStateAction<EhsFile>>;
  setModifyFloorFiles: Dispatch<SetStateAction<EhsFile[]>>;
}) => {
  const { isExpanded, toggleExpanded } = useExpandedRwdTableRow();
  const { t } = useTranslation();
  const { building, showFloorNo, floorInfos, isEditBuildingRole, floorAllFiles, configData,
    setPopupMode, setPopupDetailMode, setModifyFloor, setPopupImageView, setImageViewFile, setModifyFloorFiles } = props;
  const floorInfo = floorInfos[showFloorNo] || initEhsBuildingFloorInfo;
  const { floorId, floorDesc, floorNote } = floorInfo;
  const floorFileList = floorAllFiles[floorId] || [];
  const { floorPlanFiles, rescueImageFiles, hazardCardFiles } = floorFileList.reduce((acc: FloorFileAccumulator, file: EhsFile) => {
    switch (file.fileType) {
      case FILE_TYPE_BUILD_FLOOR_PLAN:
        acc.floorPlanFiles.push(file);
        break;
      case FILE_TYPE_BUILD_FLOOR_RESCUE_IMAGE:
        acc.rescueImageFiles.push(file);
        break;
      case FILE_TYPE_BUILD_FLOOR_HAZARD_CARD:
        acc.hazardCardFiles.push(file);
        break;
    }
    return acc;
  }, { floorPlanFiles: [], rescueImageFiles: [], hazardCardFiles: [] });
  const floorDescOption = configData[CONFIG_TYPE_FLOOR_DESC_TYPE] || [];
  const floorDescMap = floorDescOption.reduce((acc: { [key: string]: EhsConfigParam }, item: EhsConfigParam) => {
    acc[item.configId] = item;
    return acc;
  }, {});

  // 新增共用的圖片顯示組件
  const ImageGallery = ({ files, onImageClick }: { files: EhsFile[], onImageClick: (file: EhsFile) => void }) => (
    <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', flexWrap: 'wrap' }}>
      {files?.map((item: EhsFile) => (
        <div key={item.fileName} style={{ marginRight: '10px', marginBottom: '10px' }}>
          <img
            src={`data:image/png;base64,${item.fileContent}`}
            alt={item.fileName}
            title={item.fileName}
            className="my-1"
            style={{ width: '100px', height: '100px', cursor: 'pointer' }}
            onClick={() => onImageClick(item)}
          />
        </div>
      ))}
    </div>
  );

  // 建立圖片相關欄位的設定
  const imageColumns = [
    {
      title: t("table.title.building.floor.plan"),
      files: floorPlanFiles,
    },
    {
      title: t("table.title.building.rescue_image_class_b"),
      files: rescueImageFiles,
    },
    {
      title: t("table.title.building.hazard_identification_card"),
      files: hazardCardFiles,
    }
  ];

  return (
    <>
      <tr>
        <td data-title={t("table.title.building.floor.item")}>{showFloorNo}</td>
        <td data-title={t("table.title.building.floor.description")} className="text-start">{floorDescMap[floorDesc]?.configName}</td>
        <td data-title={t("table.title.building.floor.note")} className="text-start item-width-20">
          <label><TextWithLineBreaks text={floorNote} /></label>
        </td>
        {imageColumns.map(column => (
          <td key={column.title} data-title={column.title} className="responsive-cell img-cell">
            <ImageGallery
              files={column.files || []}
              onImageClick={(file) => {
                setImageViewFile(file);
                setPopupImageView(true);
              }}
            />
          </td>
        ))}
        {<td data-title={t("table.title.action")} className="action-cell">
          <div className="d-flex flex-wrap action-buttons">
            <button type="button" className="btn btn-secondary me-3 fs-5 my-2" title={t("button.detail")} onClick={() => {
              setPopupDetailMode(ActionMode.EDIT)
              setModifyFloor({ ...floorInfo, floorNo: showFloorNo });
              if (floorFileList) {
                setModifyFloorFiles(floorFileList);
              }
            }}>
              <i className="fas fa-file-alt"></i>  {t("button.detail")}
            </button>
            {isEditBuildingRole && <button type="button" className="btn btn-warning me-3 fs-5 my-2" title={t("button.edit")} onClick={() => {
              setPopupMode(ActionMode.EDIT)
              setModifyFloor({ ...floorInfo, floorNo: showFloorNo });
              if (floorFileList) {
                setModifyFloorFiles(floorFileList);
              }
            }}>
              <i className="fas fa-pen"></i>  {t("button.edit")}
            </button>}
            <ExpandRwdButton isExpanded={isExpanded} onClick={toggleExpanded} />
          </div>
        </td>}
      </tr>
      {isExpanded &&
        <tr className="expanded-row">
          <td colSpan={4} className="p-0">
            <div className="expanded-content">
              <table className="expanded-table">
                <tbody>
                  {imageColumns.map(column => (
                    <tr key={column.title}>
                      <td className="expanded-label">{column.title}</td>
                      <td className="expanded-value">
                        <ImageGallery
                          files={column.files || []}
                          onImageClick={(file) => {
                            setImageViewFile(file);
                            setPopupImageView(true);
                          }}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </td>
        </tr>
      }
    </>
  );
};

const StlyedBuildingFloorList = styled.div`
  padding-bottom:150px;
  img{
    cursor:pointer;
  }
  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }
  table {
    position:relative;
    min-height:auto;
    thead {
      background:rgb(251, 205, 165);
    }
    th {
      text-align: center;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }

      .img-cell { 
        min-height: 120px;
      }

      td {
        background: #fff!important;
        position: relative;
        padding: 6px 6px 6px 120px;
        text-align: left;
        display: flex;
        align-items: flex-start;
        flex-wrap: wrap;
        word-break: break-word;
        min-height: 45px;
        height: auto;
        .form-check {
          justify-content: start;  
        }
      }
      td::before {
        content: attr(data-title);
        position: absolute;
        top: 0;
        left: 0;
        width: 110px;
        padding: 6px;
        text-align: left;
        font-weight: bold;
        color: #1a1a1a;
        word-break: break-word;
        white-space: normal;
        min-height: 100%;
        height: auto;
        display: flex;
        align-items: flex-start;
        background: #fff;
        border-right: 1px solid #eee;
        font-size: 0.9rem;
      }

    }
    
  }
`;

export default BuildingFloorList;
