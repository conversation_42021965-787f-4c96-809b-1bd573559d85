import BlockUi from "@availity/block-ui";
import { ConfigAPI } from "api/configAPI";
import { OptionAPI } from "api/optionAPI";
import { PurchaseAPI } from "api/purchaseAPI";
import { SignoffAPI } from "api/signoffAPI";
import { AppPaths } from "config/app-paths";
import { AgentNameTag } from "ehs/common/AgentNameTag";
import BlockuiMsg from "ehs/common/BlockuiMsg";
import BackButton from "ehs/common/button/BackButton";
import CcbViewDialog from "ehs/common/chemical/CcbViewDialog";
import SubstanceInfo, { SubstanceDisplayMode } from "ehs/common/chemical/SubstanceInfo";
import Dialog from "ehs/common/Dialog";
import InputDate from "ehs/common/input/InputDate";
import SignoffActionOption from "ehs/common/SignoffActionOption";
import { confirmMsg, errorMsg } from "ehs/common/SwalMsg";
import { showSuccessToast, showWarnToast } from "ehs/common/Toast";
import { CONFIG_TYPE_CHEM, CONFIG_TYPE_CHEM_CLASS_LV, CONFIG_TYPE_CHEM_STORAGE_LOCATION, CONFIG_TYPE_MIX_CONCEN_TYPE, CONFIG_TYPE_PURCHASE_STATUS, CONFIG_TYPE_STATE, OPTION_SUBTYPE_ARRIVAL } from "ehs/constant/constants";
import { ChemicalCcbLinkType } from "ehs/enums/ChemicalCcbLinkType";
import { PurchaseDetailStatus } from "ehs/enums/PurchaseDetailStatus";
import { PurchaseMode } from "ehs/enums/PurchaseMode";
import { PurchaseStatus } from "ehs/enums/PurchaseStatus";
import { SignIdType } from "ehs/enums/SignIdType";
import { SignoffRecordType } from "ehs/enums/SignoffRecordType";
import useLoginUser from "ehs/hooks/useLoginUser";
import useServerNowDate from "ehs/hooks/useServerNowDate";
import { useSignoffRecord } from "ehs/hooks/useSignoffRecord";
import Breadcrumbs from "ehs/layout/Breadcrumbs";
import Footer from "ehs/layout/Footer";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsOptions } from "ehs/models/EhsOptions";
import { EhsPurchase, initEhsPurchase } from "ehs/models/EhsPurchase";
import { EhsPurchaseDetail } from "ehs/models/EhsPurchaseDetail";
import { EhsPurchaseSubst } from "ehs/models/EhsPurchaseSubst";
import { SignoffListView, initSignoffListView } from "ehs/models/SignoffListView";
import SignoffRecordTable, { SignoffTableTheme } from 'ehs/signoff/components/SignoffRecordTable';
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { checkTimeoutAction, getBasicLoginUserInfo, navigateToHome } from "ehs/utils/authUtil";
import { getFirstChemNames } from "ehs/utils/chemicalUtil";
import { getLabTextObj, notChineseLang } from "ehs/utils/langUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { getGroupSignoffRecordsBySeq, getSignoffStatusShow } from "ehs/utils/signoffUtil";
import { getFormatDateDash, getFormatDateSlash, getFormatTimeSlash } from "ehs/utils/stringUtil";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import styled from "styled-components";

function PurchaseOperation() {
  const navigate = useNavigate();
  const { loginUser } = useLoginUser();
  const [nowDate] = useServerNowDate(loginUser);
  const { t, i18n } = useTranslation();
  const { state } = useLocation();
  const { labNameText, labPhoneText } = getLabTextObj(loginUser, t);
  const { mode, initPurchaseId, initSignStatusId }: { mode: PurchaseMode, initPurchaseId: string, initSignStatusId: string } = state || {};
  const [purchaseInfo, setPurchaseInfo] = useState<EhsPurchase>(initEhsPurchase);
  const [substMap, setSubstMap] = useState<{ [purchaseDetailId: string]: EhsPurchaseSubst[] }>({});
  const [initPurchaseDetailList, setInitPurchaseDetailList] = useState<EhsPurchaseDetail[]>([]);
  const [initSignStatus, setInitSignStatus] = useState<SignoffListView>(initSignoffListView);
  const [isRePurchase, setIsRePurchase] = useState(false);
  const [isItemPurchase, setIsItemPurchase] = useState(false);
  const [showCCBView, setShowCCBView] = useState<boolean | null>(null);
  const [loadingBlock, setLoadingBlock] = useState(false);
  const [initBarcodeInputMap, setInitBarcodeInputMap] = useState(false);
  const [initOriginDetailList, setInitOriginDetailList] = useState(false);
  const [needEnterBarcode, setNeedEnterBarcode] = useState(true);
  const [operNote, setOperNote] = useState("");
  const [checkeddPurchaseDetailIds, setCheckeddPurchaseDetailIds] = useState<string[]>([]); // 訂單明細勾選
  const [configMap, setConfigMap] = useState<{ [configId: string]: EhsConfigParam }>({});// config對應
  const [barcodeInputMap, setBarcodeInputMap] = useState<{ [purchaseDetailId: string]: string }>({});// 驗貨輸入條碼
  const [signRecordStatus, setSignRecordStatus] = useState(SignoffRecordType.Approve);
  const { purchaseId, signStatusId, purchaseStatus, applicantName: userName, agentId, agentName, labName, labPhone, manufacturerName,
    detailList = [], recordList } = purchaseInfo || {};
  const isSignoff = mode === PurchaseMode.SIGNOFF;
  const isDetail = mode === PurchaseMode.DETAIL;
  const isArrival = mode === PurchaseMode.ARRIVAL;
  const isInspection = mode === PurchaseMode.INSPECTION;
  const isReturn = mode === PurchaseMode.RETURN;
  const isCancel = mode === PurchaseMode.CANCEL;
  const needSignRecord = purchaseStatus === PurchaseStatus.SIGNING || isDetail;
  const {
    signoffRecords = [],
  } = useSignoffRecord({
    loginUser,
    signStatusId: initSignStatusId,
    enabled: !!loginUser && (isSignoff || needSignRecord),
    i18n: { language: i18n.language }
  });
  const showOperCheckbox = !isDetail;
  const existCanArrivalProduct = initPurchaseDetailList?.filter(item => item.arrivalDate === null).length > 0;
  const existCanInspectionProduct = initPurchaseDetailList?.filter(item => item.inspDate === null).length > 0;
  const existCanReturnProduct = initPurchaseDetailList?.filter(item => item.arrivalDate !== null && item.inspDate === null && item.cancelDate === null).length > 0;
  const existCanCancelProduct = initPurchaseDetailList?.filter(item => item.arrivalDate === null && item.inspDate === null && item.returnDate === null).length > 0;
  const canCheckedPurchaseDetail = (detail: EhsPurchaseDetail) => {
    const { arrivalDate, inspDate, returnDate, cancelDate } = detail;
    const noneOperDate = arrivalDate === null && inspDate === null && returnDate === null && cancelDate === null;

    const canArrival = isArrival && noneOperDate;
    const canInspection = isInspection && arrivalDate !== null && inspDate === null && returnDate === null && cancelDate === null;
    const canReturn = isReturn && arrivalDate !== null && inspDate === null && returnDate === null && cancelDate === null;
    const canCancel = isCancel && noneOperDate;

    return canArrival || canInspection || canReturn || canCancel;
  };
  const canCheckedDetailList = initPurchaseDetailList?.filter(detail => canCheckedPurchaseDetail(detail));
  const checkedProductIds = canCheckedDetailList.map(detail => detail.purchaseDetailId) || [];
  const checkedDetailList = detailList.filter(item =>
    checkeddPurchaseDetailIds.includes(item.purchaseDetailId)
  )
  const isSignoffApprovedOption = signRecordStatus === SignoffRecordType.Approve;

  const updateArrival = () => {
    if (!loginUser) {
      return;
    }
    setLoadingBlock(true);
    PurchaseAPI.updatePurchaseArrival({
      ...getBasicLoginUserInfo(loginUser),
      purchaseId: purchaseId!,
      purchaseDetailIdList: checkeddPurchaseDetailIds,
      detailList: checkedDetailList,
      note: operNote,
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
        navigate("/" + AppPaths.chemical.purchaseList);
      } else {
        errorMsg(result.message);
      }
      setLoadingBlock(false);
    }).catch((error) => {
      errorMsg(error);
      setLoadingBlock(false);
    });
  }

  const updateInspection = () => {
    if (!loginUser) {
      return;
    }
    setLoadingBlock(true);
    PurchaseAPI.updatePurchaseInspection({
      ...getBasicLoginUserInfo(loginUser),
      purchaseId: purchaseId!,
      purchaseDetailIdList: checkeddPurchaseDetailIds,
      detailList: checkedDetailList,
      note: operNote,
      isRePurchase: isRePurchase,
      isItemPurchase: isItemPurchase
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
        navigate("/" + AppPaths.chemical.purchaseList);
      } else {
        errorMsg(result.message);
      }
      setLoadingBlock(false);
    }).catch((error) => {
      errorMsg(error);
      setLoadingBlock(false);
    });
  }

  const updateCancel = () => {
    if (!loginUser) {
      return;
    }
    setLoadingBlock(true);
    PurchaseAPI.updatePurchaseCancel({
      ...getBasicLoginUserInfo(loginUser),
      purchaseId: purchaseId!,
      purchaseDetailIdList: checkeddPurchaseDetailIds,
      note: operNote,
      isRePurchase: isRePurchase,
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
        navigate("/" + AppPaths.chemical.purchaseList);
      } else {
        errorMsg(result.message);
      }
      setLoadingBlock(false);
    }).catch((error) => {
      errorMsg(error);
      setLoadingBlock(false);
    });
  }

  const updateReturn = () => {
    if (!loginUser) {
      return;
    }
    setLoadingBlock(true);
    PurchaseAPI.updatePurchaseReturn({
      ...getBasicLoginUserInfo(loginUser),
      purchaseId: purchaseId!,
      purchaseDetailIdList: checkeddPurchaseDetailIds,
      note: operNote,
      isRePurchase: isRePurchase,
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
        navigate("/" + AppPaths.chemical.purchaseList);
      } else {
        errorMsg(result.message);
      }
      setLoadingBlock(false);
    }).catch((error) => {
      errorMsg(error);
      setLoadingBlock(false);
    });
  }

  const updateSignoff = () => {
    if (!loginUser) {
      return;
    }
    setLoadingBlock(true);
    SignoffAPI.doSignoffAction({
      ...getBasicLoginUserInfo(loginUser),
      signStatusId,
      recordStatus: signRecordStatus,
      note: operNote,
      isRePurchase: isRePurchase,
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
        navigate("/" + AppPaths.chemical.purchaseList);
      } else {
        errorMsg(result.message);
      }
      setLoadingBlock(false);
    }).catch((error) => {
      errorMsg(error);
      setLoadingBlock(false);
    });
  }

  const modeObj: Record<PurchaseMode, { pageName: string, actBtnName: string, bgColor: string, canAction: boolean, confirmMessage?: string, updateAction?: () => void }> = {
    [PurchaseMode.SIGNOFF]: {
      pageName: t('func.signoff.item'),
      actBtnName: t('button.signoff'),
      bgColor: 'btn-purple',
      canAction: true,
      confirmMessage: t('message.confirm.signoff', { recordStatus: getSignoffStatusShow(signRecordStatus, t) }),
      updateAction: updateSignoff
    },
    [PurchaseMode.DETAIL]: {
      pageName: t('func.detail'),
      actBtnName: t('button.detail'),
      bgColor: '',
      canAction: false
    },
    [PurchaseMode.ARRIVAL]: {
      pageName: t('func.chemical.purchase.arrival'),
      actBtnName: t('button.arrival'),
      bgColor: 'btn-info',
      canAction: existCanArrivalProduct,
      confirmMessage: t('message.confirm.arrival'),
      updateAction: updateArrival
    },
    [PurchaseMode.INSPECTION]: {
      pageName: t('func.chemical.purchase.inspection'),
      actBtnName: t('button.inspection'),
      bgColor: 'btn-primary',
      canAction: existCanInspectionProduct,
      confirmMessage: t('message.confirm.inspection'),
      updateAction: updateInspection
    },
    [PurchaseMode.RETURN]: {
      pageName: t("func.chemical.purchase.return"),
      actBtnName: t("button.product_return"),
      bgColor: 'btn-danger',
      canAction: existCanReturnProduct,
      confirmMessage: t('message.confirm.return_goods'),
      updateAction: updateReturn
    },
    [PurchaseMode.CANCEL]: {
      pageName: t('func.chemical.purchase.cancel'),
      actBtnName: t('button.cancel_purchase'),
      bgColor: 'bg-light-red text-white',
      canAction: existCanCancelProduct,
      confirmMessage: t('message.confirm.purchase_cancel'),
      updateAction: updateCancel
    },
  };
  const { pageName, actBtnName, canAction, bgColor: actBtnColor, confirmMessage, updateAction } = modeObj[mode] || {};
  const actBtn = canAction && <button type="button" className={`btn ${actBtnColor}`} onClick={() => checkSubmitAction()}>{actBtnName}</button>;
  const noteTitle = `${pageName}${notChineseLang(i18n.language) ? ' ' : ''}${t('text.note')}`;
  const groupedSignoffRecords = getGroupSignoffRecordsBySeq(signoffRecords);

  useEffect(() => {
    if (loginUser) {
      if (!initPurchaseId) {
        navigateToHome(navigate);
        return;
      }
      fetchData();
      fetchConfigData();
    }
  }, [loginUser, i18n.language]);

  useEffect(() => {
    if (!loginUser || !initSignStatusId) {
      return;
    }
    fetchSignStatus();
  }, [loginUser, initSignStatusId])

  useEffect(() => {
    if (initBarcodeInputMap && canCheckedDetailList && !isArrayEmpty(canCheckedDetailList)) {
      const updatedMap: { [purchaseDetailId: string]: string } = {};
      canCheckedDetailList.forEach((detail) => {
        updatedMap[detail.purchaseDetailId] = '';
      });
      setBarcodeInputMap(updatedMap);
    }
  }, [initBarcodeInputMap])

  useEffect(() => {
    if (initOriginDetailList) {
      setInitPurchaseDetailList(detailList);
    }
  }, [initOriginDetailList])

  useEffect(() => {
    if (!isArrayEmpty(initPurchaseDetailList)) {
      const chemClassLv = initPurchaseDetailList[0].chemClassLv;
      OptionAPI.getOptionListBySubTypes({
        ...getBasicLoginUserInfo(loginUser),
        subtypeList: [OPTION_SUBTYPE_ARRIVAL],
      }).then((result) => {
        if (isApiCallSuccess(result)) {
          const options: EhsOptions[] = result.results;
          const arrivalOption = options.find(option => (option.optionMapId === chemClassLv))
          setNeedEnterBarcode(Number(arrivalOption?.optionValue) === 1);
        }
      })
    }
  }, [initPurchaseDetailList])

  const fetchData = () => {
    PurchaseAPI.getPurchaseDetail({
      ...getBasicLoginUserInfo(loginUser),
      purchaseId: initPurchaseId!,
    }).then((rs) => {
      const data = rs.results;
      const { purchaseDetail, substMap } = data;
      setPurchaseInfo(purchaseDetail);
      setSubstMap(substMap);
      setInitBarcodeInputMap(true);
      setInitOriginDetailList(true);
    }).catch((error) => {
      checkTimeoutAction(error, navigate, t);
    })
  };

  const fetchConfigData = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser),
      configTypeList: [CONFIG_TYPE_PURCHASE_STATUS, CONFIG_TYPE_STATE, CONFIG_TYPE_CHEM_CLASS_LV, CONFIG_TYPE_MIX_CONCEN_TYPE, CONFIG_TYPE_CHEM,
        CONFIG_TYPE_CHEM_STORAGE_LOCATION],
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        setConfigMap(result.results.reduce((acc: { [configId: string]: EhsConfigParam }, config: EhsConfigParam) => {
          if (config.configType === CONFIG_TYPE_PURCHASE_STATUS) {
            acc[config.configIvalue] = config;
          } else {
            acc[config.configId] = config;
          }
          return acc;
        }, {}))
      }
    });
  }

  const fetchSignStatus = () => {
    SignoffAPI.getSignoffStatusDetail({
      ...getBasicLoginUserInfo(loginUser),
      signStatusId: initSignStatusId,
    }).then((rs) => {
      if (isApiCallSuccess(rs)) {
        setInitSignStatus(rs.results);
        const signId = rs.results.signId;
        if (signId === SignIdType.CHEM_REPURCHASE) {
          setIsRePurchase(true);
        } else if (signId?.startsWith(SignIdType.CHEM_ITEM_PURCHASE_PREFIX)) {
          setIsItemPurchase(true);
        }
      }
    })
  }

  const changePurchaseInfo = (condition: object) => {
    setPurchaseInfo({ ...purchaseInfo, ...condition });
  }

  const changeDetailList = (fieldName: string, value: any, purchaseDetailId: string) => {
    const newDetailList = detailList.map(item => {
      if (item.purchaseDetailId === purchaseDetailId) {
        return { ...item, [fieldName]: value }; // 使用動態鍵來更新欄位
      }
      return item;
    });
    changePurchaseInfo({ detailList: newDetailList });
  };

  const getDetailStatusShow = (detail: EhsPurchaseDetail) => {
    let status = {
      name: t('text.purchase.purchasing'),
      id: PurchaseDetailStatus.PURCHASING // 預設值
    };
    const { arrivalDate, inspDate, returnDate, cancelDate } = detail;

    if (cancelDate) {
      status.name = t('text.purchase.cancel');
      status.id = PurchaseDetailStatus.CANCEL;
    } else if (returnDate) {
      status.name = t('text.purchase.return_goods');
      status.id = PurchaseDetailStatus.RETURN_GOODS;
    } else if (inspDate) {
      status.name = t('text.purchase.inspection_complete');
      status.id = PurchaseDetailStatus.INSPECTION_COMPLETE;
    } else if (arrivalDate) {
      status.name = t('text.purchase.pending_inspection');
      status.id = PurchaseDetailStatus.PENDING_INSPECTION;
    } else {
      status.name = t('text.purchase.purchasing');
      status.id = PurchaseDetailStatus.PURCHASING;
    }

    return status;
  }

  const onChangeCheckAllProduct = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    if (isChecked) {
      setCheckeddPurchaseDetailIds(checkedProductIds);
    } else {
      setCheckeddPurchaseDetailIds([]);
    }
  }

  const onChangeCheckPurchaseDetail = (e: React.ChangeEvent<HTMLInputElement>, purchaseDetailId: string) => {
    const isChecked = e.target.checked;
    if (isChecked) {
      // 新增選中的 purchaseDetailId 到 checkeddPurchaseDetailIds 中
      setCheckeddPurchaseDetailIds([...checkeddPurchaseDetailIds, purchaseDetailId]);
    } else {
      // 從 checkeddPurchaseDetailIds 中移除未選中的 purchaseDetailId
      setCheckeddPurchaseDetailIds(checkeddPurchaseDetailIds.filter(id => id !== purchaseDetailId));
    }
  };

  const handleBarcodeInput = (event: React.FormEvent<HTMLInputElement>) => {
    const input = event.target as HTMLInputElement;
    const newValue = input.value;
    const filteredValue = newValue.replace(/\D/g, '');
    input.value = filteredValue;
  };

  const handleBarcodeChange = (e: React.ChangeEvent<HTMLInputElement>, purchaseDetailId: string) => {
    const { value } = e.target;
    setBarcodeInputMap((prevMap) => ({
      ...prevMap,
      [purchaseDetailId]: value,
    }));

  };

  /**
   * 驗證條碼是否有誤
   * @returns 回傳錯誤的 purchaseDetailId
   */
  const validationBarcodeInput = () => {
    // 使用 some 方法來檢查是否有任何條目不滿足條件

    const errorPurchaseDetailIds = checkedDetailList
      .map((detail) => {
        const { purchaseDetailId, barcodeShowPrefix, barcode } = detail;
        // 檢查條件是否不相等
        return barcodeShowPrefix + (barcodeInputMap[purchaseDetailId] || '') !== barcode ? purchaseDetailId : null;
      })
      .filter(id => id !== null);

    return errorPurchaseDetailIds; // 只要有一個條目不滿足條件就返回 true
  };

  /**
   * 驗證驗貨日 或是毒化物第四類
   * @returns 回傳錯誤的 purchaseDetailId
   */
  const validationInfoInput = () => {
    let msg = null;

    // 儲存錯誤的 purchaseDetailId 和其對應的錯誤類型
    const errorPurchaseDetails = checkedDetailList
      .map((detail) => {
        const { purchaseDetailId, inspDate, toxicClass4, shipCoupon } = detail;
        const errors = [];

        // 檢查 inspDate 是否為 null
        if (inspDate === null) {
          errors.push('missingDate');
        }

        // 檢查 toxicClass4 是否為 true 且 shipCoupon 是否為空
        if (toxicClass4 && !shipCoupon) {
          errors.push('missingShipCoupon');
        }

        // 如果有錯誤，返回 purchaseDetailId 和錯誤類型
        return errors.length > 0 ? { id: purchaseDetailId, errors } : null;
      })
      .filter(error => error !== null); // 過濾掉沒有錯誤的項目

    // 獲取錯誤索引
    const missingInfoIndexes = errorPurchaseDetails.map(error =>
      detailList.findIndex(item => error && item.purchaseDetailId === error.id)
    ).filter(index => index !== -1);

    // 構建錯誤訊息
    if (!isArrayEmpty(missingInfoIndexes)) {
      msg = (
        <div>
          {missingInfoIndexes.map((idx, index) => {
            const errors = errorPurchaseDetails[index]?.errors || [];
            return (
              <div key={'missing-info-' + idx}>
                <span className="text-danger">{t('text.product.item')} {idx + 1}</span>
                {errors.includes('missingDate') && <span> - {t('message.purchase.no_choose_insp_date')}</span>}<br />
                {errors.includes('missingShipCoupon') && <span>  {t('message.purchase.no_enter_coupon')}</span>}
                <br />
              </div>
            );
          })}
        </div>
      );
    }

    return msg;
  };

  const checkSubmitAction = () => {
    if (!loginUser) {
      return;
    }
    if (!isSignoff && isArrayEmpty(checkeddPurchaseDetailIds)) {
      showWarnToast(t("message.purchase.operation.no_check_product", { action: actBtnName }));
      return;
    }
    if (isArrival) {
      setLoadingBlock(true);
      PurchaseAPI.checkPurchaseArrival({
        ...getBasicLoginUserInfo(loginUser),
        purchaseId: purchaseId!,
        purchaseDetailIdList: checkeddPurchaseDetailIds,
      }).then((result) => {
        if (isApiCallSuccess(result)) {
          const rs = result.results;
          const { msg, success } = rs;
          if (success) {
            submitAction();
          } else {
            errorMsg(msg);
            setLoadingBlock(false);
          }
        } else {
          setLoadingBlock(false);
        }
      }).catch((error) => {
        errorMsg(error);
        setLoadingBlock(false);
      });
    } else if (isInspection) {

      /* 需輸入條碼 才要檢查 */
      if (needEnterBarcode) {
        const checkedBarcodeInputMap = Object.fromEntries(
          Object.entries(barcodeInputMap).filter(([key]) =>
            checkeddPurchaseDetailIds.includes(key)
          )
        );

        if (Object.values(checkedBarcodeInputMap).some(barcode => !barcode)) {
          // 獲取沒有條碼的鍵
          const missingBarcodeKeys = Object.entries(checkedBarcodeInputMap)
            .filter(([key, barcode]) => !barcode)
            .map(([key]) => key);

          // 獲取在列表中的索引
          const missingBarcodeIndexes = missingBarcodeKeys.map(key =>
            detailList.findIndex(item => item.purchaseDetailId === key)
          ).filter(index => index !== -1);

          showWarnToast(
            <div>
              {t('text.purchase.remind_fill_barcode')}
              {missingBarcodeIndexes.map(idx => (
                <div key={'missing-barcode-' + idx} className="text-danger">
                  {t('text.product.item')} {idx + 1}
                  <br />
                </div>
              ))}
            </div>
          );
          return;
        }

        const barcodeErrorPurchaseDetailIds = validationBarcodeInput();
        if (!isArrayEmpty(barcodeErrorPurchaseDetailIds)) {

          // 獲取在列表中的索引
          const missingBarcodeIndexes = barcodeErrorPurchaseDetailIds.map(key =>
            detailList.findIndex(item => item.purchaseDetailId === key)
          ).filter(index => index !== -1);

          showWarnToast(
            <div>
              {t('text.barcode_error')}
              {missingBarcodeIndexes.map(idx => (
                <div key={'missing-barcode-' + idx} className="text-danger">
                  {t('text.product.item')} {idx + 1}
                  <br />
                </div>
              ))}
            </div>
          );
          return;
        }
      }

      const validInfoMsg = validationInfoInput();
      if (validInfoMsg) {
        showWarnToast(validInfoMsg);
        return;
      }

      setLoadingBlock(true);
      PurchaseAPI.checkPurchaseInspection({
        ...getBasicLoginUserInfo(loginUser),
        purchaseId: purchaseId!,
        purchaseDetailIdList: checkeddPurchaseDetailIds,
      }).then((result) => {
        if (isApiCallSuccess(result)) {
          const rs = result.results;
          const { msg, success } = rs;
          if (success) {
            submitAction();
          } else {
            errorMsg(msg);
            setLoadingBlock(false);
          }
        } else {
          setLoadingBlock(false);
        }
      }).catch((error) => {
        errorMsg(error);
        setLoadingBlock(false);
      });
    } else if (isCancel) {
      if (!operNote) {
        showWarnToast(t('message.enter') + '：' + noteTitle);
        return
      }

      setLoadingBlock(true);
      PurchaseAPI.checkPurchaseCancel({
        ...getBasicLoginUserInfo(loginUser),
        purchaseId: purchaseId!,
        purchaseDetailIdList: checkeddPurchaseDetailIds,
      }).then((result) => {
        if (isApiCallSuccess(result)) {
          const rs = result.results;
          const { msg, success } = rs;
          if (success) {
            submitAction();
          } else {
            errorMsg(msg);
            setLoadingBlock(false);
          }
        } else {
          setLoadingBlock(false);
        }
      }).catch((error) => {
        errorMsg(error);
        setLoadingBlock(false);
      });
    } else if (isReturn) {
      if (!operNote) {
        showWarnToast(t('message.enter') + '：' + noteTitle);
        return
      }

      setLoadingBlock(true);
      PurchaseAPI.checkPurchaseReturn({
        ...getBasicLoginUserInfo(loginUser),
        purchaseId: purchaseId!,
        purchaseDetailIdList: checkeddPurchaseDetailIds,
      }).then((result) => {
        if (isApiCallSuccess(result)) {
          const rs = result.results;
          const { msg, success } = rs;
          if (success) {
            submitAction();
          } else {
            errorMsg(msg);
            setLoadingBlock(false);
          }
        } else {
          setLoadingBlock(false);
        }
      }).catch((error) => {
        errorMsg(error);
        setLoadingBlock(false);
      });
    } else if (isSignoff) {
      if (!isSignoffApprovedOption && !operNote) {
        showWarnToast(t('message.enter') + '：' + noteTitle);
        return
      }
      setLoadingBlock(true);
      SignoffAPI.checkSignoffUser({
        ...getBasicLoginUserInfo(loginUser),
        signStatusId: signStatusId,
      }).then((result) => {
        if (isApiCallSuccess(result)) {
          submitAction();
        } else {
          errorMsg(result.message);
          setLoadingBlock(false);
        }
      }).catch((error) => {
        errorMsg(error);
        setLoadingBlock(false);
      });
    }
  }

  const submitAction = async () => {
    try {
      setLoadingBlock(false);
      const msg = confirmMessage || "";

      const confirmed = await confirmMsg(msg, t);
      if (!confirmed || !updateAction) {
        return;
      }

      updateAction();
    } catch (error) {
      errorMsg(t('message.error'));
    }
  }

  return (
    <StlyedPurchaseOperation>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {
            <Dialog
              content={
                <CcbViewDialog
                  onClose={() => {
                    setShowCCBView(null);
                  }}
                  setLoadingBlock={setLoadingBlock}
                  mode={showCCBView}
                  linkType={ChemicalCcbLinkType.ADD}
                  linkId={purchaseId!}
                />
              }
              show={showCCBView !== null}
            />
          }
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.chemical.manage") },
                { label: t("func.chemical.purchase.list"), path: AppPaths.chemical.purchaseList },
                { label: pageName },
              ]}
            />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{pageName}</h1>
            <BackButton />
            {/* END page-header */}
            <div className="card mt-3"  >
              <div className="card-body">
                <div className="row">
                  <div className="col-xl-12 fs-5">
                    {actBtn}<span className="me-3">{isSignoff && canAction && <SignoffActionOption className="mt-md-1 d-md-flex" recordStatus={signRecordStatus} handleSignoffActionChange={(e) => { setSignRecordStatus(parseInt(e.target.value)) }} />}</span>
                  </div>
                </div>
                <div className="row mt-3">
                  <div className="col-xl-12 fs-5">
                    <div className="d-md-flex">
                      <div className="left-block">
                        <h3>{t('text.purchase.item')}</h3>
                        <ul>
                          <li>{t('text.purchase.no')}：{purchaseId}</li>
                          <li>{t('text.applicant')}：{userName}
                            <AgentNameTag agentId={agentId} agentName={agentName} prefix=" " />
                          </li>
                          <li>{labNameText}：{labName}</li>
                          <li>{labPhoneText}：{labPhone}</li>
                          <li>{t('text.vendor')}：{manufacturerName}</li>
                          <li>{t('text.chemical.ccb.item')}：
                            <button className="btn btn-primary" title={t('text.chemical.ccb_view')}
                              onClick={() => {
                                setShowCCBView(true);
                              }}>
                              <i className="fa fa-eye me-1"></i>{t('text.chemical.ccb_view')}
                            </button>
                          </li>
                        </ul>
                      </div>
                      <div className="right-block">
                        <h3>{t('text.purchase.process')}</h3>
                        <div className="table-responsive">
                          <table className="table table-hover text-center align-middle fs-5">
                            <thead className="bg-lime-200">
                              <tr>
                                <th>{t('table.title.type')}</th>
                                <th>{t('table.title.personnel')}</th>
                                <th>{t('table.title.time')}</th>
                                <th>{t('table.title.reason_note')}</th>
                              </tr>
                            </thead>
                            <tbody>
                              {purchaseId && initSignStatus?.signFlowStatus === 0 && isArrayEmpty(recordList) && <tr>
                                <td colSpan={99} className="text-center">{t('text.signoff.under_review')}</td>
                              </tr>}
                              {recordList?.map((record) => (
                                <tr key={record.purchaseRecordId}>
                                  <td data-title={t('table.title.type')}>{configMap[record.recordType]?.configName}</td>
                                  <td data-title={t('table.title.personnel')}>
                                    {record.userName}
                                    <AgentNameTag agentId={record.agentId} agentName={record.agentName} withBreak />
                                  </td>
                                  <td data-title={t('table.title.time')}>{getFormatTimeSlash(record.recordDate)}</td>
                                  <td data-title={t('table.title.reason_note')}>{record.note}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                    {<div className={`d-md-flex`}>
                      <div className="left-block">
                        {!isDetail && canAction && <div className="col-12 pe-md-3">
                          <h3>{noteTitle} {(isCancel || isReturn || (isSignoff && !isSignoffApprovedOption)) && <span className="text-danger">*</span>}</h3><br />
                          <textarea
                            className="form-control"
                            id="note"
                            rows={7}
                            cols={50}
                            onChange={(e) => setOperNote(e.target.value)}
                          />
                        </div>}
                      </div>
                      {!isArrayEmpty(signoffRecords) && <div className={`right-block ${needSignRecord ? '' : 'd-none'}`}>
                        <h3>{t('text.signoff.process')}</h3>
                        <SignoffRecordTable
                          groupedSignoffRecords={groupedSignoffRecords}
                          themeColor={SignoffTableTheme.GREEN}
                        />
                      </div>}
                    </div>}
                    {/* {!isDetail && canAction && <div className="col-6">
                      <h3>{noteTitle} {(isCancel || isReturn) && <span className="text-danger">*</span>}</h3><br />
                      <textarea
                        className="form-control"
                        id="note"
                        rows={4}
                        cols={50}
                        onChange={(e) => setOperNote(e.target.value)}
                      />
                    </div>} */}
                    <hr />
                    <h3>{t('text.purchase.details')}</h3>
                    {!isDetail && !isSignoff && canAction && <div className="form-check my-3 ms-1">
                      <input className="form-check-input" type="checkbox" id="checkAllProduct" onChange={(e) => onChangeCheckAllProduct(e)}
                        checked={checkeddPurchaseDetailIds.length === checkedProductIds?.length} />
                      <label className="form-check-label" htmlFor="checkAllProduct">{t('text.all')}{notChineseLang(i18n.language) && ' '}{actBtnName}</label>
                    </div>}
                    <div className="row mt-3 px-1">
                      {detailList?.map((detail, idx) => {
                        const { purchaseDetailId, chemical, productNo, productName, weight,
                          concentration, phaseState, productDescription, isFutures, futuresDay,
                          chemClassLv, toxicClass4, arrivalDate, inspDate, barcode, barcodeShowPrefix,
                          shipCoupon, shipNo, estShipDate, inventoryLocation, inventoryNote, estMaxStorageQty, estMaxUseQty,
                          respiratoryProtection, handProtection, eyeProtection, skinBodyProtection
                        } = detail;
                        const { chemCtrlNo, casnoList, nameList } = chemical;
                        const { firstName, firstEnName } = getFirstChemNames(nameList, i18n.language);
                        const { id: purchaseDetailStatusId, name: purchaseDetailStatusName } = getDetailStatusShow(detail);
                        const isValidBarcode = barcodeShowPrefix + barcodeInputMap[purchaseDetailId] === barcode;
                        const canEdit = canAction && checkedProductIds.includes(purchaseDetailId);
                        const isChecked = checkeddPurchaseDetailIds.some(id => id === purchaseDetailId);
                        const substList = substMap[purchaseDetailId] || [];
                        const reqEnterShipCoupon = isInspection && toxicClass4 && canEdit;
                        return (
                          <div className="col-xl-3 border rounded-3 shadow p-3 my-3" key={purchaseDetailId}>
                            <div className="text-start position-relative ps-3">
                              <label className="form-label fs-4">{t('text.product.item') + ' ' + (idx + 1)}</label>
                              {/* <!-- 若為列管，就出現此行 ↓ --> */}
                              {chemClassLv && <label className={`badge bg-danger d-block col-3`} data-toggle="tooltip" data-placement="top"
                                title={t('text.chemical.class_status')}> {configMap[chemClassLv]?.configName}</label>}
                              <div className="mt-2"></div>
                              {/* <!-- 若為列管，就出現此行 ↑ --> */}
                              {/* <!-- 列管編號，有才出現 ↓ --> */}
                              {chemCtrlNo && <h5 className="text-danger mt-1">{t('text.ctrl_no')}：{chemCtrlNo}</h5>}
                              {/* <!-- 列管編號，有才出現 ↑ --> */}
                              <h5 className="text-primary">{t('text.chemical.casno')}：{casnoList[0]?.casno}</h5>
                              <h3>{firstName ? firstName : firstEnName} </h3>
                              {firstName && <div className="fs-5 text-gray">{firstEnName}</div>}
                              {showOperCheckbox && canEdit &&
                                <div className="single-check text-center">
                                  <input className="form-check-input" type="checkbox" title={t('text.single_bottle') + pageName} value={purchaseDetailId}
                                    checked={isChecked} onChange={(e) => onChangeCheckPurchaseDetail(e, purchaseDetailId)} />
                                  <br />{t('text.single_bottle')}<br />{pageName}</div>}
                            </div>
                            <div className="pt-1 ps-3">
                              {isInspection && canEdit && barcode && needEnterBarcode &&
                                <div>
                                  <div className="d-flex align-items-center">
                                    <label className="fs-4">{t('text.barcode')}<span className="text-danger">*</span>：{barcodeShowPrefix}</label>
                                    <input type='text' className={`form-control barcode-input ms-1 ps-0`} maxLength={3}
                                      onInput={handleBarcodeInput}
                                      onChange={(e) => handleBarcodeChange(e, purchaseDetailId)}
                                      disabled={isValidBarcode} />
                                    {isValidBarcode && <div className='form-control is-valid barcode-valid-img ms-1 ps-0'></div>}
                                    {!isValidBarcode && barcodeInputMap[purchaseDetailId]?.length === 3 && <div className='form-control is-invalid barcode-valid-img ms-1 ps-0'></div>}
                                  </div>
                                  {!isValidBarcode && barcodeInputMap[purchaseDetailId]?.length === 3 && <label className='text-danger'>{t('text.barcode_error')}</label>}
                                  {!isValidBarcode && <label className="alert alert-danger">{t('text.purchase.remind_fill_barcode')}</label>}
                                </div>
                              }
                              {barcode && ((isInspection && canEdit && !needEnterBarcode) || (!isInspection && inspDate !== null)) && <div className="fs-5">{t('text.barcode')}：<span>{barcode}</span></div>}
                              {(isDetail || isArrival || isInspection) &&
                                <div className={`${canEdit && 'mb-3'}`}>
                                  {isInspection && <>
                                    <label className="fs-4">{t('text.inspection_date')}{canEdit && <span className="text-danger">*</span>}：</label>
                                    {canEdit && <br />}
                                    {canEdit ? <InputDate className="form-control" minDate={getFormatDateSlash(arrivalDate)} maxDate={getFormatDateSlash(nowDate)}
                                      onChange={(date) => {
                                        changeDetailList("inspDate", date ? getFormatDateDash(date || "") : null, purchaseDetailId);
                                      }} />
                                      : <label className="fs-4">{getFormatDateSlash(inspDate)}</label>
                                    }
                                    <br />
                                  </>
                                  }
                                  <label className="fs-4">{t('text.delivery_note')}{reqEnterShipCoupon && (<><span className="text-danger">*</span> <span className="text-danger">{t('text.purchase.insp_toxic4_title')}</span></>)}</label>：{reqEnterShipCoupon && <br />}
                                  {canEdit ? <input type="text" className="form-control" value={shipCoupon} onChange={(e) => {
                                    changeDetailList("shipCoupon", e.target.value, purchaseDetailId);
                                  }} />
                                    : <label className="fs-4">{shipCoupon}</label>}
                                  <br />
                                  <label className="fs-4">{t('text.vendor_estimated_shipping_date')}：</label>
                                  {canEdit && isArrival ? <InputDate className="form-control" defaultValue={getFormatDateSlash(estShipDate)} onChange={(date) => {
                                    changeDetailList("estShipDate", date ? getFormatDateDash(date || "") : null, purchaseDetailId);
                                  }} /> : <label className="fs-4">{getFormatDateSlash(estShipDate)}</label>}
                                  <br />
                                  <label className="fs-4">{t('text.shipping_order_number')}：</label>
                                  {canEdit ? <input type="text" className="form-control" value={shipNo} onChange={(e) => {
                                    changeDetailList("shipNo", e.target.value, purchaseDetailId);
                                  }} /> : <label className="fs-4">{shipNo}</label>}
                                </div>
                              }
                              <div className="fs-5">{t('text.product_item_number')}：<span>{productNo}</span></div>
                              <div className="fs-5">{t('text.product.name')}：<span>{productName}</span></div>
                              <div className="fs-5">{t('text.weight')}(kg)：<span>{weight}</span>
                              </div>
                              <div className="fs-5">{t('text.concentration')}(%)：<span>{concentration}</span>
                              </div>
                              <div className="fs-5">{t('text.chemical.phase_state')}：<span>{configMap[phaseState]?.configName}</span>
                              </div>
                              <div className="fs-6">{t('text.product_description')}：<span>{productDescription}</span>
                              </div>
                              <div className="fs-6">{t('text.futures')}/{t('text.days')}：<span>{isFutures}/{futuresDay}</span>
                              </div>
                              <div className="fs-6">{t('table.title.storage_location')}：<span>{configMap[inventoryLocation]?.configName}</span>
                              </div>
                              <div className="fs-6">{t('text.note')}：<span>{inventoryNote}</span>
                              </div>
                              <div className="fs-6">{t('text.chemical.est_max_storage_qty')}：<span>{estMaxStorageQty}</span>
                              </div>
                              <div className="fs-6">{t('text.chemical.est_max_use_qty')}：<span>{estMaxUseQty}</span>
                              </div>
                              <div className="fs-6">{t('text.chemical.respiratory_protection')}：<span>{respiratoryProtection}</span>
                              </div>
                              <div className="fs-6">{t('text.chemical.hand_protection')}：<span>{handProtection}</span>
                              </div>
                              <div className="fs-6">{t('text.chemical.eye_protection')}：<span>{eyeProtection}</span>
                              </div>
                              <div className="fs-6">{t('text.chemical.skin_body_protection')}：<span>{skinBodyProtection}</span>
                              </div>
                              <div className="fs-6">{t('text.status')}：{purchaseDetailStatusName}</div>
                            </div>
                            {/* 物質明細部分 */}
                            <div className="mt-2">
                              {
                                <div className="mt-2 p-2 border rounded bg-light">
                                  <div className="d-flex align-items-center fs mb-2"  >
                                    <span className="me-2 h4">{t('text.chemical.substance_info')}</span>
                                  </div>
                                  <div className="mb-2">
                                    <SubstanceInfo
                                      substList={substList}
                                      configMap={configMap}
                                      mode={SubstanceDisplayMode.DETAIL}
                                      showAdditionalInfo={true}
                                    />
                                  </div>
                                </div>
                              }
                            </div>
                          </div>
                        )
                      })}
                    </div>
                    <div className="row justify-content-center mt-5">
                      <div className="col-xl-2 d-grid">
                        {actBtn}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </BlockUi>
    </StlyedPurchaseOperation>
  );
}

const StlyedPurchaseOperation = styled.div`
  padding-bottom:150px;
  
  .bg-light-red{
    background-color:	#FF0000;
  }

  .barcode-valid-img{
    height: 25px;
    width: 10px;//限制最小 避免撐到整行
  }

  .barcode-input {
    font-size: 1rem;
    font-weight:400;
    height: 25px;
    width: 40px;
  }

  .left-block {
    width: 38%; /* 調整左區塊的寬度，可以依實際需求調整 */
  }
  .right-block {
    width: 78%; /* 調整右區塊的寬度，可以依實際需求調整 */
  }


  label, .single-check{
    user-select: none;
  }

  .single-check {
      position: absolute;
      top: 5%;
      right: 3%;
  }

  /* 商品明細包裝表格垂直文字 */
  table tr td.w-25 {
      -webkit-writing-mode: vertical-lr;
      writing-mode: vertical-lr;
      vertical-align: middle;
      letter-spacing: .5rem;
  }

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }  
  table {
    position:relative;
    th {
      text-align: center;
      white-space:nowrap;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
  
  .left-block {
    width: 108%; /* 調整左區塊的寬度，可以依實際需求調整 */
  }
  .right-block {
    width: 108%; /* 調整右區塊的寬度，可以依實際需求調整 */
  }

    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 110px;
        text-align:left;
        min-height:39.5px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 15px;
          white-space: nowrap;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }

   .sign-type-header{
    display:none;
   }

    .table-responsive .signflow-div[data-title],
    .sign-type-div[data-title] {
        background: #fff!important;
        position:relative;
        padding-left: 110px;
        text-align:left;
        min-height:39.5px;
        .form-check {
          justify-content:start;  
        }
    }
    .table-responsive .signflow-div[data-title]::before,
    .sign-type-div[data-title]::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 15px;
          white-space: nowrap;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
    } 
    
  }
`;

export default PurchaseOperation;

