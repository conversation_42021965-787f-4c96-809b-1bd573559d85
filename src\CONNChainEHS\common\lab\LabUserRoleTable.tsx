import NamesSplitFragment from 'ehs/common/NamesSplitFragment';
import { ORG_SPLIT_FLAG } from 'ehs/constant/constants';
import useLoginUser from 'ehs/hooks/useLoginUser';
import { EhsRole } from 'ehs/models/EhsRole';
import { EhsRou, initEhsRou } from 'ehs/models/EhsRou';
import { getUserUnitTextObj } from 'ehs/utils/langUtil';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';

interface LabUserRoleTableProps {
    labRoleList: EhsRole[];
    labUserRoles: { [key: string]: EhsRou[] };
    labUsers: EhsRou[];
}

// 新增用戶資料欄位組件
interface UserDataCellsProps {
    userInfo: EhsRou;
    userIdText: string;
    t: (key: string) => string;
}

const UserDataCells: React.FC<UserDataCellsProps> = ({ userInfo, userIdText, t }) => (
    <>
        <td data-title={userIdText}>{userInfo.userId}</td>
        <td
            data-title={t('table.title.lab.username_jobtitle')}
            className="text-start"
        >
            {userInfo.userName}<br />{userInfo.jobTitleName}
        </td>
        <td
            data-title={t('table.title.org.item')}
            className="text-start"
        >
            <NamesSplitFragment
                names={userInfo.orgNames || userInfo.orgName}
                separator={ORG_SPLIT_FLAG}
            />
        </td>
        <td
            data-title={t('table.title.email')}
            className="text-start"
        >
            {userInfo.userEmail}
        </td>
    </>
);

const LabUserRoleTable: React.FC<LabUserRoleTableProps> = ({
    labRoleList,
    labUserRoles,
    labUsers,
}) => {
    const { t } = useTranslation();
    const { loginUser } = useLoginUser();
    const { userIdText } = getUserUnitTextObj(loginUser, t);
    const { roleIdCountsMap, roleUserIdsMap } = useMemo(() => {
        const newRoleIdCountsMap = new Map<string, number>();
        const newRoleUserIdsMap = new Map<string, string[]>();

        Object.values(labUserRoles).forEach((roles) => {
            roles.forEach((role) => {
                // 更新 roleIdCountsMap
                newRoleIdCountsMap.set(
                    role.roleId,
                    (newRoleIdCountsMap.get(role.roleId) || 0) + 1
                );

                // 更新 roleUserIdsMap
                if (newRoleUserIdsMap.has(role.roleId)) {
                    newRoleUserIdsMap.get(role.roleId)?.push(role.userId);
                } else {
                    newRoleUserIdsMap.set(role.roleId, [role.userId]);
                }
            });
        });

        return {
            roleIdCountsMap: newRoleIdCountsMap,
            roleUserIdsMap: newRoleUserIdsMap
        };
    }, [labUserRoles]);

    return (
        <div className="table-container table-responsive">
            <StyledTable id="data-table-default" className="table align-middle dt-responsive">
                <thead className="text-center fs-4 fw-bold">
                    <tr>
                        <th>{t('table.title.role.item')}</th>
                        <th>{userIdText}</th>
                        <th className="text-start">{t('table.title.lab.username_jobtitle')}</th>
                        <th className="text-start">{t('table.title.org.item')}</th>
                        <th className="text-start">{t('table.title.email')}</th>
                    </tr>
                </thead>
                <tbody className="text-center fs-5">
                    {labRoleList?.map((role, index) => {
                        const isOddDataRowClass = index % 2 === 0 ? "bg-gray-100" : "";
                        const userIds = roleUserIdsMap.get(role.roleId);
                        const [firstUserId, ...remainingUserIds] = userIds || [];
                        const firstUserInfo = firstUserId
                            ? labUsers.find(rou => rou.userId === firstUserId) || initEhsRou
                            : initEhsRou;

                        return (
                            <React.Fragment key={`labuser-${role.roleId}`}>
                                <tr className={isOddDataRowClass}>
                                    <td
                                        data-title={t('table.title.role.item')}
                                        rowSpan={roleIdCountsMap.get(role.roleId)}
                                    >
                                        {role.roleName}
                                    </td>
                                    <UserDataCells
                                        userInfo={firstUserInfo}
                                        userIdText={userIdText}
                                        t={t}
                                    />
                                </tr>
                                {remainingUserIds?.map((userId) => {
                                    const userInfo = labUsers.find(rou => rou.userId === userId) || initEhsRou;
                                    return (
                                        <tr
                                            className={isOddDataRowClass}
                                            key={`labuser-${role.roleId}-${userId}`}
                                        >
                                            <UserDataCells
                                                userInfo={userInfo}
                                                userIdText={userIdText}
                                                t={t}
                                            />
                                        </tr>
                                    );
                                })}
                            </React.Fragment>
                        );
                    })}
                </tbody>
            </StyledTable>
        </div>
    );
};

const StyledTable = styled.table`
  &.table {
    thead {
      background: rgb(251, 205, 165);
    }
  }
`;

export default LabUserRoleTable; 