import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const FuncAPI = {
  getFuncList: async (parms: BaseParams) => {
    return apiRequest(getApiAddress + "func/list", createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  getFuncListByGroupId: async (
    parms: BaseParams & {
      groupId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "func/list/groupid", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getFuncListByRoleId: async (
    parms: BaseParams & {
      roleId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "func/list/roleid", createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  getFuncListByPid: async (
    parms: BaseParams & {
      funcPid: string;
    }
  ) => {
    return apiRequest(getApiAddress + "func/list/pid", createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  getFuncFavListByUser: async (
    parms: BaseParams & {
      userId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "func/favorite/list", createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  addFuncFav: async (
    parms: BaseParams & {
      funcId: string;
      userId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "func/favorite/add", createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  updateRoleObjFuncs: async (
    parms: BaseParams & {
      roleId: string;
      objFuncId: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "func/objfunc/edit", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  delFuncFav: async (
    parms: BaseParams & {
      funcFavId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "func/favorite/del", createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
};
