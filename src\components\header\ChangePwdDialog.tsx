import BlockUi from "@availity/block-ui";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { Tooltip } from "react-tooltip";
import styled from "styled-components";
import { AuthorAPI } from "../../api/authorAPI";
import useLoginUser from "../../CONNChainEHS/hooks/useLoginUser";
import BlockuiMsg from "../../CONNChainEHS/common/BlockuiMsg";
import { confirmMsg } from "../../CONNChainEHS/common/SwalMsg";
import { showSuccessToast } from "../../CONNChainEHS/common/Toast";
import { PWD_MIN_LENGTH } from "../../CONNChainEHS/constant/constants";
import { getBasicLoginUserInfo } from "../../CONNChainEHS/utils/authUtil";
import { isApiCallSuccess } from "../../CONNChainEHS/utils/resultUtil";
import { checkFormatPwdComplexity } from "../../CONNChainEHS/utils/stringUtil";

const formDataField = {
    newPwd: 'newPwd',
    confirmNewPwd: 'confirmNewPwd',
};

export default function ChangePwdDialog({
    onActionSuccess = () => { },
    onClose = () => { },
}: {
    onActionSuccess: () => void;
    onClose: () => void;
}) {
    const { loginUser } = useLoginUser();
    const { t } = useTranslation();
    const actTitle = t("text.change_password");
    const { reset, register, handleSubmit, watch, formState: { errors } } = useForm();
    const [loadingBlock, setLoadingBlock] = useState(false);

    const onSubmit = (data: any) => {
        onAction(data);
    };

    const onAction = (data: any) => {
        if (!loginUser) {
            return;
        }
        confirmMsg(t('message.confirm.change_pwd'), t).then((value) => {
            if (value) {
                setLoadingBlock(true);
                AuthorAPI.changePwd({
                    ...getBasicLoginUserInfo(loginUser),
                    userId: loginUser.loginUserId,
                    pwd: data.newPwd.trim()
                }).then((result) => {
                    if (isApiCallSuccess(result)) {
                        showSuccessToast(t('message.success'));
                        onActionSuccess();
                    }
                    reset();
                    setLoadingBlock(false);
                }).catch(err => {
                    reset();
                    setLoadingBlock(false);
                })
            }
        });
    };

    const clickClose = () => {
        onClose();
        reset();
    }
    const minPwdLegMsg = t('message.password_too_short', { minLen: PWD_MIN_LENGTH });
    const pwdComplexityMsg = t('message.password_complexity');

    return (
        <StyledChangePwdDialog>
            <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
                <div className="changePwd">
                    <div className="changePwd-header">
                        <h4 className="modal-title">{actTitle}</h4>
                        <button
                            type="button"
                            className="btn-close"
                            aria-hidden="true"
                            onClick={clickClose}
                        ></button>
                    </div>
                    <div className="changePwd-body d-flex align-items-start flex-wrap">
                        <form onSubmit={handleSubmit(onSubmit)} className="col-md-10 mb-3">
                            <div className="mb-3">
                                <label className="fw-bold mb-1">
                                    {t('text.new_password')}：<span className="text-danger">*</span>
                                    <Tooltip
                                        id="pwdComplexityTooltip"
                                        place="right"
                                        variant="info"
                                    >
                                        {minPwdLegMsg} <br />{pwdComplexityMsg}
                                    </Tooltip>
                                    <i className="fas fa-question-circle fs-5 ms-1" data-tooltip-id="pwdComplexityTooltip" />
                                </label>
                                <input
                                    type="password"
                                    {...register(formDataField.newPwd, {
                                        required: t('message.enter'),
                                        validate: {
                                            minLength: (value) =>
                                                value.length >= PWD_MIN_LENGTH || minPwdLegMsg, // 密碼最少 x 個字元
                                            complexity: (value) =>
                                                checkFormatPwdComplexity(value) || pwdComplexityMsg,
                                        },
                                    })}
                                    className={`form-control form-control-lg ${errors[formDataField.newPwd] ? 'is-invalid' : ''}`}
                                />
                                {errors[formDataField.newPwd] && <div className="mt-1"><span className="text-danger">{errors[formDataField.newPwd]?.message?.toString()}</span></div>}
                            </div>
                            <div className="mb-3">
                                <label className="fw-bold mb-1">
                                    {t('text.confirm_new_password')}：<span className="text-danger">*</span>
                                </label>
                                <input
                                    type="password"
                                    {...register(formDataField.confirmNewPwd, {
                                        required: t('message.enter'),
                                        validate: (value) => value === watch(formDataField.newPwd) || t('message.password_not_match')
                                    })}
                                    className={`form-control form-control-lg ${errors[formDataField.confirmNewPwd] ? 'is-invalid' : ''}`}
                                />
                                {errors[formDataField.confirmNewPwd] && <div className="mt-1"><span className="text-danger">{errors[formDataField.confirmNewPwd]?.message?.toString()}</span></div>}
                            </div>
                        </form>
                    </div>
                    {actTitle && <div className="changePwd-footer">
                        <div className="btn btn-white" aria-hidden="true" onClick={clickClose}>
                            <i className="fas fa-close" /> {t("button.close")}
                        </div>
                        <div className={`btn btn-warning ms-3`} onClick={() => handleSubmit(onSubmit)()}>
                            <i className="fas fa-pen" /> {actTitle}
                        </div>
                    </div>}
                </div>
            </BlockUi>
        </StyledChangePwdDialog >
    );
}

const StyledChangePwdDialog = styled.div`
  background: white;
  // padding: 10px;
  width: 500px;

  .download-button:hover {
    text-decoration: underline; /* 滑鼠懸停時顯示底線 */
    }

  .chem-info-div { 
      overflow-y: auto;
      height: 675px;
  }
  
  label{
    user-select: none;
  }

  .changePwd-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  .changePwd-body {
    padding: 15px;
  }
  .changePwd-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-purple {
      margin-left: 10px;
    }
  }
  
  @media (max-width: 600px){
    .chem-info-div { 
        min-height: 0; 
    }
    
    .changePwd-footer {
        border-top: 1px solid #ced4da;
        padding: 15px;
        margin-top: 140px; 
        justify-content: center;
        .btn.btn-purple {
        margin-left: 10px;
        position: relative;  
        }
    }
  }
    
    @media (max-width: 768px) {
        width: 100%;
        min-width: auto;
        
        .changePwd-footer {
            flex-wrap: wrap;
        }
    }
`;
