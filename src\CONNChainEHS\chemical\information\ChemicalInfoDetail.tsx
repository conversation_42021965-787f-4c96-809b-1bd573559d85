import BlockUi from "@availity/block-ui";
import { AreaAPI } from "api/areaAPI";
import { ChemicalAPI } from "api/chemicalAPI";
import { ConfigAPI } from "api/configAPI";
import { FileAPI } from "api/fileAPI";
import { OptionAPI } from "api/optionAPI";
import { AppPaths } from "config/app-paths";
import BlockuiMsg from "ehs/common/BlockuiMsg";
import BackButton from "ehs/common/button/BackButton";
import DownloadButton from "ehs/common/button/DownloadButton";
import ChemicalClassificationBadge from "ehs/common/chemical/ChemicalClassificationBadge";
import GhsImage from "ehs/common/GhsImage";
import { CHEMICAL_PHASE_LIQUID, CONFIG_ID_START_PUB_HAZ, CONFIG_TYPE_CHEM, CONFIG_TYPE_CHEM_AREA_APPROVE_TYPE, CONFIG_TYPE_CHEM_CLASS_RULE_COMPARE, CONFIG_TYPE_CHEM_CLASS_RULE_TYPE, CONFIG_TYPE_CHEM_STATUS, CONFIG_TYPE_CHEM_TEMPRATURE_TYPE, CONFIG_TYPE_CITY, CONFIG_TYPE_CONCEN_TYPE, CONFIG_TYPE_CONCERNED, CONFIG_TYPE_CTRL_CONCEN_TYPE, CONFIG_TYPE_CTRL_TOTAL_QTY_TYPE, CONFIG_TYPE_GHS_CLASS, CONFIG_TYPE_GHS_IMG, CONFIG_TYPE_PRIORITY, CONFIG_TYPE_PUBLIC_HAZARD, CONFIG_TYPE_STATE, CONFIG_TYPE_TOXIC, CONFIG_VAL_GHS, CONFIG_VAL_PRIORITY, CONFIG_VAL_PUBLIC_HAZARD, CONFIG_VAL_CONCERNED, CONFIG_VAL_TOXIC, FILE_TYPE_AREA_CHEMICAL_APPROVAL_INFO, OPTION_CHEM_CLASSIFY_MODE, OPTION_CHEM_INFO_MODIFY_FIRST, OPTION_VAL_CHEM_CLASSIFY_MODE_STRICT } from "ehs/constant/constants";
import { ChemicalTemperatureType } from "ehs/enums/ChemicalTempratureType";
import useGhsMapping from "ehs/hooks/useGhsMapping";
import useLoginUser from "ehs/hooks/useLoginUser";
import Breadcrumbs from "ehs/layout/Breadcrumbs";
import Footer from "ehs/layout/Footer";
import { EhsArea } from "ehs/models/EhsArea";
import { EhsAreaChemicalApproveInfo } from "ehs/models/EhsAreaChemicalApproveInfo";
import { EhsChemical, initEhsChemical } from "ehs/models/EhsChemical";
import { EhsChemicalCasno } from "ehs/models/EhsChemicalCasno";
import { EhsChemicalClassRule } from "ehs/models/EhsChemicalClassRule";
import { EhsChemicalInfoArea, initEhsChemicalInfoArea } from "ehs/models/EhsChemicalInfoArea";
import { EhsChemicalName } from "ehs/models/EhsChemicalName";
import { EhsConfigParam, initEhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsFile } from "ehs/models/EhsFile";
import { EhsOptions, initEhsOptions } from "ehs/models/EhsOptions";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { getBasicLoginUserInfo, navigateToHome } from "ehs/utils/authUtil";
import { getApprovalNoPre } from "ehs/utils/chemicalUtil";
import { isChineseLang, isEnglishLang, splitChemNameListByLang } from "ehs/utils/langUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { getFormatDateSlash } from "ehs/utils/stringUtil";
import React, { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from 'react-router-dom';
import styled from "styled-components";
import ChemicalInfoConcen from "./ChemicalInfoConcen";
import ChemicalAreaApprovalSection from "./ChemicalAreaApprovalSection";

//分類選項類型
type OptionState = {
  chemClassify: EhsConfigParam[];
  chemPhaseStates: EhsConfigParam[];
  chemStatus: EhsConfigParam[];
  toxicClassify: EhsConfigParam[];
  concernedClassify: EhsConfigParam[];
  priorityClassify: EhsConfigParam[];
  concenType: EhsConfigParam[];
  chemConcType: EhsConfigParam[];
  ghsClassify: EhsConfigParam[];
  ghsImg: EhsConfigParam[];
  publicHazardClassify: EhsConfigParam[];
  areaApprovalType: EhsConfigParam[];
  cityList: EhsConfigParam[];
  ctrlTotalQtyType: EhsConfigParam[];
  chemClassRuleType: EhsConfigParam[];
  chemClassRuleCompare: EhsConfigParam[];
  tempratureTypeOptions: EhsConfigParam[];
};

//分類選項類型對應
const configTypeMap = {
  chemClassify: CONFIG_TYPE_CHEM,
  chemPhaseStates: CONFIG_TYPE_STATE,
  chemStatus: CONFIG_TYPE_CHEM_STATUS,
  toxicClassify: CONFIG_TYPE_TOXIC,
  concernedClassify: CONFIG_TYPE_CONCERNED,
  priorityClassify: CONFIG_TYPE_PRIORITY,
  concenType: CONFIG_TYPE_CONCEN_TYPE,
  chemConcType: CONFIG_TYPE_CTRL_CONCEN_TYPE,
  ghsClassify: CONFIG_TYPE_GHS_CLASS,
  ghsImg: CONFIG_TYPE_GHS_IMG,
  publicHazardClassify: CONFIG_TYPE_PUBLIC_HAZARD,
  areaApprovalType: CONFIG_TYPE_CHEM_AREA_APPROVE_TYPE,
  cityList: CONFIG_TYPE_CITY,
  ctrlTotalQtyType: CONFIG_TYPE_CTRL_TOTAL_QTY_TYPE,
  chemClassRuleType: CONFIG_TYPE_CHEM_CLASS_RULE_TYPE,
  chemClassRuleCompare: CONFIG_TYPE_CHEM_CLASS_RULE_COMPARE,
  tempratureTypeOptions: CONFIG_TYPE_CHEM_TEMPRATURE_TYPE
};//取得的config type
const initOption = {
  chemClassify: [],
  chemPhaseStates: [],
  chemStatus: [],
  toxicClassify: [],
  concernedClassify: [],
  priorityClassify: [],
  concenType: [],
  chemConcType: [],
  ghsClassify: [],
  ghsImg: [],
  publicHazardClassify: [],
  areaApprovalType: [],
  cityList: [],
  ctrlTotalQtyType: [],
  chemClassRuleType: [],
  chemClassRuleCompare: [],
  tempratureTypeOptions: [],
};

//多輸入框型態
const multiValType = {
  casno: "casno",
  name: "name",
  chName: "chName",
  enName: "enName",
}

function ChemicalInfoDetail() {
  const { t, i18n } = useTranslation();
  const { state } = useLocation();
  const { initChemId } = state || {};
  const { loginUser } = useLoginUser();
  const ghsMappingObj = useGhsMapping(loginUser);//ghs分類勾選對應
  const navigate = useNavigate();
  const [loadingBlock, setLoadingBlock] = useState(false);
  const [hasDuplicateCasno, setHasDuplicateCasno] = useState<boolean[]>([false]);//檢查casno 是否在系統內有重複
  const [needGrandHandQty, setNeedGrandHandQty] = useState<boolean>(false);//是否需要分級運作量
  const [initGhsClass, setInitGhsClass] = useState<boolean>(false);//是否初始過ghs分類
  const [initChemInfoArea, setInitChemInfoArea] = useState<boolean>(false);//是否初始過化學品區域設定狀態
  const [boilType, setBoilType] = useState(ChemicalTemperatureType.Single); //沸點類型 1:單一 2:區間
  const [meltType, setMeltType] = useState(ChemicalTemperatureType.Single); //熔點類型 1:單一 2:區間
  const [firstValidCasno, setFirstValidCasno] = useState<string>("");//暫存第一個有效格式casno 避免未修改重複call api判斷
  const [originEditCasno, setOriginEditCasno] = useState<string>("");//修改暫存第一個修改前的casno 避免未修改重複call api判斷卡到
  const [checkedChemClass, setCheckedChemClass] = useState<{ [key: string]: EhsConfigParam }>({});//勾選的化學品分類 (判斷用)
  const [ghsClassObj, setGhsClassObj] = useState<{ [key: string]: string[] }>({});//ghs分類相關內容(公共危險分類 & 圖片)
  const [casnoVaild, setCasnoVaild] = useState<(boolean | null)[]>([null]);//判斷casno格式是否正確
  const [grandHandQtyTemp, setGrandHandQtyTemp] = useState<(number | null)>(null);//暫存分級運作量
  const [chemClassifyMode, setChemClassifyMode] = useState<(EhsOptions)>(initEhsOptions);//化學品分類模式 0:簡單版 1:需針對每個分類設定
  const [canModifyFirstInfo, setCanModifyFirstInfo] = useState<(EhsOptions)>(initEhsOptions);//化學品基本資訊能否修改比對過分類的第一筆(毒化 關注等)
  const [chemicalInfo, setChemicalInfo] = useState<EhsChemical>(initEhsChemical);//化學品資訊 
  const [option, setOption] = useState<OptionState>(initOption);//checkbox radio option
  const [selectedOptions, setSelectedOptions] = useState<OptionState>(initOption);//checkbox radio selected option
  const [areaList, setAreaList] = useState<EhsArea[]>([]);
  const [chemApproveInfoMap, setChemApproveInfoMap] = useState<{ [type: string]: EhsAreaChemicalApproveInfo }>({});
  const [chemApproveInfoFile, setChemApproveInfoFile] = useState<{ [type: string]: EhsFile | null }>({});
  const [chemInfoArea, setChemInfoArea] = useState<{ [areaId: string]: EhsChemicalInfoArea }>({});
  const [chemClassRuleObj, setChemClassRuleObj] = useState<{ [configId: string]: EhsChemicalClassRule }>({});
  const { conList } = chemicalInfo;
  const reqValType = {
    chemClass: "chemClass",
  }
  const errorConditions: { [key: string]: () => boolean } = {
    chemClass: () => Object.keys(checkedChemClass).length === 0,//化學品分類必選
    // 可以添加更多的條件
  };//必填判斷條件
  const initialReqState: { [key: string]: boolean | null } = {};
  Object.keys(reqValType).forEach((key) => {
    initialReqState[key] = null;
  });
  const [reqField, setReqField] = useState(initialReqState);//判斷必填欄位
  const { chemClassify, chemPhaseStates, chemStatus, toxicClassify, concernedClassify,
    priorityClassify, chemConcType, ghsClassify, ghsImg, publicHazardClassify,
    concenType, areaApprovalType, cityList, ctrlTotalQtyType, chemClassRuleType,
    chemClassRuleCompare, tempratureTypeOptions } = option;
  const ghsItem = chemClassify.find(item => item.configValue === CONFIG_VAL_GHS) || initEhsConfigParam;//危害性化學品分類選項


  const selectedConcType = useMemo(() =>
    chemConcType.find(type => type.configIvalue === chemicalInfo.chemCtrlConcenType),
    [chemConcType, chemicalInfo.chemCtrlConcenType]
  );
  // 新增一個 memo 來記憶 selectedChemClassify
  const selectedChemClassify = useMemo(() => selectedOptions.chemClassify, [selectedOptions.chemClassify]);
  const selectedGhsImg = ghsImg.filter(item => selectedOptions.ghsImg.some(selected => selected.configId === item.configId));
  const selectedPubHazClass = publicHazardClassify.filter(item => selectedOptions.publicHazardClassify.some(selected => selected.configId === item.configId));
  const hasSelectedPubHazClass = !isArrayEmpty(selectedPubHazClass);
  const mainGhsClass = ghsClassify.filter(item => !item.configSubType);
  const subGhsClass = ghsClassify.filter(item => item.configSubType);
  const cityMap: { [key: string]: EhsConfigParam } = cityList.reduce((acc, item) => {
    acc[item.configId] = item;
    return acc;
  }, {} as { [key: string]: EhsConfigParam });

  interface SubGhsClassObject {
    [key: string]: EhsConfigParam[]; // 或者根据实际情况定义更具体的类型
  }
  const subGhsClassObject: SubGhsClassObject = subGhsClass.reduce((acc, curr) => {
    const { configSubType } = curr;
    if (!acc[configSubType]) {
      acc[configSubType] = [];
    }
    acc[configSubType].push(curr);
    return acc;
  }, {} as SubGhsClassObject);
  const [multiInputs, setMultiInputs] = useState({
    [multiValType.casno]: [''],
    [multiValType.name]: [''],
    [multiValType.chName]: [''],
    [multiValType.enName]: [''],
  }); // 儲存輸入框的內容
  const [isRepeatInputs, setIsRepeatInputs] = useState({
    [multiValType.casno]: [false],
    [multiValType.name]: [false],
    [multiValType.chName]: [false],
    [multiValType.enName]: [false],
  });//判斷多個輸入框是否有填入重複的內容
  const isChinese = isChineseLang(i18n.language);
  const isEnglish = isEnglishLang(i18n.language);

  const isDisplayApproveinfoSection = areaList.some((area) => {
    const { areaId } = area;
    const enabledArea = chemInfoArea[areaId]?.chemStatus !== 0;
    return enabledArea;
  });
  const isStrictChemClass = chemClassifyMode.optionValue === OPTION_VAL_CHEM_CLASSIFY_MODE_STRICT;
  const selectedPhaseStates = selectedOptions.chemPhaseStates;
  const hasLiquid = selectedPhaseStates.some(phaseState => phaseState.configValue === CHEMICAL_PHASE_LIQUID);

  useEffect(() => {
    if (loginUser) {
      fetchOption();
    }
  }, [loginUser]);

  useEffect(() => {
    if (loginUser) {
      if (!initChemId) {
        navigateToHome(navigate);
        return;
      }
      fetchConfigData();
      clearMultiValEnName();
      fetchAreaData();
      fetchAreaChemApproveInfoList();
      fetchChemicalApprovalFile();
      fetchChemData();
    }
  }, [loginUser, i18n.language]);

  //化學品勾選後切換必填提示訊息
  useEffect(() => {
    if (reqField[reqValType.chemClass] != null) {
      setReqField({
        ...reqField,
        [reqValType.chemClass]: errorConditions[reqValType.chemClass]()
      });
    }
  }, [checkedChemClass])

  useEffect(() => {
    if (grandHandQtyTemp) {
      changeChemInfo({ chemGradeHandQty: grandHandQtyTemp });
    }
  }, [grandHandQtyTemp])

  useEffect(() => {
    if (!initGhsClass && !isArrayEmpty(Object.keys(ghsMappingObj)) && !isArrayEmpty(selectedOptions.ghsClassify)) {
      setInitGhsClass(true);
      const newGhsClassObj = getNewGhsClassObj(selectedOptions.ghsClassify.map(item => item.configId), true);
      setGhsClassObj(newGhsClassObj);
    }
  }, [ghsMappingObj, selectedOptions.ghsClassify])

  /**
   * db 沒資料時 初始化化學品區域資訊
   */
  useEffect(() => {
    if (!isArrayEmpty(areaList) && !isArrayEmpty(conList) && initChemInfoArea) {
      const initialChemInfoArea = conList.reduce((conAcc, con) => {
        // 對每個濃度區間，初始化所有區域的狀態
        const areaStates = areaList.reduce((areaAcc, area) => {
          const key = `${con.chemConId}-${area.areaId}`;
          areaAcc[key] = {
            ...initEhsChemicalInfoArea,
            chemConId: con.chemConId,  // 加入濃度區間ID
            areaId: area.areaId,       // 加入區域ID
          };
          return areaAcc;
        }, {} as { [key: string]: EhsChemicalInfoArea });

        return { ...conAcc, ...areaStates };
      }, {} as { [key: string]: EhsChemicalInfoArea });

      setChemInfoArea(initialChemInfoArea);
      setInitChemInfoArea(false);
    }
  }, [areaList, conList, initChemInfoArea]);

  /**
   * 需濃度控管分類時要抓設定的值
   */
  useEffect(() => {
    if (loginUser && isStrictChemClass) {
      fetchChemClassRule();
    }
  }, [loginUser, isStrictChemClass])

  useEffect(() => {

    // 根據 hasPubHazClass 的值來決定是否加入或移除 CONFIG_VAL_PUBLIC_HAZARD
    const publicHazardOption = chemClassify.find(item => item.configValue === CONFIG_VAL_PUBLIC_HAZARD);
    if (publicHazardOption) {
      handleChemClassChange({} as React.ChangeEvent<HTMLInputElement>, publicHazardOption, hasSelectedPubHazClass);
    }
  }, [hasSelectedPubHazClass])

  const changeChemInfo = (obj: object) => {
    setChemicalInfo({ ...chemicalInfo, ...obj })
  }

  //判斷是否選取
  const isCheckedOptionById = (configList: EhsConfigParam[], targetConfigId: string): boolean => {
    return configList.some((item) => item.configId === targetConfigId);
  };

  //切換語系如果是英文 須清除英文名稱
  const clearMultiValEnName = () => {
    multiInputs[multiValType.enName] = [""];
    isRepeatInputs[multiValType.enName] = [false];
  }

  //變更化學品分類時的動作
  const handleChemClassChange = (event: React.ChangeEvent<HTMLInputElement>, chemClass: EhsConfigParam, checked?: boolean) => {
    const isChecked = checked !== undefined ? checked : event.target.checked;

    setCheckedChemClass(prevState => {
      const updatedState = { ...prevState };

      // 更新 CheckedChemClass 狀態
      if (isChecked) {
        updatedState[chemClass.configValue] = chemClass; // 添加選中的化學分類
      } else {
        delete updatedState[chemClass.configValue]; // 如果取消勾選，移除對應項目
      }

      return updatedState;
    });

    // 調用處理選項變更的函數
    handleCheckboxOptionChange(event, chemClass, configTypeMap.chemClassify, checked);
  };

  //變更選項時的動作
  const handleCheckboxOptionChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    config: EhsConfigParam,
    option: string,
    checked?: boolean
  ) => {
    const isChecked = checked !== undefined ? checked : event.target.checked;
    const classifyKey = Object.keys(configTypeMap).find(
      (key) => configTypeMap[key as keyof typeof configTypeMap] === option
    ) as keyof OptionState; // 使用型別斷言告訴 TypeScript classifyKey 是 OptionState 的合法屬性名稱
    const isChemClassify = classifyKey === 'chemClassify';

    if (classifyKey !== undefined) {
      setSelectedOptions(prevSelectedOptions => {
        const newSelectedOptions = { ...prevSelectedOptions };

        // 檢查是否已存在相同的 configId
        const isDuplicate = newSelectedOptions[classifyKey].some(
          item => item.configId === config.configId
        );

        if (isChecked && !isDuplicate) {
          // 只有在重複的情況下才添加
          newSelectedOptions[classifyKey] = [...newSelectedOptions[classifyKey], config];

          // 判斷是否為 chemClassify 並添加 ghsItem (同樣檢查重複)
          if (isChemClassify && ghsItem) {
            const isGhsDuplicate = newSelectedOptions[classifyKey].some(
              item => item.configId === ghsItem.configId
            );
            if (!isGhsDuplicate) {
              newSelectedOptions[classifyKey] = [...newSelectedOptions[classifyKey], ghsItem];
            }
          }
        } else if (!isChecked) {
          // 取消勾選時的邏輯保持不變
          newSelectedOptions[classifyKey] = newSelectedOptions[classifyKey].filter(
            param => param.configId !== config.configId
          );

          const nonGhsCount = newSelectedOptions[classifyKey].filter(
            param => param.configValue !== CONFIG_VAL_GHS
          ).length;
          if (isChemClassify && nonGhsCount === 0) {
            newSelectedOptions[classifyKey] = newSelectedOptions[classifyKey].filter(
              param => param.configValue !== CONFIG_VAL_GHS
            );
          }
        }

        return newSelectedOptions;
      });
    }
  };

  // 更新GHS选项的通用函数
  const getNewGhsClassObj = (configIds: string[], checked: boolean) => {
    const newGhsClassObj: { [key: string]: string[] } = { ...ghsClassObj };
    if (!isArrayEmpty(configIds)) {
      configIds.forEach(configId => {
        const ghsMappingArray = ghsMappingObj[configId];
        if (ghsMappingArray) {
          ghsMappingArray.forEach((key) => {
            if (checked) {
              // 勾选逻辑
              if (newGhsClassObj[key]) {
                newGhsClassObj[key].push(configId);
              } else {
                newGhsClassObj[key] = [configId];
              }
            } else {
              // 取消勾选逻辑
              if (newGhsClassObj[key]) {
                newGhsClassObj[key] = newGhsClassObj[key].filter((id) => id !== configId);
                if (isArrayEmpty(newGhsClassObj[key])) {
                  delete newGhsClassObj[key];
                }
              }
            }
          });
        }
      });
    }

    const objKeys = Object.keys(newGhsClassObj);
    let updatedOptions: Partial<OptionState> = {};
    // 通过 reduce 一次性设置 selectedOptions
    let updatedSelectedOptions = objKeys.reduce((prevOptions, key) => {

      // 根据 key 开头进行相应的处理
      if (key.startsWith(CONFIG_TYPE_GHS_IMG)) {
        const optionArray = option.ghsImg || []; // 根据 key 找到对应的 option 数组 
        // 如果 key 开头是 ghs_img，将符合条件的 option 加入 selectedOptions 的 ghsImg 数组中
        updatedOptions = {
          ...updatedOptions,
          ghsImg: [
            ...(updatedOptions.ghsImg || []),
            ...optionArray.filter(option => key === option.configId),
          ],
        };
      } else if (key.startsWith(CONFIG_ID_START_PUB_HAZ)) {
        const optionArray = option.publicHazardClassify || []; // 根据 key 找到对应的 option 数组
        // 如果 key 开头是 pub_haz，将符合条件的 option 加入 selectedOptions 的 publicHazardClassify 数组中
        updatedOptions = {
          ...updatedOptions,
          publicHazardClassify: [
            ...(updatedOptions.publicHazardClassify || []),
            ...optionArray.filter(option => key === option.configId),
          ],
        };
      }

      return updatedOptions;
    }, {});

    if (!objKeys.some(str => str.startsWith(CONFIG_TYPE_GHS_IMG))) {
      updatedSelectedOptions = {
        ...updatedSelectedOptions,
        ghsImg: [],
      };
    }
    if (!objKeys.some(str => str.startsWith(CONFIG_ID_START_PUB_HAZ))) {
      updatedSelectedOptions = {
        ...updatedSelectedOptions,
        publicHazardClassify: [],
      };
    }


    // 更新 selectedOptions
    setSelectedOptions(prevOptions => ({
      ...prevOptions,
      ...updatedSelectedOptions,
    }));
    return newGhsClassObj;
  };

  const checkedToxic = !!checkedChemClass[CONFIG_VAL_TOXIC];
  const checkedSvhc = !!checkedChemClass[CONFIG_VAL_CONCERNED];
  const checkedPriority = !!checkedChemClass[CONFIG_VAL_PRIORITY];

  const fetchChemData = () => {

    ChemicalAPI.getChemicalDetail({
      ...getBasicLoginUserInfo(loginUser),
      chemId: initChemId
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const chemInfo = result.results;
        const { infoAreaList, casnoList, nameList, categoryList, chemGradeHandQty,
          chemBoilPoint, chemBoilPointMax, chemMeltPoint, chemMeltPointMax } = chemInfo;
        setNeedGrandHandQty(chemGradeHandQty !== null);
        setChemicalInfo(chemInfo);
        if (chemBoilPoint && chemBoilPointMax) {
          setBoilType(ChemicalTemperatureType.Range);
        }
        if (chemMeltPoint && chemMeltPointMax) {
          setMeltType(ChemicalTemperatureType.Range);
        }

        const newSelectedOptions = { ...selectedOptions };
        for (const key in configTypeMap) {
          if (configTypeMap.hasOwnProperty(key)) {
            const configType = configTypeMap[key as keyof typeof configTypeMap];
            const filteredCategoryList = categoryList.filter((item: EhsConfigParam) => item.configType === configType);

            // 使用 Set 來去除重複項目
            const existingIds = new Set(newSelectedOptions[key as keyof OptionState].map(item => item.configId));
            const uniqueNewItems = filteredCategoryList.filter((item: EhsConfigParam) => !existingIds.has(item.configId));

            newSelectedOptions[key as keyof OptionState] = [
              ...newSelectedOptions[key as keyof OptionState],
              ...uniqueNewItems
            ];
          }
        }

        const chemClassObject = newSelectedOptions.chemClassify.reduce((acc: { [key: string]: EhsConfigParam }, item: EhsConfigParam) => {
          acc[item.configValue] = item;
          return acc;
        }, {} as { [key: string]: EhsConfigParam });
        setCheckedChemClass(chemClassObject);
        setSelectedOptions({
          ...newSelectedOptions
        });
        const { currentLangNames, enNames, chNames } = splitChemNameListByLang(nameList, i18n.language);
        const casnoArray: string[] = casnoList.map((item: EhsChemicalCasno) => item.casno);

        // 根據語言選擇要使用的名稱數組
        let nameArray: string[] = isEnglish ? enNames.map((item: EhsChemicalName) => item.chemName) : currentLangNames.map((item: EhsChemicalName) => item.chemName);
        let chNameArray: string[] = isChinese ? [] : chNames.map((item: EhsChemicalName) => item.chemName);
        let enNameArray: string[] = isEnglish ? [] : enNames.map((item: EhsChemicalName) => item.chemName);

        //沒有名稱時需預設第一個空的input
        if (isArrayEmpty(nameArray)) {
          nameArray = [""];
        }
        if (isArrayEmpty(enNameArray)) {
          enNameArray = [""];
        }

        // 建立一個通用的陣列長度映射
        const arrayLengthMap = {
          [multiValType.casno]: casnoArray.length,
          [multiValType.name]: nameArray.length,
          [multiValType.chName]: chNameArray.length,
          [multiValType.enName]: enNameArray.length
        };

        // 使用 Object.keys() 動態生成 initialIsRepeatArrays
        const initialIsRepeatArrays = Object.keys(multiValType).reduce((acc, key) => {
          acc[key] = Array(arrayLengthMap[key]).fill(false);
          return acc;
        }, {} as { [key: string]: boolean[] });

        if (!isArrayEmpty(casnoArray)) {
          setOriginEditCasno(casnoArray[0] || "");
        }

        setMultiInputs({
          ...multiInputs,
          [multiValType.casno]: casnoArray,
          [multiValType.name]: nameArray,
          [multiValType.chName]: chNameArray,
          [multiValType.enName]: enNameArray,
        })
        setIsRepeatInputs({
          ...isRepeatInputs,
          ...initialIsRepeatArrays,
        })

        if (!isArrayEmpty(infoAreaList)) {
          const initialChemInfoArea = infoAreaList.reduce((acc: { [areaId: string]: EhsChemicalInfoArea }, areaInfo: EhsChemicalInfoArea) => {
            acc[areaInfo.chemConId + "-" + areaInfo.areaId] = areaInfo;
            return acc;
          }, {});
          setChemInfoArea(initialChemInfoArea);
        } else {
          setInitChemInfoArea(true);
        }
      }
    });
  };
  const fetchConfigData = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser),
      configTypeList: Object.values(configTypeMap)
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const rs: EhsConfigParam[] = result.results;
        const newOptions = (Object.keys(configTypeMap) as Array<keyof typeof configTypeMap>).reduce((acc, key) => {
          acc[key] = rs.filter((item: EhsConfigParam) => item.configType === configTypeMap[key]);
          return acc;
        }, {} as { [K in keyof typeof configTypeMap]: EhsConfigParam[] });

        setOption(newOptions);
      }
    });
  };

  const fetchAreaData = () => {
    AreaAPI.getAreaList({
      ...getBasicLoginUserInfo(loginUser),
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        setAreaList(result.results);
      }
    });
  };

  const fetchAreaChemApproveInfoList = () => {
    AreaAPI.getAreaChemicalApproveInfoList({
      ...getBasicLoginUserInfo(loginUser),
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const approveInfoMap = result.results.reduce((map: { [type: string]: EhsAreaChemicalApproveInfo }, item: EhsAreaChemicalApproveInfo) => {
          map[item.areaId + '-' + item.approvalType] = item;
          return map;
        }, {} as { [type: string]: EhsAreaChemicalApproveInfo });
        setChemApproveInfoMap(approveInfoMap);
      }
    });
  }

  const fetchChemicalApprovalFile = () => {
    FileAPI.getFileList({
      ...getBasicLoginUserInfo(loginUser),
      fileTypeList: [FILE_TYPE_AREA_CHEMICAL_APPROVAL_INFO],
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const resultFileList: EhsFile[] = result.results;
        const fileInfos: Record<string, EhsFile> = {};
        resultFileList.forEach((file: EhsFile) => {
          fileInfos[file.fileMappingId + '-' + file.fileSubtype] = file;
        });
        setChemApproveInfoFile(fileInfos);
      }
    });
  };

  const fetchOption = () => {
    const optionIds = [OPTION_CHEM_CLASSIFY_MODE, OPTION_CHEM_INFO_MODIFY_FIRST];
    OptionAPI.getOptionListByIds({
      ...getBasicLoginUserInfo(loginUser),
      optionIdList: optionIds
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const options = result.results;
        const optionsMap: {
          [key: string]: EhsOptions;
        } = {};
        options.forEach((item: any) => {
          if (optionIds.includes(item.optionId)) {
            optionsMap[item.optionId] = item;
          }
        });
        setChemClassifyMode(optionsMap[OPTION_CHEM_CLASSIFY_MODE]);
        setCanModifyFirstInfo(optionsMap[OPTION_CHEM_INFO_MODIFY_FIRST]);
      }
    });
  };

  const fetchChemClassRule = () => {
    ChemicalAPI.getChemicalClassByChemId({
      ...getBasicLoginUserInfo(loginUser),
      chemId: initChemId
    }).then(result => {
      if (isApiCallSuccess(result)) {
        setChemClassRuleObj(result.results.reduce((acc: { [key: string]: EhsChemicalClassRule }, item: EhsChemicalClassRule) => {
          acc[item.configId] = item;
          return acc;
        }, {}));
      }
    });
  }

  const infoAreaFragment = (
    <ChemicalAreaApprovalSection
      isDisplayApproveinfoSection={isDisplayApproveinfoSection}
      areaApprovalType={areaApprovalType}
      checkedChemClass={checkedChemClass}
      areaList={areaList}
      chemApproveInfoMap={chemApproveInfoMap}
      chemApproveInfoFile={chemApproveInfoFile}
      chemInfoArea={chemInfoArea}
      cityMap={cityMap}
      isChinese={isChinese}
      className="mt-1" // 可以自定義寬度
    />
  );

  return (
    <StlyedChemicalInfoDetail>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.chemical.manage") },
                { label: t("func.chemical.info"), path: AppPaths.chemical.informationList },
                { label: t('func.detail') },
              ]}
            />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{t('func.detail')}</h1>
            {/* END page-header */}
            <BackButton />
            <div className="card mt-3">
              <div className="card-body">
                <div className="row align-items-start mt-4">
                  {/* <!-- 名稱 --> */}
                  <div className="col-xl-12 px-5">
                    <div className="d-md-flex flex-wrap align-items-center">
                      <label className="ms-1 h2">{t('text.name')}：</label>
                      {multiInputs.name.map((input, index) => (
                        <React.Fragment key={'name' + index}>
                          <label className="h2">{input}</label>
                          {index < multiInputs.name.length - 1 && (
                            <span className="me-2">,</span>
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                  {/* <!-- CAS NO --> */}
                  <div className="col-xl-12 px-5">
                    <div className="d-md-flex flex-wrap align-items-center">
                      <label className="ms-1 mb-0 h5 text-gray">{t('text.chemical.casno')}：</label>
                      {multiInputs.casno.map((input, index) => (
                        <React.Fragment key={'casno' + index}>
                          <label className="text-gray">{input}</label>
                          {index < multiInputs.casno.length - 1 && (
                            <span className="me-2">,</span>
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                </div>
                <hr />
                <div className="row justify-content-center">
                  <div className="col-xl-12 px-5">
                    <div className="fs-5 ms-1 d-inline h5"> {t("text.chemical.class")}</div>
                    <br />
                    {selectedChemClassify.length > 0 ? (
                      selectedChemClassify.map((chemClass: EhsConfigParam) =>
                        <React.Fragment key={'chemclass-badge' + chemClass.configId}>
                          <span className="d-inline-block mb-1">
                            <ChemicalClassificationBadge item={chemClass} />
                          </span>
                        </React.Fragment>
                      )
                    ) : (
                      <div>{t('text.chemical.no_checked_class')}</div>
                    )}
                  </div>
                </div>
                <hr />
                <div className="row align-items-start mt-1">
                  <ChemicalInfoConcen param={{
                    page: "detail",
                    chemical: chemicalInfo,
                    setChemical: setChemicalInfo,
                    setShowGrandHandQty: (show) => {
                      setNeedGrandHandQty(show)
                    },
                    ctrlTotalQtyTypeOption: ctrlTotalQtyType,
                    concenTypeOption: concenType,
                    checkedChemClass: checkedChemClass,
                    areaList: areaList,
                    chemInfoArea: chemInfoArea,
                    chemStatus: chemStatus,
                    setChemInfoArea: setChemInfoArea,
                    infoAreaFragment: infoAreaFragment
                  }} />
                </div>
                <hr />
                <div className="row align-items-start mt-1">
                  {/* <!-- 勞動部公告之優先管理化學品 --> */}
                  {checkedPriority && <div className="col-xl-4 ps-5 mb-3">
                    <label className="h5">{t('text.chemical.prority_class_title')}</label><br />
                    {selectedOptions.priorityClassify.map((priorityClass: EhsConfigParam, index: number) =>
                      <React.Fragment key={'check' + priorityClass.configId}>
                        <label className="text-gray">{priorityClass.configName}</label>
                        {index < selectedOptions.priorityClassify.length - 1 && (
                          <span className="me-2">,</span>
                        )}
                      </React.Fragment>
                    )}
                  </div>}
                  {/* <!-- 相態 --> */}
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="h5">{t('text.chemical.phase_state')}</label><br />
                    {!isArrayEmpty(selectedPhaseStates) ? (
                      selectedPhaseStates.map((phaseState: EhsConfigParam, index: number) =>
                        <React.Fragment key={'check' + phaseState.configId}>
                          <label className="text-gray">{phaseState.configName}</label>
                          {index < selectedPhaseStates.length - 1 && (
                            <span className="me-2">,</span>
                          )}
                        </React.Fragment>
                      )
                    ) : (
                      <label className="text-gray">{t('message.no_information')}</label>
                    )}
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="h5" title={t('text.chemical.un_no_desc')}>{t('text.chemical.un_no')}</label><br />
                    <label className="text-gray">
                      {chemicalInfo.chemUnNo || t('message.no_information')}
                    </label>
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="h5">{t('text.chemical.response_handling_principal')}</label><br />
                    <label className="text-gray">
                      {chemicalInfo.respHandPrinciple || t('message.no_information')}
                    </label>
                  </div>

                  {/* <!-- 列管編號 --> */}
                  {(checkedToxic || checkedSvhc) && <div className="col-xl-4 px-5 mb-3">
                    <label className="h5">{t('text.ctrl_no')}</label><br />
                    <label className="text-gray">{chemicalInfo.chemCtrlNo}</label>
                  </div>}
                  {checkedToxic && <div className="col-xl-4 px-5 mb-3">
                    <label className="h5">{t('text.chemical.toxic_class')}</label><br />
                    <div className="d-md-flex">
                      {selectedOptions.toxicClassify.map((toxClass: EhsConfigParam, index: number) =>
                        <React.Fragment key={'check' + toxClass.configId}>
                          <label className="text-gray">{toxClass.configName}</label>
                          {index < selectedOptions.toxicClassify.length - 1 && (
                            <span className="me-2">,</span>
                          )}
                        </React.Fragment>
                      )}
                    </div>
                  </div>}
                  {checkedSvhc && <div className="col-xl-4 px-5 mb-3">
                    <label className="h5">{t('text.chemical.concerned_class')}</label><br />
                    {selectedOptions.concernedClassify.map((concernedClass: EhsConfigParam, index: number) =>
                      <React.Fragment key={'check' + concernedClass.configId}>
                        <label className="text-gray">{concernedClass.configName}</label>
                        {index < selectedOptions.concernedClassify.length - 1 && (
                          <span className="me-2">,</span>
                        )}
                      </React.Fragment>
                    )}
                  </div>}
                  {(checkedToxic || checkedSvhc) && (
                    <div className="col-xl-4 px-5 mb-3">
                      <label className="mb-2 h5">{t('text.chemical.ctrl_concen')}</label><br />
                      <label className="text-gray">{chemicalInfo.chemCtrlConcen > 0 && `${chemicalInfo.chemCtrlConcen}%`}</label>
                      {selectedConcType?.configName && (
                        <label className="form-check-label text-gray ms-2">
                          {selectedConcType.configName}
                        </label>
                      )}
                    </div>
                  )}
                  {/* <!-- 管制總量 分級運作量--> */}
                  {needGrandHandQty && <div className="col-xl-4 px-5 mb-3">
                    <label className="h5">{t('text.chemical.ctrl_qty')}</label><br />
                    <label className="text-gray">
                      {chemicalInfo.chemGradeHandQty !== null
                        ? `${chemicalInfo.chemGradeHandQty}Kg`
                        : t('text.chemical.no_ctrL_limit')}
                    </label>
                  </div>}
                </div>
                {/* <hr /> */}
                <div className="row align-items-start">
                  {!isArrayEmpty(selectedGhsImg) &&
                    <div className="col-xl-4 px-5 mb-3">
                      <label className="mb-2 h5">{t('text.chemical.ghs_img')}</label> <br />
                      {selectedGhsImg.map(filteredItem => {
                        return (
                          <React.Fragment key={filteredItem.configId}>
                            <GhsImage src={filteredItem.configValue} alt={filteredItem.configName} title={filteredItem.configName} />
                          </React.Fragment>
                        )
                      })}
                    </div>
                  }
                  {hasSelectedPubHazClass &&
                    <div className="col-xl-4 px-5 mb-3">
                      <label className="mb-2 h5">{t('text.chemical.public_hazard_classify')}</label> <br />
                      {selectedPubHazClass.map(filteredItem => (
                        <React.Fragment key={filteredItem.configId}>
                          <label className="mb-2">{filteredItem.configName}</label>
                          <br />
                        </React.Fragment>
                      ))}
                    </div>
                  }
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.chemical.formula')}</label><br />
                    <label className="text-gray">
                      {chemicalInfo.chemFormula || t('message.no_information')}
                    </label>
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.chemical.formula_structural')}</label><br />
                    <label className="text-gray">
                      {chemicalInfo.chemStructFormula || t('message.no_information')}
                    </label>
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.chemical.boil_point')}</label>{hasLiquid && <span className="text-danger ms-1">*</span>} <br />
                    <label className="text-gray">
                      {chemicalInfo.chemBoilPoint !== null ?
                        `${chemicalInfo.chemBoilPoint}°C${chemicalInfo.chemBoilPointMax !== null ? ` ~ ${chemicalInfo.chemBoilPointMax}°C` : ''}` :
                        t('message.no_information')}
                    </label>
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.chemical.melt_point')}</label> <br />
                    <label className="text-gray">
                      {chemicalInfo.chemMeltPoint !== null ?
                        `${chemicalInfo.chemMeltPoint}°C${chemicalInfo.chemMeltPointMax !== null ? ` ~ ${chemicalInfo.chemMeltPointMax}°C` : ''}` :
                        t('message.no_information')}
                    </label>
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.chemical.allow_concen')}</label> <br />
                    <label className="text-gray">
                      {chemicalInfo.chemPermConcen ? `${chemicalInfo.chemPermConcen} ppm` : t('message.no_information')}
                    </label>
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.chemical.allow_concen')} 2</label> <br />
                    <label className="text-gray">
                      {chemicalInfo.chemPermConcenSecond ? `${chemicalInfo.chemPermConcenSecond} mg/m³` : t('message.no_information')}
                    </label>
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.note')}</label><br />
                    <label className="text-gray">
                      {chemicalInfo.chemNote || t('message.no_information')}
                    </label>
                  </div>
                </div>
                <hr />
                <div className="px-3 ms-3">
                  <label className="h4">{t('button.chemical.ghs_classification')}</label>
                </div>
                <div className="row justify-content-start align-items-start mt-4" id="other-info">
                  {mainGhsClass.some(ghs =>
                    subGhsClassObject[ghs.configId]?.some(subGhs =>
                      isCheckedOptionById(selectedOptions.ghsClassify, subGhs.configId)
                    )
                  ) ? (
                    mainGhsClass.map((ghs, index) => {
                      const hasSelectedSubItems = subGhsClassObject[ghs.configId]?.some(subGhs =>
                        isCheckedOptionById(selectedOptions.ghsClassify, subGhs.configId)
                      );

                      return hasSelectedSubItems && (
                        <React.Fragment key={ghs.configId}>
                          <div className="col-xl-4 px-5 mb-3" >
                            <label className="h5">{ghs.configName}</label>
                            <br />
                            {subGhsClassObject[ghs.configId]
                              .filter(subGhs => isCheckedOptionById(selectedOptions.ghsClassify, subGhs.configId))
                              .map(subGhs => (
                                <div className="" key={'label' + subGhs.configId}>
                                  <label className="form-check-label text-gray">{subGhs.configName}</label>
                                </div>
                              ))}
                          </div>
                          {(index + 1) % 3 === 0 && index !== mainGhsClass.length - 1 && <hr />}
                        </React.Fragment>
                      );
                    })
                  ) : (
                    <div className="col-12 px-5 mb-3">
                      <label className="text-gray">{t('message.no_information')}</label>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </BlockUi>
    </StlyedChemicalInfoDetail >
  );
}

const StlyedChemicalInfoDetail = styled.div`
  padding-bottom:150px;
  
  .react-datepicker-wrapper {
    width: 100%;
  }

  .multi-inputs{
    user-select: none;
  }
  .fa-trash {
      cursor: pointer; /* 将鼠标光标形状改为手指指示器 */
  }
  .badge, .form-check-label {
    user-select: none;
  }

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    font-size: 14px;
  }
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }
  table {
    position:relative;
    th {
      text-align: center;
      white-space:nowrap;
    }
    tr:nth-of-type(2n+1){
      background:#e9ecef;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
  .card-body{
    overflow-x:auto;
  }
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 100px;
        text-align:left;
        min-height:39.5px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          white-space: nowrap;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
`;

export default ChemicalInfoDetail;
