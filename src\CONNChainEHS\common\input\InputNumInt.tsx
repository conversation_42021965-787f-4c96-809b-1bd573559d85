import React, { useState, ChangeEvent, FocusEvent, useEffect } from 'react';

interface IntegerInputProps {
    className?: string;
    value?: number;
    placeholder?: string;
    disabled?: boolean;
    maxLength?: number;
    onBlur?: (value: number) => void;
    onChange?: (event: ChangeEvent<HTMLInputElement>) => void; // 新增 onChange 属性
    minValue?: number | undefined;
    maxValue?: number | undefined;
    allowNegative?: boolean;
}

const InputNumInt: React.FC<IntegerInputProps> = ({
    className,
    value,
    placeholder,
    disabled,
    maxLength,
    onBlur,
    onChange,
    minValue,
    maxValue,
    allowNegative,
}) => {
    const initialNum = (() => {
        let initialValue = value || 0;
        if (minValue !== undefined && initialValue < minValue) {
            initialValue = minValue;
        }
        if (maxValue !== undefined && initialValue > maxValue) {
            initialValue = maxValue;
        }
        return initialValue;
    })();

    const [num, setNum] = useState(initialNum);
    const [displayValue, setDisplayValue] = useState(value ? initialNum.toString() : "");

    useEffect(() => {
        setNum(initialNum);
        setDisplayValue(initialNum.toString());
    }, [initialNum])
    
    const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
        let inputValue = event.target.value;
        let processedValue = inputValue;

        // 檢查開頭是否為0，如果是且後面還是0，則不更新顯示值
        if (inputValue.startsWith("0") && inputValue.length > 1 && inputValue[1] === "0") {
            processedValue = inputValue.slice(0, 1);
        }

        if (allowNegative || /^\d+$/.test(inputValue) || /^\-$/.test(inputValue)) {
            // 如果允許負數或者輸入的是正數或負號，則更新顯示值
            setDisplayValue(processedValue);
        }

        if (onChange) {
            onChange(event);
        }
    };


    const handleBlur = (event: FocusEvent<HTMLInputElement>) => {
        const inputValue = displayValue.trim();
        let parsedValue = parseInt(inputValue, 10) || 0;

        // 如果負數不允許，則強制設為正數
        if (!allowNegative && parsedValue < 0) {
            parsedValue = Math.abs(parsedValue);
        }

        // 應用最小值和最大值約束
        if (!isNaN(parsedValue)) {
            if (minValue !== undefined && parsedValue < minValue) {
                parsedValue = minValue;
            } else if (maxValue !== undefined && parsedValue > maxValue) {
                parsedValue = maxValue;
            }
        }

        setNum(parsedValue);
        setDisplayValue(parsedValue.toString());

        if (onBlur) {
            onBlur(parsedValue);
        }
    };

    return (
        <input
            type="text"
            placeholder={placeholder}
            disabled={disabled}
            maxLength={maxLength}
            value={displayValue}
            onChange={handleInputChange}
            onBlur={handleBlur}
            className={className}
        />
    );
};

export default InputNumInt;
