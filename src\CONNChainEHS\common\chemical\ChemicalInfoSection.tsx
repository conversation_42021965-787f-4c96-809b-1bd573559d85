import React from 'react';
import { useTranslation } from 'react-i18next';
import { isArrayEmpty } from 'ehs/utils/arrayUtil';
import SubstanceInfo from 'ehs/common/chemical/SubstanceInfo';
import ChemicalClassificationBadge from 'ehs/common/chemical/ChemicalClassificationBadge';
import GhsImage from 'ehs/common/GhsImage';
import { EhsConfigParam, initEhsConfigParam } from 'ehs/models/EhsConfigParam';

interface ChemicalInfoSectionProps {
    substList: any[];
    configMap: { [key: string]: EhsConfigParam };
    chemClassify: EhsConfigParam[];
    toxicClassify: any[];
    phaseState: string;
    ghsImages: EhsConfigParam[];
    publicHazardClassifys: EhsConfigParam[];
    totalQty?: number;
    totalWeight?: number;
    setShowCCBView?: (show: boolean) => void;
}

export const ChemicalInfoSection: React.FC<ChemicalInfoSectionProps> = ({
    substList,
    configMap,
    chemClassify,
    toxicClassify,
    phaseState,
    ghsImages,
    publicHazardClassifys,
    totalQty,
    totalWeight,
    setShowCCBView = () => { }
}) => {
    const { t } = useTranslation();

    return (
        <div className="row justify-content-around mt-3">
            <h4>{t('text.chemical.information')}</h4>
            <div className="col-xl-4 align-self-center">
                <div className="col-12">
                    <h4 className="">
                        <SubstanceInfo
                            substList={substList}
                            configMap={configMap}
                        />
                    </h4>
                </div>
                <div className="col-6 offset-3 my-3 d-none">
                    <div className="border p-2">
                        <i className="fas fa-flask text-primary fa-lg"></i>
                        <div className="fs-5">{t('text.inventory_sum')}</div>
                        <h3 className="mt-1">{t('text.chemical.inventory_total_title', { qty: totalQty, weight: totalWeight })}</h3>
                    </div>
                </div>
            </div>
            <div className="col-xl-8 fs-4">
                <ul>
                    {!isArrayEmpty(chemClassify) && (
                        <li>
                            {t("text.chemical.class")}：
                            {chemClassify.map((item, idx) => (
                                <React.Fragment key={'category_' + item?.configId + '_' + idx}>
                                    <span className="d-inline-block mb-1">
                                        <ChemicalClassificationBadge item={item} />
                                    </span>
                                </React.Fragment>
                            ))}
                        </li>
                    )}
                    {!isArrayEmpty(toxicClassify) && (
                        <li>
                            {t("text.chemical.toxic_class")}：
                            {toxicClassify.map((item) => (
                                <span className="d-inline-block mb-1 me-3" key={'toxic_' + item?.configId}>
                                    {item.configName}
                                </span>
                            ))}
                        </li>
                    )}
                    <li>{t('text.chemical.phase_state')}：{configMap[phaseState]?.configName}</li>
                    {!isArrayEmpty(ghsImages) && (
                        <li>
                            {t('text.chemical.ghs_img')}：
                            {ghsImages.map((item) => {
                                const { configId, configValue, configName } = item || initEhsConfigParam;
                                return (
                                    configValue && (
                                        <React.Fragment key={'oper-detail-ghs-img-' + configId}>
                                            <GhsImage src={configValue} alt={configName} title={configName} />
                                        </React.Fragment>
                                    )
                                );
                            })}
                        </li>
                    )}
                    {!isArrayEmpty(publicHazardClassifys) && (
                        <li>
                            {t('text.chemical.public_hazard_classify')}：
                            {publicHazardClassifys.map((item) => {
                                const { configId, configValue, configName } = item || initEhsConfigParam;
                                return (
                                    configValue && (
                                        <React.Fragment key={'oper-detail-ghs-img-' + configId}>
                                            <label className="mb-2">{configName}</label>
                                            <br />
                                        </React.Fragment>
                                    )
                                );
                            })}
                        </li>
                    )}
                    <li>{t('text.chemical.ccb.item')}：
                        <button className="btn btn-primary" title={t('text.chemical.ccb_view')}
                            onClick={() => {
                                setShowCCBView(true);
                            }}>
                            <i className="fa fa-eye me-1"></i>{t('text.chemical.ccb_view')}
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    );
}; 