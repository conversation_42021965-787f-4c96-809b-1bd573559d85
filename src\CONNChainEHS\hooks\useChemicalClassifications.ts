import { CONFIG_TYPE_GHS_IMG, CONFIG_TYPE_PUBLIC_HAZARD, CONFIG_TYPE_TOXIC } from 'ehs/constant/constants';
import { EhsConfigParam } from 'ehs/models/EhsConfigParam';
import { EhsChemicalCategory } from 'ehs/models/EhsChemicalCategory';
import { EhsPurchaseDetailCategory } from 'ehs/models/EhsPurchaseDetailCategory';
import { EhsPurchaseSubst } from 'ehs/models/EhsPurchaseSubst';

// 定義常量配置類型
const CATEGORY_CONFIG = {
    TOXIC: CONFIG_TYPE_TOXIC,
    GHS_IMG: CONFIG_TYPE_GHS_IMG,
    PUBLIC_HAZARD: CONFIG_TYPE_PUBLIC_HAZARD
} as const;

interface UseChemicalClassificationsProps {
    categoryList?: EhsChemicalCategory[];
    detailCategoryList?: EhsPurchaseDetailCategory[];
    configMap?: Record<string, EhsConfigParam>;
    substList?: EhsPurchaseSubst[];
}

interface ClassificationOptions {
    includeAll?: boolean;
    includeToxic?: boolean;
    includeGhs?: boolean;
    includePublicHazard?: boolean;
    includeChemClass?: boolean;
}

export const useChemicalClassifications = (
    {
        categoryList = [],
        detailCategoryList = [],
        configMap = {},
        substList = []
    }: UseChemicalClassificationsProps,
    options: ClassificationOptions = {}
) => {
    // 處理 includeAll 的邏輯
    const normalizedOptions: ClassificationOptions = {
        includeToxic: options.includeAll ?? options.includeToxic ?? false,
        includeGhs: options.includeAll ?? options.includeGhs ?? false,
        includePublicHazard: options.includeAll ?? options.includePublicHazard ?? false,
        includeChemClass: options.includeAll ?? options.includeChemClass ?? false
    };

    // 使用 normalizedOptions 來處理後續邏輯
    const getClassifications = () => {
        const result: Record<string, any> = {};

        if (normalizedOptions.includeToxic) {
            const toxicClassify = convertConfigListToMap(
                categoryList,
                new Map([[CATEGORY_CONFIG.TOXIC, []]])
            ).get(CATEGORY_CONFIG.TOXIC) ?? [];
            result.toxicClassify = toxicClassify;
        }

        if (normalizedOptions.includeGhs) {
            const ghsImageIdList = convertConfigListToMap(
                detailCategoryList,
                new Map([[CATEGORY_CONFIG.GHS_IMG, []]])
            ).get(CATEGORY_CONFIG.GHS_IMG) ?? [];
            result.ghsImages = ghsImageIdList.map(item => configMap[item.configId]).filter(Boolean);
        }

        if (normalizedOptions.includePublicHazard) {
            const publicHazardIdList = convertConfigListToMap(
                detailCategoryList,
                new Map([[CATEGORY_CONFIG.PUBLIC_HAZARD, []]])
            ).get(CATEGORY_CONFIG.PUBLIC_HAZARD) ?? [];
            result.publicHazardClassifys = publicHazardIdList.map(item => configMap[item.configId]).filter(Boolean);
        }

        if (normalizedOptions.includeChemClass) {
            const chemClassIdList = Array.from(
                new Set(substList.flatMap(subst =>
                    subst.categoryList.map(category => category.configId)
                ))
            );
            result.chemClassify = chemClassIdList.map(id => configMap[id]).filter(Boolean);
        }

        return result;
    };

    return getClassifications();
};

// 優化泛型函數，增加錯誤處理
const convertConfigListToMap = <T extends { configType: string }>(
    list: T[],
    map: Map<string, T[]>
): Map<string, T[]> => {
    if (!Array.isArray(list)) return map;

    return list.reduce((acc, item) => {
        if (!acc.has(item.configType)) {
            acc.set(item.configType, []);
        }
        acc.get(item.configType)?.push(item);
        return acc;
    }, new Map(map));
};