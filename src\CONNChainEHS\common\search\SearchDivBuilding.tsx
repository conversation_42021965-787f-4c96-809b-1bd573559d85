import { SelectSearch } from "ehs/models/SearchLabInfo";
import { filterSelectLabByCondition, isArrayEmpty } from "ehs/utils/arrayUtil";
import { useTranslation } from "react-i18next"; 

interface SearchBuildingDivProps {
    dataList: Array<SelectSearch>
    show: boolean;
    showAll?: boolean;
    showBuilding?: boolean;
    showFloor?: boolean;
    showHouseNum?: boolean;
    isLinkSelect?: boolean;
    condition?: any;
    setCondition?: React.Dispatch<React.SetStateAction<any>>;
}
const SearchDivBuilding = ({ dataList = [], show, showAll, showBuilding, showFloor, showHouseNum, isLinkSelect, condition, setCondition }: SearchBuildingDivProps) => {
    const { t } = useTranslation();
    const { buildId: selectedBuildingValues, floor: selectedFloorValues, houseNum: selectedHousenumValues } = condition

    /* -------------------paragraph change method ------------------- */
    const handleSelectBuildingChange = (event: any) => {
        const newValue = event.target.value;
        if (setCondition) {
            setCondition((prevCondition: any) => ({
                ...prevCondition,
                buildId: newValue,
                floor: "",
                houseNum: "",
            }));
        }
    }

    const handleSelectFloorChange = (event: any) => {
        const newValue = event.target.value;
        if (setCondition) {
            setCondition((prevCondition: any) => ({
                ...prevCondition,
                floor: newValue,
                houseNum: ""
            }));
        }
    }

    const handleSelectHoustnumChange = (event: any) => {
        const newValue = event.target.value;
        if (setCondition) {
            setCondition((prevCondition: any) => ({
                ...prevCondition,
                houseNum: newValue
            }));
        }
    };

    /* -------------------paragraph building ------------------- */
    // 創建一個新的 Map 對象，用於存儲唯一的 buildId 和對應的 buildName
    const uniqueBuildingMap = new Map();
    const buildingList = isLinkSelect ? filterSelectLabByCondition(dataList, isLinkSelect, condition) : dataList;
    buildingList?.forEach(item => {
        if (item.buildId) {
            uniqueBuildingMap.set(item.buildId, item);
        }
    });

    const sortedBuildingEntries = Array.from(uniqueBuildingMap.entries()).sort((a, b) => {
        return a[1].buildName.localeCompare(b[1].buildName); // 根據 buildName 進行排序
    });

    // 使用 Map 的 entries() 方法，將鍵值對轉換為數組，並進行 map() 轉換為 <option> 元素
    const buildingOption = sortedBuildingEntries.map(([buildId, build]) => {
        return (<option key={buildId} value={buildId} title={build.buildName}>
            {build.buildName}
        </option>);
    });

    /* -------------------paragraph floor ------------------- */
    const uniqueFloorMap = new Map();
    buildingList?.forEach(item => {
        if (item.buildId && item.labFloor) {
            uniqueFloorMap.set(item.buildId + '-' + item.labFloor, item);
        }
    });

    const sortedFloorEntries = Array.from(uniqueFloorMap.entries()).sort((a, b) => {
        const floorA = a[1]?.labFloor || ''; // 如果 labFloor 不存在，則設為空字符串
        const floorB = b[1]?.labFloor || ''; // 如果 labFloor 不存在，則設為空字符串
        return floorA.localeCompare(floorB); // 根據樓層進行排序
    });

    const floorOption = sortedFloorEntries.map(([key, item]) => {
        if (!selectedBuildingValues || selectedBuildingValues !== item.buildId) {
            return null;
        }
        return (
            <option key={key} value={item.labFloor} title={item.labFloor}>
                {item.labFloor}
            </option>
        );
    });
    const hasFloorOption = !isArrayEmpty(floorOption.filter(option => option !== null));

    /* -------------------paragraph house num ------------------- */
    const uniqueHousenumMap = new Map();
    buildingList?.forEach(item => {
        if (item.labHouseNum) {
            uniqueHousenumMap.set(item.labHouseNum, item);
        }
    });

    const sortedHousenumEntries = Array.from(uniqueHousenumMap.entries()).sort((a, b) => {
        const houseNumA = a[1]?.labHouseNum || ''; // 如果 labHouseNum 不存在，則設為空字符串
        const houseNumB = b[1]?.labHouseNum || ''; // 如果 labHouseNum 不存在，則設為空字符串
        return houseNumA.localeCompare(houseNumB); // 根據房號進行排序
    });

    const housenumOption = sortedHousenumEntries.map(([housenum, item]) => {
        if (!selectedFloorValues || selectedFloorValues !== item.labFloor || selectedBuildingValues !== item.buildId) {
            return null;
        }
        return (
            <option key={housenum} value={housenum} title={housenum}>
                {housenum}
            </option>
        );
    });

    const hasHousnumOption = !isArrayEmpty(housenumOption.filter(option => option !== null));

    /* -------------------paragraph content ------------------- */
    return (
        show && <div className="row my-2">
            {(showAll || showBuilding) && <div className="col-xl-3 d-flex align-items-center">
                <label className="w-25 me-1">{t('text.building.item')}</label>
                <select className="form-select w-75" value={selectedBuildingValues} onChange={(e) => handleSelectBuildingChange(e)}>
                    <option key="all" value="">{t('text.all')}</option>
                    {buildingOption}
                </select>
            </div>}
            {(showAll || showFloor) && selectedBuildingValues && hasFloorOption && <div className="col-xl-3 d-flex align-items-center">
                <label className="w-25 me-1">{t('text.building.floor.item')}</label>
                <select className="form-select w-75" value={selectedFloorValues} onChange={(e) => handleSelectFloorChange(e)}>
                    <option key="all" value="">{t('text.all')}</option>
                    {floorOption}
                </select>
            </div>}
            {(showAll || showHouseNum) && selectedFloorValues && hasHousnumOption && <div className="col-xl-3 d-flex align-items-center">
                <label className="w-25 me-1">{t('text.lab.house_number')}</label>
                <select className="form-select w-75" value={selectedHousenumValues} onChange={(e) => handleSelectHoustnumChange(e)}>
                    <option key="all" value="">{t('text.all')}</option>
                    {housenumOption}
                </select>
            </div>}
        </div>
    );
};

export default SearchDivBuilding;
