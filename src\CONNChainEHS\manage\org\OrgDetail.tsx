
import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { EhsOrg } from "../../models/EhsOrg";
export default function OrgDetail(props: {
    onClose: () => void;
    modifyData: EhsOrg;
}) {
    const { onClose, modifyData } = props;
    const { orgNo, orgName, orgFloor, orgHousenum, orgPhone, orgPhoneExt, orgUrl, orgEmail, orgTypeName, areaName, buildName } = modifyData;
    const { t } = useTranslation();

    return (
        <StyledOrgDetail>
            <div className="orgDetail">
                <div className="orgDetail-header">
                    <h4 className="modal-title">{t('text.detail')}</h4>
                    <button
                        type="button"
                        className="btn-close"
                        aria-hidden="true"
                        onClick={onClose}
                    ></button>
                </div>
                <div className="orgDetail-body">
                    <div className="row mb-4 justify-content-center">
                        <div className="col-md-10 mb-3">
                            <label className="fw-bold col-4">
                                {t('text.no')}：
                            </label>
                            <label className="fw-bold col-8">
                                {orgNo}
                            </label>
                        </div>
                        <div className="col-md-10 mb-3">
                            <label className="fw-bold col-4">
                                {t('text.name')}：
                            </label>
                            <label className="fw-bold col-8">
                                {orgName}
                            </label>
                        </div>
                        <div className="col-md-10 mb-3">
                            <label className="fw-bold col-4">
                                {t('text.org.classification')}：
                            </label>
                            <label className="fw-bold col-8">
                                {orgTypeName}
                            </label>
                        </div>
                        <div className="col-md-10 mb-3">
                            <label className="fw-bold col-4">
                                {t('text.area.item')}：
                            </label>
                            <label className="fw-bold col-8">
                                {areaName}
                            </label>
                        </div>
                        <div className="col-md-10 mb-3">
                            <label className="fw-bold col-4">
                                {t('text.building.item')}：
                            </label>
                            <label className="fw-bold col-8">
                                {buildName}
                            </label>
                        </div>
                        <div className="col-md-10 mb-3">
                            <label className="fw-bold col-4">
                                {t('text.building.floor.item')}：
                            </label>
                            <label className="fw-bold col-8">
                                {orgFloor}
                            </label>
                        </div>
                        <div className="col-md-10 mb-3">
                            <label className="fw-bold col-4">
                                {t('text.org.house_number')}：
                            </label>
                            <label className="fw-bold col-8">
                                {orgHousenum}
                            </label>
                        </div>
                        <div className="col-md-10 mb-3">
                            <label className="fw-bold col-4">
                                {t('text.phone_ext')}：
                            </label>
                            <label className="fw-bold col-8">
                                {orgPhoneExt}
                            </label>
                        </div>
                        <div className="col-md-10 mb-3">
                            <label className="fw-bold col-4">
                                {t('text.dedicated_line')}：
                            </label>
                            <label className="fw-bold col-8">
                                {orgPhone}
                            </label>
                        </div>
                        <div className="col-md-10 mb-3">
                            <label className="fw-bold col-4">
                                {t('text.url')}：
                            </label>
                            <label className="fw-bold col-8">
                                {orgUrl}
                            </label>
                        </div>
                        <div className="col-md-10 mb-3">
                            <label className="fw-bold col-4">
                                {t('text.email')}：
                            </label>
                            <label className="fw-bold col-8">
                                {orgEmail}
                            </label>
                        </div>
                    </div>
                </div>
                <div className="orgDetail-footer">
                    <div className="btn btn-white" aria-hidden="true" onClick={onClose}>
                        <i className="fas fa-times me-1" />
                        {t("button.close")}
                    </div>
                </div>
            </div>
        </StyledOrgDetail>
    );
}

const StyledOrgDetail = styled.div`
  background: white;

  width: 100%;
  max-width: 600px;

  .btn-close{
    margin-right: 0;
  }
  
  .orgDetail {
    width: 100%;
    overflow-x: hidden;  // 防止水平滾動
  }

  .orgDetail-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
    background:rgb(251, 205, 165);
  }
  .orgDetail-body {
    padding: 15px;
    font-size: 1.1em;
    .row {
      margin: 0;
    }
  }
  .orgDetail-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-purple {
      margin-left: 10px;
    }
  }

  @media (max-width: 600px) {
    width: 100%;
    margin: 0;
    
    .orgDetail-body {
      padding: 15px 10px;  // 減少左右padding
    }
  }
`;
