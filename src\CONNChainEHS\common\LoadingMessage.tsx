import React from 'react';
import styled from 'styled-components';

interface LoadingMessageProps {
    message?: string;
}

const LoadingMessage: React.FC<LoadingMessageProps> = ({ message }) => {
    return (
        <StyledLoadingOverlay>
            <StyledLoadingCard>
                {/* 載入動畫 */}
                <StyledSpinner />
                
                {/* 載入訊息 */}
                <StyledPrimaryText>
                    {message || '載入中...'}
                </StyledPrimaryText>
                
                {/* 次級文字 */}
                <StyledSecondaryText>
                    請稍候片刻
                </StyledSecondaryText>
            </StyledLoadingCard>
        </StyledLoadingOverlay>
    );
};

const StyledLoadingOverlay = styled.div`
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(8px);
    z-index: 1000;
`;

const StyledLoadingCard = styled.div`
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1), 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 172, 172, 0.1);
    min-width: 300px;
    max-width: 400px;
`;

const StyledSpinner = styled.div`
    width: 48px;
    height: 48px;
    border: 4px solid var(--bs-gray-200, #f2f3f4);
    border-top: 4px solid var(--bs-teal, #00acac);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1.5rem;

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;

const StyledPrimaryText = styled.div`
    font-size: 18px;
    font-weight: 600;
    color: var(--bs-gray-800, #2d353c);
    text-align: center;
    line-height: 1.5;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
`;

const StyledSecondaryText = styled.div`
    font-size: 14px;
    color: var(--bs-gray-600, #6c757d);
    text-align: center;
    margin-top: 0.5rem;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
`;

export default LoadingMessage;