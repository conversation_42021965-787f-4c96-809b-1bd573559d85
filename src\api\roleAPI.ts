import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { EhsLanguage } from "../CONNChainEHS/models/EhsLanguage";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const RoleAPI = {
  getRoleList: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "role/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addRole: async (
    parms: BaseParams & {
      roleNameList: EhsLanguage[];
      roleDescription: string;
      roleLevel: number;
    }
  ) => {
    return apiRequest(getApiAddress + "role/add", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  editRole: async (
    parms: BaseParams & {
      roleNameList: EhsLanguage[];
      roleDescription: string;
      roleLevel: number;
      roleId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "role/edit", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  deleteRole: async (
    parms: BaseParams & {
      roleId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "role/del", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getRoleDetail: async (
    parms: BaseParams & {
      roleId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "role/detail", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getLabRoleList: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "role/labrole", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};
