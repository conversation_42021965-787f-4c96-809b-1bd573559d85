import { Suspense, type ReactNode } from 'react'
import { <PERSON><PERSON><PERSON>Rout<PERSON> } from 'react-router-dom'
import { QueryClient, QueryClientProvider, QueryCache } from '@tanstack/react-query'
import { SidebarProvider } from '../contexts/SidebarContext'

const queryClient = new QueryClient({
  queryCache: new QueryCache({
    onError: (error) => console.error('[RQ] Query error:', error),
  }),
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5,
      gcTime: 1000 * 60 * 30,
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
})

export function AppProviders({ children }: { children: ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <SidebarProvider>
          <Suspense fallback={null}>{children}</Suspense>
        </SidebarProvider>
      </BrowserRouter>
    </QueryClientProvider>
  )
}


