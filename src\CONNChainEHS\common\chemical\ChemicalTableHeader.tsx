import React from 'react';
import { ColumnConfig } from './ChemicalColumnConfig';
import SortIcon from 'ehs/common/SortIcon';

interface ChemicalTableHeaderProps {
    columns: ColumnConfig[];
    dataList: any[];
    setFunction: (data: any) => void;
    sortFields?: Record<string, string>; // 映射欄位key到資料排序欄位
}

const ChemicalTableHeader: React.FC<ChemicalTableHeaderProps> = ({
    columns,
    dataList,
    setFunction,
    sortFields = {}
}) => {
    return (
        <thead className="text-center fs-4 fw-bold bg-lime-200">
            <tr>
                {columns.map((column, index) => (
                    <th
                        key={`header-${column.key}-${index}`}
                        className={`${column.align ? `text-${column.align}` : ''} ${column.width || ''}`}
                    >
                        {column.label}
                        {sortFields[column.key] && (
                            <SortIcon
                                dataList={dataList}
                                dataField={sortFields[column.key]}
                                setFunction={setFunction}
                            />
                        )}
                    </th>
                ))}
            </tr>
        </thead>
    );
};

export default ChemicalTableHeader; 