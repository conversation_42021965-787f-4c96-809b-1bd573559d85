import BlockUi from "@availity/block-ui";
import { AuthorAPI } from "api/authorAPI";
import { RouAPI } from "api/rouAPI";
import { AppPaths } from "config/app-paths";
import { AgentTag } from "ehs/common/AgentTag";
import useLoginUser from "ehs/hooks/useLoginUser";
import { EhsRou } from "ehs/models/EhsRou";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { getBasicLoginUserInfo, getLoginOriginUserId, getLoginRoleName, getLoginUserId, getLoginUserRoleId } from "ehs/utils/authUtil";
import { getBtnByUserInfo, getMenuByUserInfo } from "ehs/utils/funcUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { setUserPreferences } from "ehs/utils/storageUtil";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import BlockuiMsg from "../../CONNChainEHS/common/BlockuiMsg";
import { confirmMsg, errorMsg } from "../../CONNChainEHS/common/SwalMsg";
import { showSuccessToast } from "../../CONNChainEHS/common/Toast";

// 定義角色類型
// interface Role {
//   id: string;
//   name: string;
//   isDefault?: boolean;  // 新增預設角色標記
// }

interface ChangePermissionDialogProps {
  onClose: () => void;
  show: boolean;  // 新增 show prop
}

export default function ChangePermissionDialog({
  onClose = () => { },
  show = false,
}: ChangePermissionDialogProps) {
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const userInfo = loginUser?.userInfo;
  const originUserId = getLoginOriginUserId(loginUser);
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [loadingBlock, setLoadingBlock] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const defaultLoginRoleFlag = 1;
  // const [roles, setRoles] = useState<Role[]>([
  //   {
  //     id: 'ADMIN-001',
  //     name: '系統管理員',
  //     isDefault: true
  //   },
  //   {
  //     id: 'DEPT-001',
  //     name: '部門主管',
  //   },
  //   {
  //     id: 'USER-001',
  //     name: '一般使用者',
  //   },
  //   {
  //     id: 'AUDIT-001',
  //     name: '稽核人員',
  //   }
  // ]);
  const [roles, setRoles] = useState<EhsRou[]>([]);

  useEffect(() => {
    if (show && loginUser) {
      fetchSwitchableRoles();
    }
  }, [show, loginUser]); // 當 show 變成 true 時觸發

  const fetchSwitchableRoles = async () => {
    if (!originUserId || !userInfo?.activeRouId) return;
    setIsLoading(true);
    try {
      const res = await RouAPI.getSwitchableRoles({
        ...getBasicLoginUserInfo(loginUser),
        userId: originUserId,
        rouId: userInfo?.activeRouId,
        includeAgent: true,
      });
      if (isApiCallSuccess(res)) {
        const currentRoleId = getLoginUserRoleId(loginUser);
        // 過濾掉非代理且與當前角色相同的選項
        const filteredRoles = res.results.filter((role: EhsRou) =>
          role.agentId || role.roleId !== currentRoleId
        );
        setRoles(filteredRoles);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleChangePermission = async () => {
    try {
      const confirmed = await confirmMsg(t('message.confirm.change_role'), t);
      if (!confirmed || !originUserId || !selectedRole) return;

      setLoadingBlock(true);

      const res = await AuthorAPI.changePermission({
        userId: originUserId,
        rouId: selectedRole,
        langType: i18n.language,
      });


      if (!isApiCallSuccess(res)) {
        errorMsg(res.message);
        return;
      }

      showSuccessToast(t('message.success'));

      // 更新使用者權限相關資訊
      const newUserInfo = res.results;
      const menuList = newUserInfo?.menuList || [];
      const { userMenuMap } = getMenuByUserInfo(newUserInfo, menuList, t);
      const btnList = getBtnByUserInfo(newUserInfo);

      // 更新使用者偏好設定
      setUserPreferences({
        userMenuList: menuList,
        userMenuMap: Array.from(userMenuMap.keys()),
        userBtnMap: btnList,
      });

      // 重定向到首頁
      window.location.href = `/${AppPaths.home}`;
    } catch (error) {
      console.error('Change permission failed:', error);
      errorMsg(t('message.error'));
    } finally {
      setLoadingBlock(false);
    }
  };

  const handleSetDefaultRole = async (rouId: string) => {
    try {
      const confirmed = await confirmMsg(t('message.confirm.set_default_role'), t);
      if (!confirmed) return;

      setLoadingBlock(true);

      // 調用設置預設角色 API
      const res = await RouAPI.updateDefaultLoginPermission({
        ...getBasicLoginUserInfo(loginUser),
        userId: originUserId,
        rouId: rouId
      });

      if (!isApiCallSuccess(res)) {
        errorMsg(res.message);
        return;
      }

      // 更新角色狀態
      setRoles(prevRoles => prevRoles.map(role => ({
        ...role,
        rouLoginDefault: role.rouId === rouId ? defaultLoginRoleFlag : 0
      })));

      showSuccessToast(t('message.success'));
    } catch (error) {
      console.error('Set default role failed:', error);
      errorMsg(t('message.error'));
    } finally {
      setLoadingBlock(false);
    }
  };

  // 在 ChangePermissionDialog 組件中新增一個函數來判斷當前選中的角色是否為代理角色
  const getSelectedRole = () => {
    return roles.find(role => role.rouId === selectedRole);
  };

  const isCurrentRoleDefault = () => {
    if (userInfo?.agentId || isArrayEmpty(roles)) return false;
    // 如果在可切換角色中找到相同 rouId 且是預設登入的，或是找不到任何相同 rouId 的角色
    const currentRoleInList = roles.find(role => role.rouLoginDefault === defaultLoginRoleFlag);
    return !currentRoleInList;
  };

  const SetDefaultRoleButton = ({
    rouId,
    isSmall = false
  }: {
    rouId: string,
    isSmall?: boolean
  }) => (
    <button
      className={`btn btn-warning ${isSmall ? 'btn-sm' : ''}`}
      onClick={() => handleSetDefaultRole(rouId)}
      title={t('button.set_default_role')}
    >
      <i className="fas fa-star me-1"></i>
      {t('button.set_default_role')}
    </button>
  );

  const defaultLoginRoleBadge = <span className="default-badge">{t('text.default_login_role')}</span>;

  return (
    <StyledChangePermissionDialog>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="changePermission">
          <div className="changePermission-header">
            <h4 className="modal-title">{t('text.switch_permission')}</h4>
            <button type="button" className="btn-close" onClick={onClose}></button>
          </div>
          <div className="changePermission-body">
            <div className="current-role mb-3">
              <h4>{t('text.current_role')}</h4>
              <div className="current-role-info p-2 bg-light">
                <div className="d-flex align-items-center justify-content-between">
                  <span><AgentTag /> {getLoginRoleName(loginUser)} {getLoginUserId(loginUser)}</span>
                  {!userInfo?.agentId && !isCurrentRoleDefault() && (
                    <SetDefaultRoleButton rouId={userInfo?.activeRouId || ''} isSmall />
                  )}
                  {isCurrentRoleDefault() && (
                    <span className="default-badge">{t('text.default_login_role')}</span>
                  )}
                </div>
              </div>
            </div>
            <div className="available-roles">
              <h4>{t('text.available_switchable_roles')}</h4>
              <div className="role-grid">
                {isLoading ? (
                  <div className="loading-message">
                    {t('message.loading')}
                  </div>
                ) : !isArrayEmpty(roles) ? (
                  roles.map((role) => (
                    <div
                      key={role.rouId}
                      className={`role-card d-flex align-items-center ${selectedRole === role.rouId ? 'selected' : ''}`}
                      onClick={() => setSelectedRole(role.rouId)}
                    >
                      <div className="role-header">
                        <h4>
                          {role.agentId && (
                            <>{t('text.agent')} {role.userId} {role.userName}<br /></>
                          )}
                          {role.roleName}
                        </h4>
                        {role.rouLoginDefault === defaultLoginRoleFlag && (
                          defaultLoginRoleBadge
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="no-roles-message">
                    {t('text.no_switchable_roles')}
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="changePermission-footer">
            <div className="d-flex justify-content-between w-100">
              <div>
                {!userInfo?.agentId && selectedRole && !getSelectedRole()?.agentId &&
                  getSelectedRole()?.rouLoginDefault !== defaultLoginRoleFlag && (
                    <SetDefaultRoleButton rouId={selectedRole} />
                  )}
              </div>
              <div>
                <button className="btn btn-white" onClick={onClose} title={t('button.cancel')}>
                  <i className="fas fa-close me-1"></i>
                  {t('button.cancel')}
                </button>
                <button
                  className="btn btn-warning ms-2"
                  onClick={handleChangePermission}
                  disabled={!selectedRole}
                  title={t('button.change_permission')}
                >
                  <i className="fas fa-check me-1"></i>
                  {t('button.change_permission')}
                </button>
              </div>
            </div>
          </div>
        </div>
      </BlockUi>
    </StyledChangePermissionDialog >
  );
}

const StyledChangePermissionDialog = styled.div`
  background: white;
  width: 800px;
  max-width: 95vw;
  padding: 20px; 

  .current-role {
    background-color: #f8f9fa;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
  }

  .role-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .role-card {
    position: relative;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      border-color: #1890ff;
    }

    &.selected {
      background-color: #e6f0ff;
      border-color: #1890ff;

      &::after {
        content: "✓";
        position: absolute;
        top: 15px;
        right: 15px;
        color: #1890ff;
        font-weight: bold;
        font-size: 16px;
        z-index: 1;
      }
    }

    .role-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding-right: 25px;
      
      h4 {
        margin: 0;
        flex: 1;
      }

      .default-badge {
        flex-shrink: 0;
        margin-left: 10px;
      }
    } 
  }

  .changePermission-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h4 {
      margin: 0;
    }
  }

  .changePermission-footer {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    label {
        cursor: pointer;
        user-select: none;
    }
  }

  .default-badge {
    font-size: 12px;
    padding: 2px 8px;
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
    border-radius: 12px;
    white-space: nowrap;
    display: inline-block;
    user-select: none;
  }

  .no-roles-message {
    grid-column: 1 / -1;
    padding: 20px;
    text-align: center;
    color: #666;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px dashed #dee2e6;
  }

  .loading-message {
    grid-column: 1 / -1;
    padding: 20px;
    text-align: center;
    color: #666;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px dashed #dee2e6;
  }

  @media (max-width: 768px) {
    .role-grid {
      grid-template-columns: 1fr;
    }
  }
`; 