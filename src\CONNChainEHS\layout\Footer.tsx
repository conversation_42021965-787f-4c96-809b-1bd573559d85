import styled from "styled-components";
import ScrollToTop from "react-scroll-to-top";
import { useSidebar } from "contexts/SidebarContext";
import Dialog from "../common/Dialog";
import { useState } from "react";
import { USAGE_POLICY_TEXT } from "ehs/constant/constants";
import CompanyLogo from "../common/CompanyLogo";
import { useTranslation } from "react-i18next";

const ScrollToTopIcon = () => <i className="fa fa-angle-up text-white"></i>;

// 自定義 ScrollToTop 樣式
const StyledScrollToTop = styled(ScrollToTop)`
  &.btn-scroll-to-top {
    position: fixed !important;
    bottom: 90px !important; /* 調整位置，使其位於客服按鈕上方 */
    right: 20px !important;
    z-index: 998 !important; /* 確保 z-index 小於客服按鈕 */
  }
`;

export default function Footer() {
  const { t } = useTranslation();
  const { isMinified } = useSidebar();
  const [showDialog, setShowDialog] = useState(false);
  const usageText = USAGE_POLICY_TEXT;
  return (
    <StyledFooter $isMinified={isMinified}>
      <Dialog
        show={showDialog}
        content={
          <div className="dialog-container">
            <div className="dialog-header">
              <h3 className="dialog-title">使用規範</h3>
              <button
                className="dialog-close-btn"
                onClick={() => setShowDialog(false)}
                aria-label={t("button.close")}
              >
                ×
              </button>
            </div>
            <div className="dialog-content">
              {usageText}
            </div>
          </div>
        }
        onClick={e => {
          if (e.target === e.currentTarget) setShowDialog(false);
        }}
      />
      <div className="row justify-content-center">
        <div className="col-12 col-md-10 fs-5 text-center mb-1">
          <div className="copyright-text">
            本網站系統智慧財產權為雲集科技行銷有限公司所有 未經正式書面授權 禁止重製轉載節錄任何畫面與功能 敬請詳閱並遵守
            <button
              type="button"
              className="usage-rule-btn"
              onClick={() => setShowDialog(true)}
            >
              使用規範
            </button>
          </div>
        </div>
        <div className="col-12 col-xl-4 fs-5 text-center">
          <div>Copyright © 2025 <CompanyLogo /> All Right Reserved</div>
        </div>
      </div>
      <StyledScrollToTop
        smooth
        className="btn btn-icon btn-circle btn-success btn-scroll-to-top show"
        component={<ScrollToTopIcon />}
      />
    </StyledFooter>
  );
}

const StyledFooter = styled.div<{ $isMinified: boolean }>`
  padding: 0.9375rem 1rem;
  margin: 0;
  border-top: 1px solid var(--bs-component-border-color);
  font-weight: 600;
  color: #6c757d;
  width: ${props => props.$isMinified ? 'calc(100% - 60px)' : 'calc(100% - 220px)'};
  position: fixed;
  left: ${props => props.$isMinified ? '60px' : '220px'};
  background: #dee2e6;
  z-index: 22;
  bottom: 0;
  
  @media (max-width: 767px) {
    position: static;
    width: 100%;
    left: 0;
    padding: 0.9375rem 1rem;
  }
  
  .row {
    margin-right: 0;
    margin-left: 0;
    line-height: 1.2;
  }
  
  .copyright-text {
    word-wrap: break-word;
    hyphens: auto;
    line-height: 1.3;
  }
  
  .scroll-to-top {
    bottom: 20px; /* 調整底部距離 */
    right: 15px; /* 調整右側距離 */
  }
  
  .usage-rule-btn {
    color: #007bff;
    text-decoration: underline;
    cursor: pointer;
    margin-left: 4px;
    background: transparent;
    border: none;
    padding: 0;
    font-family: inherit;
    font-size: inherit;
  }
  
  .dialog-container {
    max-width: ${() => window.innerWidth > 1024 ? '900px' : '600px'};
    width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    padding: 0;
    position: relative;
    background: #fff;
    border-radius: 8px;
  }
  
  @media (max-width: 576px) {
    .dialog-container {
      width: 95vw;
    }
  }
  
  .dialog-header {
    position: sticky;
    top: 0;
    z-index: 2;
    background: #fff;
    border-bottom: 1px solid #bbb;
    padding: 16px 24px 8px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .dialog-title {
    margin: 0;
    text-align: center;
    flex: 1;
    font-weight: 700;
    font-size: 20px;
  }
  
  .dialog-close-btn {
    margin-left: 16px;
    border: none;
    background: transparent;
    font-size: 28px;
    cursor: pointer;
    line-height: 1;
  }
  
  .dialog-content {
    white-space: pre-wrap;
    font-size: 16px;
    line-height: 1.7;
    padding: 24px;
  }
  
  @media (max-width: 576px) {
    .fs-5 {
      font-size: 0.9rem!important;
      line-height: 1.2;
    }
    
    .dialog-content {
      font-size: 14px;
      padding: 16px;
    }
  }
`;
