import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { EhsFile } from "../models/EhsFile";

export default function ImageView(props: {
  imageFile: EhsFile;
  onClose: () => void
}) {
  const { imageFile, onClose } = props;
  const { fileName, fileContent } = imageFile;
  const { t } = useTranslation();

  const clickClose = () => {
    onClose();
  }

  return (
    <StyledImageView>
      <div className="imageView">
        <div className="imageView-header">
          <h4 className="modal-title">{t('func.image_view')}</h4>
          <button
            type="button"
            className="btn-close"
            aria-hidden="true"
            onClick={clickClose}
          ></button>
        </div>
        <div className="imageView-body">
          <div className="ms-3 h5 filename-div">{t('text.file_name')}：{fileName}</div>
          <div className="imageview-div">
            <img
              src={`data:image/png;base64,${fileContent}`}
              alt={fileName} title={fileName}
              className="my-1 ms-3"
            />
          </div>
        </div>
        <div className="imageView-footer">
          <div className="btn btn-white" aria-hidden="true" onClick={clickClose}>
            <i className="fas fa-times me-1" />
            {t("button.close")}
          </div>
        </div>
      </div>
    </StyledImageView>
  );
}

const StyledImageView = styled.div`
  background: white;
  width: 1000px;
  img{
    max-width:1000px;
    max-height:700px;
    cursor: default
  }
  .imageview-div, .filename-div{
    text-align:center;
  }
  .imageView-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  .imageView-body {
    padding-top: 15px;
  }
  .imageView-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-purple {
      margin-left: 10px;
    }
  }
  
  @media (max-width: 600px){
    
    width: 600px;
    .imageview-div, .filename-div{
      text-align:left;
    }
    img {
      max-width: 500px;
      max-height: auto;
    }
  }
`;
