import React, { useState } from "react";
import styled from "styled-components";
import Dialog from "CONNChainEHS/common/Dialog";

interface Props {
    open: boolean;
    onAgree: () => void;
    usageText: string;
}

const UsagePolicyDialog: React.FC<Props> = ({ open, onAgree, usageText }) => {
    const [checked, setChecked] = useState(false);

    return (
        <Dialog
            show={open}
            content={
                <DialogContainer className="p-0">
                    {/* Header */}
                    <div className="position-sticky bg-white border-bottom px-4 py-3 d-flex align-items-center justify-content-center"
                        style={{ top: 0, zIndex: 2, borderBottomColor: '#bbb' }}>
                        <h3 className="m-0 text-center flex-fill fw-bold fs-4">使用規範</h3>
                    </div>

                    {/* Content */}
                    <ContentText className="p-4">
                        {usageText}
                    </ContentText>

                    {/* Footer */}
                    <div className="px-4 pb-4 d-flex align-items-center">
                        <div className="form-check me-3">
                            <input
                                type="checkbox"
                                id="agree"
                                checked={checked}
                                onChange={e => setChecked(e.target.checked)}
                                className="form-check-input"
                            />
                            <label htmlFor="agree" className="form-check-label">
                                我已閱讀並同意使用規範
                            </label>
                        </div>
                        <button
                            disabled={!checked}
                            className={`btn ms-auto px-4 ${checked ? 'btn-primary' : 'btn-secondary'}`}
                            style={{ cursor: checked ? 'pointer' : 'not-allowed' }}
                            onClick={onAgree}
                        >
                            關閉
                        </button>
                    </div>
                </DialogContainer>
            }
            onClick={() => { }} // 不允許點 Dialog 外關閉
        />
    );
};

const DialogContainer = styled.div`
    max-width: ${window.innerWidth > 1024 ? '900px' : '600px'};
    width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    background: #fff;
    border-radius: 8px;
`;

const ContentText = styled.div`
    white-space: pre-wrap;
    font-size: 16px;
    line-height: 1.7;
`;

export default UsagePolicyDialog;
