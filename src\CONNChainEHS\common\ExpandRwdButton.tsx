import styled from 'styled-components';

interface ExpandRwdButtonProps {
  isExpanded: boolean;
  onClick: () => void;
}

export default function ExpandRwdButton({ isExpanded, onClick }: ExpandRwdButtonProps) {
  return (
    <StyledExpandButton
      className="btn btn-link expand-toggle d-none mt-1"
      onClick={onClick}
      type="button"
    >
      <i className={`fas fa-chevron-${isExpanded ? 'up' : 'down'}`}></i>
    </StyledExpandButton>
  );
}

const StyledExpandButton = styled.button`
  &.expand-toggle {
    display: none;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    color: #2196f3;
    background: rgba(33, 150, 243, 0.1);
    border-radius: 50%;
    border: none;
    cursor: pointer;
    margin-left: 8px;
    transition: all 0.2s ease;
    padding: 0;
    
    @media (min-width: 601px) and (max-width: 820px) {
      display: inline-flex !important;
    }
    
    &:hover {
      background: rgba(33, 150, 243, 0.2);
      color: #1976d2;
    }

    i {
      font-size: 14px;
    }
  }
`; 