import React from 'react';
import LoadingSpinner from './LoadingSpinner';
import Loader from './Loader';

interface LoadingTableProps {
    loading: boolean;
    isCutomLoading?: boolean;
    colSpan: number;
    children: React.ReactNode;
    spinnerSize?: 'sm' | 'md' | 'lg';
    spinnerColor?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';
}

/**
 * LoadingTable 組件
 * 
 * 在表格中顯示 loading 效果的通用組件
 * 
 * @param loading 是否正在載入
 * @param colSpan 橫跨的列數
 * @param children 子元素（渲染數據的內容）
 * @param spinnerSize spinner 大小，預設 'md'
 * @param spinnerColor spinner 顏色，預設 'primary'
 * @returns 
 */
const LoadingTable: React.FC<LoadingTableProps> = ({
    loading,
    isCutomLoading,
    colSpan,
    children,
    spinnerSize = 'md',
    spinnerColor = 'primary'
}) => {
    if (loading) {
        return (
            <tr>
                <td colSpan={colSpan} className="py-4">
                    {isCutomLoading ? <Loader /> : <LoadingSpinner size={spinnerSize} color={spinnerColor} />}
                </td>
            </tr>
        );
    }

    return <>{children}</>;
};

export default LoadingTable; 