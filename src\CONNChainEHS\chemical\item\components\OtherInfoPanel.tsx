import CheckboxWithInput from 'ehs/common/CheckboxWithInput';
import InputNumFloat from 'ehs/common/input/InputNumFloat';
import ToggleDisplayDiv from 'ehs/common/ToggleDisplayDiv';
import { CHEMICAL_WEIGHT_MIN, CHEMICAL_WEIGHT_MAX, CHEMICAL_WEIGHT_DECIMAL_PLACES } from 'ehs/constant/constants';
import { EhsConfigParam } from 'ehs/models/EhsConfigParam';
import React from 'react';
import { Controller, UseFormRegister, Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface OtherInfoPanelProps {
    register: UseFormRegister<any>;
    control: Control<any>;
    registerObj: any;
    packTypeOptions: EhsConfigParam[];
    packMalOptions: EhsConfigParam[];
    detailCategoryMap: { [key: string]: EhsConfigParam };
    handleCheckedCategoryChange: (configId: string, config: EhsConfigParam, isChecked: boolean) => void;
    handleOtherInfoChange: (type: string, content: string) => void;
    CONFIG_VAL_OTHER: string;
    CONFIG_TYPE_PACKTYPE: string;
    CONFIG_TYPE_PACKMAL: string;
}

const OtherInfoPanel: React.FC<OtherInfoPanelProps> = ({
    register,
    control,
    registerObj,
    packTypeOptions,
    packMalOptions,
    detailCategoryMap,
    handleCheckedCategoryChange,
    handleOtherInfoChange,
    CONFIG_VAL_OTHER,
    CONFIG_TYPE_PACKTYPE,
    CONFIG_TYPE_PACKMAL
}) => {
    const { t } = useTranslation();

    return (
        <div className="row border border-2 border-info py-3">
            <ToggleDisplayDiv
                className="row"
                header={(isVisible) => (
                    <h3>
                        <label className="d-flex align-items-center fw-bold mb-3">
                            <i className={`ms-2 me-1 fas ${isVisible ? "fa-chevron-up" : "fa-chevron-down"}`} />
                            {t('text.other_info')}
                        </label>
                    </h3>
                )}
                content={
                    <>
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('table.title.product.name')}</label>
                            <input type="text" className="form-control"  {...register(registerObj.productName)} />
                        </div>
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('table.title.product.description')}</label>
                            <input type="text" className="form-control"  {...register(registerObj.productDescription)} />
                        </div>
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('table.title.product.brand')}</label>
                            <input type="text" className="form-control"  {...register(registerObj.brand)} />
                        </div>
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('table.title.packing')}</label>
                            <input type="text" className="form-control"  {...register(registerObj.packing)} />
                        </div>
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('table.title.capacity')}</label>
                            <input type="text" className="form-control"  {...register(registerObj.capacity)} />
                        </div>
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('table.title.level')}</label>
                            <input type="text" className="form-control"  {...register(registerObj.productLevel)} />
                        </div>

                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('text.chemical.est_max_storage_qty')}</label>
                            <Controller
                                name={registerObj.estMaxStorageQty}
                                control={control}
                                defaultValue=""
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <InputNumFloat
                                        className={`form-control`}
                                        minValue={CHEMICAL_WEIGHT_MIN}
                                        maxValue={CHEMICAL_WEIGHT_MAX}
                                        maxLength={7 + CHEMICAL_WEIGHT_DECIMAL_PLACES}
                                        decimalPlaces={CHEMICAL_WEIGHT_DECIMAL_PLACES}
                                        value={value}
                                        onChange={onChange}
                                        onBlur={onBlur}
                                        allowEmptyUndefine
                                    />
                                )}
                            />
                        </div>
                        {/* 新增的PPE欄位 */}
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('text.chemical.respiratory_protection')}</label>
                            <input
                                type="text"
                                className={`form-control`}
                                {...register(registerObj.respiratoryProtection)}
                            />
                        </div>
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('text.chemical.hand_protection')}</label>
                            <input
                                type="text"
                                className={`form-control`}
                                {...register(registerObj.handProtection)}
                            />
                        </div>
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('text.chemical.eye_protection')}</label>
                            <input
                                type="text"
                                className={`form-control`}
                                {...register(registerObj.eyeProtection)}
                            />
                        </div>
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('text.chemical.skin_body_protection')}</label>
                            <input
                                type="text"
                                className={`form-control`}
                                {...register(registerObj.skinBodyProtection)}
                            />
                        </div>
                        <h3>
                            <hr />
                            <label className="d-flex align-items-center fw-bold mb-3">
                                {t('table.title.product.packing_format')}
                            </label>
                        </h3>

                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('table.title.product.packing_length')}</label>
                            <Controller
                                name={registerObj.packingLong}
                                control={control}
                                defaultValue=""
                                render={({ field: { onChange, onBlur, value } }) =>
                                    <InputNumFloat
                                        value={value}
                                        maxLength={4}
                                        onChange={onChange}
                                        onBlur={onBlur}
                                        allowEmptyUndefine
                                    />
                                }
                            />
                        </div>
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('table.title.product.packing_width')}</label>
                            <Controller
                                name={registerObj.packingWidth}
                                control={control}
                                defaultValue=""
                                render={({ field: { onChange, onBlur, value } }) =>
                                    <InputNumFloat
                                        value={value}
                                        maxLength={4}
                                        onChange={onChange}
                                        onBlur={onBlur}
                                        allowEmptyUndefine
                                    />
                                }
                            />
                        </div>
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('table.title.product.packing_height')}</label>
                            <Controller
                                name={registerObj.packingHigh}
                                control={control}
                                defaultValue=""
                                render={({ field: { onChange, onBlur, value } }) =>
                                    <InputNumFloat
                                        value={value}
                                        maxLength={4}
                                        onChange={onChange}
                                        onBlur={onBlur}
                                        allowEmptyUndefine
                                    />
                                }
                            />
                        </div>
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('table.title.product.packing_diameter')}</label>
                            <Controller
                                name={registerObj.packingDiameter}
                                control={control}
                                defaultValue=""
                                render={({ field: { onChange, onBlur, value } }) =>
                                    <InputNumFloat
                                        value={value}
                                        maxLength={4}
                                        onChange={onChange}
                                        onBlur={onBlur}
                                        allowEmptyUndefine
                                    />
                                }
                            />
                        </div>
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('table.title.product.packing_full_capacity')}</label>
                            <Controller
                                name={registerObj.fullKilogram}
                                control={control}
                                defaultValue=""
                                render={({ field: { onChange, onBlur, value } }) =>
                                    <InputNumFloat
                                        value={value}
                                        maxLength={4}
                                        onChange={onChange}
                                        onBlur={onBlur}
                                        allowEmptyUndefine
                                    />
                                }
                            />
                        </div>
                        <div className="col-sm-12 col-md-6 col-xl-2 mb-4">
                            <label className="fw-bold">{t('table.title.product.packing_full_pressure')}</label>
                            <Controller
                                name={registerObj.fullPressure}
                                control={control}
                                defaultValue=""
                                render={({ field: { onChange, onBlur, value } }) =>
                                    <InputNumFloat
                                        value={value}
                                        maxLength={4}
                                        onChange={onChange}
                                        onBlur={onBlur}
                                        allowEmptyUndefine
                                    />
                                }
                            />
                        </div>

                        <hr />
                        <h3>
                            <label className="d-flex align-items-center fw-bold mb-3">
                                {t('text.product.packing_type')}
                            </label>
                        </h3>
                        <div className="d-md-flex">
                            {packTypeOptions?.map((config) => {
                                const { configId, configName, configValue } = config;
                                const id = 'packtype-checkbox-' + configId;
                                return (configValue === CONFIG_VAL_OTHER ?
                                    <CheckboxWithInput
                                        key={'packtype-checkbox-' + configId}
                                        label={configName}
                                        checkboxId={id}
                                        checkboxValue={configId}
                                        inputPlaceholder={t('message.enter_other')}
                                        onCheckboxChange={(checked) => {
                                            handleCheckedCategoryChange(configId, config, checked);
                                            if (!checked) {
                                                handleOtherInfoChange(CONFIG_TYPE_PACKTYPE, '');
                                            }
                                        }}
                                        onInputChange={(value) => {
                                            handleOtherInfoChange(CONFIG_TYPE_PACKTYPE, value);
                                        }}
                                    />
                                    :
                                    <div key={'packtype-checkbox-' + configId} className="form-check mb-3 me-3">
                                        <input
                                            className="form-check-input"
                                            type="checkbox"
                                            id={id}
                                            checked={Boolean(detailCategoryMap[configId])}
                                            onChange={(e) => {
                                                handleCheckedCategoryChange(configId, config, e.target.checked);
                                            }}
                                        />
                                        <label className="form-check-label" htmlFor={id}>{configName}</label>
                                    </div>
                                );
                            })}
                        </div>

                        <hr />
                        <h3>
                            <label className="d-flex align-items-center fw-bold my-3">
                                {t('text.product.packing_material')}
                            </label>
                        </h3>
                        <div className="d-md-flex">
                            {packMalOptions?.map((config) => {
                                const { configId, configName, configValue } = config;
                                const id = 'packmal-checkbox-' + configId;
                                return (configValue === CONFIG_VAL_OTHER ?
                                    <CheckboxWithInput
                                        key={'packmal-checkbox-' + configId}
                                        label={configName}
                                        checkboxId={id}
                                        checkboxValue={configId}
                                        inputPlaceholder={t('message.enter_other')}
                                        onCheckboxChange={(checked) => {
                                            handleCheckedCategoryChange(configId, config, checked);
                                            if (!checked) {
                                                handleOtherInfoChange(CONFIG_TYPE_PACKMAL, '');
                                            }
                                        }}
                                        onInputChange={(value) => {
                                            handleOtherInfoChange(CONFIG_TYPE_PACKMAL, value);
                                        }}
                                    />
                                    :
                                    <div key={'packmal-checkbox-' + configId} className="form-check mb-3 me-3">
                                        <input
                                            className="form-check-input"
                                            type="checkbox"
                                            id={id}
                                            checked={Boolean(detailCategoryMap[configId])}
                                            onChange={(e) => {
                                                handleCheckedCategoryChange(configId, config, e.target.checked);
                                            }}
                                        />
                                        <label className="form-check-label" htmlFor={id}>{configName}</label>
                                    </div>
                                );
                            })}
                        </div>
                    </>
                }
                preventMouseExpand
            />
        </div>
    );
};

export default OtherInfoPanel; 