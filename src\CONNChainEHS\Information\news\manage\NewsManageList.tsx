import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from "react-router-dom";
import styled from 'styled-components';
import { AppPaths } from '../../../../config/app-paths';
import useLoginUser from '../../../hooks/useLoginUser';
import Breadcrumbs from '../../../layout/Breadcrumbs';
import Footer from '../../../layout/Footer';
import Loader from "../../../common/Loader";
import { checkTimeoutAction, getBasicLoginUserInfo, navigateToHome } from '../../../utils/authUtil';
import { NEWS_STATUS_SHOW }  from "ehs/constant/constants";
import { isApiCallSuccess } from '../../../utils/resultUtil';
import { getFormatDateSlash } from '../../../utils/stringUtil';
import { NewsAPI } from "../../../../api/newsAPI";
import { errorMsg } from "../../../common/SwalMsg";
import SortIcon from "../../../common/SortIcon";
import { EhsNews } from "../../../models/EhsNews";
import { ActionMode } from "ehs/enums/ActionMode";

function NewsManageList() {
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const [loading, setLoading] = useState(false);
  const [newsList, setNewsList] = useState<EhsNews[]>([]);
  const navigate = useNavigate();

  useEffect(() => {
    if(!loginUser){
      navigateToHome(navigate);
      return;
    }
    fetchData();
  }, [loginUser, i18n.language])

  const fetchData = async () => {
    try{
      setLoading(true);
      const result = await NewsAPI.getNewsList({
        ...getBasicLoginUserInfo(loginUser)!,
      })
      if (isApiCallSuccess(result)) {
        setLoading(false);
        setNewsList(result.results);
      }else{
        errorMsg(result.message);
      }
    }catch(err){
      checkTimeoutAction(err, navigate, t);
      errorMsg(t('message.error'));
    }
  }

  return (
    <StyledNewsManageList $loading={loading}>
        <div className="d-flex flex-column p-0" id="content">
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.information.manage") },
                { label: t("func.information.news.manage_list") },
              ]}
            />
            <h1 className="page-header">{t("func.information.news.manage_list")}</h1>
            <div className="row">
              <div className="col-xl-12">
                <div className="card mt-3">
                  <div className="card-body">
                    <button
                      type="button"
                      className="btn btn-purple fs-5"
                      title={t("button.add")}
                      onClick={() => {
                        navigate('/' + AppPaths.information.newsModify, { state: { mode: ActionMode.ADD, initNewsId: "" } });
                      }}
                    >
                      <i className="fas fa-plus"></i> {t("button.add")}
                    </button>
                  </div>
                </div>
                <div className="card py-3">
                  <div className="card-body">
                    {loading && <Loader />}
                      <div>
                        <table
                          id="data-table-default"
                          className={
                            "table table-hover align-middle dt-responsive"
                          }
                        >
                          <thead className="text-center fs-4 fw-bold">
                            <tr>
                              <th>{t("table.title.item")} </th>
                              <th>
                                {t("table.title.news.announcement_type")}
                                <SortIcon
                                  dataList={newsList}
                                  dataField={"newsType"}
                                  setFunction={setNewsList}
                                />
                              </th>
                              <th>
                                {t("table.title.news.announcement_title")}
                                <SortIcon
                                  dataList={newsList}
                                  dataField={"newsTitle"}
                                  setFunction={setNewsList}
                                />
                              </th>
                              <th>
                                {t("table.title.news.view_count")}
                                <SortIcon
                                  dataList={newsList}
                                  dataField={"viewsNum"}
                                  setFunction={setNewsList}
                                />
                              </th>
                              <th>
                                {t("table.title.news.uploader")}                   
                                <SortIcon
                                  dataList={newsList}
                                  dataField={"createId"}
                                  setFunction={setNewsList}
                                />
                              </th>
                              <th>
                                {t("table.title.news.published_date")}  
                                <SortIcon
                                  dataList={newsList}
                                  dataField={"createDate"}
                                  setFunction={setNewsList}
                                />                  
                              </th>
                              <th>
                                {t("table.title.status")}  
                                <SortIcon
                                  dataList={newsList}
                                  dataField={"newsStatus"}
                                  setFunction={setNewsList}
                                />  
                              </th>
                              <th>
                                {t("table.title.action")}   
                              </th>
                            </tr>
                          </thead>
                          <tbody className="text-center fs-5"> 
                            {!loading &&
                              newsList.map((data, idx) => {
                                return <Row 
                                  key={idx}
                                  index={idx + 1} 
                                  news={data}
                                />;
                              })}
                          </tbody>
                        </table>
                      </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
    </StyledNewsManageList>
  );
}

const Row = (props: {
  index: number; 
  news: EhsNews;
}) => {
  const { t } = useTranslation();
  const { index, news} = props;
  const { newsId, newsType, newsTitle, viewsNum, newsStatus, createName,createDate} = news;
  const showCreateDate = createDate ? getFormatDateSlash(createDate) : "";
  const navigate = useNavigate();
  const editNews = () => navigate('/' + AppPaths.information.newsModify, { state: { mode: ActionMode.EDIT, initNewsId: newsId } });
  const showStatus = newsStatus === NEWS_STATUS_SHOW ? t("table.title.news.show") : t("table.title.news.hide");

  return (
    <tr>
      <td data-title={t("table.title.item")}>{index}</td>
      <td data-title={t("table.title.news.announcement_type")}>{newsType}</td>
      <td data-title={t("table.title.news.announcement_title")} className='ellipsis-cell' title={newsTitle}>{newsTitle}</td>
      <td data-title={t("table.title.news.view_count")}>{viewsNum}</td>
      <td data-title={t("table.title.news.uploader")}>{createName}</td>
      <td data-title={t("table.title.news.published_date")}>{showCreateDate}</td>
      <td data-title={t("table.title.status")}>{showStatus}</td>
      <td data-title={t("table.title.action")}>
        <button
          type="button"
          className="btn btn-warning me-3 fs-5"
          title={t("button.edit")}
          onClick={editNews}
        >
          <i className="fas fa-pen"></i> {t("button.edit")}
        </button>
      </td>
    </tr>
  );
};

const StyledNewsManageList = styled.div<{ $loading?: boolean }>`
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }

  .ellipsis-cell {
    max-width: 200px; 
    white-space: nowrap; 
    overflow: hidden;
    text-overflow: ellipsis;
  }

  table {
    position: relative;
    min-height: ${(props) => (props.$loading ? "200px" : "auto")};
    table-layout: auto; /* <-- 根據內容自動調整欄位寬度 */

    thead {
      background: rgb(251, 205, 165);
    }

    th, td {
      text-align: start;
      white-space: nowrap;  /* <-- 避免自動換行 */
      padding: 8px 12px;
      min-width: 100px;     /* <-- 避免欄位太窄，可視情況調整 */
    }

    td .form-check {
      justify-content: center;
    }
  }

  .topFunctionRow {
    padding: 0 15px;

    .left {
      select {
        width: 80px;
        margin: 0 10px;
      }
    }

    .right {
      .dataTables_filter {
        justify-content: right;
        align-items: center;

        @media (max-width: 767px) {
          justify-content: left;
          margin-top: 10px;
          .ellipsis-cell {
            max-width: 100px;
          }
        }
        input {
          width: 200px;
          margin-left: 10px;
        }
      }
    }
  }

  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    .ellipsis-cell {
      white-space: normal;   
      overflow: visible;
      text-overflow: unset;
      max-width: 100%;
    }   
    
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;
      }  
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        display: flex;
        flex-direction: row;
        padding: 6px;
        min-height: auto;
        align-items: flex-start;
        gap: 10px;
        word-break: break-word;
        white-space: normal;
      }
      td::before {
        content: attr(data-title);
        flex: 0 0 20%; /* 左側 label 寬度：可視需要微調 */
        font-weight: bold;
        color: #1a1a1a;
        white-space: normal;
        word-break: break-word;
      }
    }
  }
`

export default NewsManageList;