import { CHEMICAL_CONCEN_DECIMAL_PLACES, CHEMICAL_CONCEN_MAX, CHEMICAL_CONCEN_MIN, CONFIG_TYPE_MIX_CONCEN_TYPE_RANGE, CONFIG_TYPE_MIX_CONCEN_TYPE_SINGLE } from 'ehs/constant/constants';
import InputMixConCenContext from 'ehs/context/InputMixConCenContext';
import { EhsConfigParam } from 'ehs/models/EhsConfigParam';
import React, { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import InputNumFloat from './InputNumFloat';

export interface InputMixConCenObj {
    concenType: string;
    concenMin: number | null;
    concenMax: number | null;
}

export const initInputMixConCenObj: InputMixConCenObj = {
    concenType: '',
    concenMin: null,
    concenMax: null,
};

interface InputMixConcenProps {
    mixConcenTypeOptions: EhsConfigParam[];
    setInputMixConCenObj?: React.Dispatch<React.SetStateAction<InputMixConCenObj>>
    inputMixConCenObj?: InputMixConCenObj
}

const InputMixConcen: React.FC<InputMixConcenProps> = ({ mixConcenTypeOptions, inputMixConCenObj = initInputMixConCenObj,
    setInputMixConCenObj: propSetInputMixConCenObj = () => { } }) => {
    const { t } = useTranslation();
    const { inputMixConCenObj: inputMixConCenObjParam, setInputMixConCenObj: contextSetInputMixConCenObj } = useContext(InputMixConCenContext);
    const { concenType: concenTypeParam, concenMin: concenMinParam, concenMax: concenMaxParam } = inputMixConCenObjParam;
    const initialState = {
        concenType: concenTypeParam || inputMixConCenObj.concenType,
        concenMin: concenMinParam || inputMixConCenObj.concenMin,
        concenMax: concenMaxParam || inputMixConCenObj.concenMax,
    };
    const setInputMixConCenObj: React.Dispatch<React.SetStateAction<InputMixConCenObj>> = contextSetInputMixConCenObj || propSetInputMixConCenObj;
    const [state, setState] = useState<InputMixConCenObj>(initialState);
    const { concenType } = state;
    const isSingleConcen = concenType === CONFIG_TYPE_MIX_CONCEN_TYPE_SINGLE;
    const isRangeConcen = concenType === CONFIG_TYPE_MIX_CONCEN_TYPE_RANGE;

    // 更新狀態的函數
    const setConcenType = (type: string) => {
        const updateObj = { concenType: type, ...(isSingleConcen ? { concenMax: null } : {}) };
        setState(prevState => ({ ...prevState, ...updateObj }));
        setInputMixConCenObj(prevState => ({ ...prevState, ...updateObj }));
    };

    const setConcenMin = (min: number | null) => {
        setState(prevState => ({ ...prevState, concenMin: min }));
        setInputMixConCenObj(prevState => ({ ...prevState, concenMin: min }));
    };

    const setConcenMax = (max: number | null) => {
        setState(prevState => ({ ...prevState, concenMax: max }));
        setInputMixConCenObj(prevState => ({ ...prevState, concenMax: max }));
    };

    const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setConcenType(e.target.value);
    };

    return (
        <>
            <label className="h5 me-2">{t('text.chemical.substance_concen')}</label>
            <div className="d-md-flex align-items-center">
                {mixConcenTypeOptions.map((item, index) => {
                    const { configId, configName } = item;
                    const objId = `substanceConcenType-${configId}`;
                    return (
                        <div key={index} className="form-check form-check-inline">
                            <input
                                className="form-check-input"
                                type="radio"
                                name="substanceConcenType"
                                id={objId}
                                value={configId}
                                onChange={handleRadioChange}
                                checked={concenType === configId}
                            />
                            <label className="form-check-label" htmlFor={objId}>
                                {configName}
                            </label>
                        </div>
                    );
                })}
            </div>

            {concenType && (
                <div className="mt-3">
                    {<div className="d-md-flex align-items-center">
                        <div className="row d-md-flex align-items-center">
                            <div className="col-auto d-flex align-items-center">
                                <InputNumFloat className="form-control" minValue={CHEMICAL_CONCEN_MIN} maxValue={CHEMICAL_CONCEN_MAX} maxLength={3 + CHEMICAL_CONCEN_DECIMAL_PLACES}
                                    decimalPlaces={CHEMICAL_CONCEN_DECIMAL_PLACES} value={undefined}
                                    allowEmptyUndefine
                                    onBlur={num => {
                                        setConcenMin(num === undefined ? null : num);
                                    }}
                                /><span className='ms-1'>%</span>
                            </div>
                            {isRangeConcen && <div className="col-auto px-0">~</div>}
                            {isRangeConcen && <div className="col-auto d-flex align-items-center">
                                <InputNumFloat className="form-control" minValue={CHEMICAL_CONCEN_MIN} maxValue={CHEMICAL_CONCEN_MAX} maxLength={3 + CHEMICAL_CONCEN_DECIMAL_PLACES}
                                    decimalPlaces={CHEMICAL_CONCEN_DECIMAL_PLACES} value={undefined}
                                    allowEmptyUndefine
                                    onBlur={num => {
                                        setConcenMax(num === undefined ? null : num);
                                    }}
                                /><span className='ms-1'>%</span>
                            </div>}
                        </div>
                    </div>}
                </div>
            )}
        </>
    );
};

export default InputMixConcen;
