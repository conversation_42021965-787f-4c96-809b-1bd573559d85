import DownloadButton from "ehs/common/button/DownloadButton";
import { EhsArea } from "ehs/models/EhsArea";
import { EhsAreaChemicalApproveInfo } from "ehs/models/EhsAreaChemicalApproveInfo";
import { EhsChemicalInfoArea } from "ehs/models/EhsChemicalInfoArea";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsFile } from "ehs/models/EhsFile";
import { getApprovalNoPre } from "ehs/utils/chemicalUtil";
import { getFormatDateSlash } from "ehs/utils/stringUtil";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

interface ChemicalAreaApprovalSectionProps {
    isDisplayApproveinfoSection: boolean;
    areaApprovalType: EhsConfigParam[];
    checkedChemClass: { [key: string]: EhsConfigParam };
    areaList: EhsArea[];
    chemApproveInfoMap: { [type: string]: EhsAreaChemicalApproveInfo };
    chemApproveInfoFile: { [type: string]: EhsFile | null };
    chemInfoArea: { [areaId: string]: EhsChemicalInfoArea };
    cityMap: { [key: string]: EhsConfigParam };
    isChinese: boolean;
    className?: string; // 允許外部傳入自定義 className
}

const ChemicalAreaApprovalSection: React.FC<ChemicalAreaApprovalSectionProps> = ({
    isDisplayApproveinfoSection,
    areaApprovalType,
    checkedChemClass,
    areaList,
    chemApproveInfoMap,
    chemApproveInfoFile,
    chemInfoArea,
    cityMap,
    isChinese,
    className = '' // 預設寬度，可被外部覆蓋
}) => {
    const { t } = useTranslation();

    if (!isDisplayApproveinfoSection || !areaApprovalType.some(item => checkedChemClass[item.configValue])) {
        return null;
    }

    return (
        <StlyedChemicalAreaApprovalSection className={className}>
            <h5 className="mb-3">{t('text.chemical.area_cert_info')}</h5>
            {areaApprovalType.map((item) => {
                const { configId, configName, configValue } = item;
                if (!checkedChemClass[configValue]) return null;

                return (
                    <div key={'areaApprovalType' + configId} className="mb-4">
                        <div className="approval-section p-3 border border-info rounded">
                            <div className="d-flex align-items-center mb-3">
                                <span className="badge bg-primary px-3 py-2">{configName}</span>
                            </div>

                            {areaList.map(area => {
                                const { areaId, areaName, areaCity } = area;
                                const chemApproveInfo = chemApproveInfoMap[areaId + '-' + configId];
                                const { approvalNo, approvalExpiryDate } = chemApproveInfo || {};
                                const approveInfoFile = chemApproveInfoFile[areaId + '-' + configId];
                                const { fileContentBase64, fileName = "" } = approveInfoFile || {};
                                const isShow = approvalNo && approvalExpiryDate && fileContentBase64;
                                const noEnterInfoMsg = t('message.chemical.no_enter_area_approval_info');
                                const approvalNoPre = getApprovalNoPre(cityMap[areaCity]?.configName, configName, isChinese, t);
                                const enabledArea = Object.entries(chemInfoArea)
                                    .filter(([key]) => key.endsWith(`-${areaId}`))
                                    .some(([_, value]) => value.chemStatus !== 0);

                                if (!enabledArea) return null;

                                return (
                                    <div key={'area-ApprovalType' + configId + '-' + areaId}
                                        className="area-item p-3 mb-3 bg-light rounded">
                                        <h6 className="border-bottom pb-2 mb-3">{areaName}</h6>

                                        {isShow ? (
                                            <div className="row g-3">
                                                <div className="col-12 col-md-6 col-lg-4">
                                                    <div className="info-item">
                                                        <small className="text-muted d-block mb-1">
                                                            {t('text.chemical.certification_no')}
                                                        </small>
                                                        <div>
                                                            {approvalNoPre}{approvalNo}
                                                            {isChinese ? "" : " "}{t('text.area.chem_approval_no_suffix')}
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="col-12 col-md-6 col-lg-4">
                                                    <div className="info-item">
                                                        <small className="text-muted d-block mb-1">
                                                            {t('text.chemical.certification_exp_date')}
                                                        </small>
                                                        <div>{getFormatDateSlash(approvalExpiryDate)}</div>
                                                    </div>
                                                </div>

                                                <div className="col-12 col-md-6 col-lg-4">
                                                    <div className="info-item">
                                                        <small className="text-muted d-block mb-1">
                                                            {t('text.chemical.certification_file')}
                                                        </small>
                                                        <DownloadButton
                                                            content={fileContentBase64}
                                                            fileName={fileName}
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        ) : (
                                            <div className="text-danger">{noEnterInfoMsg}</div>
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                );
            })}
        </StlyedChemicalAreaApprovalSection>
    );
};

const StlyedChemicalAreaApprovalSection = styled.div` 
.approval-section {
    transition: all 0.3s ease;
}

.area-item {
    transition: all 0.3s ease;
}

.area-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.info-item {
    height: 100%;
    padding: 0.5rem;
}
`;

export default ChemicalAreaApprovalSection;