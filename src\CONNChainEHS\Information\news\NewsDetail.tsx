import { useEffect, useState, useRef } from "react";
import { useTranslation } from 'react-i18next';
import styled from "styled-components";
import { useLocation, useNavigate } from 'react-router-dom';
import Breadcrumbs from "ehs/layout/Breadcrumbs";
import Footer from "ehs/layout/Footer";
import { AppPaths } from "config/app-paths";
import BackButton from "ehs/common/button/BackButton";
import { NewsAPI } from "api/newsAPI";
import { getBasicLoginUserInfo, navigateToHome } from '../../utils/authUtil';
import useLoginUser from '../../hooks/useLoginUser';
import { isApiCallSuccess } from "../../utils/resultUtil";
import { EhsNews, initEhsNews } from "ehs/models/EhsNews";
import { errorMsg } from "ehs/common/SwalMsg";
import { getFormatDateSlash } from '../../utils/stringUtil';
import DownloadButton from "ehs/common/button/DownloadButton";
import { EhsFile } from "../../models/EhsFile";
import { FileAPI } from "../../../api/fileAPI";
import { FILE_TYPE_NEWS } from "ehs/constant/constants";
import LoadingSpinner from "ehs/common/LoadingSpinner";

function NewsDetail() {
  const { t } = useTranslation();
  const { loginUser } = useLoginUser();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [newsDetail, setNewsDatail] = useState<EhsNews>(initEhsNews);
  const [loading, setLoading] = useState(false);
  const [newsFileMap, setNewsFileMap] = useState<{ [key: string]: EhsFile[] }>({});
  const hasViewedRef = useRef(false);
  const { initNewsId }: ({ initNewsId: string }) = state || {};
  const { newsTitle, createName, viewsNum, createDate, editDate } = newsDetail;
  const showCreateDate = createDate ? getFormatDateSlash(createDate) : "";
  const showUpdateDate = editDate ? getFormatDateSlash(editDate) : "";

  useEffect(() => {
    if (!loginUser || !initNewsId) {
      navigateToHome(navigate);
      return;
    }
    if (!hasViewedRef.current) {
      increaseViews();
      hasViewedRef.current = true;
    }
    fetchData();
    fetchNewsFile();
  }, [loginUser]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const result = await NewsAPI.getNewsById({
        ...getBasicLoginUserInfo(loginUser)!,
        newsId: initNewsId,
      });
      if (isApiCallSuccess(result)) {
        const detailResult = result.results;
        setNewsDatail(detailResult);
      } else {
        errorMsg(result.message);
      }
    } catch (error) {
      navigateToHome(navigate);
      errorMsg(t('message.error'));
    } finally {
      setLoading(false);
    }
  }

  const fetchNewsFile = async () => {
    try {
      setLoading(true);
      const result = await FileAPI.getFileList({
        ...getBasicLoginUserInfo(loginUser),
        fileTypeList: [FILE_TYPE_NEWS],
        fileMappingIdList: [initNewsId]
      })

      if (isApiCallSuccess(result)) {
        const fileList: EhsFile[] = result.results;
        const fileMap: { [key: string]: EhsFile[] } = {};
        fileList.forEach((file) => {
          if (!fileMap[file.fileSubtype]) {
            fileMap[file.fileSubtype] = [];
          }
          fileMap[file.fileSubtype].push(file);
        });
        setNewsFileMap(fileMap);
      } else {
        errorMsg(result.message);
      }
    } catch (error) {
      navigateToHome(navigate);
      errorMsg(t('message.error'));
    } finally {
      setLoading(false);
    }
  }

  const increaseViews = async () => {
    try {
      setLoading(true);
      const result = await NewsAPI.increaseViews({
        ...getBasicLoginUserInfo(loginUser),
        newsId: initNewsId,
      })
      if (!isApiCallSuccess(result)) {
        errorMsg(result.message);
      }
    } catch (error) {
      navigateToHome(navigate);
      errorMsg(t('message.error'));
    } finally {
      setLoading(false);
    }
  }

  return (
    <StyledNewsDetail $loading={loading}>
      <div className="d-flex flex-column p-0" id="content">
        {/* BEGIN scrollbar */}
        <div className="app-content-padding flex-grow-1">
          {/* BEGIN breadcrumb */}
          <Breadcrumbs
            items={[
              { label: t("func.information.manage") },
              { label: t("func.information.news.manage_list"), path: AppPaths.information.newsManageList },
              { label: t("text.detail") },
            ]}
          />
          {/* END breadcrumb */}
          {/* BEGIN page-header */}
          {/* END page-header */}
          <BackButton />
          <div className="card mt-3">
            <div className="card-body position-relative">
              {loading && (
                <LoadingSpinner />
              )}
              {!loading && <div className="row align-items-start mt-4">
                <div className="col-xl-12 mb-2">
                  <label className="ms-1 h5 title-text aligh-center">{newsTitle}</label>
                  <label className="ms-1 h5 aligh-right">{t('text.published_date')} : {showCreateDate}</label>
                  <label className="ms-1 h5 aligh-right">{t('text.update_date')} : {showUpdateDate}</label>
                  <label className="ms-1 h5 aligh-right">{t('text.creator')} : {createName}</label>
                  <label className="ms-1 h5 aligh-right">{t('text.views')} : {viewsNum}</label>
                </div>
                <hr />
                <div className="col-xl-12 mb-2">
                  <label className="ms-1 h5 sub-title-text aligh-center">{t('text.announcement_content')}</label>
                  <div className="row d-flex align-items-center">
                    <div className="col-xl-12 mb-2">
                      <div className="pre-wrap content-section font-size-1">
                        {newsDetail.newsContent}
                      </div>
                    </div>
                  </div>
                </div>
                <hr />
                <div className="col-xl-12 mb-2">
                  <label className="ms-1 h5 sub-title-text aligh-center">{t('text.announcement_files')}</label>
                  <div className="row d-flex align-items-center aligh-center">
                    {newsFileMap[FILE_TYPE_NEWS]?.length > 0 ? (
                      newsFileMap[FILE_TYPE_NEWS].map((file) => (
                        <DownloadButton key={'newsfile-' + file.fileId} content={file.fileContentBase64} fileName={file.fileName} />
                      ))
                    ) : (
                      <div className="col-12 text-center py-3 font-italic text-muted">{t('text.no_files')}</div>
                    )}
                  </div>
                </div>
              </div>}
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </div>
    </StyledNewsDetail>
  );
}

const StyledNewsDetail = styled.div<{ $loading?: boolean }>`
  
  label {
    padding: 10px;
    width: 100%;
  }

  button {
    font-size: 1.0rem;
  }

  .aligh-center {
    text-align: center;
  }

  .aligh-right {
    text-align: right;
    padding: 0px;
  }
  
  .pre-wrap {
    white-space: pre-wrap;
  }

  .content-section {
    margin: 10px;
    padding: 20px;
    border-radius: 5px;
  }

  .font-size-1 {
    font-size: 1.0rem;
  }

  .title-text {
    caption-side: top;
    font-size: 2.5rem;
    font-weight: bold;
    color: #333;
  }
  
  .sub-title-text {
    caption-side: top;
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
  }

  @media (max-width: 600px){
    .content-section {
      padding: 0; 
    }
  }
`;

export default NewsDetail;