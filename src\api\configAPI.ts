import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const ConfigAPI = {
  getConfigByType: async (
    parms: BaseParams & {
      configTypeList: string[];
      configSubTypeList?: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "config/list/type", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getConfigMapping: async (
    parms: BaseParams & {
      configTypeList: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "config/mapping", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getConfigChemClassCtrl: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "config/chemclass/ctrl", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemclassMappingByLabid: async (
    parms: BaseParams & {
      labId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "config/chemclass/mapping/labid", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },

};
