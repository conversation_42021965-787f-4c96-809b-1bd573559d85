import { Config<PERSON><PERSON> } from "api/configAPI";
import { BarcodeAPI } from "api/barcodeAPI";
import Loader from "ehs/common/Loader";
import NoDataRow from "ehs/common/NoDataRow";
import SortIcon from "ehs/common/SortIcon";
import { CONFIG_TYPE_CHEM_CLASS_LV, SORT_DIRECTION, type SortDirection } from "ehs/constant/constants";
import useLoginUser from "ehs/hooks/useLoginUser";
import Breadcrumbs from "ehs/layout/Breadcrumbs";
import Footer from "ehs/layout/Footer";
import PageSizeSelector from "ehs/layout/PageSizeSelector";
import Pagination from "ehs/layout/Pagination";
import { EhsBarcode } from "ehs/models/EhsBarcode";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { initPageInfo, PageInfo } from "ehs/models/PageInfo";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { getBasicLoginUserInfo } from "ehs/utils/authUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { getFormatDateDash, getFormatDateSlash } from "ehs/utils/stringUtil";
import QRCode from "qrcode-generator";
import { getLabTextObj } from "ehs/utils/langUtil";
import InputDate from "ehs/common/input/InputDate";
import { showWarnToast } from "ehs/common/Toast";

interface ConditionType {
  currentPage: number;
  pageSize: number;
  keyword?: string;
  createDateStart?: string;
  createDateEnd?: string;
}

// QR Code 組件
const QRCodeDisplay = ({ value }: { value: string }) => {
  const generateQRCode = (text: string) => {
    if (!text) return '';

    try {
      const qr = QRCode(0, 'M');
      qr.addData(text);
      qr.make();
      return qr.createDataURL(4);
    } catch (error) {
      console.error('QR Code generation failed:', error);
      return '';
    }
  };

  const qrDataUrl = generateQRCode(value);

  if (!qrDataUrl) {
    return <span className="text-muted">{value}</span>;
  }

  return (
    <div className="qr-code-container">
      <img
        src={qrDataUrl}
        alt={`QR Code: ${value}`}
        className="qr-code-img"
        title={value}
      />
      {/* <div className="qr-code-text">{value}</div> */}
    </div>
  );
};



function ChemicalBarcodeList() {
  const { t } = useTranslation();
  const { loginUser } = useLoginUser();
  const [loading, setLoading] = useState<boolean>(false);
  const [, setConfigMap] = useState<{ [key: string]: EhsConfigParam[] }>({});
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(SORT_DIRECTION.ASC);
  const [barcodeList, setBarcodeList] = useState<EhsBarcode[]>([]);
  const [activeTab, setActiveTab] = useState<string>("");
  const [chemClassTabs, setChemClassTabs] = useState<any[]>([]);
  const [pageInfo, setPageInfo] = useState<PageInfo>(initPageInfo);
  const { labNoText, labNameText } = getLabTextObj(loginUser, t);

  // 為每個 tab 維護獨立的條件狀態
  const initCondition: ConditionType = {
    currentPage: 1,
    pageSize: 50,
    keyword: '',
    createDateStart: '',
    createDateEnd: '',
  };
  const [condition, setCondition] = useState<{ [key: string]: ConditionType }>({});

  // 獲取當前 tab 的條件
  const currentCondition = activeTab && condition[activeTab] ? condition[activeTab] : initCondition;
  const { pageSize } = currentCondition;

  // 更新搜尋條件（僅更新狀態，不觸發 API）
  const updateSearchCondition = (field: keyof ConditionType, value: string) => {
    if (!activeTab) return;

    setCondition(prev => ({
      ...prev,
      [activeTab]: {
        ...prev[activeTab] || initCondition,
        [field]: value,
        currentPage: 1 // 重置到第一頁
      }
    }));
  };

  // 執行搜尋
  const handleSearch = () => {
    if (activeTab) {
      const searchCondition = condition[activeTab] || initCondition;

      // 驗證日期範圍
      if (searchCondition.createDateStart && searchCondition.createDateEnd) {
        if (searchCondition.createDateEnd < searchCondition.createDateStart) {
          showWarnToast(t("message.date_range_invalid"));
          return;
        }
      }

      fetchBarcodeData(searchCondition);
    }
  };

  // 重置搜尋條件
  const handleResetSearch = () => {
    if (!activeTab) return;

    const resetCondition = {
      ...initCondition,
      pageSize: currentCondition.pageSize // 保持當前的頁面大小
    };

    setCondition(prev => ({
      ...prev,
      [activeTab]: resetCondition
    }));

    // 重置後自動搜尋
    fetchBarcodeData(resetCondition);
  };



  // 處理排序
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === SORT_DIRECTION.ASC ? SORT_DIRECTION.DESC : SORT_DIRECTION.ASC);
    } else {
      setSortField(field);
      setSortDirection(SORT_DIRECTION.ASC);
    }
  };

  // 更新當前 tab 的條件
  // const updateTabCondition = (updateFn: (prevTabCondition: ConditionType) => ConditionType) => {
  //   setCondition(prev => ({
  //     ...prev,
  //     [activeTab]: updateFn(prev[activeTab] || currentCondition)
  //   }));
  // };

  // const handleSetCondition = (prevCondition: any) => updateTabCondition(tabCondition => ({
  //   ...tabCondition,
  //   ...(typeof prevCondition === 'function' ? prevCondition(tabCondition) : prevCondition)
  // }));

  // 獲取條碼數據
  const fetchBarcodeData = useCallback(async (searchCondition: ConditionType) => {
    if (!loginUser || !activeTab || !searchCondition) return;

    setLoading(true);
    try {
      const requestParams: any = {
        ...getBasicLoginUserInfo(loginUser),
        barcodeTableName: activeTab,
        currentPage: searchCondition.currentPage,
        pageSize: searchCondition.pageSize,
      };

      // 添加搜尋條件
      if (searchCondition.keyword?.trim()) {
        requestParams.keyword = searchCondition.keyword.trim();
      }
      if (searchCondition.createDateStart?.trim()) {
        requestParams.createDateStart = searchCondition.createDateStart.trim();
      }
      if (searchCondition.createDateEnd?.trim()) {
        requestParams.createDateEnd = searchCondition.createDateEnd.trim();
      }

      const result = await BarcodeAPI.getBarcodeList(requestParams);

      if (isApiCallSuccess(result)) {
        const data = result.results || [];
        const pageInfoData = result.pageinfo || initPageInfo;

        setBarcodeList(data);
        setPageInfo(pageInfoData);
      }
    } catch (error) {
      console.error('獲取條碼清單失敗:', error);
      setBarcodeList([]);
      setPageInfo(initPageInfo);
    } finally {
      setLoading(false);
    }
  }, [loginUser, activeTab]);

  // 根據當前 tab 過濾數據
  const getTabFilteredData = () => {
    return barcodeList;
  };

  // 獲取當前頁面的數據（API 已經返回分頁後的數據，不需要再次分頁）
  const getCurrentPageData = () => {
    return getTabFilteredData();
  };

  // 處理 tab 切換
  const handleTabClick = useCallback((tabValue: string) => {
    if (tabValue !== activeTab) {
      setActiveTab(tabValue);

      // 如果該 tab 還沒有條件，初始化條件
      if (!condition[tabValue]) {
        setCondition(prev => ({
          ...prev,
          [tabValue]: initCondition
        }));
      }
    }
  }, [activeTab, condition]);

  const fetchConfig = useCallback(async () => {
    try {
      const result = await ConfigAPI.getConfigByType({
        ...getBasicLoginUserInfo(loginUser),
        configTypeList: [CONFIG_TYPE_CHEM_CLASS_LV],
      });

      if (isApiCallSuccess(result)) {
        const newConfigMap = result.results.reduce((acc: { [key: string]: EhsConfigParam[] }, config: EhsConfigParam) => {
          if (!acc[config.configType]) {
            acc[config.configType] = [];
          }
          acc[config.configType].push(config);
          return acc;
        }, {});
        setConfigMap(newConfigMap);

        // 建立 tab 選項，顯示所有可用的化學品分類
        const chemClassConfigs = newConfigMap[CONFIG_TYPE_CHEM_CLASS_LV] || [];
        const tabItems = chemClassConfigs.map((config: EhsConfigParam) => ({
          value: config.configValue,
          label: config.configName
        }));

        setChemClassTabs(tabItems);

        // 設置初始活動標籤
        if (tabItems.length > 0 && !activeTab) {
          const firstTabValue = tabItems[0].value;
          setActiveTab(firstTabValue);

          // 初始化第一個 tab 的條件
          setCondition(prev => ({
            ...prev,
            [firstTabValue]: initCondition
          }));
        }
      }
    } catch (err) {
      console.error('獲取配置資料失敗:', err);
    }
  }, [loginUser]);

  // 初始化時載入配置
  useEffect(() => {
    if (loginUser) {
      fetchConfig();
    }
  }, [loginUser, fetchConfig]);

  // 當 activeTab 改變時獲取數據（初始載入）
  useEffect(() => {
    if (activeTab && loginUser) {
      // 使用當前 tab 的條件，如果沒有則使用初始條件
      const tabCondition = condition[activeTab] || initCondition;
      fetchBarcodeData(tabCondition);
    }
  }, [activeTab, loginUser]);

  return (
    <StlyedChemicalBarcodeList $loading={loading}>
      <div className="d-flex flex-column p-0" id="content">
        <div className="app-content-padding flex-grow-1">
          {/* 麵包屑導航 */}
          <Breadcrumbs
            items={[
              { label: t("func.chemical.manage") },
              { label: t("func.chemical.barcode_list") },
            ]}
          />
          {/* 頁面標題 */}
          <h1 className="page-header">{t("func.chemical.barcode_list")}</h1>

          {/* Tab 導航 */}
          {!isArrayEmpty(chemClassTabs) && (
            <>
              <ul className="nav nav-tabs">
                {chemClassTabs.map((tab) => (
                  <li className="nav-item" key={tab.value}>
                    <a
                      href={`#tab-${tab.value}`}
                      data-bs-toggle="tab"
                      className={`nav-link ${activeTab === tab.value ? 'active' : ''}`}
                      onClick={() => handleTabClick(tab.value)}
                    >
                      {tab.label}
                    </a>
                  </li>
                ))}
              </ul>
              <div className="tab-content panel p-3 rounded-0 rounded-bottom">
                {chemClassTabs.map((tab) => (
                  <div
                    className={`tab-pane fade ${activeTab === tab.value ? 'active show' : ''}`}
                    id={`tab-${tab.value}`}
                    key={tab.value}
                  >
                    <div className="card pt-3">
                      {/* 搜尋區域 */}
                      <div className="search-section mb-3 p-3 rounded">
                        <div className="row g-3 align-items-end">
                          <div className="col-xl-4 d-flex align-items-center">
                            <label className="form-label pe-3">{t("text.create_date")}</label>
                            <InputDate
                              className="form-control"
                              defaultValue={currentCondition.createDateStart || ''}
                              onChange={(date) => {
                                updateSearchCondition('createDateStart', getFormatDateDash(date));
                              }}
                              isClearable={true}
                            />
                            <span className="mx-2">~</span>
                            <InputDate
                              className="form-control"
                              defaultValue={currentCondition.createDateEnd || ''}
                              onChange={(date) => {
                                updateSearchCondition('createDateEnd', getFormatDateDash(date));
                              }}
                              isClearable={true}
                            />
                          </div>
                          <div className="col-xl-3 d-flex align-items-center">
                            <label className="form-label pe-3">{t("text.search.keyword")}</label>
                            <input
                              type="text"
                              className="form-control w-75"
                              placeholder={t("table.title.barcode")}
                              value={currentCondition.keyword || ''}
                              onChange={(e) => updateSearchCondition('keyword', e.target.value)}
                            />
                          </div>
                          <div className="col-md-3">
                            <div className="d-flex gap-2">
                              <button
                                type="button"
                                className="btn btn-primary flex-fill"
                                onClick={handleSearch}
                                title={t('button.search.item')}
                              >
                                <i className="fas fa-search me-1"></i>
                                {t("button.search.item")}
                              </button>
                              <button
                                type="button"
                                className="btn btn-secondary flex-fill"
                                onClick={handleResetSearch}
                                title={t('button.reset_search')}
                              >
                                <i className="fas fa-undo me-1"></i>
                                {t("button.reset_search")}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="row topFunctionRow">
                        <div className="col-sm-12 col-md-6 left">
                          <PageSizeSelector
                            pageSize={pageSize}
                            condition={condition}
                            specialCondition={{
                              tabPagination: {
                                tabId: activeTab,
                                updateTabPageSize: (tabId: string, newPageSize: number) => {
                                  // 更新指定 tab 的頁面大小並重置到第一頁
                                  setCondition(prev => ({
                                    ...prev,
                                    [tabId]: {
                                      ...prev[tabId],
                                      pageSize: newPageSize,
                                      currentPage: 1
                                    }
                                  }));
                                }
                              }
                            }}
                            setCondition={(newCondition) => {
                              setCondition(newCondition);
                            }}
                          />
                        </div>
                        <div className="col-sm-12 col-md-6 right"></div>
                      </div>
                      <div className="card-body">
                        {loading && <Loader />}
                        <div className="table-responsive mt-1">
                          <table
                            id="data-table-default"
                            className="table table-hover align-middle dt-responsive nowrap"
                          >
                            <thead className="text-center fs-4 fw-bold bg-lime-200">
                              <tr>
                                <th className="w-4">{t("table.title.item")}</th>
                                <th className='text-start w-8'>{t("table.title.barcode")}</th>
                                <th className='text-start w-5'>QR Code</th>
                                <th className='text-start w-15'>
                                  {t("text.chemical.name")}
                                  <SortIcon
                                    dataList={getCurrentPageData()}
                                    dataField="chemName"
                                    setFunction={() => handleSort("chemName")}
                                  />
                                </th>
                                <th className='text-start w-8'>
                                  {t("table.title.ctrl_no")}
                                </th>
                                <th className='text-start w-8'>
                                  {t("table.title.casno")}
                                </th>
                                <th className='text-start w-8'>{t("table.title.area.no")}</th>
                                <th className='text-start w-9'>{labNoText}</th>
                                <th className='text-start w-12'>{labNameText}</th>
                                <th className='text-start w-8'>{t("table.title.area.resp_name")}</th>
                                <th className='text-start w-4'>{t("text.create_date")}</th>
                              </tr>
                            </thead>
                            <tbody className="text-center fs-5">
                              {!loading && !isArrayEmpty(getCurrentPageData()) ?
                                getCurrentPageData().map((data, idx) => {
                                  const { barcodeId, barcode, qrcode, chemName, chemNameEn,
                                    chemCtrlNo, casNo, areaNo, labName, labNo,
                                    respUser, createDate } = data;
                                  return (
                                    <tr key={barcodeId || idx}>
                                      <td data-title={t("table.title.item")}><label className="my-3 me-2">{idx + 1}</label></td>
                                      <td data-title={t("table.title.barcode")} className='text-start'>{barcode}</td>
                                      <td data-title="QR Code" className='text-start'>
                                        <QRCodeDisplay value={qrcode || ''} />
                                      </td>
                                      <td data-title={t("text.chemical.name")} className='text-start'>
                                        <div>{chemName}</div>
                                        <div className="text-muted">{chemNameEn}</div>
                                      </td>
                                      <td data-title={t("table.title.ctrl_no")} className='text-start'>{chemCtrlNo}</td>
                                      <td data-title={t("table.title.casno")} className='text-start'>{casNo}</td>
                                      <td data-title={t("table.title.area.no")} className='text-start'>{areaNo}</td>
                                      <td data-title={labNoText} className='text-start'>{labNo}</td>
                                      <td data-title={labNameText} className='text-start'>{labName}</td>
                                      <td data-title={t("table.title.area.resp_name")} className='text-start'>{respUser}</td>
                                      <td data-title={t("text.create_date")} className='text-start'>{getFormatDateSlash(createDate)}</td>
                                    </tr>
                                  );
                                })
                                : (!loading && <NoDataRow />)}
                            </tbody>
                          </table>
                        </div>
                        <Pagination
                          pageInfo={pageInfo}
                          specialCondition={{
                            tabPagination: {
                              tabId: activeTab,
                              updateTabPage: (tabId: string, page: number) => {
                                // 更新指定 tab 的頁碼
                                setCondition(prev => ({
                                  ...prev,
                                  [tabId]: {
                                    ...prev[tabId],
                                    currentPage: page
                                  }
                                }));
                              }
                            }
                          }}
                          setCondition={(prevCondition) => {
                            setCondition(prevCondition);
                          }}
                          condition={condition}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
        {/* 頁腳 */}
        <Footer />
      </div>
    </StlyedChemicalBarcodeList>
  );
}

const StlyedChemicalBarcodeList = styled.div<{ $loading?: boolean }>`
  padding-bottom:150px;

  /* 設置容器為 flex，並允許換行 */
  .blocks-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px; /* 區塊之間的間距 */
  }

  /* 每個區塊固定寬度，使得每行最多 4 個區塊 */
  .block {
    flex: 1 1 calc(25% - 10px); /* 每行 4 個區塊，減去間距 */
    box-sizing: border-box;
    border: 1.5px solid #ccc; /* 區塊邊框 */
    padding: 10px; /* 區塊內間距 */
  }

  /* 區塊內部的內容垂直排列 */
  .block-content {
    margin-bottom: 10px; /* 每個內容項之間的間距 */
  }

  /* 移除最後一個內容項的 margin-bottom */
  .block-content:last-child {
    margin-bottom: 0;
  }

  .custom-link {
    color: blue;
    cursor: pointer;
  }

  .custom-link:hover {
      color: #551A8B; 
  }

  .w-4{
    width:4%;
  }
  
  .w-5{
    width:5%;
  }
  
  .w-6{
    width:6%;
  }
  
  .w-8{
    width:8%;
  }

  .w-9{
    width:9%;
  }

  .w-10{
    width:10%;
  }
  
  .w-12{
    width:12%;
  }
  
  .w-15{
    width:15%;
  }

  .ghs-img{
    width:40px;
  }

  .qr-code-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    min-width: 80px;
  }

  .qr-code-img {
    width: 60px;
    height: 60px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    object-fit: contain;
  }

  .qr-code-text {
    font-size: 11px;
    color: #666;
    word-break: break-all;
    max-width: 100px;
    line-height: 1.2;
  }

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  
  .search-section {
    
    .form-label {
      font-weight: 500;
      margin-bottom: 0.5rem;
      color: #333;
      font-size: 14px;
    }
    
    .d-flex.gap-2 {
      gap: 0.5rem !important;
      
      .btn {
        font-size: 14px;
        padding: 0.5rem 1rem;
      }
    }
    
    .form-control {
      font-size: 14px;
    }
  }

  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }
  
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }
  
  table {
    position:relative;
    min-height:${props => props.$loading ? "300px" : "auto"};
    th {
      text-align: center;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
  
    .ghs-img{
      width:30%;
    }
    
    .qr-code-img {
      width: 50px;
      height: 50px;
    }
    
    .qr-code-text {
      font-size: 10px;
      max-width: 80px;
    }
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 125px;
        text-align:left;
        min-height:100px; // rwd後 td最小高度
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          
    padding-bottom: 10px;
    min-height: auto; /* 重置最小高度 */
    height: auto; /* 重置高度 */
    white-space: normal; /* 讓長標題能夠換行 */
    word-wrap: break-word; /* 在需要時強制斷詞 */
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }
    }
  }
`;

export default ChemicalBarcodeList;
