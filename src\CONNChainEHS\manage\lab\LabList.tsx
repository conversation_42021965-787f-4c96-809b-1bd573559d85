import BlockUi from "@availity/block-ui";
import { ConfigAPI } from "api/configAPI";
import { LabAPI } from "api/labAPI";
import { OptionAPI } from "api/optionAPI";
import { SelectListAPI } from "api/selectListAPI";
import { AppPaths } from "config/app-paths";
import BlockuiMsg from "ehs/common/BlockuiMsg";
import ExpandableRwdTable from "ehs/common/ExpandableRwdTable";
import ExpandRwdButton from "ehs/common/ExpandRwdButton";
import Loader from "ehs/common/Loader";
import NamesSplitFragment from "ehs/common/NamesSplitFragment";
import NoDataRow from "ehs/common/NoDataRow";
import SearchConditionButton from "ehs/common/search/SearchConditionButton";
import SearchDivBuilding from "ehs/common/search/SearchDivBuilding";
import SearchDivLab from "ehs/common/search/SearchDivLab";
import SearchDivUnit from "ehs/common/search/SearchDivUnit";
import SearchDropdownSwitch from "ehs/common/search/SearchDropdownSwitch";
import SortIcon from "ehs/common/SortIcon";
import { confirmMsg, errorMsg } from "ehs/common/SwalMsg";
import { showSuccessToast } from "ehs/common/Toast";
import { CONFIG_TYPE_LAB_STATUS, OPTION_SHOW_SEARCH_ADVANCE, OPTION_SHOW_SEARCH_BUILDING, OPTION_SHOW_SEARCH_UNIT, ORG_SPLIT_FLAG } from "ehs/constant/constants";
import { ActionMode } from "ehs/enums/ActionMode";
import { BtnType } from "ehs/enums/BtnType";
import { isLabSigning } from "ehs/enums/LabStatus";
import { SearchCondition } from "ehs/enums/SearchCondition";
import { useExpandedRwdTableRow } from "ehs/hooks/useExpandedRwdTableRow";
import useLoginUser from "ehs/hooks/useLoginUser";
import Breadcrumbs from "ehs/layout/Breadcrumbs";
import Footer from "ehs/layout/Footer";
import PageSizeSelector from "ehs/layout/PageSizeSelector";
import Pagination from "ehs/layout/Pagination";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsLab } from "ehs/models/EhsLab";
import { EhsOptions } from "ehs/models/EhsOptions";
import { PageInfo, initPageInfo } from "ehs/models/PageInfo";
import { ReactSelectOption } from "ehs/models/ReactSelectOption";
import { SelectSearch } from "ehs/models/SearchLabInfo";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { checkBtnAuth, getBasicLoginUserInfo } from "ehs/utils/authUtil";
import { getLabTextObj } from "ehs/utils/langUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import Select from "react-select";
import styled from "styled-components";
interface Condition {
  keyword: string;
  currentPage: number;
  pageSize: number;
  areaId: string;
  orgType: string;
  unit: { [key: number]: string };
  queryOrgId: string;
  buildId: string;
  floor: string;
  houseNum: string;
  queryLabIds: string[];
  labAttrs: string[];
}

function LabList() {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const keywordInput = useRef<HTMLInputElement>(null)
  const [labList, setLabList] = useState<EhsLab[]>([]);
  const [searchSelectList, setSearchSelectList] = useState<SelectSearch[]>([]);
  const [localSearchResult, setLocalSearchResult] = useState<EhsLab[]>([]);
  const [selectedLabAttrOption, setSelectedLabAttrOption] = useState<ReactSelectOption[]>([]);
  const [labAttrOption, setLabAttrOption] = useState<ReactSelectOption[]>([]);
  const [optionsMap, setOptionsMap] = useState<{ [key: string]: EhsOptions }>({});
  const [localSearchKey, setLocalSearchKey] = useState("");
  const [isLinkSelect, setIsLinkSelect] = useState(true);
  const [showUnitCondition, setShowUnitCondition] = useState(false);
  const [showBuildCondition, setShowBuildCondition] = useState(false);
  const [showAdvCondition, setShowAdvCondition] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingBlock, setLoadingBlock] = useState(false);
  const initCondition = {
    keyword: "",
    currentPage: 1,
    pageSize: 50,
    areaId: "",
    orgType: "",
    unit: {},
    queryOrgId: "",
    buildId: "",
    floor: "",
    houseNum: "",
    queryLabIds: [],
    labAttrs: [],
  };
  const [condition, setCondition] = useState<Condition>(initCondition);
  const [pageInfo, setPageInfo] = useState<PageInfo>(initPageInfo);
  const [configMap, setConfigMap] = useState<{
    [configId: string]: EhsConfigParam;
  }>({}); // config對應
  const hasSetDefaultArea = useRef(false);
  const isAddLabRole = checkBtnAuth(BtnType.ADD_LAB)
  const { funcLabManageText, labNoText, labNameText, labAttribute, msgNoLabAttr } = getLabTextObj(loginUser, t);
  // const { state } = useLocation();
  // const { condition: prevCondition }: ({ condition: Condition }) = state || {};

  // useEffect(() => {
  //   if (prevCondition) {
  //     setCondition(prevCondition);
  //   }
  // }, [prevCondition])  

  useEffect(() => {
    if (loginUser) {
      fetchData();
    }
  }, [loginUser, condition.currentPage, condition.pageSize, i18n.language]);

  useEffect(() => {
    if (loginUser) {
      fetchSelectListData();
      fetchConfig();
      fetchOption();
    }
  }, [loginUser]);

  useEffect(() => {
    // 只在初始載入時設置預設區域
    if (loginUser?.userInfo?.areaId && !condition.areaId && !isArrayEmpty(searchSelectList) && !hasSetDefaultArea.current) {
      setCondition(prev => ({
        ...prev,
        areaId: loginUser.userInfo.areaId
      }));
      hasSetDefaultArea.current = true;
    }
  }, [loginUser, searchSelectList, condition.areaId]);

  useEffect(() => {
    setCondition({
      ...condition,
      labAttrs: selectedLabAttrOption?.map(item => String(item.value)) || []
    })

  }, [selectedLabAttrOption]);

  useEffect(() => {
    if (localSearchKey) {
      let resultList = labList.filter((data: EhsLab) => {
        return [data.areaName, data.buildName, data.labNo, data.labName, data.labStatusName].some(property => property && property.includes(localSearchKey));
      });
      setLocalSearchResult(resultList);
    }

  }, [localSearchKey])

  // 根據選項設定是否顯示搜尋條件
  useEffect(() => {
    if (!isArrayEmpty(Object.keys(optionsMap))) {
      const unitOption = optionsMap[OPTION_SHOW_SEARCH_UNIT];
      const buildOption = optionsMap[OPTION_SHOW_SEARCH_BUILDING];
      const advOption = optionsMap[OPTION_SHOW_SEARCH_ADVANCE];

      setShowUnitCondition(unitOption?.optionEnabled);
      setShowBuildCondition(buildOption?.optionEnabled);
      setShowAdvCondition(advOption?.optionEnabled);
    }
  }, [optionsMap]);

  const fetchData = (searchCondition?: Condition) => {
    setLoading(true)
    const targetCondition = searchCondition || condition;
    const { floor, houseNum, queryLabIds } = targetCondition;
    const finalCondition = {
      ...targetCondition,
      queryLabIds: queryLabIds ? (Array.isArray(queryLabIds) ? queryLabIds : [queryLabIds]) : [],
    };
    LabAPI.getLabList({
      ...getBasicLoginUserInfo(loginUser)!,
      ...finalCondition,
      labFloor: floor,
      labHousenum: houseNum,
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        setLabList(result.results);
        setPageInfo(result.pageinfo);
        setLoading(false)
      } else {
        if (result && result.message) {
          errorMsg(result.message)
        } else {
          errorMsg(t('text.error'))
        }
      }
    }).catch(err => {
      debugger;
      console.error(err)
    });
  };

  const fetchConfig = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser),
      configTypeList: [CONFIG_TYPE_LAB_STATUS]
    }).then(result => {
      if (isApiCallSuccess(result)) {
        setConfigMap(result.results.reduce((acc: {
          [configId: string]: EhsConfigParam;
        }, config: EhsConfigParam) => {
          acc[config.configId] = config;
          return acc;
        }, {}));
      }
    });
  };

  const fetchSelectListData = () => {
    SelectListAPI.getSelectLabSearchView({
      ...getBasicLoginUserInfo(loginUser)!,
    }).then((result) => {
      if (result) {
        setSearchSelectList(result.results);
      }
    });
    SelectListAPI.getSelectLabAttributes({
      ...getBasicLoginUserInfo(loginUser)!,
    }).then((result) => {
      if (result) {
        setLabAttrOption(result.results);
      }
    });
  };


  const fetchOption = () => {
    // 獲取搜尋條件選項配置，用於決定是否預設顯示搜尋條件
    const optionIds = [OPTION_SHOW_SEARCH_UNIT, OPTION_SHOW_SEARCH_BUILDING, OPTION_SHOW_SEARCH_ADVANCE];
    OptionAPI.getOptionListByIds({
      ...getBasicLoginUserInfo(loginUser),
      optionIdList: optionIds
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const options = result.results;
        const newOptionsMap: {
          [key: string]: EhsOptions;
        } = {};
        options.forEach((item: any) => {
          if (optionIds.includes(item.optionId)) {
            newOptionsMap[item.optionId] = item;
          }
        });
        setOptionsMap(newOptionsMap);
      }
    });
  };

  const getShowList = () => {
    if (!localSearchKey) {
      return labList || [];
    }
    return localSearchResult;
  };

  const clickSearch = () => {
    const searchCondition = {
      ...condition,
      keyword: keywordInput.current && keywordInput.current!.value ? keywordInput.current!.value.trim() : "",
      currentPage: 1,
    }
    setCondition(searchCondition);
    fetchData(searchCondition);
  }

  const onDeleteSuccess = () => {
    fetchData();
  }

  const handleChangeLabAttr = (selectedOption: any) => {
    setSelectedLabAttrOption(selectedOption);
  }

  return (
    <StlyedLabList $loading={loading}>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.system_manage_setting") },
                { label: funcLabManageText },
              ]}
            />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{funcLabManageText} </h1>
            {/* END page-header */}

            <div className="card">
              {isAddLabRole && <div className="card-body">
                {/* 新增建築 button */}
                <button
                  type="button"
                  className="btn btn-purple fs-5"
                  title={t("button.add")}
                  onClick={() => {
                    navigate("/" + AppPaths.manage.labAdd, { state: { mode: ActionMode.ADD, initLabId: "" } });
                  }}
                >
                  <i className="fas fa-plus"></i> {t("button.add")}
                </button></div>}
              <div className="card-body p-4">
                <SearchDropdownSwitch checked={isLinkSelect} onChangeChecked={setIsLinkSelect} onChangeCondition={() => setCondition(initCondition)} />
                <SearchConditionButton buttonType={SearchCondition.UNIT} onClick={() => { setShowUnitCondition((pre) => !pre) }} />
                <SearchConditionButton buttonType={SearchCondition.BUILDING} onClick={() => { setShowBuildCondition((pre) => !pre) }} />
                <SearchConditionButton buttonType={SearchCondition.ADVANCEd} onClick={() => { setShowAdvCondition((pre) => !pre) }} />
                <button type="button" className="btn btn-secondary ms-1 my-2" title={t('button.reset_search')} onClick={() => setCondition(initCondition)}><i className="fas fa-undo mx-1"></i>{t('button.reset_search')}</button>
                <div className="row my-3">
                  <SearchDivUnit dataList={searchSelectList} show={showUnitCondition} isLinkSelect={isLinkSelect}
                    condition={condition} setCondition={setCondition} showAll loginUser={loginUser} autoSetDefaultArea={false} />
                  <SearchDivBuilding dataList={searchSelectList} show={showBuildCondition} isLinkSelect={isLinkSelect} condition={condition} setCondition={setCondition} showAll />
                  <div className="row my-2">
                    <SearchDivLab dataList={searchSelectList} isLinkSelect={isLinkSelect} condition={condition} setCondition={setCondition} />
                    {showAdvCondition && <>
                      <div className="col-xl-3 d-flex align-items-center">
                        <label className="w-25">{labAttribute}</label>
                        <Select options={labAttrOption || []} isMulti isSearchable menuPosition="fixed" placeholder={t('text.all')} className="w-75"
                          onChange={handleChangeLabAttr} value={selectedLabAttrOption}
                          noOptionsMessage={() => msgNoLabAttr}
                        />
                      </div>
                      <div className="col-xl-3 d-flex align-items-center">
                        <label className="w-25">{t('text.search.keyword')}</label>
                        <input type="text" className="form-control form-control-lg" ref={keywordInput} />
                      </div>
                    </>
                    }
                    <div className="col-xl-3">
                      <button type="button" className="btn btn-primary mt-1" title={t('button.search.item')} onClick={clickSearch}><i className="fas fa-magnifying-glass"></i> {t('button.search.item')}</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="card pt-3">
              <div className="row topFunctionRow">
                <div className="col-sm-12 col-md-6 left">
                  <PageSizeSelector pageSize={condition.pageSize} setCondition={setCondition} />
                </div>
                <div className="col-sm-12 col-md-6 right">
                  <div className="dataTables_filter d-flex">
                    search:
                    <input
                      value={localSearchKey}
                      onChange={(e) => {
                        setLocalSearchKey(e.target.value);
                      }}
                      type="search"
                      className="form-control form-control-sm"
                      placeholder=""
                      aria-controls="data-table-default"
                    />
                  </div>
                </div>
              </div>
              <div className="card-body">
                {loading && <Loader />}
                <ExpandableRwdTable>
                  <div className="table-container">
                    <table
                      id="data-table-default"
                      className={
                        "table table-hover align-middle dt-responsive"
                      }
                    >
                      <thead className="text-center fs-4 fw-bold">
                        <tr>
                          <th>{t("table.title.item")} </th>
                          <th className='text-start responsive-header'>
                            {t("table.title.area.item")}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"areaName"}
                              setFunction={setLabList}
                            />
                          </th>
                          <th className='text-start responsive-header'>
                            {t("table.title.building.item")}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"buildName"}
                              setFunction={setLabList}
                            />
                          </th>
                          <th className='text-start responsive-header'>
                            {t("table.title.org.item")}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"orgNames"}
                              setFunction={setLabList}
                            />
                          </th>
                          <th className='text-start'>
                            {labNoText}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"labNo"}
                              setFunction={setLabList}
                            />
                          </th>
                          <th className='text-start'>
                            {labNameText}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"labName"}
                              setFunction={setLabList}
                            />
                          </th>
                          <th className='text-start responsive-header'> {t("table.title.status")}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"labStatus"}
                              setFunction={setLabList}
                            />
                          </th>
                          <th data-orderable="false" className='text-start'>{t("table.title.action")}</th>
                        </tr>
                      </thead>
                      <tbody className="text-center fs-5">
                        {!loading && getShowList() && !isArrayEmpty(getShowList()) ?
                          getShowList().map((data, idx) => {
                            return <Row key={data.labId} index={idx + 1} lab={data} condition={condition}
                              configMap={configMap}
                              setLoadingBlock={setLoadingBlock} onDeleteSuccess={onDeleteSuccess} />;
                          }) : (!loading && <NoDataRow />)}
                      </tbody>
                    </table>
                  </div>
                </ExpandableRwdTable>
                <Pagination pageInfo={pageInfo} setCondition={setCondition} />
              </div>
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </BlockUi>
    </StlyedLabList >
  );
}

const Row = (props: {
  index: number; lab: EhsLab
  condition: Condition;
  configMap: { [configId: string]: EhsConfigParam };
  setLoadingBlock: (block: boolean) => void;
  onDeleteSuccess: () => void;
}) => {
  const { isExpanded, toggleExpanded } = useExpandedRwdTableRow();
  const { index, lab, condition, configMap, setLoadingBlock, onDeleteSuccess } = props;
  const { labId, areaName, buildName, orgNames, labNo, labName, labStatus } = lab;
  const navigate = useNavigate();
  const location = useLocation();
  const { loginUser } = useLoginUser();
  const { t } = useTranslation();
  const { tableTitleLabNo, tableTitleLabName } = getLabTextObj(loginUser, t);
  const isEditLabRole = checkBtnAuth(BtnType.EDIT_LAB)
  const isDeleteLabRole = checkBtnAuth(BtnType.DELETE_LAB)
  const labStatusMap = Object.values(configMap)
    .filter((v) => v.configType === CONFIG_TYPE_LAB_STATUS)
    .reduce<Record<string, EhsConfigParam>>((acc, curr) => {
      acc[curr.configIvalue] = curr; // 使用 configIvalue 作為鍵，值為該物件
      return acc;
    }, {} as Record<string, EhsConfigParam>);

  const clickDetail = () => {
    navigate("/" + AppPaths.manage.labDetail, { state: { initLabId: labId } });
  };

  const clickEdit = () => {
    navigate("/" + AppPaths.manage.labEdit, {
      state: {
        mode: ActionMode.EDIT, initLabId: labId,
        condition: condition,
        fromPath: location.pathname
      }
    });
  };
  const clickDelete = () => {
    if (loginUser) {
      confirmMsg(t('message.confirm.delete'), t).then((value) => {
        if (value) {
          setLoadingBlock(true);
          LabAPI.deleteLab(
            {
              ...getBasicLoginUserInfo(loginUser)!,
              labId: labId,
            }
          ).then(result => {
            if (isApiCallSuccess(result)) {
              showSuccessToast(t('message.success'));
              onDeleteSuccess();
            } else {
              errorMsg(result.message);
            }
            setLoadingBlock(false);
          }).catch(err => {
            setLoadingBlock(false);
          })
        }
      });
    }
  };

  return (
    <>
      <tr>
        <td data-title={t("table.title.item")}>{index}</td>
        <td data-title={t("table.title.area.item")} className='text-start responsive-cell'>{areaName}</td>
        <td data-title={t("table.title.building.item")} className='text-start responsive-cell'>{buildName}</td>
        <td data-title={t("table.title.org.item")} className='text-start responsive-cell'><NamesSplitFragment names={orgNames} separator={ORG_SPLIT_FLAG} /></td>
        <td data-title={tableTitleLabNo} className='text-start'>{labNo}</td>
        <td data-title={tableTitleLabName} className='text-start'>{labName}</td>
        <td data-title={t("table.title.status")} className='text-start responsive-cell'>{labStatusMap[labStatus]?.configName} </td>
        <td data-title={t("table.title.action")} className='text-start action-cell'>
          <button type="button" className="btn btn-secondary m-1 fs-5" title={t("button.detail")} onClick={clickDetail}>
            <i className="fas fa-file-alt"></i>  {t("button.detail")}
          </button>
          {isEditLabRole && !isLabSigning(labStatus) && <button
            type="button"
            className="btn btn-warning m-1 fs-5"
            title={t("button.edit")}
            onClick={clickEdit}
          >
            <i className="fas fa-pen"></i> {t("button.edit")}
          </button>}
          {isDeleteLabRole && !isLabSigning(labStatus) && <button
            type="button"
            className="btn btn-danger m-1 fs-5"
            title={t("button.delete")}
            onClick={clickDelete}
          >
            <i className="fas fa-trash-can fa-lg"></i> {t("button.delete")}
          </button>}
          <ExpandRwdButton isExpanded={isExpanded} onClick={toggleExpanded} />
        </td>
      </tr>
      {isExpanded && (
        <tr className="expanded-row">
          <td colSpan={4} className="p-0">
            <div className="expanded-content">
              <table className="expanded-table">
                <tbody>
                  <tr>
                    <td className="expanded-label">{t("table.title.building.item")}</td>
                    <td className="expanded-value"> {buildName} </td>
                  </tr>
                  <tr>
                    <td className="expanded-label">{t("table.title.org.item")}</td>
                    <td className="expanded-value"> {<NamesSplitFragment names={orgNames} separator={ORG_SPLIT_FLAG} />} </td>
                  </tr>
                  <tr>
                    <td className="expanded-label">{t("table.title.status")}</td>
                    <td className="expanded-value"> {labStatusMap[labStatus]?.configName} </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </td>
        </tr>
      )}
    </>
  );
};

const StlyedLabList = styled.div<{ $loading?: boolean }>`
  padding-bottom:150px;

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }
  table {
    position:relative;
    min-height:${props => props.$loading ? "200px" : "auto"};
    thead {
      background:rgb(251, 205, 165);
    }
    th {
      text-align: center;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 100px;
        text-align:left;
        min-height:50px; // rwd後 td最小高度
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          
    padding-bottom: 10px;
    min-height: auto; /* 重置最小高度 */
    height: auto; /* 重置高度 */
    white-space: normal; /* 讓長標題能夠換行 */
    word-wrap: break-word; /* 在需要時強制斷詞 */
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
`;

export default LabList;
