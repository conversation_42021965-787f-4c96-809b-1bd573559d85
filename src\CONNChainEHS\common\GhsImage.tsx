// GhsImage.tsx

import React from 'react';
import { useTranslation } from 'react-i18next';

interface GhsImageProps {
    src: string;
    alt: string;
    title: string;
    className?: string;       // 可選的完整替換 className
    extraClassName?: string;  // 可選的額外 className
}

const GhsImage: React.FC<GhsImageProps> = ({ src, alt, title, className, extraClassName }) => {
    const { t } = useTranslation();
    // 基本的 className，如果提供了完整替換的 className，則使用完整替換的，否則使用默認的
    const baseClassName = className ?? "mb-2";

    // 最終的 className，如果提供了額外的 className，則將它加到 baseClassName 上
    const finalClassName = extraClassName ? `${baseClassName} ${extraClassName}` : baseClassName;

    return (
        <React.Fragment>
            {src ? (
                <img className={finalClassName} src={require(`../../assets/img/ghs/${src}`)} alt={alt} title={title} />
            ) : (
                <span>{t('text.image_not_found')}</span> // 提供替代內容以防止錯誤
            )}
        </React.Fragment>
    );
};

export default GhsImage;
