import { useEffect, useState, ChangeEvent, useMemo } from "react";
import { useTranslation } from 'react-i18next';
import styled from "styled-components";
import BlockUi from "@availity/block-ui";
import BlockuiMsg from "ehs/common/BlockuiMsg";
import BackButton from "ehs/common/button/BackButton";
import Breadcrumbs from "ehs/layout/Breadcrumbs";
import Footer from "ehs/layout/Footer";
import { AppPaths } from "config/app-paths";
import { useLocation, useNavigate } from 'react-router-dom';
import { EhsNews, initEhsNews } from "ehs/models/EhsNews";
import { CONFIG_TYPE_NEWS, CONFIG_TYPE_NEWS_STATUS, FILE_TYPE_NEWS, UPLOAD_ACCEPT_TYPE_IMAGE_PDF } from "ehs/constant/constants";
import useLoginUser from "ehs/hooks/useLoginUser";
import { ConfigAPI } from "api/configAPI";
import { NewsAPI } from "api/newsAPI";
import { checkTimeoutAction, getBasicLoginUserInfo, navigateToHome } from "ehs/utils/authUtil";
import { errorMsg } from "ehs/common/SwalMsg";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { ActionMode } from "../../../enums/ActionMode";
import { isApiCallSuccess } from "../../../utils/resultUtil";
import { showSuccessToast, showWarnToast } from "../../../common/Toast";
import { FileAPI } from "../../../../api/fileAPI";
import { EhsFile, initEhsFile } from "../../../models/EhsFile";
import FileUploader from "../../../common/FileUploader";
import { convertToFiles } from "../../../utils/fileUtil";

type newsTypeOption = {
  newsTypeList: EhsConfigParam[];
  newsStatusList: EhsConfigParam[];
}

const configTypeMap = {
  newsTypeList: CONFIG_TYPE_NEWS,
  newsStatusList: CONFIG_TYPE_NEWS_STATUS
}

const newsTypeInitOption = {
  newsTypeList: [],
  newsStatusList: [],
}

function NewsModify() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { loginUser } = useLoginUser();
  const { state } = useLocation();
  const [option, setOption] = useState<newsTypeOption>(newsTypeInitOption);
  const [loadingBlock, setLoadingBlock] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newsInfo, setNewsInfo] = useState<EhsNews>(initEhsNews);
  const [newsFileList, setNewsFileList] = useState<EhsFile[]>([]);
  const [newsFileMap, setNewsFileMap] = useState<{ [key: string]: EhsFile[] }>({});
  const [hasFetchedFile, sethasFetchedFile] = useState(false);
  const { mode, initNewsId }: ({ mode: ActionMode, initNewsId: string }) = state || {};
  const isAddMode = mode === ActionMode.ADD;
  const funcName = isAddMode ? t('text.add') : t('text.edit')
  const saveBtn = <button className="btn btn-success ms-4 fs-5" type="button" title={t('button.save')} onClick={() => saveNewsModify()}>
    <i className="fas fa-floppy-disk me-1" />{t('button.save')}
  </button>
  const memoizedNewsFileObj = useMemo(() => {
    const result: { [key: string]: File[] } = {};
    for (const key in newsFileMap) {
      result[key] = convertToFiles(newsFileMap[key]);
    }
    return result;
  }, [newsFileMap]);

  useEffect(() => {
    if (!isAddMode && !initNewsId) {
      navigateToHome(navigate);
      return;
    }
    if (loginUser) {
      fetchConfig();
      fetchData();
      if (!hasFetchedFile) {
        fetchNewsFile();
      }
    }
  }, [loginUser]);

  const fetchData = async () => {
    try {
      setLoadingBlock(true);
      setLoading(true);
      if (!isAddMode && !initNewsId) {
        navigateToHome(navigate);
        return;
      }
      if (initNewsId) {
        const result = await NewsAPI.getNewsById({
          ...getBasicLoginUserInfo(loginUser),
          newsId: initNewsId
        });
        if (isApiCallSuccess(result)) {
          const newsResults = result.results;
          setNewsInfo(newsResults);
        } else {
          errorMsg(result.message);
        }
      }
    } catch (err) {
      checkTimeoutAction(err, navigate, t);
      errorMsg(t('message.error'));
    } finally {
      setLoadingBlock(false);
      setLoading(false);
    }
  };

  const fetchConfig = async () => {
    try {
      const result = await ConfigAPI.getConfigByType({
        ...getBasicLoginUserInfo(loginUser),
        configTypeList: Object.values(configTypeMap),
      });
      if (isApiCallSuccess(result)) {
        const rs: EhsConfigParam[] = result.results;
        const newsOptions = (Object.keys(configTypeMap) as Array<keyof typeof configTypeMap>).reduce((acc, key) => {
          acc[key] = rs.filter((item: EhsConfigParam) => item.configType === configTypeMap[key]);
          return acc;
        }, {} as { [K in keyof typeof configTypeMap]: EhsConfigParam[] });
        setOption((prev) => ({
          ...prev,
          newsTypeList: newsOptions.newsTypeList,
          newsStatusList: newsOptions.newsStatusList,
        }));
      } else {
        errorMsg(result.message);
      }
    } catch (err) {
      navigateToHome(navigate);
      errorMsg(t('message.error'));
    }
  };

  const fetchNewsFile = async () => {
    const result = await FileAPI.getFileList({
      ...getBasicLoginUserInfo(loginUser),
      fileTypeList: [FILE_TYPE_NEWS],
      fileMappingIdList: [initNewsId]
    });
    if (isApiCallSuccess(result)) {
      const fileList: EhsFile[] = result.results;
      const fileMap: { [key: string]: EhsFile[] } = {};
      fileList.forEach((file) => {
        if (!fileMap[file.fileSubtype]) {
          fileMap[file.fileSubtype] = [];
        }
        fileMap[file.fileSubtype].push(file);
      });
      setNewsFileMap(fileMap);
      setNewsFileList(fileList);
      sethasFetchedFile(true);
    } else {
      errorMsg(result.message);
    }
  }

  const setChangeNewsFile = (file: File, status: boolean, id: string, fileType: string) => {
    if (status) {
      // 當 status 為 true 時，加入文件
      setNewsFileList(prevList => {
        // 確保不重複添加相同的文件
        if (prevList.some(f => f.fileId === id)) {
          return prevList;
        }
        return [...prevList, { ...initEhsFile, fileId: id, fileName: file.name, fileType: FILE_TYPE_NEWS, fileSubtype: fileType, fileObj: file }];
      });
    } else {
      // 當 status 為 false 時，移除文件
      setNewsFileList(prevList => prevList.filter(f => f.fileId !== id));
    }
  }

  const saveNewsModify = async () => {
    try {
      const { newsTitle, newsContent, newsType } = newsInfo;
      if (!newsTitle) {
        showWarnToast(t('message.required_news_title'));
        return;
      }
      if (!newsContent) {
        showWarnToast(t('message.required_news_content'));
        return;
      }
      if (!newsType) {
        showWarnToast(t('message.required_news_type'));
        return;
      }

      const updateNewsObj = {
        ...getBasicLoginUserInfo(loginUser),
        ehsNews: newsInfo,
        fileList: newsFileList,
      }

      setLoadingBlock(true);
      const result = isAddMode ? await NewsAPI.addNews(updateNewsObj) : await NewsAPI.editNews(updateNewsObj);
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
        navigate("/" + AppPaths.information.newsManageList);
      } else {
        errorMsg(result.message);
      }
    } catch (err) {
      checkTimeoutAction(err, navigate, t);
      errorMsg(t('message.error'));
    } finally {
      setLoadingBlock(false);
    }
  }

  return (
    <StyledNewsModify $loading={loading}>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.information.manage") },
                { label: t("func.information.news.manage_list"), path: AppPaths.information.newsManageList },
                { label: funcName },
              ]}
            />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{funcName}</h1>
            {/* END page-header */}
            <BackButton />
            <div className="card mt-3">
              <div className="card-body">
                <div className="row align-items-start mt-4">
                  <div className="col-xl-12 px-5 mb-2">
                    <label className="ms-1 h4">{t('text.announcement_title')}<span className="text-danger mx-1">*</span></label>
                    <div className="row d-flex align-items-center px-3">
                      <input
                        type="text"
                        className={`form-control my-1 input-text`}
                        data-parsley-required="true"
                        value={newsInfo.newsTitle || ""}
                        onChange={(e) => {
                          let newVal = e.target.value;
                          setNewsInfo({ ...newsInfo, newsTitle: newVal });
                        }}
                      />
                    </div>
                  </div>
                  <hr />
                  <div className="col-xl-12 px-5 mb-2">
                    <label className="ms-1 h4 mb-2 d-block">{t('text.announcement_type')}<span className="text-danger mx-1">*</span></label>
                    <div className="d-md-flex align-items-center px-3">
                      {option.newsTypeList.map((item) => {
                        const id = `news_${item.configId}`;
                        const isChecked = newsInfo.newsType === item.configId;
                        return (
                          <div className="form-check me-4" key={id}>
                            <input
                              id={id}
                              type="radio"
                              value={item.configId}
                              checked={isChecked}
                              onChange={(e: ChangeEvent<HTMLInputElement>) => {
                                let newVal = e.target.value;
                                setNewsInfo({ ...newsInfo, newsType: newVal });
                              }}
                              className={"form-check-input me-2"}
                            />
                            <label className="form-check-label" htmlFor={id}>
                              {item.configName}
                            </label>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                  <hr />
                  <div className="col-xl-12 px-5 mb-2">
                    <label className="ms-1 h4 mb-2 d-block">{t('text.announcement_status')}<span className="text-danger mx-1">*</span></label>
                    <div className="d-md-flex align-items-center px-3">
                      {option.newsStatusList.map((item) => {
                        const id = `news_${item.configIvalue}`;
                        const isChecked = newsInfo.newsStatus === item.configIvalue;
                        return (
                          <div className="form-check me-4" key={id}>
                            <input
                              id={id}
                              type="radio"
                              value={item.configIvalue}
                              checked={isChecked}
                              onChange={(e: ChangeEvent<HTMLInputElement>) => {
                                const newVal = parseInt(e.target.value, 10);
                                setNewsInfo({ ...newsInfo, newsStatus: newVal });
                              }}
                              className={"form-check-input me-2"}
                            />
                            <label className="form-check-label" htmlFor={id}>
                              {item.configName}
                            </label>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                  <hr />
                  <div className="col-xl-12 px-5 mb-2">
                    <label className="ms-1 h4">{t('text.announcement_content')}<span className="text-danger mx-1">*</span></label>
                    <div className="row d-flex align-items-center px-3">
                      <textarea
                        id="content"
                        name="content"
                        value={newsInfo.newsContent || ""}
                        className="form-control mt-1 block w-full p-2 border rounded-md input-text"
                        rows={20}
                        required
                        onChange={(e) => {
                          let newVal = e.target.value;
                          setNewsInfo({ ...newsInfo, newsContent: newVal });
                        }}
                      />
                    </div>
                  </div>
                  <hr />

                  <div className="col-xl-12 px-5 mb-2">
                    <label className="ms-1 h4">{t('text.announcement_files')}</label>
                    <div className="row d-flex align-items-center px-3">
                      <FileUploader
                        key={'labfile-uploader' + FILE_TYPE_NEWS}
                        fileInfo={{
                          ...initEhsFile,
                        }}
                        accept={UPLOAD_ACCEPT_TYPE_IMAGE_PDF}
                        initStrKey={FILE_TYPE_NEWS}
                        setFile={(file, status, id) => setChangeNewsFile(file, status, id, FILE_TYPE_NEWS)}
                        initialFiles={memoizedNewsFileObj[FILE_TYPE_NEWS]}
                        initialFileInfos={newsFileMap[FILE_TYPE_NEWS]}
                      />
                    </div>
                  </div>
                  <div className="text-center mt-3 mb-5 pb-4">{saveBtn}</div>
                </div>
              </div>
            </div>
            {/* BEGIN #footer */}
            <Footer />
            {/* END #footer */}
          </div>
        </div>
      </BlockUi>
    </StyledNewsModify>
  );
}

const StyledNewsModify = styled.div<{ $loading?: boolean }>`
  .input-text {
    font-size: 1.0rem;
  }
`

export default NewsModify;