/**
 * 實驗室狀態列舉
 */
export enum LabStatus {
    /** 不運作 */
    DISABLED = 0,

    /** 運作中 */
    ENABLED = 1,

    /** 簽核中 */
    SIGNING = 2,

    /** 已刪除 */
    DELETED = 3,

    /** 未申請運作 */
    OPER_NOT_APPLY = 4
}

// 提供狀態檢查的 helper functions
export const isLabEnabled = (status: LabStatus): boolean => status === LabStatus.ENABLED;
export const isLabDisabled = (status: LabStatus): boolean => status === LabStatus.DISABLED;
export const isLabSigning = (status: LabStatus): boolean => status === LabStatus.SIGNING;
export const isLabDeleted = (status: LabStatus): boolean => status === LabStatus.DELETED;
export const isLabNotApplied = (status: LabStatus): boolean => status === LabStatus.OPER_NOT_APPLY; 