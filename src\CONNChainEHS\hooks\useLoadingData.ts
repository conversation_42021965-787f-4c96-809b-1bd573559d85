import { useState } from 'react';

/**
 * useLoadingData Hook
 * 
 * 封裝數據加載時的 loading 狀態管理邏輯
 * 
 * @returns loading 狀態和控制方法
 */
export default function useLoadingData() {
    const [loading, setLoading] = useState(false);

    /**
     * 執行數據加載，並自動處理 loading 狀態
     * 
     * @param loadFunc 加載數據的異步函數
     * @returns 加載數據函數的結果
     */
    const executeWithLoading = async <T,>(loadFunc: () => Promise<T>): Promise<T> => {
        setLoading(true);
        try {
            return await loadFunc();
        } finally {
            setLoading(false);
        }
    };

    return {
        loading,
        setLoading,
        executeWithLoading,
    };
} 