import { ORG_SPLIT_FLAG } from "ehs/constant/constants";
import useLoginUser from "ehs/hooks/useLoginUser";
import { ReactSelectOption } from "ehs/models/ReactSelectOption";
import { SelectSearch } from "ehs/models/SearchLabInfo";
import { filterSelectLabByCondition, isArrayEmpty } from "ehs/utils/arrayUtil";
import { getLabTextObj } from "ehs/utils/langUtil";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Select from "react-select";

interface SearchDivLabProps {
    dataList: Array<SelectSearch>
    isLinkSelect: boolean;
    condition?: any;
    setCondition?: React.Dispatch<React.SetStateAction<any>>;
}

interface Option extends ReactSelectOption {
    item: SelectSearch | null;
}

const SearchDivLab = ({ dataList = [], isLinkSelect, condition, setCondition }: SearchDivLabProps) => {
    const { t } = useTranslation();
    const { loginUser } = useLoginUser();
    const [selectedOption, setSelectedOption] = useState<Option[]>([]);
    const { queryLabIds = [] } = condition;
    const labList = filterSelectLabByCondition(dataList, isLinkSelect, condition) || [];
    const { labText, msgNoLabOption } = getLabTextObj(loginUser, t);
    const options: Option[] = labList?.map(item => ({
        value: item.labId,
        label: item.labNo + ' ' + item.labName,
        item: item
    }));

    const allOptionValue = "all"
    options.unshift({
        value: allOptionValue,
        label: t('text.all'),
        item: null
    });

    //實驗室排序
    options.sort((a, b) => {
        if (a.value === allOptionValue) {
            return -1;
        }
        // 如果 b 的 label 是特殊标签，则将其排在最前面
        if (b.value === allOptionValue) {
            return 1;
        }

        if (a.label < b.label) {
            return -1;
        }
        if (a.label > b.label) {
            return 1;
        }
        return 0;
    });


    useEffect(() => {
        if (isArrayEmpty(queryLabIds)) {
            setSelectedOption([]); // 如果 queryLabIds 是空的，则直接设置为 
        } else {
            const matchingOption = options.filter(option => queryLabIds.includes(option.value));
            if (!isArrayEmpty(matchingOption)) {
                setSelectedOption(matchingOption); // 将匹配的选项数组设置为 selectedOption
            } else {
                setSelectedOption([]); // 如果没有找到匹配项，则设置为 
            }
        }
    }, [queryLabIds]);


    useEffect(() => {
        if (isLinkSelect && selectedOption && Array.isArray(selectedOption) && selectedOption.length > 1) {
            setSelectedOption([selectedOption[0]]);
            if (setCondition) {
                setCondition((prev: any) => ({
                    ...prev,
                    queryLabIds: [selectedOption[0].item?.labId],
                }));
            }
        }
    }, [isLinkSelect, selectedOption])

    const handleChange = (selectedOption: any) => {
        if (isLinkSelect) {
            if (selectedOption.length > 1) {
                // 如果是連動且選擇了多個選項，則只保留第一個選項
                setSelectedOption(selectedOption[0]);
                if (setCondition) {
                    setCondition((prev: any) => ({
                        ...prev,
                        queryLabIds: [selectedOption[0].item.labId],
                    }));
                }
            } else {
                setSelectedOption(selectedOption); // 單選時直接設置選中值
                const selectLabId = selectedOption.item?.labId || [];
                if (setCondition) {
                    setCondition((prev: any) => ({
                        ...prev,
                        queryLabIds: selectLabId,
                    }));
                }
            }
        } else {
            //多選 非連動
            setSelectedOption(selectedOption); // 直接設置選中值
            const labIds = selectedOption.map((option: any) => option.item.labId);
            if (setCondition) {
                setCondition((prev: any) => ({
                    ...prev,
                    queryLabIds: labIds,
                }));
            }
        }

        // 連動時 選擇實驗室會直接帶入其他條件
        if (isLinkSelect && setCondition) {
            const labItem = selectedOption.item;
            if (labItem) {
                const orgArray = labItem.orgIds ? labItem.orgIds.split(ORG_SPLIT_FLAG) : []; // 將逗號分隔的字符串轉換為數組
                const newUnit: { [key: number]: string } = {}; // 創建一個新的 unit 對象

                // 遍歷 orgArray 數組，將每個元素添加到 newUnit 對象中
                orgArray.forEach((org: string, index: number) => {
                    newUnit[index + 1] = org; // 鍵為索引加1，值為 org
                });

                setCondition((prevCondition: any) => ({
                    ...prevCondition,
                    areaId: labItem.areaId || "",
                    orgType: labItem.orgType || "",
                    buildId: labItem.buildId || "",
                    floor: labItem.labFloor || "",
                    houseNum: labItem.labHouseNum || "",
                    unit: newUnit
                }));
            }
        }
    }

    return (
        <div className="col-xl-3 d-flex align-items-center">
            <label className="w-25">{labText}</label>
            <Select options={options} isMulti={!isLinkSelect} isSearchable menuPosition="fixed" placeholder={t('text.all')} className="w-75 ms-1" onChange={handleChange} value={selectedOption}
                noOptionsMessage={() => msgNoLabOption}
            />
        </div>
    );
};

export default SearchDivLab;
