export enum PurchaseStatus {
  SIGNING = 1, // 簽核中
  PURCHASING = 2, // 採購中
  SHIPPING = 3, // 廠商出貨中
  PARTIAL_ARRIVAL = 4, // 部分到貨
  PENDING_INSPECTION = 5, // 待驗貨
  PARTIAL_INSPECTION = 6, // 部分驗貨
  INSPECTION_COMPLETE = 7, // 驗貨完成
  REJECTED = 8, // 不通過
  RETURNED = 9, // 退貨
  CANCELLED = 10, // 取消
  CANCELLED_BY_VENDOR = 11, // 供應商取消採購
  RETURN_MODIFICATIONS = 12, // 退回修改
}

// 提供從數字值到 enum 的轉換方法
export const fromValuePurchaseStatus = (value: number): PurchaseStatus => {
  switch (value) {
    case 1:
      return PurchaseStatus.SIGNING;
    case 2:
      return PurchaseStatus.PURCHASING;
    case 3:
      return PurchaseStatus.SHIPPING;
    case 4:
      return PurchaseStatus.PARTIAL_ARRIVAL;
    case 5:
      return PurchaseStatus.PENDING_INSPECTION;
    case 6:
      return PurchaseStatus.PARTIAL_INSPECTION;
    case 7:
      return PurchaseStatus.INSPECTION_COMPLETE;
    case 8:
      return PurchaseStatus.REJECTED;
    case 9:
      return PurchaseStatus.RETURNED;
    case 10:
      return PurchaseStatus.CANCELLED;
    case 11:
      return PurchaseStatus.CANCELLED_BY_VENDOR;
    case 12:
      return PurchaseStatus.RETURN_MODIFICATIONS;
    default:
      throw new Error(`Unknown status value: ${value}`);
  }
};

// 提供從 enum 到數字值的轉換方法
export const toValuePurchaseStatus = (status: PurchaseStatus): number => {
  return status;
};

// 使用範例
// const statusValue = 2;
// const statusEnum = fromValuePurchaseStatus(statusValue);
// console.log(`Status enum: ${PurchaseStatus[statusEnum]}`); // Output: Status enum: PURCHASING

// const enumToValue = toValuePurchaseStatus(PurchaseStatus.SHIPPING);
// console.log(`Status value: ${enumToValue}`); // Output: Status value: 3
