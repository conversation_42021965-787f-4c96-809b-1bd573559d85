import { ChemicalAP<PERSON> } from "api/chemicalAPI";
import { LabAPI } from "api/labAPI";
import { SelectListAPI } from "api/selectListAPI";
import SelectOrg from "ehs/common/SelectOrg";
import { confirmMsg, errorMsg } from "ehs/common/SwalMsg";
import { LAB_STATUS_CAN_OPERATION } from "ehs/constant/constants";
import useLoginUser from "ehs/hooks/useLoginUser";
import { EhsLab } from "ehs/models/EhsLab";
import { EhsOrg } from "ehs/models/EhsOrg";
import { EhsOrgLevel } from "ehs/models/EhsOrgLevel";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { getBasicLoginUserInfo } from "ehs/utils/authUtil";
import { getLabTextObj } from "ehs/utils/langUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

interface LabPlaceModifyFormData {
    modifyNote: string;
    newLabId?: string;
}

interface ChemLabPlaceModifyProps {
    onClose: () => void;
    onActionSuccess: () => void;
    setLoadingBlock: (block: boolean) => void;
    currentLabId: string;
    inventoryIdList: string[];
    isShow: boolean;
}

export default function ChemLabPlaceModify({
    onClose, onActionSuccess, setLoadingBlock, isShow, currentLabId, inventoryIdList
}: ChemLabPlaceModifyProps) {
    const { t } = useTranslation();
    const { loginUser } = useLoginUser();
    const { chemChangeLabText, searchLabTitle, labNoText, labNameText, labLocation,
        labSelected, msgNoChooseLab, msgChemTransferLab, msgLabDisabledOper } = getLabTextObj(loginUser, t);
    const [orgLevelList, setOrgLevelList] = useState<EhsOrgLevel[]>([]);
    const [orgList, setOrgList] = useState<EhsOrg[]>([]);
    const [selectOrgIds, setSelectOrgIds] = useState<Record<string, string>>({});
    const [searchKeyword, setSearchKeyword] = useState<string>('');
    const [labPlaces, setLabPlaces] = useState<EhsLab[]>([]);
    const [selectedLab, setSelectedLab] = useState<EhsLab | null>(null);
    const [hasSearched, setHasSearched] = useState<boolean>(false);

    useEffect(() => {
        if (isShow) {
            fetchOrgData();
        }
    }, [isShow])

    const getDefaultValues = () => ({
        modifyNote: ''
    });

    const { register, handleSubmit, formState: { errors }, setValue, watch, reset } = useForm<LabPlaceModifyFormData>({
        defaultValues: getDefaultValues(),
        resolver: (values) => {
            const errors: any = {};

            if (!values.newLabId) {
                errors.newLabId = {
                    type: 'required',
                    message: msgNoChooseLab
                };
            }

            return {
                values,
                errors: !isArrayEmpty(Object.keys(errors)) ? errors : {}
            };
        }
    });

    const resetForm = () => {
        reset(getDefaultValues());
    };

    const clickClose = () => {
        resetForm();
        onClose();
    }

    const fetchOrgData = () => {
        SelectListAPI.getSelectOrgList({
            ...getBasicLoginUserInfo(loginUser),
        }).then(result => {
            if (isApiCallSuccess(result)) {
                const orgData = result.results;
                setOrgLevelList(orgData.orgLevelList);
                setOrgList(orgData.orgList);

                if (orgData.orgLevelList.length > 0) {
                    const firstLevel = orgData.orgLevelList[0].orglvLevel;
                    setSelectOrgIds({ [firstLevel]: '' });
                }
            }
        })
    }

    const fetchLabPlaces = async () => {
        setHasSearched(true);
        const maxOrgLevelKey = Object.keys(selectOrgIds)
            .filter(key => selectOrgIds[key])
            .reduce((maxKey, key) => key > maxKey ? key : maxKey, "-1");
        const selectOrgid = selectOrgIds[maxOrgLevelKey] || "";
        const res = await LabAPI.getChemicalLabList({
            ...getBasicLoginUserInfo(loginUser)!,
            queryOrgId: selectOrgid,
            keyword: searchKeyword
        });
        if (isApiCallSuccess(res)) {
            const labPlaces = res.results.filter((lab: EhsLab) => lab.labId !== currentLabId);
            setLabPlaces(labPlaces);
        }
    }

    const onSubmit = handleSubmit(async (data) => {
        const confirmed = await confirmMsg(msgChemTransferLab, t);
        if (!confirmed) return;
        setLoadingBlock(true);
        try {
            const requestData = {
                ...getBasicLoginUserInfo(loginUser),
                currentLabId: currentLabId,
                newLabId: data.newLabId,
                modifyNote: data.modifyNote,
                inventoryIdList: inventoryIdList
            };

            const res = await ChemicalAPI.updateChemicalInventoryTransfer(requestData);
            if (isApiCallSuccess(res)) {
                onActionSuccess();
                resetForm();
            } else {
                errorMsg(t('message.error'));
            }
        } catch (error) {
            errorMsg(t('message.error'));
            console.error(error);
        } finally {
            setLoadingBlock(false);
        }
    });

    const handleSearch = () => {
        setHasSearched(true);
        fetchLabPlaces();
    };

    const handleSelectLab = (lab: EhsLab) => {
        setSelectedLab(lab);
        setValue('newLabId', lab.labId);
    };

    return (
        <StyledChemLabPlaceModify>
            <form onSubmit={onSubmit}>
                <div className="ChemLabPlaceModify">
                    <div className="ChemLabPlaceModify-header">
                        <h4 className="modal-title">{chemChangeLabText}</h4>
                        <button
                            type="button"
                            className="btn-close"
                            onClick={clickClose}
                        ></button>
                    </div>
                    <div className="ChemLabPlaceModify-body">
                        <div className="row g-3 mb-3">
                            {orgLevelList
                                .filter(orglv => {
                                    const level = orglv.orglvLevel;
                                    const isFirstLevel = level === 1;
                                    const parentSelected = selectOrgIds[level - 1];
                                    return isFirstLevel || parentSelected;
                                })
                                .map((orglv) => (
                                    <div className="col-12 col-sm-6 col-md-4 col-xl-3" key={'orglv-container-' + orglv.orglvId}>
                                        <SelectOrg
                                            key={'orglv' + orglv.orglvId}
                                            className="d-flex align-items-center"
                                            labelClassName="col-4"
                                            selectClassName="col-8"
                                            orglv={orglv}
                                            orgList={orgList}
                                            selectOrgIds={selectOrgIds}
                                            setSelectOrgIds={setSelectOrgIds}
                                            defaultOptionType={1}
                                        />
                                    </div>
                                ))}

                            <div className="col-12 col-sm-6 col-md-4 col-xl-3">
                                <div className="d-flex align-items-center">
                                    <label className="col-4">{t('text.search.keyword')}</label>
                                    <div className="col-8">
                                        <input
                                            type="text"
                                            className="form-control"
                                            value={searchKeyword}
                                            onChange={(e) => {
                                                setSearchKeyword(e.target.value);
                                            }}
                                            placeholder={labNoText + ' / ' + labNameText}
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className="col-12 col-sm-6 col-md-4 col-xl-3">
                                <div className="d-flex justify-content-center">
                                    <button
                                        type="button"
                                        className="btn btn-primary w-75"
                                        onClick={handleSearch}
                                    >
                                        <i className="fas fa-magnifying-glass me-1" />
                                        {t('text.search.item')}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="row justify-content-center align-items-center my-3">
                            <div className="col-11">
                                <h5 className="mb-1 mx-2 mt-2">
                                    {searchLabTitle}
                                </h5>
                                <div className="table-responsive">
                                    {!hasSearched ? (
                                        <div className="text-center py-4 text-secondary">
                                            <i className="fas fa-search fa-2x mb-2"></i>
                                            <p>{t('message.please_search')}</p>
                                        </div>
                                    ) : isArrayEmpty(labPlaces) ? (
                                        <div className="text-center py-4 text-secondary">
                                            <i className="fas fa-info-circle fa-2x mb-2"></i>
                                            <p>{t('message.no_data')}</p>
                                        </div>
                                    ) : (
                                        <div className="table-container">
                                            <table className="table">
                                                <thead>
                                                    <tr>
                                                        <th className="text-start">{labNoText}</th>
                                                        <th className="text-start">{labLocation}</th>
                                                        <th className="text-start">{labNameText}</th>
                                                        <th className="text-start">{t('table.title.action')}</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {labPlaces.map((lab) => {
                                                        const isSelected = selectedLab?.labId === lab.labId;
                                                        const isLabCanOperation = LAB_STATUS_CAN_OPERATION.includes(lab.labStatus);
                                                        return (
                                                            <tr key={lab.labId} className={isSelected ? 'table-primary' : ''}>
                                                                <td>{lab.labNo}</td>
                                                                <td>{`${lab.buildName} ${lab.labFloor} ${lab.labHousenum || " "}`}</td>
                                                                <td>{lab.labName}</td>
                                                                <td>
                                                                    {isLabCanOperation ? (
                                                                        <button
                                                                            type="button"
                                                                            className={`btn btn-sm ${isSelected ? 'btn-primary' : 'btn-outline-primary'}`}
                                                                            title={isSelected ? t('button.selected') : t('button.choose')}
                                                                            onClick={() => handleSelectLab(lab)}
                                                                        >
                                                                            {isSelected
                                                                                ? <><i className="fas fa-check me-1" />{t('button.selected')}</>
                                                                                : t('button.choose')
                                                                            }
                                                                        </button>
                                                                    ) : (<span className="text-danger">{msgLabDisabledOper}</span>)}
                                                                </td>
                                                            </tr>
                                                        )
                                                    })}
                                                </tbody>
                                            </table>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {!selectedLab && errors.newLabId && (
                            <div className="row justify-content-center align-items-center my-3">
                                <div className="col-11">
                                    <div className="alert alert-danger">
                                        {errors.newLabId.message}
                                    </div>
                                </div>
                            </div>
                        )}

                        {selectedLab && (
                            <div className="row justify-content-center align-items-center my-3">
                                <div className="col-11">
                                    <div className="alert alert-info">
                                        <strong>{labSelected}：</strong>
                                        {`${selectedLab.labNo} - ${selectedLab.labName} (${selectedLab.buildName} ${selectedLab.labFloor} ${selectedLab.labHousenum})`}
                                    </div>
                                </div>
                            </div>
                        )}

                        <div className="row justify-content-center align-items-center my-3">
                            <div className="col-11 mb-4">
                                <label className="fw-bold">{t('text.note')}</label>
                                <textarea
                                    className="form-control"
                                    value={watch('modifyNote')}
                                    onChange={e => setValue('modifyNote', e.target.value)}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="ChemLabPlaceModify-footer">
                        <button type="button" className="btn btn-white" onClick={clickClose}>
                            <i className="fas fa-close" /> {t("button.close")}
                        </button>
                        <button type="submit" className="btn btn-success ms-3">
                            <i className="fas fa-exchange-alt me-1" /> {chemChangeLabText}
                        </button>
                    </div>
                </div>
            </form>
        </StyledChemLabPlaceModify>
    );
}

const StyledChemLabPlaceModify = styled.div`
  background: white;

    .table-responsive {
        max-height: 400px; /* 設定最大高度 */
        overflow-y: auto; /* 超出時顯示垂直滾動條 */
    }

  .download-button:hover {
    text-decoration: underline; /* 滑鼠懸停時顯示底線 */
    }

  .chem-info-div { 
      overflow-y: auto;
      height: 675px;
  }
  
  label{
    user-select: none;
  }

  .ChemLabPlaceModify-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  .ChemLabPlaceModify-body {
    padding: 15px;
  }
  .ChemLabPlaceModify-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-purple {
      margin-left: 10px;
    }
  }
   
  @media (min-width: 992px) {
    width: 1000px;
  }
  
  @media (max-width: 600px){
    .chem-info-div { 
        min-height: 0; 
    }
    
    .ChemLabPlaceModify-footer {
        border-top: 1px solid #ced4da;
        padding: 15px;
        margin-top: 140px; 
        justify-content: center;
        .btn.btn-purple {
        margin-left: 10px;
        position: relative;  
        }
    }
  }
`;
