import React, { useState, ChangeEvent, useEffect, useRef } from 'react';

interface NumberInputProps {
    className?: string;
    name?: string;
    value?: number | undefined;
    placeholder?: string;
    disabled?: boolean;
    maxLength?: number;
    onBlur?: (value: number | undefined) => void;
    onChange?: (event: ChangeEvent<HTMLInputElement>, num: number) => void;
    onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
    minValue?: number | undefined;
    maxValue?: number | undefined;
    decimalPlaces?: number | undefined;
    allowNegative?: boolean;
    allowEmptyUndefine?: boolean;
    initKey?: string;
}

const InputNumFloat: React.FC<NumberInputProps> = ({
    className,
    name,
    value,
    placeholder,
    disabled,
    maxLength,
    onBlur,
    onChange,
    onKeyDown,
    minValue,
    maxValue,
    decimalPlaces,
    allowNegative,
    allowEmptyUndefine: alliowEmptyUndefine,
    initKey,
}) => {
    const objRef = useRef<HTMLInputElement>(null);
    const [num, setNum] = useState<number>(value ?? 0);
    const [inputValue, setInputValue] = useState<string>(value?.toString() ?? '');

    useEffect(() => {
        setNum(value ?? 0);
        setInputValue(value?.toString() ?? '');
    }, [value, initKey]);

    const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
        const newInputValue = event.target.value;

        if (!isValidInput(newInputValue)) {
            return;
        }

        setInputValue(newInputValue);
        const parsedValue = parseFloat(newInputValue);

        if (!isNaN(parsedValue) && (!newInputValue.includes('-') || allowNegative)) {
            if ((maxValue === undefined || parsedValue <= maxValue) &&
                (minValue === undefined || parsedValue >= minValue)) {
                setNum(parsedValue);
                if (onChange) {
                    onChange(event, parsedValue);
                }
                return;
            } else if (maxValue && parsedValue > maxValue) {
                setNum(maxValue);
                setInputValue(maxValue?.toString());
                if (onChange) {
                    onChange(event, parsedValue);
                }
                return;
            }
        } else if (isNaN(parsedValue) && alliowEmptyUndefine) {
            if (onChange) {
                onChange(event, parsedValue);
            }
            return;
        }

        setNum(value ?? 0);
    };

    const handleBlur = () => {
        const parsedValue = parseFloat(inputValue);

        if (onBlur && !isNaN(parsedValue)) {
            // if (minValue && parsedValue < minValue) {
            //     const minValueString = minValue.toString();
            //     const minDecimalPlaces = minValueString.includes('.') ? minValueString.split('.')[1].length : 0;
            //     setNum(minDecimalPlaces); // 將 state 中的數字設為最小值
            //     setInputValue(minDecimalPlaces.toString()); // 更新 input 的值為最小值的字串表示
            //     onBlur(minValue);
            //     console.log("🚀 ~ handleBlur ~ minValue:", minValue)
            // } else {
            onBlur(parsedValue);
            // }
        } else if (onBlur) {
            if (alliowEmptyUndefine && objRef.current?.value === '') {
                onBlur(undefined);
            } else {
                onBlur(value ?? 0);
            }
        }
    };

    const isValidInput = (value: string): boolean => {
        // 檢查是否包含兩個以上的連續負號或小數點
        const invalidPattern = /--|[.]{2,}/;
        if (invalidPattern.test(value)) {
            return false;
        }

        // 如果允許負數，檢查是否以負號開頭
        if (allowNegative && value.startsWith('-')) {
            //避免打-負號後面接.
            if (value.length === 2 && value[1] === '.') {
                return false;
            }
            if (value.length === 3 && value[1] === '0' && value[2] !== '.') {
                // 檢查是否以負零開頭，且第二個字符不是小數點
                return false;
            }
            // 檢查是否存在多個負號
            if (value.lastIndexOf('-') > 0) {
                return false;
            }
        } else if (value.includes('-')) {
            // 如果不允許負數，則檢查是否包含負號
            return false;
        }

        // 檢查是否以小數點開頭
        if (value.startsWith('.')) {
            return false;
        }

        // 檢查是否有多個小數點
        if ((value.match(/\./g) || []).length > 1) {
            return false;
        }

        // 檢查是否以零開頭，且第二個字符不是小數點
        if (value.length > 1 && value[0] === '0' && value[1] !== '.') {
            return false;
        }

        // 檢查是否包含除了數字和小數點之外的其他字符
        const nonDigitDotPattern = allowNegative ? /[^0-9.-]/ : /[^0-9.]/;
        if (nonDigitDotPattern.test(value)) {
            return false;
        }

        //是否超過限制小數點位數
        if (decimalPlaces) {
            const decimalPart = value.split('.')[1];
            if (decimalPart && decimalPart.length > decimalPlaces) {
                return false;
            }
        }


        return true;
    };

    return (
        <input
            key={'InputNumFloat' + initKey}
            type="text"
            ref={objRef}
            placeholder={placeholder}
            disabled={disabled}
            maxLength={maxLength}
            name={name}
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleBlur}
            onKeyDown={onKeyDown}
            className={className || "form-control"}
        />
    );
};

export default InputNumFloat;
