import InputNumFloat from "ehs/common/input/InputNumFloat";
import { CHEMICAL_WEIGHT_DECIMAL_PLACES, CHEMICAL_WEIGHT_MAX, CHEMICAL_WEIGHT_MIN } from "ehs/constant/constants";
import { isEnterKey } from "ehs/utils/stringUtil";
import { useTranslation } from "react-i18next";
import styled from "styled-components";


function EditWeight(props: {
  onClose: () => void,
  weights: (number | null)[]
  setWeights: React.Dispatch<React.SetStateAction<(number | null)[]>>
}) {
  const { onClose, weights = [], setWeights } = props;
  const { t } = useTranslation();

  const handleBlur = (index: number, value: number | undefined) => {
    const newWeights = [...weights];
    newWeights[index] = !value || value === 0 ? null : value;
    setWeights(newWeights);
  };

  const handleKeyDown = (e: any, index: number) => {
    if (isEnterKey(e)) {
      const nextIndex = index + 1;
      const nextInput = document.querySelectorAll<HTMLInputElement>('.input-weight')[nextIndex < weights.length ? nextIndex : 0];
      nextInput?.focus();
    }
  };

  const copyFirstWeight = () => {
    const firstWeight = weights[0];
    if (firstWeight !== null) {
      // 使用 Array.from 創建新的 weights 陣列
      const newWeights = Array.from({ length: weights.length }, () => firstWeight);
      setWeights(newWeights);
    }
  };

  const clearWeights = () => {
    const newWeights = Array.from({ length: weights.length }, () => null);
    setWeights(newWeights);
  };
  const filledWeightCount = weights.filter(weight => weight !== null).length;

  const renderCopyFirstWeightButton = (hasMoreThanTenWeights?: boolean) => {
    return (!hasMoreThanTenWeights || (hasMoreThanTenWeights && weights.length >= 10)) && weights[0] !== null && (
      <button
        className="btn btn-primary my-3"
        title={t('button.copy_first_weight')}
        onClick={copyFirstWeight}
      >
        {t('button.copy_first_weight')}
      </button>
    );
  };

  return (
    <StlyedEditWeight >
      <div className="modifyLabUser">
        <div className="modal-header">
          <h4 className="modal-title">{t('button.edit')}</h4>
          <button type="button" className="btn-close" aria-hidden="true" onClick={onClose}></button>
        </div>
        <div className="modal-body">
          {<div className="text-center">
            {renderCopyFirstWeightButton(true)}
            {weights.map((weight, index) => (
              <div key={'weight-' + index} className="d-flex align-items-center justify-content-center">
                <p className="pt-3"><span className="text-danger">*</span>{t('text.bottle_num', { num: index + 1 })} ：</p>
                <InputNumFloat className="form-control item-width-30 me-1 input-weight" minValue={CHEMICAL_WEIGHT_MIN} maxValue={CHEMICAL_WEIGHT_MAX} maxLength={7 + CHEMICAL_WEIGHT_DECIMAL_PLACES}
                  decimalPlaces={CHEMICAL_WEIGHT_DECIMAL_PLACES} onBlur={(num) => handleBlur(index, num)}
                  onKeyDown={(e) => handleKeyDown(e, index)}
                  placeholder={t('message.chemical.no_product_weight')}
                  allowEmptyUndefine={true}
                  value={weight !== null ? weight : undefined}
                /> kg
              </div>
            ))}
            {renderCopyFirstWeightButton()}
            {filledWeightCount > 1 && <button className="btn btn-danger mx-1" title={t('button.clear_weights')} onClick={clearWeights}>{t('button.clear_weights')}</button>}
          </div>}
        </div>
        <div className="modal-footer">
          <button className="btn btn-success" aria-hidden="true" onClick={onClose} title={t("button.finish")}>
            <i className="fas fa-check me-1" />
            {t("button.finish")}
          </button>
        </div>
      </div>
    </StlyedEditWeight>
  );
}


const StlyedEditWeight = styled.div`
  font-size: 1.2em;
  background: white;
  width: 500px;
  .w-35{
    width: 35%;
  }
  .modal-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  .modal-body {
    padding-top: 15px; 
  }
  .modal-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end; 
  }
`;

export default EditWeight;
