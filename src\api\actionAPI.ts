import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const ActionAPI = {
  getActionList: async (parms: BaseParams) => {
    return apiRequest(getApiAddress + "action/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getActionLoginList: async (
    parms: BaseParams
  ) => {
    return apiRequest(getApiAddress + "action/login/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};
