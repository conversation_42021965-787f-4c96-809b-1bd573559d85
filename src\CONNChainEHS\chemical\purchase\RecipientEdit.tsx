import styled from "styled-components";
import { useTranslation } from "react-i18next";
import { Recipient } from "../../models/Recipient";
import { useEffect, useState } from "react";

function RecipientEdit(props: {
  onClose: () => void;
  title: string;
  vendorName: string;
  recipients: Recipient[];
  setRecipients: React.Dispatch<React.SetStateAction<Recipient[]>>;
  recipientOptions: Recipient[];
}) {
  const { t } = useTranslation();
  const { onClose, title, vendorName, recipients, setRecipients, recipientOptions } = props;
  const [checkedRecipientIds, setCheckedRecipientIds] = useState<string[]>([]);

  useEffect(() => {
    setCheckedRecipientIds(recipients.filter(recipient => recipient.checked).map(recipient => recipient.id));
  }, [recipientOptions])

  const onChangeCheckRecipient = (event: React.ChangeEvent<HTMLInputElement>, recipientId: string) => {
    if (checkedRecipientIds.includes(recipientId)) {
      setCheckedRecipientIds(checkedRecipientIds.filter((id) => id !== recipientId));
    } else {
      setCheckedRecipientIds([...checkedRecipientIds, recipientId]);
    }
  }

  const onEdit = () => {
    const updatedOptions = recipientOptions.map(option => ({
      ...option,
      checked: checkedRecipientIds.includes(option.id)
    }));
    setRecipients(updatedOptions);
    onClose();
  }

  return (
    <StyledRecipientEdit >
      <div className="modifyLabUser">
        <div className="modal-header">
          <h4 className="modal-title">{title}</h4>
          <button type="button" className="btn-close" aria-hidden="true" onClick={onClose}></button>
        </div>
        <div className="modal-body">
          <h4 className="mx-3">{t('text.vendor')}：{vendorName}</h4>
          <div className="table-responsive mx-3">
            <table className="table table-bordered text-center fs-5 mt-3">
              <thead>
                <tr className="table-info">
                  <th>{t('table.title.checked') }</th>
                  <th>{t('text.recipient.manufacturer')}</th>
                  <th>{t('text.email')}</th>
                  <th>{t('text.phone')}</th>
                </tr>
              </thead>
              <tbody>
                {
                  recipientOptions.map((recipient, index) => (
                    <tr key={'recipient' + index}>
                      <td>
                        <input className="form-check-input" type="checkbox" id={`checkbox${index}`}
                          checked={checkedRecipientIds.some(id => id === recipient.id)} onChange={(e) => onChangeCheckRecipient(e, recipient.id)} />
                        <label className="form-check-label" htmlFor={`checkbox${index}`}></label>
                      </td>
                      <td>{recipient.name}</td>
                      <td>{recipient.email}</td>
                      <td>{recipient.phone}</td>
                    </tr>
                  ))
                }
              </tbody>
            </table>
          </div>
        </div>
        <div className="modal-footer">
          <button className="btn btn-secondary me-1" aria-hidden="true" onClick={onClose} title={t("button.close")}>
            <i className="fas fa-times me-1" />
            {t("button.close")}
          </button>
          <button className="btn btn-warning" aria-hidden="true" onClick={onEdit} title={t("button.edit")}>
            <i className="fas fa-pen me-1" />
            {t("button.edit")}
          </button>
        </div>
      </div>
    </StyledRecipientEdit>
  );
}


const StyledRecipientEdit = styled.div`
  font-size: 1.2em;
  background: white;
  width: 1100px;
  .modal-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  .modal-body {
    padding-top: 15px; 
  }
  .modal-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end; 
  }
`;

export default RecipientEdit;
