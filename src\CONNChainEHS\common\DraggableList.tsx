import React, { useRef } from "react";
import { useTranslation } from 'react-i18next';
import styled from "styled-components";
import { DndContext, closestCenter, PointerSensor, useSensor, useSensors, DragEndEvent, DragStartEvent } from "@dnd-kit/core";
import { SortableContext, useSortable, arrayMove, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

export interface DraggableItem {
  id: string; // 唯一識別碼
  label: string; // 顯示的名稱
  stutus?: string; // 狀態，預設為 '1' 表示啟用，'0' 表示禁用
}

interface DraggableListProps {
  items: DraggableItem[];
  onChange?: (items: DraggableItem[]) => void;
  onSortedUpdate?: (changed: any[]) => Promise<void>;
  onStatusChange?: (id: string, activated: boolean) => void;
  onEditClick?: (id: string) => void;
  onDeleteClick?: (id: string) => void;
  styledClassName?: string;
}

// 渲染每個拖曳區塊的組件，內含拖曳控制與按鈕
const SortableItem: React.FC<{
  item: DraggableItem;
  onEditClick?: (id: string) => void;
  onDeleteClick?: (id: string) => void;
  onStatusChange?: (id: string, activated: boolean) => void;
  isDraggingRef: React.MutableRefObject<boolean>;
}> = ({ item, onEditClick, onDeleteClick, onStatusChange, isDraggingRef }) => {
  const { id, label, stutus } = item;
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });
  const { t } = useTranslation();
  const style = {
    transform: CSS.Transform.toString(transform),
    transition
  };

  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isDraggingRef.current && onEditClick) {
      onEditClick(id);
    }
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isDraggingRef.current && onDeleteClick) {
      onDeleteClick(id);
    }
  };

  return (
    <ItemContainer ref={setNodeRef} style={style} {...attributes}>
      <ItemLabel>
        <div {...listeners} className="drag-handle">≡</div>
        <span className="label-text">{label}</span>
      </ItemLabel>
      <div className="all-sides">
          <div className="d-flex align-items-center">
              <label htmlFor="oshmsShow" className="form-label mb-0 me-2 h4">{t('text.toshms.front_display')}：</label>
              <div className="form-check form-switch m-0 h4">
                  <input
                      id="oshmsShow"
                      className="form-check-input"
                      type="checkbox"
                      checked={stutus == '1'}
                      onChange={(e) => {
                          onStatusChange?.(id, e.target.checked);
                      }}
                  />
              </div>
          </div>
      </div>
      <div className="button-group">
        <button className="btn btn-primary" onClick={handleEditClick}>{t('button.edit')}</button>
        <button className="btn btn-danger" onClick={handleDeleteClick}>{t('button.delete')}</button>
      </div>
    </ItemContainer>
  );
};

const DraggableList: React.FC<DraggableListProps> = ({
  items,
  onChange,
  onSortedUpdate,
  onStatusChange,
  onEditClick,
  onDeleteClick,
  styledClassName = ""
}) => {
  const sensors = useSensors(useSensor(PointerSensor));
  const isDraggingRef = useRef(false);

  const handleDragStart = (event: DragStartEvent) => {
    isDraggingRef.current = true;
  };

  // 結束拖曳時比對與更新。
  const handleDragEnd = async (event: DragEndEvent) => {
    isDraggingRef.current = false;
    const { active, over } = event;
    if (!over) return;
    if (active.id !== over.id) {
      const oldIndex = items.findIndex((item) => item.id === active.id);
      const newIndex = items.findIndex((item) => item.id === over.id);
      const newList = arrayMove(items, oldIndex, newIndex);

      onChange?.(newList);

      if (onSortedUpdate) {
        const changed = newList
          .map((item, index) => ({
            id: item.id,
            seq: index + 1
          }))
          .filter((item, index) => item.id !== items[index]?.id);

        if (changed.length > 0) {
          console.log("insert parameter:", changed);
          await onSortedUpdate(changed);
        }
      }
    }
  };

  return (
    <StyledDiv className={styledClassName}>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={items} strategy={verticalListSortingStrategy}>
          <div>
            {items.map((item, idx) => (
              <SortableItem
                key={idx}
                item={item}
                onStatusChange={onStatusChange}
                onEditClick={onEditClick}
                onDeleteClick={onDeleteClick}
                isDraggingRef={isDraggingRef}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>
    </StyledDiv>
  );
};

const StyledDiv = styled.div`
  .drag-handle {
    margin-right: 12px;
    cursor: grab;
  }

  .label-text {
    overflow-wrap: break-word;
  }

  .button-group {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
  }
`;

const ItemContainer = styled.div`
  padding: 16px;
  margin: 10px;
  background-color: #ffffff;
  border: 1px solid #ccc;
  border-radius: 6px;
  width: 100%;
  font-size: 18px;
  font-weight: 600;
  color: #000;
  letter-spacing: 0.5px;
  display: flex;
  flex-wrap: wrap; /* 允許自動換行 */
  gap: 12px;
  justify-content: space-between;
  align-items: center;
`;

const ItemLabel = styled.div`
  display: flex;
  align-items: center;
  flex: 1 1 auto;
  min-width: 0;
  .label-text {
    white-space: normal; /* 允許文字換行 */
    word-break: break-word; /* 長單字也可斷行 */
  }
`;


export default DraggableList;
