import { AgentNameTag } from "ehs/common/AgentNameTag";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import { ConfigAPI } from "../../../api/configAPI";
import { PurchaseAPI } from "../../../api/purchaseAPI";
import { AppPaths } from "../../../config/app-paths";
import Loader from "../../common/Loader";
import NoDataRow from "../../common/NoDataRow";
import ProgressBar from "../../common/ProgressBar";
import SortIcon from "../../common/SortIcon";
import { CONFIG_TYPE_PURCHASE_STATUS, PAGE_SOURCE, PageSource } from "../../constant/constants";
import { PurchaseMode } from "../../enums/PurchaseMode";
import { PurchaseStatus } from "../../enums/PurchaseStatus";
import useLoginUser from "../../hooks/useLoginUser";
import Breadcrumbs from "../../layout/Breadcrumbs";
import Footer from "../../layout/Footer";
import PageSizeSelector from "../../layout/PageSizeSelector";
import Pagination from "../../layout/Pagination";
import { EhsConfigParam } from "../../models/EhsConfigParam";
import { EhsPurchase } from "../../models/EhsPurchase";
import { PageInfo, initPageInfo } from "../../models/PageInfo";
import { SignoffFlowRecord } from "../../models/SignoffFlowRecord";
import { isArrayEmpty } from "../../utils/arrayUtil";
import { checkTimeoutAction, getBasicLoginUserInfo, getLoginUserId, getLoginUserRoleId, isLabRoleLv, isManagerRoleLv } from "../../utils/authUtil";
import { isApiCallSuccess } from "../../utils/resultUtil";
import { getSignoffTypeShow } from "../../utils/signoffUtil";
import { getFormatTimeSlash } from "../../utils/stringUtil";

interface PurchaseListProps {
  source?: PageSource;
}

function PurchaseList({ source = PAGE_SOURCE.CHEMICAL_PURCHASE_LIST }: PurchaseListProps) {
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const navigate = useNavigate();
  const purchaseStatusSelect = useRef<HTMLSelectElement>(null)
  const [purchaseList, setPurchaseList] = useState<EhsPurchase[]>([]);
  const [localSearchResult, setLocalSearchResult] = useState<EhsPurchase[]>([]);
  const [configMap, setConfigMap] = useState<{ [configId: string]: EhsConfigParam }>({});// config對應
  const [localSearchKey, setLocalSearchKey] = useState("");
  const [loading, setLoading] = useState(false);
  const [condition, setCondition] = useState<{
    keyword: string;
    currentPage: number;
    pageSize: number;
    purchaseStatus: number | null;
  }>({
    keyword: "",
    currentPage: 1,
    pageSize: 50,
    purchaseStatus: null,
  });
  const [pageInfo, setPageInfo] = useState<PageInfo>(initPageInfo);
  const pageObjMap = {
    [PAGE_SOURCE.SIGN_OFF_PURCHASE_LIST]: {
      func: t("func.signoff.manage"),
      title: t("func.signoff.purchase"),
      theadBgColor: 'signoff-thead'
    },
    [PAGE_SOURCE.CHEMICAL_PURCHASE_LIST]: {
      func: t("func.chemical.manage"),
      title: t("func.chemical.purchase.list"),
      theadBgColor: 'bg-lime-200'
    }
  }
  const pageObj = pageObjMap[source];

  useEffect(() => {
    if (loginUser) {
      fetchConfigData();
    }
  }, [loginUser, i18n.language]);

  useEffect(() => {
    if (loginUser) {
      fetchData();
    }
  }, [loginUser, condition, i18n.language]);

  useEffect(() => {
    if (localSearchKey) {
      let resultList = purchaseList.filter((data: EhsPurchase) => {
        return [data.purchaseId, data.applicantName, getFormatTimeSlash(data.createDate)].some(property => property && property.includes(localSearchKey));
      });
      setLocalSearchResult(resultList);
    }

  }, [localSearchKey])

  const fetchData = () => {
    setLoading(true)
    PurchaseAPI.getPurchaseList({
      ...getBasicLoginUserInfo(loginUser)!,
      ...condition,
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        setPurchaseList(result.results);
        setPageInfo(result.pageinfo);
        setLoading(false)
      }
    }).catch(error => {
      checkTimeoutAction(error, navigate, t);
    });
  };

  const fetchConfigData = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser)!,
      configTypeList: [CONFIG_TYPE_PURCHASE_STATUS],
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        setConfigMap(result.results.reduce((acc: { [configId: string]: EhsConfigParam }, config: EhsConfigParam) => {
          acc[config.configIvalue] = config;
          return acc;
        }, {}))
      }
    });
  }

  const getShowList = () => {
    if (!localSearchKey) {
      return purchaseList;
    }
    return localSearchResult;
  };

  const clickSearch = () => {
    const purchaseStatusValue = purchaseStatusSelect.current?.value;
    const purchaseStatus = purchaseStatusValue ? parseInt(purchaseStatusValue) : null;
    setCondition({
      ...condition,
      currentPage: 1,
      purchaseStatus: purchaseStatus,
    })
  }

  return (
    <StlyedPurchaseList $loading={loading}>
      <div className="d-flex flex-column p-0" id="content">
        {/* BEGIN scrollbar */}
        <div className="app-content-padding flex-grow-1">
          {/* BEGIN breadcrumb */}
          <Breadcrumbs
            items={[
              { label: pageObj.func },
              { label: pageObj.title },
            ]}
          />
          {/* END breadcrumb */}
          {/* BEGIN page-header */}
          <h1 className="page-header">{pageObj.title} </h1>
          {/* END page-header */}

          <div className="card">
            <div className="card-body p-4">
              <div className="row">
                <div className="col-xl-3 d-flex align-items-center">
                  <label className="pe-3">{t("table.title.progress")}</label>
                  <select className="form-select w-75" ref={purchaseStatusSelect}>
                    <option value="">{t('text.all')}</option>
                    {Object.entries(configMap).map(([key, value]) => (
                      <option key={key} value={key}>
                        {value.configName}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="col-xl-3">
                  <button type="button" className="btn btn-primary" onClick={clickSearch}><i className="fas fa-magnifying-glass"></i> {t('button.search.item')}</button>
                </div>
              </div>
            </div>
          </div>
          <div className="card pt-3">
            <div className="row topFunctionRow">
              <div className="col-sm-12 col-md-6 left">
                <PageSizeSelector pageSize={condition.pageSize} setCondition={setCondition} />
              </div>
              <div className="col-sm-12 col-md-6 right">
                <div className="dataTables_filter d-flex">
                  search:
                  <input
                    value={localSearchKey}
                    onChange={(e) => {
                      setLocalSearchKey(e.target.value);
                    }}
                    type="search"
                    className="form-control form-control-sm"
                    placeholder=""
                    aria-controls="data-table-default"
                  />
                </div>
              </div>
            </div>
            <div className="card-body">
              {loading && <Loader />}
              <table
                id="data-table-default"
                className={
                  "table table-hover align-middle dt-responsive nowrap"
                }
              >
                <thead className={`text-center fs-4 fw-bold ${pageObj.theadBgColor}`}>
                  <tr>
                    <th>{t("table.title.item")}</th>
                    <th className="text-start">
                      {t('table.title.purchase_order_no')}
                      <SortIcon
                        dataList={getShowList()}
                        dataField={"purchaseId"}
                        setFunction={setPurchaseList}
                      />
                    </th>
                    <th className="text-start">
                      {t('table.title.applicant')}
                      <SortIcon
                        dataList={getShowList()}
                        dataField={"userName"}
                        setFunction={setPurchaseList}
                      />
                    </th>
                    <th className="text-start">
                      {t('table.title.request_time')}
                      <SortIcon
                        dataList={getShowList()}
                        dataField={"createDate"}
                        setFunction={setPurchaseList}
                      />
                    </th>
                    <th className="text-start">
                      {t('table.title.vendor')}
                      <SortIcon
                        dataList={getShowList()}
                        dataField={"manufacturerName"}
                        setFunction={setPurchaseList}
                      />
                    </th>
                    <th data-orderable="false">{t('table.title.note')}</th>
                    <th data-orderable="false" className="item-width-15">{t("table.title.progress")}</th>
                    <th data-orderable="false" className="text-start">{t("table.title.action")}</th>
                  </tr>
                </thead>
                <tbody className="text-center fs-5">
                  {!loading && getShowList() && !isArrayEmpty(getShowList()) ?
                    getShowList().map((data, idx) => {
                      return <Row key={'purchase-row' + data.purchaseId + idx} index={idx + 1} purchase={data} configMap={configMap} onEditStatus={() => { }} onDeleteSuccess={() => { }} />;
                    }) : (!loading && <NoDataRow />)}
                </tbody>
              </table>
              <Pagination pageInfo={pageInfo} setCondition={setCondition} />
            </div>
          </div>
        </div>
        {/* BEGIN #footer */}
        <Footer />
        {/* END #footer */}
      </div>
    </StlyedPurchaseList>
  );
}

const Row = (props: {
  index: number; purchase: EhsPurchase;
  configMap: { [configId: string]: EhsConfigParam };
  onEditStatus: (chemicalId: string, activated: boolean) => void; onDeleteSuccess: () => void;
}) => {
  const { loginUser } = useLoginUser();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { index, purchase, configMap, onEditStatus, onDeleteSuccess } = props;
  const { purchaseId, labId, signStatusId, purchaseStatus,
    note, createId, createDate, editId, editDate,
    agentId, agentName, applicantName, manufacturerName, noArrivalDateCount, noInspDateCount,
    nextFlowSeq, signoffFlowList } = purchase;
  const loginUserId = getLoginUserId(loginUser);
  const loginRoleId = getLoginUserRoleId(loginUser);
  const { currentFlowList, afterFlowList } = signoffFlowList.reduce((acc: { currentFlowList: SignoffFlowRecord[], afterFlowList: SignoffFlowRecord[] }, flow: SignoffFlowRecord) => {
    if (flow.signFlowSeq === nextFlowSeq) {
      acc.currentFlowList.push(flow);
    } else {
      acc.afterFlowList.push(flow);
    }
    return acc;
  }, { currentFlowList: [], afterFlowList: [] });//afterFlowList 之後需要權限大 不通過時會用到
  const isMultiCurrentFlow = currentFlowList.length > 1;

  const modeToPathMap = {
    [PurchaseMode.SIGNOFF]: AppPaths.chemical.purchaseSignOff,
    [PurchaseMode.DETAIL]: AppPaths.chemical.purchaseDetail,
    [PurchaseMode.ARRIVAL]: AppPaths.chemical.purchaseArrival,
    [PurchaseMode.INSPECTION]: AppPaths.chemical.purchaseInspection,
    [PurchaseMode.RETURN]: AppPaths.chemical.purchaseReturn,
    [PurchaseMode.CANCEL]: AppPaths.chemical.purchaseCancel,
  };

  const clickNavOperation = (mode: PurchaseMode) => {
    const targetPath = modeToPathMap[mode] || "";
    if (targetPath) {
      navigate("/" + targetPath, { state: { mode, initPurchaseId: purchaseId, initSignStatusId: signStatusId } });
    }
  };

  const progressBarConfig: { [key: number]: any } = {
    1: { width: "20%", bgColor: "bg-gray", striped: true },
    2: { width: "40%", bgColor: "bg-info", striped: true },
    3: { width: "40%", bgColor: "bg-info", striped: true },
    4: { width: "50%", bgColor: "bg-yellow", striped: true },
    5: { width: "80%", bgColor: "bg-blue", striped: true },
    6: { width: "80%", bgColor: "bg-cyan", striped: true },
    7: { width: "100%", bgColor: "bg-green", striped: false },
    8: { width: "100%", bgColor: "bg-black", striped: false },
    9: { width: "100%", bgColor: "bg-danger", striped: false },
    10: { width: "100%", bgColor: "bg-light-red", striped: false },
    11: { width: "100%", bgColor: "bg-warning", striped: true },
  };

  const showSignTypeText = getSignoffTypeShow(currentFlowList?.[0]?.signType, t);

  const showArrivalBtn = [PurchaseStatus.PURCHASING, PurchaseStatus.SHIPPING, PurchaseStatus.PARTIAL_ARRIVAL, PurchaseStatus.PARTIAL_INSPECTION].includes(purchaseStatus) && noArrivalDateCount > 0 && isManagerRoleLv(loginUser);
  const showInspectionBtn = [PurchaseStatus.PARTIAL_ARRIVAL, PurchaseStatus.PENDING_INSPECTION, PurchaseStatus.PARTIAL_INSPECTION].includes(purchaseStatus) && noInspDateCount > 0 && (isManagerRoleLv(loginUser) || isLabRoleLv(loginUser));
  const showReturnBtn = [PurchaseStatus.PARTIAL_ARRIVAL, PurchaseStatus.PENDING_INSPECTION, PurchaseStatus.PARTIAL_INSPECTION].includes(purchaseStatus) && noInspDateCount > 0 && (isManagerRoleLv(loginUser) || isLabRoleLv(loginUser));
  const showCancelBtn = [PurchaseStatus.SIGNING, PurchaseStatus.RETURN_MODIFICATIONS, PurchaseStatus.PURCHASING, PurchaseStatus.SHIPPING, PurchaseStatus.PARTIAL_ARRIVAL, PurchaseStatus.PARTIAL_INSPECTION].includes(purchaseStatus) && noArrivalDateCount > 0 && createId === loginUserId;
  const showSignoffBtn = [PurchaseStatus.SIGNING].includes(purchaseStatus) && (currentFlowList.find((item) => item.roleId === loginRoleId) || currentFlowList.find((item) => item.userId === loginUserId));
  const showModifyBtn = [PurchaseStatus.RETURN_MODIFICATIONS].includes(purchaseStatus) && (createId === loginUserId);

  const { width, bgColor, striped } = progressBarConfig[purchaseStatus] || {};
  return (
    <tr key={'purchase-item' + purchaseId}>
      <td data-title={t("table.title.item")}>{index}</td>
      <td data-title={t('table.title.purchase_order_no')} className="text-start">{purchaseId}</td>
      <td data-title={t('table.title.applicant')} className="text-start">{applicantName}
        <AgentNameTag agentId={agentId} agentName={agentName} withBreak />
      </td>
      <td data-title={t('table.title.request_time')} className="text-start">{getFormatTimeSlash(createDate)}</td>
      <td data-title={t('table.title.vendor')} className="text-start">{manufacturerName}
      </td>
      <td data-title={t('table.title.note')}>{note}</td>
      <td data-title={t("table.title.progress")}>
        <ProgressBar width={width} bgColor={bgColor} striped={striped} />{configMap[purchaseStatus]?.configName}<br />
        {purchaseStatus === PurchaseStatus.SIGNING && <>
          <br />
          <div className="border border-black  p-1">
            <div className="d-inline-block text-start">
              {currentFlowList.map((item, index) => {
                return <React.Fragment key={'signoff-flow-' + item.signStatusId + item.signFlowSeq + index}>
                  <label>{t('text.signoff.wait_auth_prefix')} {item.roleName || item.userName}</label>{isMultiCurrentFlow && <br />}
                </React.Fragment>;
              })} {isMultiCurrentFlow && showSignTypeText} {t('text.signoff.wait_auth_suffix')}
            </div>
          </div>
        </>}
      </td>
      <td data-title={t("table.title.action")} className="text-start">
        <button type="button" className="btn btn-secondary me-3 fs-5 my-2" title={t("button.detail")} onClick={() => { clickNavOperation(PurchaseMode.DETAIL) }}>
          <i className="fas fa-file-alt me-1" />
          {t("button.detail")}
        </button>
        {showModifyBtn && <button type="button" className="btn btn-warning me-3 fs-5 my-2" title={t("button.modify")} onClick={() => { }}>
          <i className="fas fas fa-pen me-1" />
          {t("button.modify")}
        </button>}
        {showSignoffBtn &&
          <button type="button" className="btn btn-success me-3 fs-5 my-2" title={t("button.signoff")} onClick={() => { clickNavOperation(PurchaseMode.SIGNOFF) }}>
            <i className="fas fa-pen me-1" />
            {t("button.signoff")}
          </button>}

        {showArrivalBtn &&
          <button type="button" className="btn btn-info me-3 fs-5 my-2" title={t("button.arrival")} onClick={() => clickNavOperation(PurchaseMode.ARRIVAL)}>
            <i className="fas fa-truck me-1" />
            {t("button.arrival")}
          </button>}
        {showInspectionBtn && <button
          type="button"
          className="btn btn-blue me-3 fs-5"
          title={t("button.inspection")}
          onClick={() => clickNavOperation(PurchaseMode.INSPECTION)} >
          <i className="fas fa-check me-1" />
          {t("button.inspection")}
        </button>}
        {showReturnBtn && <button
          type="button"
          className="btn btn-danger me-3 fs-5"
          title={t("button.product_return")}
          onClick={() => clickNavOperation(PurchaseMode.RETURN)} >
          <i className="fas fa-undo me-1" />
          {t("button.product_return")}
        </button>}
        {showCancelBtn && <button
          type="button"
          className="btn bg-light-red text-white me-3 fs-5"
          title={t("button.cancel_purchase")}
          onClick={() => clickNavOperation(PurchaseMode.CANCEL)} >
          <i className="fas fa-times me-1" />
          {t("button.cancel_purchase")}
        </button>}
      </td>
    </tr>
  );
};

const StlyedPurchaseList = styled.div<{ $loading?: boolean }>`
  padding-bottom:150px;

  .bg-light-red{
    background-color:	#FF0000;
  }

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }
  table {
    position:relative;
    min-height:${props => props.$loading ? "200px" : "auto"};
    th {
      text-align: center;
      white-space:nowrap;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 110px;
        text-align:left;
        min-height:39.5px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 15px;
          white-space: nowrap;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }

  .signoff-thead {
    background: rgb(251, 205, 165);  // 與 Area 相同的橘色
  }
`;

export default PurchaseList;
