import { Rou<PERSON><PERSON> } from 'api/rouAPI';
import { UserAPI } from 'api/userAPI';
import NamesSplitFragment from 'ehs/common/NamesSplitFragment';
import { confirmMsg, errorMsg } from 'ehs/common/SwalMsg';
import { showSuccessToast } from 'ehs/common/Toast';
import { ORG_SPLIT_FLAG } from 'ehs/constant/constants';
import useLoginUser from 'ehs/hooks/useLoginUser';
import { EhsRou } from 'ehs/models/EhsRou';
import { EhsUser } from 'ehs/models/EhsUser';
import { isArrayEmpty } from 'ehs/utils/arrayUtil';
import { getBasicLoginUserInfo, getLoginUserId } from 'ehs/utils/authUtil';
import { getUserUnitTextObj } from 'ehs/utils/langUtil';
import { isApiCallSuccess } from 'ehs/utils/resultUtil';
import { isEnterKey } from 'ehs/utils/stringUtil';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';

interface AgentSettingDialogProps {
  onClose: () => void;
  show: boolean;  // 新增 show prop
}

interface TableContainerProps {
  $hasData: boolean;
}

export const AgentSettingDialog = ({ onClose = () => { }, show = false }: AgentSettingDialogProps) => {
  const { loginUser } = useLoginUser();
  const userId = getLoginUserId(loginUser);
  const [searchText, setSearchText] = useState('');
  const [lastSearchText, setLastSearchText] = useState('');
  const [selectedRoleId, setSelectedRoleId] = useState<string>('');
  const [roles, setRoles] = useState<EhsRou[]>([]);
  const [agents, setAgents] = useState<EhsRou[]>([]);
  const [searchResults, setSearchResults] = useState<EhsUser[]>([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const { t } = useTranslation();
  const isEmptySwitchableRole = isArrayEmpty(roles);// ... existing code ...
  const { userIdText, searchPlaceholder } = getUserUnitTextObj(loginUser, t);


  useEffect(() => {
    if (show && loginUser) {
      fetchSwitchableRoles();
      fetchAgentList();
    }
  }, [show, loginUser]);

  const fetchSwitchableRoles = async () => {
    if (!userId) {
      return;
    }
    try {
      const res = await RouAPI.getSwitchableRoles({
        ...getBasicLoginUserInfo(loginUser),
        userId: userId,
        includeAgent: false,
      });
      if (isApiCallSuccess(res)) {
        setRoles(res.results);
      }
    } catch (error) {
      console.error('Fetch switchable roles error:', error);
    }
  };

  const fetchAgentList = async () => {
    if (!userId) {
      return;
    }
    try {
      const res = await RouAPI.getAgentList({
        ...getBasicLoginUserInfo(loginUser),
        userId: userId,
      });
      if (isApiCallSuccess(res)) {
        setAgents(res.results);
      }
    } catch (error) {
      console.error('Fetch agent list error:', error);
    }
  };

  // 模擬搜尋用戶的函數
  const handleSearch = async () => {
    const trimmedSearchText = searchText.trim();
    if (!trimmedSearchText) {
      setShowSearchResults(false);
      return;
    }

    setLastSearchText(trimmedSearchText);
    setSearchResults([]);
    try {
      // 這裡先用模擬資料，之後可以替換成實際的 API 呼叫
      UserAPI.getUserSearchList({
        ...getBasicLoginUserInfo(loginUser),
        keyword: trimmedSearchText.toLowerCase(),
        pageSize: 50,
      }).then((result) => {
        if (isApiCallSuccess(result)) {
          if (!isArrayEmpty(result.results)) {
            // 過濾掉當前登入使用者
            const filteredResults = result.results.filter(
              (user: EhsUser) => user.userId !== userId
            );
            setSearchResults(filteredResults);
          }
        }
      });
      setShowSearchResults(true);
    } catch (error) {
      console.error('Search user error:', error);
    }
  };

  const handleSearchTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchText(newValue);
  };

  const handleAddAgent = async (agent: EhsUser) => {
    // 檢查是否已選擇權限
    const selectedRole = roles.find(role => role.rouId === selectedRoleId);
    if (!selectedRole) {
      errorMsg(t('message.please_select_agent_role'));
      return;
    }

    try {
      const result = await RouAPI.addAgent({
        ...getBasicLoginUserInfo(loginUser),
        userId: agent.userId,
        rouId: selectedRoleId,
      });
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
        const newAgent = {
          ...result.results,
          userName: agent.userName,
          roleName: selectedRole.roleName,
        };
        setAgents([...agents, newAgent]);
      } else {
        errorMsg(result.message);
      }
    } catch (error) {
      console.error('Add agent error:', error);
    }
  };

  const handleDeleteAgent = async (rouId: string) => {
    try {
      const confirmed = await confirmMsg(t('message.confirm_delete_agent'), t);
      if (!confirmed) return;

      const result = await RouAPI.delRou({
        ...getBasicLoginUserInfo(loginUser),
        rouId: rouId,
      });
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
        setAgents(prevAgents => prevAgents.filter(agent =>
          agent.rouId !== rouId
        ));
      } else {
        errorMsg(result.message);
      }
    } catch (error) {
      console.error('Delete agent error:', error);
    }
  };

  const isSearchDisabled = () => {
    const trimmedSearchText = searchText.trim();
    return trimmedSearchText === '' || (showSearchResults && trimmedSearchText === lastSearchText.trim());
  };

  const handleClearSearch = () => {
    setSearchText('');
    setShowSearchResults(false);
  };

  return (
    <StyledDialog>
      <DialogHeader>
        <h2>{t('text.agent_setting')}</h2>
        <CloseButton onClick={onClose} title={t('button.close')}>
          <i className="fas fa-times" />
        </CloseButton>
      </DialogHeader>

      <DialogContent>
        <LeftSection>
          <FormGroup>
            <label>{t('text.agent_role')}</label>
            {roles.length === 0 ? (
              <NoRoleMessage>
                <i className="fas fa-exclamation-circle" />
                {t('message.no_agent_role')}
              </NoRoleMessage>
            ) : (
              <select
                className="form-control"
                value={selectedRoleId}
                onChange={(e) => setSelectedRoleId(e.target.value)}
              >
                <option value="">{t('text.please_select_role')}</option>
                {roles.map(role => (
                  <option key={role.rouId} value={role.rouId}>
                    {role.roleName}
                  </option>
                ))}
              </select>
            )}
          </FormGroup>

          <FormGroup>
            <label>{t('text.agent_prefix')}</label>
            <SearchBox>
              <input
                type="text"
                value={searchText}
                onChange={handleSearchTextChange}
                onKeyDown={(e) => {
                  if (isEnterKey(e) && !isSearchDisabled()) {
                    handleSearch();
                  }
                }}
                title={searchPlaceholder}
                placeholder={searchPlaceholder}
                className="form-control"
                disabled={isEmptySwitchableRole}
              />
              {searchText && (
                <ClearButton
                  type="button"
                  onClick={handleClearSearch}
                  title={t('button.clear_search')}
                  disabled={isEmptySwitchableRole}
                >
                  <i className="fas fa-times me-2" />
                </ClearButton>
              )}
              <SearchButton
                type="button"
                onClick={handleSearch}
                title={t('button.search.item')}
                disabled={isSearchDisabled() || isEmptySwitchableRole}
              >
                <i className="fas fa-search me-1" />{t('button.search.item')}
              </SearchButton>
            </SearchBox>
            {showSearchResults && (
              <SearchResultContainer>
                {searchResults.length === 0 ? (
                  <NoResults>{t('message.no_search_user_result')}</NoResults>
                ) : (
                  searchResults.map(user => (
                    <SearchResultItem key={user.userId}>
                      <div>
                        <strong>{user.userId}</strong> - {user.userName}
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          <NamesSplitFragment names={user.orgNames} separator={ORG_SPLIT_FLAG} />
                        </div>
                      </div>
                      <AddButton
                        onClick={() => handleAddAgent(user)}
                        disabled={!selectedRoleId || agents.some(a =>
                          a.userId === user.userId &&
                          a.roleId === selectedRoleId
                        )}
                        title={agents.some(a => a.userId === user.userId && a.roleId === selectedRoleId)
                          ? t('button.agent_permission_assigned')
                          : t('button.join')}
                      >
                        <i className="fas fa-plus me-1" />
                        {agents.some(a => a.userId === user.userId && a.roleId === selectedRoleId)
                          ? t('button.agent_permission_assigned')
                          : t('button.join')}
                      </AddButton>
                    </SearchResultItem>
                  ))
                )}
              </SearchResultContainer>
            )}
          </FormGroup>
        </LeftSection>

        <RightSection>
          <TableTitle>{t('text.agent_list')}</TableTitle>
          <TableContainer $hasData={!isArrayEmpty(agents)}>
            {isArrayEmpty(agents) ? (
              <EmptyMessage>
                <div>
                  <i className="fas fa-user-plus"></i>
                  {t('message.no_agent_instruction')}
                </div>
              </EmptyMessage>
            ) : (
              <AgentTable>
                <thead>
                  <tr>
                    <th style={{ width: TableColumnWidth.userId }}>{userIdText}</th>
                      <th style={{ width: TableColumnWidth.userName }}>{t('text.user_name')}</th>
                    <th style={{ width: TableColumnWidth.roleName }}>{t('text.agent_role')}</th>
                    <th style={{ width: TableColumnWidth.action }}>{t('table.title.action')}</th>
                  </tr>
                </thead>
                <tbody>
                  {agents.map((agent, index) => (
                    <tr key={`${agent.rouId}-${index}`}>
                      <td style={{ width: TableColumnWidth.userId }}>{agent.agentId}</td>
                      <td style={{ width: TableColumnWidth.userName }}>{agent.userName}</td>
                      <td style={{ width: TableColumnWidth.roleName }}>{agent.roleName}</td>
                      <td style={{ width: TableColumnWidth.action }}>
                        <DeleteButton
                          className='btn btn-danger'
                          onClick={() => handleDeleteAgent(agent.rouId)}
                          title={t('button.delete')}
                        >
                          <i className="fas fa-trash me-1" />{t('button.delete')}
                        </DeleteButton>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </AgentTable>
            )}
          </TableContainer>
        </RightSection>
      </DialogContent>

      <DialogFooter>
        <button className='btn btn-white' title={t('button.close')} onClick={onClose}>
          <i className="fas fa-close me-1"></i>{t('button.close')}</button>
      </DialogFooter>
    </StyledDialog>
  );
};

const StyledDialog = styled.div`
  width: 900px;
  max-width: 95vw;
  min-height: 400px;
  max-height: 90vh;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  padding: 0 16px;
  
  @media (max-width: 768px) {
    max-height: 85vh;
    margin: 20px auto;
  }
  
  @media (min-width: 1200px) {
    max-height: 600px;
  }
  
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  backface-visibility: hidden;
`;

const DialogHeader = styled.div`
  padding: 16px 5px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  
  @media (max-width: 768px) {
    padding: 12px 5px;
  }
`;

const DialogContent = styled.div`
  padding: 20px 0;
  display: flex;
  gap: 24px;
  overflow-y: auto;
  flex: 1;
  
  @media (max-width: 768px) {
    flex-direction: column;
    padding: 12px 0;
    gap: 12px;
  }
`;

const LeftSection = styled.div`
  flex: 0 0 320px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-right: 24px;
  border-right: 1px solid #eee;
  
  @media (max-width: 768px) {
    flex: none;
    width: 100%;
    padding-right: 0;
    border-right: none;
    border-bottom: 1px solid #eee;
    padding-bottom: 12px;
    gap: 12px;
  }
`;

const RightSection = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
  
  @media (max-width: 768px) {
    min-height: 300px;
  }
`;

const TableTitle = styled.h3`
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
`;

const EmptyMessage = styled.div`
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #999;
  background: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  
  // 讓訊息看起來更協調
  font-size: 15px;
  line-height: 1.5;
  padding: 20px;
  
  // 增加一些視覺提示
  i {
    display: block;
    font-size: 24px;
    margin-bottom: 8px;
    color: #d9d9d9;
  }
`;

const FormGroup = styled.div`
  position: relative;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 15px;
  }

  input {
    font-size: 15px;
  }

  select {
    font-size: 15px;
  }
`;

const SearchBox = styled.div`
  position: relative;
  display: flex;
  width: 100%;
  
  input {
    flex: 1;
    height: 36px;
    padding: 8px 95px 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    
    &:focus {
      border-color: #40a9ff;
      outline: none;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
`;

const SearchButton = styled.button`
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  padding: 0 12px;
  min-width: 70px;
  background: #40a9ff;
  border: none;
  border-radius: 0 4px 4px 0;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  transition: all 0.3s;

  &:hover:not(:disabled) {
    background: #1890ff;
  }

  &:disabled {
    background: #d9d9d9;
    cursor: not-allowed;
    opacity: 0.7;
  }
`;

const AgentTable = styled.table`
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: white;
  height: 100%;
  
  thead tr:first-child {
    th:first-child {
      border-top-left-radius: 8px;
    }
    th:last-child {
      border-top-right-radius: 8px;
    }
  }
  
  th {
    position: sticky;
    top: 0;
    background: #f0f5ff;
    color: #1890ff;
    font-weight: 500;
    z-index: 1;
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e8e8e8;
    font-size: 15px;

    &:first-child {
      padding-left: 16px;
    }

    &:last-child {
      padding-right: 16px;
    }
  }

  td {
    padding: 12px;
    border-bottom: 1px solid #eee;
    font-size: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:first-child {
      padding-left: 16px;
    }

    &:last-child {
      padding-right: 16px;
      text-align: center;
    }

    @media (max-width: 768px) {
      padding: 8px;
      
      &:first-child {
        padding-left: 8px;
      }

      &:last-child {
        padding-right: 8px;
      }
    }
  }
  
  tbody {
    display: block;
    overflow-y: auto;
    height: calc(100% - 43px);
    
    // 美化捲軸
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }
  
  thead, tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
  }
  
  thead {
    width: calc(100%);  // 移除滾動條寬度的計算，讓它完全對齊
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  color: #999;

  &:hover {
    color: #666;
  }
`;

const DeleteButton = styled.button`
  white-space: nowrap;
  min-width: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;

  @media (max-width: 768px) {
    padding: 4px;
    min-width: 60px;
  }
`;

const SearchResultContainer = styled.div`
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-top: 8px;

  @media (min-width: 768px) {
    max-height: 250px;  // 在大螢幕上顯示更多內容
  }

  // 美化捲軸
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
`;

const SearchResultItem = styled.div`
  padding: 10px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: default;
  border-bottom: 1px solid #eee;
  font-size: 15px;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f5f5f5;
  }

  div {
    strong {
      font-size: 15px;
    }
    
    div {
      font-size: 13px;
      color: #666;
    }
  }
`;

const NoResults = styled.div`
  padding: 12px;
  text-align: center;
  color: #999;
  background: white;
`;

const DialogFooter = styled.div`
  padding: 16px 10px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  flex-shrink: 0;
  
  @media (max-width: 768px) {
    padding: 12px 10px;
  }
`;

const TableContainer = styled.div<TableContainerProps>`
  margin: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 20px;
  overflow: hidden;
  
  // 只有在有資料時才顯示邊框
  ${({ $hasData }) => $hasData && `
    border: 1px solid #e8e8e8;
  `}
`;

const AddButton = styled.button`
  padding: 4px 12px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;

  &:hover {
    background: #40a9ff;
  }

  // 已選擇的狀態
  &:disabled {
    background: #d9d9d9;
    cursor: not-allowed;
  }
`;

// 表格列寬設定
const TableColumnWidth = {
  userId: '25%',
  userName: '30%',
  roleName: '25%',
  action: '20%'
};

const ClearButton = styled.button`
  position: absolute;
  right: 75px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s;

  &:hover {
    color: #666;
  }
`;

const NoRoleMessage = styled.div`
  padding: 8px 12px;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  display: flex;
  align-items: center;
  gap: 8px;
  
  i {
    color: #faad14;
  }
`;

export default AgentSettingDialog;
