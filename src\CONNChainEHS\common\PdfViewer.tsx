import "react-pdf/dist/Page/TextLayer.css";
import "react-pdf/dist/Page/AnnotationLayer.css";
import { Document, Page, pdfjs } from 'react-pdf';
import styled from "styled-components";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

pdfjs.GlobalWorkerOptions.workerSrc = '/pdfjs/pdf.worker.min.mjs';
interface PdfViewerProps {
    pdfFile: File | null,
    className?: string,
    styledClassName?: string
    noFileMsg?: string
}

const PdfViewer: React.FC<PdfViewerProps> = ({
    pdfFile,
    className = 'ms-2 container-div',
    styledClassName = "",
    noFileMsg = ""
}) => {
    const defaultScale = 1;
    const { t } = useTranslation();
    const [scale, setScale] = useState(defaultScale); // 預設縮放比例
    const [numPages, setNumPages] = useState<number | null>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (pdfFile) {
            setScale(defaultScale);
        }
    }, [pdfFile])

    // 設定頁碼數量
    const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
        setNumPages(numPages);
    };

    return <StyledDiv className={styledClassName}>
        {pdfFile && <div style={{ marginBottom: '10px' }}>
            <label htmlFor="zoom-range">{t('text.file_prview_room')}: </label>
            <input
                id="zoom-range"
                type="range"
                min="0.5"
                max="2.0"
                step="0.1"
                value={scale}
                onChange={(e) => setScale(parseFloat(e.target.value))}
                style={{ width: '300px', margin: '0 10px' }}
            />
            <span>{(scale * 100).toFixed(0)}%</span>
        </div>}
        <div ref={containerRef} className={className}>
            <Document
                className={'ms-2'}
                file={pdfFile}
                error={t('text.pdf_viewer_error')}
                loading={t('text.pdf_viewer_loading')}
                noData={noFileMsg || t('text.pdf_viewer_no_data')}
                onLoadSuccess={onDocumentLoadSuccess}
            >
                {numPages &&
                    Array.from(new Array(numPages), (el, index) => (
                        <div key={index} className="mb-2">
                            <Page
                                pageNumber={index + 1}
                                scale={scale}
                            />
                            <div className="text-center mt-2">
                                {t('text.pdf_viewer_page_index', { pageNum: index + 1, totalPage: numPages })}
                            </div>
                        </div>
                    ))
                }
            </Document>
        </div>
    </StyledDiv>
}

const StyledDiv = styled.div`
    .container-div{
        max-height:700px;
        overflow-y:auto;
        overflow-x: auto;
    }
`;

export default PdfViewer;
