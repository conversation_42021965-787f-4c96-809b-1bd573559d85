import React from 'react';
import { EhsConfigParam } from 'ehs/models/EhsConfigParam';
import { getShowChemCon } from 'ehs/utils/chemicalUtil';
import { useTranslation } from 'react-i18next';
import { EhsPurchaseSubstCategory } from 'ehs/models/EhsPurchaseSubstCategory';
import ChemicalClassificationBadge from './ChemicalClassificationBadge';

export const enum SubstanceDisplayMode {
    LIST = 'list',
    DETAIL = 'detail'
}

interface SubstanceProps {
    substList: any[];
    configMap: { [configId: string]: EhsConfigParam };
    mode?: SubstanceDisplayMode;  // 使用 enum
    showAdditionalInfo?: boolean
}

const SubstanceInfo: React.FC<SubstanceProps> = ({
    substList,
    configMap,
    mode = SubstanceDisplayMode.LIST,  // 使用 enum 作為預設值
    showAdditionalInfo = false
}) => {
    const { t } = useTranslation();
    const isDetail = mode === SubstanceDisplayMode.DETAIL;
    const getSubstChemClassify = (categoryList: EhsPurchaseSubstCategory[]): EhsConfigParam[] => {
        return categoryList
            .map(item => configMap[item.configId])
            .filter((config): config is EhsConfigParam => config !== undefined);
    };

    return (
        <>
            {substList.map((subst, idx) => {
                const { chemCtrlNo, chemName, casNo, conLower, conUpper, conType,
                    substPhaseState, note, categoryList } = subst;
                const substChemClassify = getSubstChemClassify(categoryList);
                const conTypeConfig = configMap[conType];
                const { configIvalue, configName } = conTypeConfig || {};
                const showCon = getShowChemCon(configIvalue, conLower, conUpper, configName);

                return (
                    <React.Fragment key={`subst-${idx}`}>
                        {chemCtrlNo && (
                            <>
                                <span className="badge bg-danger">
                                    {t("text.ctrl_no")}：{chemCtrlNo}
                                </span>
                                <br />
                            </>
                        )}
                        <div className="mt-1 mb-2">
                            {chemName} ({casNo}) {showCon}
                        </div>
                        {isDetail && showAdditionalInfo && (
                            <div className="substance-detail mb-3">
                                {t('text.chemical.substance_phase_state')}： {configMap[substPhaseState]?.configName}
                                <br />
                                {t('text.chemical.substance_class')}：
                                {substChemClassify.map((chemClass: EhsConfigParam) =>
                                    <React.Fragment key={'chemclass-badge' + chemClass.configId}>
                                        <span className="d-inline-block mb-1">
                                            <ChemicalClassificationBadge item={chemClass} />
                                        </span>
                                    </React.Fragment>
                                )}
                                <br />
                                {note && <>{t('text.note')}： {note}</>}
                            </div>
                        )}
                    </React.Fragment>
                );
            })}
        </>
    );
};

export default SubstanceInfo;