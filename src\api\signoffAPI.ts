import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const SignoffAPI = {
  getSignoffListView: async (
    parms: BaseParams & {
      signIdList: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "signoff/list/view", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSignoffStatusDetail: async (
    parms: BaseParams & {
      signStatusId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "signoff/status/detail", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSignoffDetailLabUserModify: async (
    parms: BaseParams & {
      signStatusId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "signoff/detail/lab/user/modify", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSignoffDetailChemOperDisable: async (
    parms: BaseParams & {
      signStatusId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "signoff/detail/chem/oper/disable", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSignoffDetailChemSdsModify: async (
    parms: BaseParams & {
      signStatusId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "signoff/detail/chem/sds/modify", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSignoffDetailChemTransfer: async (
    parms: BaseParams & {
      signStatusId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "signoff/detail/chem/transfer", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSignoffRecordList: async (
    parms: BaseParams & {
      signStatusId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "signoff/record/search", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  checkSignoffUser: async (
    parms: BaseParams & {
      signStatusId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "signoff/permission/check", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  doSignoffAction: async (
    parms: BaseParams & {
      signStatusId: string;
      recordStatus: number;
      note: string;
    }
  ) => {
    return apiRequest(getApiAddress + "signoff/action/update", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};
