import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { EhsOshms } from "../CONNChainEHS/models/EhsOshms";
import { EhsFile } from "../CONNChainEHS/models/EhsFile";
import { createLoginPostJsonConfig, createLoginPostFormDataConfig, getApiAddress, handleFetchResponse } from "./config";

export const OshmsAPI = {
  getOshmsList: async (
      parms: BaseParams
  ) => {
    return fetch(getApiAddress + "oshms/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getOshmsById: async (
      parms: BaseParams & {
        oshmsId: string;
      }
  ) => {
    return fetch(getApiAddress + "oshms/detail", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addOshms: async (
      parms: BaseParams & {
        oshms: EhsOshms;
        fileList?: EhsFile[];
      }
  ) => {
    const formData = new FormData();
    const jsonParams = Object.keys(parms).reduce((acc, key) => {
      acc[key] = (parms as any)[key]; // 使用類型斷言
      return acc;
    }, {} as any);

    jsonParams.fileList = parms.fileList?.map((file: EhsFile) => {
      return {
        ...file,
        fileContent: [], // 將 fileContent 設定為空陣列
      };
    });
    formData.append("jsonString", JSON.stringify(jsonParams));
    
    // 將檔案列表添加到FormData中
    if (parms.fileList) {
      parms.fileList.forEach((file, index) => {
        if (file.fileObj) {
          // 確認 file.fileObj 存在
          formData.append(`multiFileList[${index}]`, file.fileObj);
        }
      });
    }

    return fetch(getApiAddress + "oshms/add", createLoginPostFormDataConfig(parms, formData)).then((res) => handleFetchResponse(res));
  },
  editOshms: async (
      parms: BaseParams & {
        oshms: EhsOshms;
        fileList?: EhsFile[];
        delFileIdList: string[];
      }
  ) => {
    const formData = new FormData();
    const jsonParams = Object.keys(parms).reduce((acc, key) => {
      acc[key] = (parms as any)[key]; // 使用類型斷言
      return acc;
    }, {} as any);

    jsonParams.fileList = parms.fileList?.map((file: EhsFile) => {
      return {
        ...file,
        fileContent: [], // 將 fileContent 設定為空陣列
      };
    });
    formData.append("jsonString", JSON.stringify(jsonParams));

    // 將檔案列表添加到FormData中
    if (parms.fileList) {
      parms.fileList.forEach((file, index) => {
        if (file.fileObj) {
          // 確認 file.fileObj 存在
          formData.append(`multiFileList[${index}]`, file.fileObj);
        }
      });
    }

    return fetch(getApiAddress + "oshms/edit", createLoginPostFormDataConfig(parms, formData)).then((res) => handleFetchResponse(res));
  },
  editOshmsStatus: async (
      parms: BaseParams & {
        oshmsId: string;
        oshmsStatus: string;
      }
  ) => {
    return fetch(getApiAddress + "oshms/status/edit", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  editDownloadStatus: async (
      parms: BaseParams & {
        oshmsId: string;
        isDownloadable: string;
      }
  ) => {
    return fetch(getApiAddress + "oshms/isdownload/edit", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  editOshmsSort: async (
      parms: BaseParams & {
        oshmsList?: EhsOshms[];
      }
  ) => {
    return fetch(getApiAddress + "oshms/sort/edit", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  deleteOshms: async (
      parms: BaseParams & {
        oshmsId: string;
      }
  ) => {
    return fetch(getApiAddress + "oshms/delete", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};