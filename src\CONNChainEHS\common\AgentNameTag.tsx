import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';

interface AgentNameProps {
    agentId?: string | null;
    agentName?: string | null;
    className?: string;
    prefix?: string;
    suffix?: string;
    withBreak?: boolean;
}

export const AgentNameTag: React.FC<AgentNameProps> = ({
    agentId,
    agentName,
    className,
    prefix = '',
    suffix = '',
    withBreak = false
}) => {
    const { t } = useTranslation();
    if (!agentId || !agentName) return null;

    return (
        <StyledAgentNameTag className={className}>
            {withBreak && <br />}
            {prefix}{t('text.agent_prefix')}{agentName || agentId}{suffix}
        </StyledAgentNameTag>
    );
};

const StyledAgentNameTag = styled.span`
    color: #6c757d;  // Bootstrap 的 text-muted 顏色
    margin-left: 4px;
    font-size: 0.9em;
`;