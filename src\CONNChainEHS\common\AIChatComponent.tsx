import React, { useCallback, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { AiAPI, generateSessionId } from "../../api/aiAPI";
import useLoginUser from "../hooks/useLoginUser";
import { getBasicLoginUserInfo } from "../utils/authUtil";
import { isEnterKey } from "../utils/stringUtil";

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  isSystemGenerate?: boolean;
}

// 使用次數狀態接口
interface UsageState {
  usedCount: number;
  maxCount: number;
  isLimitReached: boolean;
  isChecking: boolean;
}

// 主要組件
const AIChatComponent: React.FC = () => {
  const { t } = useTranslation();
  const { loginUser } = useLoginUser();
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      text: t("text.ai_welcome_message"),
      isUser: false,
      timestamp: new Date(),
      isSystemGenerate: true,
    },
  ]);
  const [inputText, setInputText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string>("");
  const [usageState, setUsageState] = useState<UsageState>({
    usedCount: 0,
    maxCount: 20,
    isLimitReached: false,
    isChecking: false
  });
  const chatBodyRef = useRef<HTMLDivElement>(null);

  // 初始化會話ID
  useEffect(() => {
    setSessionId(generateSessionId());
  }, []);

  // 檢查使用次數
  const checkUsageLimit = useCallback(async () => {
    if (!loginUser) return;

    setUsageState(prev => ({ ...prev, isChecking: true }));

    try {
      const userInfo = getBasicLoginUserInfo(loginUser);
      const response = await AiAPI.checkUsage(userInfo);

      if (response.success && response.results) {
        const usageData = response.results;
        setUsageState(prev => ({
          ...prev,
          usedCount: usageData.usedCount,
          maxCount: usageData.maxCount,
          isLimitReached: usageData.isLimitReached,
          isChecking: false
        }));
      } else {
        console.error(t("message.usage_check_failed") + ":", response.resultFailMsg || "Unknown error");
        setUsageState(prev => ({ ...prev, isChecking: false }));
      }
    } catch (error) {
      console.error(t("message.usage_check_failed") + ":", error);
      setUsageState(prev => ({ ...prev, isChecking: false }));
    }
  }, [loginUser, t]);

  // 組件初始化時檢查使用次數
  useEffect(() => {
    if (isOpen && loginUser) {
      checkUsageLimit();
    }
  }, [checkUsageLimit, isOpen, loginUser]);

  // 自動滾動到底部的函數
  const scrollToBottom = () => {
    if (chatBodyRef.current) {
      chatBodyRef.current.scrollTop = chatBodyRef.current.scrollHeight;
    }
  };

  // 當訊息更新時自動滾動到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages, isLoading]);

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputText(e.target.value);
  };

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading || usageState.isLimitReached || usageState.isChecking) return;

    // 添加用戶訊息
    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prevMessages => [...prevMessages, userMessage]);
    setInputText("");
    setIsLoading(true);

    try {
      // 獲取當前用戶的登入資訊
      const userInfo = getBasicLoginUserInfo(loginUser);

      // 準備AI聊天請求參數
      const aiRequestParams = {
        ...userInfo,
        message: inputText,
        sessionId: sessionId,
        context: {
          previousMessages: messages.filter(msg => !msg.isSystemGenerate).slice(-5) // 過濾掉系統錯誤訊息，只發送最近5條正常訊息作為上下文
        }
      };

      // 發送AI聊天請求
      const response = await AiAPI.sendChatMessage(aiRequestParams);
      console.log("response", response);

      // 先存起來response.results
      const aiResult = response.results;
      console.log("aiResult", aiResult);

      // 檢查響應中的results
      if (aiResult && aiResult.response) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: aiResult.response,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prevMessages => [...prevMessages, aiMessage]);

        // 更新sessionId如果AI返回了新的
        if (aiResult.sessionId && aiResult.sessionId !== sessionId) {
          setSessionId(aiResult.sessionId);
        }

        // AI 回應成功後重新檢查使用次數，因為後端會自動增加使用次數
        await checkUsageLimit();
      } else {
        throw new Error(aiResult?.errorMessage || response.resultFailMsg || t("message.ai_service_error"));
      }
    } catch (error) {
      console.error(t("message.ai_chat_error") + ":", error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: t("message.ai_service_unavailable", { error: error instanceof Error ? error.message : t("message.unknown_error") }),
        isUser: false,
        timestamp: new Date(),
        isSystemGenerate: true, // 標記為系統錯誤訊息
      };
      setMessages(prevMessages => [...prevMessages, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (isEnterKey(e)) {
      handleSendMessage();
    }
  };

  // 檢查是否應該禁用輸入
  const isInputDisabled = isLoading || usageState.isLimitReached || usageState.isChecking;

  return (
    <>
      <ChatButton onClick={toggleChat} title={t("button.regulatory_service")}>
        <i className="fas fa-comment-dots"></i>
      </ChatButton>

      {isOpen && (
        <ChatWindow>
          <ChatHeader>
            <div className="chat-title">
              <i className="fas fa-robot me-2"></i>
              {t("text.regulatory_customer_service")} {t("text.beta_version_info")}
            </div>
            <button className="close-button" onClick={toggleChat} title={t("button.close")}>
              <i className="fas fa-times"></i>
            </button>
          </ChatHeader>
          <ChatBody ref={chatBodyRef}>
            {/* 使用次數顯示 */}
            <UsageIndicator>
              {usageState.isChecking ? (
                <span>{t("message.checking_usage_limit")}</span>
              ) : (
                <span>{t("text.usage_count", { usedCount: usageState.usedCount, maxCount: usageState.maxCount })}</span>
              )}
              {usageState.isLimitReached && (
                <span className="limit-reached">{t("text.usage_limit_reached")}</span>
              )}
            </UsageIndicator>

            {messages.map((message) => (
              <MessageBubble key={message.id} $isUser={message.isUser}>
                {message.text}
              </MessageBubble>
            ))}
            {isLoading && (
              <MessageBubble $isUser={false}>
                <LoadingDots>
                  <span></span>
                  <span></span>
                  <span></span>
                </LoadingDots>
              </MessageBubble>
            )}
          </ChatBody>
          <ChatFooter>
            <ChatInput
              type="text"
              placeholder={usageState.isLimitReached ? t("placeholder.usage_limit_exceeded") : t("placeholder.type_message")}
              value={inputText}
              onChange={handleInputChange}
              onKeyDown={handleKeyPress}
              disabled={isInputDisabled}
            />
            <SendButton
              onClick={handleSendMessage}
              disabled={isInputDisabled}
              title={usageState.isLimitReached ? t("placeholder.usage_limit_exceeded") : t("button.send")}
            >
              <i className={isLoading ? "fas fa-spinner fa-spin" : "fas fa-paper-plane"}></i>
            </SendButton>
          </ChatFooter>
        </ChatWindow>
      )}
    </>
  );
};

// 樣式組件定義
const ChatButton = styled.button`
  position: fixed;
  bottom: 10px; /* 將按鈕放在底部，回到頂部按鈕會在上方 */
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #007bff;
  color: white;
  border: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 999;
  transition: all 0.3s ease;

  i {
    font-size: 24px;
  }

  &:hover {
    background-color: #0056b3;
    transform: scale(1.05);
  }
`;

const ChatWindow = styled.div`
  position: fixed;
  bottom: 80px; /* 減少與按鈕的距離 */
  right: 20px;
  width: 350px;
  height: 450px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  overflow: hidden;
  
  @media (max-width: 480px) {
    width: 90%;
    height: 400px;
    right: 5%;
    bottom: 90px; /* 確保在移動設備上也保持一致的距離 */
  }
`;

const ChatHeader = styled.div`
  background-color: #007bff;
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .chat-title {
    font-weight: bold;
    display: flex;
    align-items: center;
    font-size: 14px;
  }

  .close-button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 16px;
  }
`;

const UsageIndicator = styled.div`
  background-color: #f8f9fa;
  padding: 8px 12px;
  margin-bottom: 10px;
  border-radius: 8px;
  font-size: 12px;
  color: #6c757d;
  text-align: center;
  border: 1px solid #e9ecef;

  .limit-reached {
    color: #dc3545;
    font-weight: bold;
    margin-left: 10px;
  }
`;

const ChatBody = styled.div`
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
  scroll-behavior: smooth; /* 添加平滑滾動效果 */
`;

const MessageBubble = styled.div<{ $isUser: boolean }>`
  max-width: 80%;
  padding: 10px 15px;
  border-radius: 18px;
  background-color: ${(props) => (props.$isUser ? "#e1f5fe" : "#f1f0f0")};
  color: #333;
  align-self: ${(props) => (props.$isUser ? "flex-end" : "flex-start")};
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  word-break: break-word;
`;

const ChatFooter = styled.div`
  padding: 10px;
  display: flex;
  align-items: center;
  border-top: 1px solid #eee;
`;

const ChatInput = styled.input<{ disabled?: boolean }>`
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 20px;
  outline: none;
  margin-right: 10px;
  background-color: ${props => props.disabled ? '#f8f9fa' : 'white'};
  color: ${props => props.disabled ? '#6c757d' : '#333'};
  cursor: ${props => props.disabled ? 'not-allowed' : 'text'};

  &:focus {
    border-color: ${props => props.disabled ? '#ddd' : '#007bff'};
  }

  &::placeholder {
    color: ${props => props.disabled ? '#adb5bd' : '#999'};
  }
`;

const SendButton = styled.button<{ disabled?: boolean }>`
  background-color: ${props => props.disabled ? '#ccc' : '#007bff'};
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${props => props.disabled ? '#ccc' : '#0056b3'};
  }
`;

const LoadingDots = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  
  span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #007bff;
    animation: loading 1.4s infinite ease-in-out;
    
    &:nth-child(1) {
      animation-delay: -0.32s;
    }
    
    &:nth-child(2) {
      animation-delay: -0.16s;
    }
  }
  
  @keyframes loading {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
`;

export default AIChatComponent;