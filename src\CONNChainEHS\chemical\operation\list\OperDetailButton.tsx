import { AppPaths } from 'config/app-paths';
import { InventoryStatus } from 'ehs/enums/InventoryStatus';
import { isOperItemDetailMode, OperDetailPageMode } from 'ehs/enums/OperDetailPageMode';
import { EhsChemicalInventory, initEhsChemicalInventory } from 'ehs/models/EhsChemicalInventory';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
interface OperDetailButtonProps {
    inventoryItem: EhsChemicalInventory;
    operDetailPageMode?: OperDetailPageMode;
}

const OperDetailButton = ({ inventoryItem = initEhsChemicalInventory, operDetailPageMode = OperDetailPageMode.OPER }: OperDetailButtonProps) => {
    const { inventoryId, inventoryStatus, labId,  chemId, chemConId, phaseState } = inventoryItem;
    const { t } = useTranslation();
    const navigate = useNavigate();
    const isShowDetailText = [InventoryStatus.DISABLED, InventoryStatus.DISPOSAL].includes(inventoryStatus);

    const navigateToOperDetailPage = () => {
        const paramStateObj = {
            labId, chemId, chemConId, phaseState, operDetailPageMode,
            ...(isOperItemDetailMode(operDetailPageMode) && { inventoryId })
        };
        navigate(`/${AppPaths.chemical.operationDetail}`, { state: paramStateObj });
    }

    return (<button type="button" className="btn btn-sm btn-success my-1" onClick={() => navigateToOperDetailPage()}><i className="fas fa-flask fa-lg me-1" />{t('button.chemical.operation')}{isShowDetailText ? ' ' + t('button.detail') : ''}</button>);
};

export default OperDetailButton;
