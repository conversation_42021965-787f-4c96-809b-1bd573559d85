import styled from "styled-components";
import { useTranslation } from "react-i18next";
import BlockUi from "@availity/block-ui";
import { ConfigAPI } from "api/configAPI";
import { ManufacturerAPI } from "api/manufacturerAPI";
import { OptionAPI } from "api/optionAPI";
import { ProductAPI } from "api/productAPI";
import { PurchaseAPI } from "api/purchaseAPI";
import { SelectListAPI } from "api/selectListAPI";
import BlockuiMsg from "ehs/common/BlockuiMsg";
import Dialog from "ehs/common/Dialog";
import SearchMethod from "ehs/common/search/SearchMethod";
import { errorMsg } from "ehs/common/SwalMsg";
import { showWarnToast, showSuccessToast } from "ehs/common/Toast";
import { SEARCH_METHOD_FUZZY, OPTION_VAL_NEED_APPROVAL_QTY_TYPE_LAB, OPTION_VAL_NEED_NOTIFY_PURCHASE_UNIT, CONFIG_ID_CHEM_CLASS_LV_CTRL, CONFIG_TYPE_STATE, CONFIG_TYPE_PACKTYPE, CONFIG_TYPE_PACKMAL, CONFIG_TYPE_CHEM_CLASS_LV, OPTION_NEED_APPROVAL_QTY_TYPE, OPTION_NOTIFY_PURCHASE_RECIPIENT_TYPE, OPTION_NOTIFY_PURCHASE_RECIPIENT, CHEMICAL_STATUS_ENABLE, CONFIG_ID_CHEM_CLASS_LV_GENERAL, PURCHASE_QTY_LIMIT_MAX, CONFIG_VAL_UNCATEGORIZED, CONFIG_TYPE_PUBLIC_HAZARD, CONFIG_TYPE_GHS_IMG, CONFIG_TYPE_GHS_CLASS, CONFIG_TYPE_CHEM, CONFIG_TYPE_MIX_CONCEN_TYPE } from "ehs/constant/constants";
import useLoginUser from "ehs/hooks/useLoginUser";
import Breadcrumbs from "ehs/layout/Breadcrumbs";
import Footer from "ehs/layout/Footer";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsConfigMapping } from "ehs/models/EhsConfigParamMapping";
import { EhsLab, initEhsLab } from "ehs/models/EhsLab";
import { EhsManufacturer } from "ehs/models/EhsManufacturer";
import { EhsOptions, initEhsOptions } from "ehs/models/EhsOptions";
import { EhsProduct, initEhsProduct } from "ehs/models/EhsProduct";
import { Recipient } from "ehs/models/Recipient";
import { SelectItem, initSelectItem } from "ehs/models/SelectItem";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { getBasicLoginUserInfo, checkTimeoutAction } from "ehs/utils/authUtil";
import { getFirstChemNames } from "ehs/utils/chemicalUtil";
import { getLabTextObj, notChineseLang } from "ehs/utils/langUtil";
import { accSubMulti, accSub, accAdd, accDiv, accMul } from "ehs/utils/numUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { isEnterKey } from "ehs/utils/stringUtil";
import { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import ProductDetail from "./ProductDetail";
import RecipientEdit from "./RecipientEdit";
import { LabAPI } from "api/labAPI";
interface CartItem extends EhsProduct {
  quantity: number;
  canPurchaseQuantity: number | null;
}
interface ChemicalQty {
  canPurchaseQuantity: number | null;
  nowQuantities: {
    productId: string;
    quantity: number;
  }[];
  remainQty: number;
  avalibleQuantity: number;
  isOverLimit: boolean;
}

function PurchaseRequest() {
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const { labText, labAttributeNoMatchChem, msgPurchaseNoLab, msgPurchaseLabAttrNoMatch, tableTitleLabInventory,
    tableTitleLabInTransitQtyDesc } = getLabTextObj(loginUser, t);
  const navigate = useNavigate();
  const [isQuery, setIsQuery] = useState<boolean>(false); //判斷是否已按搜尋
  const [loadingBlock, setLoadingBlock] = useState(false);
  const [fetchLabOptionSuccess, setFetchLabOptionSuccess] = useState<boolean>(false); //判斷是否抓取實驗室選項完成
  const [canFetchProducts, setCanFetchProducts] = useState<boolean>(false); //判斷能否從api抓取商品
  const [showProductDetail, setShowProductDetail] = useState<boolean>(false); //是否顯示商品明細 
  const [showRecipientEdit, setShowRecipientEdit] = useState<boolean>(false); //是否顯示收件人編輯
  const [searchKeyword, setSearchKeyword] = useState<string>(""); //搜尋關鍵字
  const [searchCtrlNo, setSearchCtrlNo] = useState<string>(""); //搜尋列管
  const [searchCasNo, setSearchCasNo] = useState<string>(""); //搜尋casno
  const [purchaseNote, setPurchaseNote] = useState<string>(""); //採購備註
  const [searchMethod, setSearchMethod] = useState(SEARCH_METHOD_FUZZY);
  const [chemApprovalType, setChemApprovalType] = useState<EhsOptions>(initEhsOptions);
  const [purchaseNotifyType, setPurchaseNotifyType] = useState<EhsOptions>(initEhsOptions);
  const [recipientPurchaseUnit, setRecipientPurchaseUnit] = useState<Recipient[]>([]); // 採購單位收件人
  const [recipient, setRecipient] = useState<Recipient[]>([]); // 供應商收件人
  const [productDetail, setProductDetail] = useState<EhsProduct>(initEhsProduct); //顯示商品明細
  const [selectLabOption, setSelectLabOption] = useState<EhsLab>(initEhsLab); // 選中的實驗室選項
  const [labChemClassifyMap, setLabChemClassifyMap] = useState<EhsConfigMapping[]>([]); // 實驗室屬性對應的化學品分類
  const [labOption, setLabOption] = useState<EhsLab[]>([]); // 實驗室選項
  const [vendorOption, setVendorOption] = useState<SelectItem[]>([]); // 供應商選項
  const [products, setProducts] = useState<EhsProduct[]>([]); //查詢出的商品
  const [carts, setCarts] = useState<CartItem[]>([]); //加入購買的商品 
  const [chemicalQtyMap, setChemicalQtyMap] = useState<{
    [id: string]: ChemicalQty;
  }>({}); //計算目前各化學品的數量
  const [vendorMap, setVendorMap] = useState<{
    [manufacturerId: string]: EhsManufacturer;
  }>({}); // 供應商對應
  const [configMap, setConfigMap] = useState<{
    [configId: string]: EhsConfigParam;
  }>({}); // config對應
  const [productCategoryMap, setProductCategoryMap] = useState<{
    [productId: string]: string[];
  }>({}); // config對應
  const [selectVendorId, setSelectVendorId] = useState<string>(""); // 供應商選中值
  const selectVendorRef = useRef<HTMLSelectElement>(null); // 供應商下拉
  const selectedVendorContent = selectVendorRef?.current?.selectedOptions[0]?.text || "";
  const initCartItem: CartItem = {
    ...initEhsProduct,
    quantity: 1,
    canPurchaseQuantity: 0

  };
  // 基礎變數
  const { areaId, labId } = selectLabOption;
  const needLabApproval = chemApprovalType.optionValue === OPTION_VAL_NEED_APPROVAL_QTY_TYPE_LAB;
  const notifyPurchaseUnit = purchaseNotifyType.optionValue === OPTION_VAL_NEED_NOTIFY_PURCHASE_UNIT;

  // 依賴於前面變數的條件變數
  const notifyTitle = notifyPurchaseUnit ? t('text.recipient.purchase_unit') : t('text.recipient.manufacturer');
  const checkedRecipients = recipient.filter(recipient => recipient.checked);
  const recipientArray = notifyPurchaseUnit ? recipientPurchaseUnit : checkedRecipients;
  const notEmptyRecipient = !isArrayEmpty(recipientArray);

  // 判斷類或計算變數
  const hasAddProduct = !isArrayEmpty(carts);
  const showPurchaseBtn = notEmptyRecipient && hasAddProduct;
  const notChinese = notChineseLang(i18n.language);
  const notChineseSpace = notChinese ? ' ' : '';

  // 標題和顯示文字
  const RecipientEditTitle = t('button.edit') + notChineseSpace + t('text.recipient.manufacturer');

  // 特定功能
  const labChemClassify = !isArrayEmpty(labChemClassifyMap) ? labChemClassifyMap.map(item => item.configId) : [];
  const isChemClassLvCtrl = (value: string) => value === CONFIG_ID_CHEM_CLASS_LV_CTRL;


  useEffect(() => {
    if (isQuery) {
      fetchOption();
    }
  }, [isQuery]);
  useEffect(() => {
    if (isQuery) {
      fetchConfig();
    }
  }, [isQuery, i18n.language]);
  useEffect(() => {
    if (loginUser) {
      fetchLabOption();
      fetchVendorOption();
    }
  }, [loginUser, i18n.language]);
  useEffect(() => {
    if (labId) {
      fetchChemClassByLabAttr();
    }
  }, [labId]);

  useEffect(() => {
    const updatedChemicalQtyMap = { ...chemicalQtyMap };
    let hasChanges = false;
    Object.keys(updatedChemicalQtyMap).forEach(id => {
      const chemical = updatedChemicalQtyMap[id];
      const totalNowQuantity = calculateTotalNowQuantity(chemical.nowQuantities);
      const newIsOverLimit = chemical.canPurchaseQuantity !== null && totalNowQuantity > chemical.canPurchaseQuantity;

      if (chemical.isOverLimit !== newIsOverLimit) {
        chemical.isOverLimit = newIsOverLimit;
        hasChanges = true;
      }
    });

    if (hasChanges) {
      setChemicalQtyMap(updatedChemicalQtyMap);
    }
  }, [chemicalQtyMap]);

  const fetchLabOption = () => {
    LabAPI.getChemicalLabList({
      ...getBasicLoginUserInfo(loginUser)!,
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const labOptionResult = result.results;

        if (!isArrayEmpty(labOptionResult)) {
          setSelectLabOption(labOptionResult[0]);
        }

        setLabOption(labOptionResult);
        setFetchLabOptionSuccess(true);
      }
    }).catch(error => {
      checkTimeoutAction(error, navigate, t);
    });
  };

  const fetchChemClassByLabAttr = () => {
    ConfigAPI.getChemclassMappingByLabid({
      ...getBasicLoginUserInfo(loginUser),
      labId
    }).then(result => {
      if (isApiCallSuccess(result)) {
        setLabChemClassifyMap(result.results);
      }
    });
  };

  const fetchVendorOption = () => {
    ManufacturerAPI.getEnableManufacturerList({
      ...getBasicLoginUserInfo(loginUser)!,
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const getEnableManufacturerList = result.results;
        const selectItems: SelectItem[] = getEnableManufacturerList.map((item: EhsManufacturer) => ({
          ...initSelectItem,
          label: item.manufacturerName,
          value: item.manufacturerId
        }));
        setVendorOption(selectItems);
        setVendorMap(getEnableManufacturerList.reduce((acc: {
          [configId: string]: EhsManufacturer;
        }, config: EhsManufacturer) => {
          acc[config.manufacturerId] = config;
          return acc;
        }, {}));
      }
    }).catch(error => {
      checkTimeoutAction(error, navigate, t);
    });
  };

  const fetchConfig = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser),
      configTypeList: [CONFIG_TYPE_CHEM, CONFIG_TYPE_STATE, CONFIG_TYPE_PACKTYPE, CONFIG_TYPE_PACKMAL, CONFIG_TYPE_CHEM_CLASS_LV,
        CONFIG_TYPE_PUBLIC_HAZARD, CONFIG_TYPE_GHS_CLASS, CONFIG_TYPE_GHS_IMG, CONFIG_TYPE_MIX_CONCEN_TYPE
      ]
    }).then(result => {
      if (isApiCallSuccess(result)) {
        setConfigMap(result.results.reduce((acc: {
          [configId: string]: EhsConfigParam;
        }, config: EhsConfigParam) => {
          acc[config.configId] = config;
          return acc;
        }, {}));
      }
    });
  };
  /**
  * Fetches the options with the specified IDs and sets the corresponding state variables.
  *
  * @return {void} This function does not return anything.
  */


  const fetchOption = () => {
    const optionIds = [OPTION_NEED_APPROVAL_QTY_TYPE, OPTION_NOTIFY_PURCHASE_RECIPIENT_TYPE, OPTION_NOTIFY_PURCHASE_RECIPIENT];
    OptionAPI.getOptionListByIds({
      ...getBasicLoginUserInfo(loginUser),
      optionIdList: optionIds
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const options = result.results;
        const optionsMap: {
          [key: string]: EhsOptions;
        } = {};
        let recipientArray: Recipient[] = [];
        options.forEach((item: EhsOptions) => {
          switch (item.optionId) {
            case OPTION_NOTIFY_PURCHASE_RECIPIENT:
              recipientArray = JSON.parse(item.optionValue);
              break;
            default:
              if (optionIds.includes(item.optionId)) {
                optionsMap[item.optionId] = item;
              }
              break;
          }
        });
        setChemApprovalType(optionsMap[OPTION_NEED_APPROVAL_QTY_TYPE]);
        setPurchaseNotifyType(optionsMap[OPTION_NOTIFY_PURCHASE_RECIPIENT_TYPE]);
        setRecipientPurchaseUnit(recipientArray);
      }
    });
  };

  const fetchProducts = () => {
    ProductAPI.getProductList({
      ...getBasicLoginUserInfo(loginUser),
      areaId,
      manufacturerId: selectVendorId,
      searchMethod: searchMethod,
      keyword: searchKeyword,
      chemCtrlNo: searchCtrlNo,
      casNo: searchCasNo
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const rs = result.results;
        setProducts(rs);

        if (isArrayEmpty(rs)) {
          showWarnToast(t('message.no_data'));
          setCanFetchProducts(true);
          return;
        }
      }

      setCanFetchProducts(false);
    });
  };

  const fetchPurchaseRecipient = (manufacturerId: string) => {
    PurchaseAPI.getPurchaseRecipient({
      ...getBasicLoginUserInfo(loginUser),
      manufacturerId
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const rs = result.results;
        const purchaseRecipients = rs.purchaseRecipients;
        const transformedRecipients = rs.recipients.map((recipient: any) => ({
          id: recipient.accountId,
          name: recipient.userName,
          email: recipient.email,
          phone: recipient.cellphone,
          checked: purchaseRecipients.some((purchaseRecipient: any) => purchaseRecipient.accountId === recipient.accountId)
        }));
        setRecipient(transformedRecipients);
      }
    });
  };

  const clickSearch = () => {
    let rs = Boolean(selectVendorId || searchKeyword.trim() || searchCtrlNo.trim() || searchCasNo.trim());

    if (!rs || !canFetchProducts) {
      showWarnToast(t('message.search.condition_required_modify'));
      return;
    }

    searchProducts();
  };

  const handleKeyDown = (e: any) => {
    if (isEnterKey(e)) {
      searchProducts();
    }
  };

  const searchProducts = () => {
    if (canFetchProducts) {
      setIsQuery(true);
      fetchProducts();
    }
  };

  const addShoppingList = (product: EhsProduct, noChemClassExist: boolean) => {
    if (noChemClassExist) {
      showWarnToast(msgPurchaseLabAttrNoMatch);
      return;
    } else if (carts.some(item => item.productId === product.productId)) {
      // 檢查購物車中是否已經存在該商品
      // 如果已經存在,則提示用戶無法加入
      showWarnToast(t('message.purchase.duplicate_product'));
      return;
    } else if (hasAddProduct && carts[0].chemClassLv !== product.chemClassLv) {
      //一般列管不能混著買
      const cartChemClassLv = configMap[carts[0].chemClassLv]?.configName || t('text.general')
      const prodChemClassLv = configMap[product.chemClassLv]?.configName || t('text.general')

      showWarnToast(t('message.purchase.product_already_in_cart_error_classlv', { cartType: cartChemClassLv, prodType: prodChemClassLv }));

      return;
    } else if (product.chemStatus !== CHEMICAL_STATUS_ENABLE) {
      //化學品停止新增不能買
      showWarnToast(t('message.purchase.chemical_disabled'));
      return;
    } else if (!isArrayEmpty(carts) && selectVendorId && product.manufacturerId !== selectVendorId) {
      //供應商不同不能買
      showWarnToast(t('message.purchase.vendor_not_match', { vendor: selectedVendorContent }));
      return;
    }

    const {
      chemId,
      chemConId,
      productId,
      weight,
      manufacturerId,
      ctrlTotalQtyType
    } = product;
    const needQueryPurchaseRecipient = !notifyPurchaseUnit && !hasAddProduct; //列管檢查

    if (isChemClassLvCtrl(product.chemClassLv)) {
      PurchaseAPI.checkPurchaseProduct({
        ...getBasicLoginUserInfo(loginUser),
        needLabApproval,
        ctrlTotalQtyType,
        chemId: chemId,
        chemConId: chemConId,
        areaId: areaId,
        labId: labId,
      }).then(result => {
        if (isApiCallSuccess(result)) {
          const purchaseQuantityInfo = result.results;
          const {
            avalibleQuantity,
            inTransitQuantity,
            labInventoryQuantity
          } = purchaseQuantityInfo;
          let canPurchaseQuantity: number | null = null;
          let remainPurchaseQuantity = 0;
          // console.log("🚀 ~ addShoppingList ~ ctrlTotalQtyType:", ctrlTotalQtyType)
          // console.log("🚀 ~ addShoppingList ~ avalibleQuantity:", avalibleQuantity)

          if (avalibleQuantity) {
            const hasChemicalQty: ChemicalQty = chemicalQtyMap[chemConId]; //有管制量

            canPurchaseQuantity = hasChemicalQty ? hasChemicalQty.canPurchaseQuantity : parseFloat(accSubMulti(avalibleQuantity, inTransitQuantity, labInventoryQuantity)); //TODO 需要扣除已加入相同化學品不同商品的量

            remainPurchaseQuantity = parseFloat(accSubMulti(avalibleQuantity, calculateTotalNowQuantity(hasChemicalQty?.nowQuantities || []), product.weight));

            if (remainPurchaseQuantity <= 0) {
              //化學品剩餘量不足
              showWarnToast(t('message.purchase.over_limit_qty'));
              return;
            }

            setChemicalQtyMap(prevMap => {
              const existingChemical = prevMap[chemConId] || {
                canPurchaseQuantity: null,
                avalibleQuantity: 0,
                nowQuantities: []
              };
              const updatedNowQuantities = [...existingChemical.nowQuantities, {
                productId,
                quantity: weight
              }];
              return {
                ...prevMap,
                [chemConId]: {
                  ...existingChemical,
                  avalibleQuantity,
                  canPurchaseQuantity,
                  nowQuantities: updatedNowQuantities,
                  remainQty: remainPurchaseQuantity
                }
              };
            });
          }

          if (needQueryPurchaseRecipient) {
            fetchPurchaseRecipient(manufacturerId);
          } // 如果不存在,則將商品添加到購物車


          setCarts([...carts, {
            ...initCartItem,
            ...product,
            canPurchaseQuantity
          }]);
          setSelectVendorId(manufacturerId);
        }
      });
    } else {
      //非列管
      if (needQueryPurchaseRecipient) {
        fetchPurchaseRecipient(manufacturerId);
      }

      setCarts([...carts, {
        ...initCartItem,
        ...product,
        canPurchaseQuantity: null
      }]);
      setSelectVendorId(manufacturerId);
    }
  };

  const removeFromShoppingList = (product: EhsProduct) => {
    const {
      chemConId,
      productId
    } = product;
    setCarts(carts.filter(item => item.productId !== productId));
    const existingChemical = chemicalQtyMap[chemConId];

    if (existingChemical) {
      const updatedNowQuantities = existingChemical.nowQuantities.filter(item => item.productId !== productId);
      const remainQty = parseFloat(accSub(existingChemical.avalibleQuantity, calculateTotalNowQuantity(updatedNowQuantities)));
      setChemicalQtyMap(prevMap => {
        return {
          ...prevMap,
          [chemConId]: {
            ...existingChemical,
            nowQuantities: updatedNowQuantities,
            remainQty
          }
        };
      });
    }
  };

  const updateQuantity = (index: number, quantity: number) => {
    setCarts(prevCarts => {
      const newCarts = [...prevCarts];
      newCarts[index].quantity = quantity;
      return newCarts;
    });
  };

  const calculateTotalNowQuantity = (nowQuantities: {
    productId: string;
    quantity: number;
  }[]): number => {
    return nowQuantities.reduce((acc, item) => parseFloat(accAdd(acc, item.quantity)), 0);
  };

  const fetchAndStoreProductCategories = async (product: EhsProduct) => {
    const { productId } = product;
    const packingConfigIds = productCategoryMap[productId];

    // 如果已經有分類資料，直接返回
    if (packingConfigIds) {
      return;
    }

    try {
      const result = await ProductAPI.getProductCategoryId({
        ...getBasicLoginUserInfo(loginUser),
        productId
      });

      if (isApiCallSuccess(result)) {
        const packingConfigIds = result.results;
        setProductCategoryMap(prevMap => ({
          ...prevMap,
          [productId]: packingConfigIds
        }));
      }
    } catch (error) {
      // 處理錯誤
      console.error('獲取商品分類失敗:', error);
    }
  };

  const fetchAndStoreProductSubstances = async (product: EhsProduct) => {
    const { productId } = product;

    // 如果已經有物質列表資料，直接返回
    if (product.substanceList && product.substanceList.length > 0) {
      return;
    }

    try {
      const result = await ProductAPI.getProductSubstanceById({
        ...getBasicLoginUserInfo(loginUser),
        productId
      });

      if (isApiCallSuccess(result)) {
        const substances = result.results;

        setProducts(prevProducts => {
          const updatedProducts = prevProducts.map(prod => {
            if (prod.productId === productId) {
              const updatedProduct = { ...prod, substanceList: substances };
              return updatedProduct;
            }
            return prod;
          });
          return updatedProducts;
        });

        // 更新 productDetail (如果當前顯示的是這個商品)
        setProductDetail(prev => ({
          ...prev,
          substanceList: substances
        }));
      }
    } catch (error) {
      console.error('獲取商品物質資訊失敗:', error);
    }
  };

  const submitPurchase = () => {
    setLoadingBlock(true);
    if (isArrayEmpty(carts)) {
      setLoadingBlock(false);
      return;
    }
    if (!loginUser) {
      errorMsg(t("message.login_required"));
      setLoadingBlock(false);
      return;
    }

    if (!isArrayEmpty(Object.values(chemicalQtyMap)) && Object.values(chemicalQtyMap).some(chemical => chemical.isOverLimit)) {
      showWarnToast(t("message.purchase.over_limit_qty"));
      setLoadingBlock(false);
      return;
    }

    const trimPurchaseNote = purchaseNote.trim();
    if (!trimPurchaseNote) {
      showWarnToast(t("message.purchase.note_required"));
      setLoadingBlock(false);
      return;
    }

    const chemClassLv = configMap[carts[0].chemClassLv]?.configId || CONFIG_ID_CHEM_CLASS_LV_GENERAL;

    let productIdList: string[] = [];
    let quantityList: number[] = [];

    // 使用一次迴圈同時提取 productId 和 quantity
    carts.forEach((item) => {
      productIdList.push(item.productId);
      quantityList.push(item.quantity);
    });

    PurchaseAPI.addPurchaseRequest({
      ...getBasicLoginUserInfo(loginUser),
      chemClassLv,
      manafacturerId: selectVendorId,
      labId,
      note: trimPurchaseNote,
      productIdList,
      quantityList
    }).then(result => {
      if (isApiCallSuccess(result)) {
        showSuccessToast(t("message.success"));
      }
      setLoadingBlock(false);
    }).catch(error => {
      setLoadingBlock(false);
    })
    //非採購單位 儲存使用者設定收件人 不會影響採購 另外做
    if (!notifyPurchaseUnit) {
      PurchaseAPI.modifyPurchaseRecipient({
        ...getBasicLoginUserInfo(loginUser),
        userId: loginUser!.loginUserId,
        manufacturerId: selectVendorId,
        accountIds: recipient.filter(item => item.checked).map(item => item.id)
      });
    }
  };

  return <StlyedPurchaseRequest>
    <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
      <div className="d-flex flex-column p-0" id="content">
        {<Dialog content={<ProductDetail onClose={() => {
          setShowProductDetail(false);
        }} productInfo={productDetail} configMap={configMap} vendorMap={vendorMap} productCategoryMap={productCategoryMap} />} show={showProductDetail} />}
        {<Dialog content={<RecipientEdit onClose={() => {
          setShowRecipientEdit(false);
        }} title={RecipientEditTitle} vendorName={selectedVendorContent} recipients={recipient} recipientOptions={recipient} setRecipients={setRecipient} />} show={showRecipientEdit} />}
        {
          /* BEGIN scrollbar */
        }
        <div className="app-content-padding flex-grow-1">
          {
            /* BEGIN breadcrumb */
          }
          <Breadcrumbs items={[{
            label: t("func.chemical.manage")
          }, {
            label: t("func.chemical.purchase.request")
          }]} />
          {
            /* END breadcrumb */
          }
          {
            /* BEGIN page-header */
          }
          <h1 className="page-header">{t("func.chemical.purchase.request")} </h1>
          {
            /* END page-header */
          }

          <div className="card">
            <div className="card-body">
              {isArrayEmpty(labOption) && fetchLabOptionSuccess ? <div className="row">
                <label className=" text-center h2">{msgPurchaseNoLab}</label>
              </div> : <div className="row p-3 align-items-end gy-3">
                <div className="col-xl-3" title={`${hasAddProduct ? t('text.disabled.has_add_product') : ''}`}>
                  <label>{t('text.chemical.item')}{notChinese && " "}{labText}</label>
                  <select className="form-select form-select-lg mt-1" disabled={hasAddProduct} onChange={e => {
                    setSelectLabOption(labOption.find(item => item.labId === e.target.value) || initEhsLab);
                    setCanFetchProducts(true);
                  }}>
                    {labOption.map(item => {
                      return <option value={item.labId} key={item.labId}>{item.labName}</option>;
                    })}
                  </select>
                </div>
                <div className="col-xl-3" title={`${hasAddProduct ? t('text.disabled.has_add_product') : ''}`}>
                  <label>{t('text.vendor')}</label>
                  <select className="form-select form-select-lg mt-1" disabled={hasAddProduct} ref={selectVendorRef} value={selectVendorId} onChange={e => {
                    setCanFetchProducts(true);
                    setSelectVendorId(e.target.value);
                  }}>
                    <option key="defaultOption" value="">{t("text.all")}</option>;
                    {vendorOption.map(item => {
                      return <option value={item.value} key={item.value}>{item.label}</option>;
                    })}
                  </select>
                </div>
                <div className="col-xl-3">
                  <label>{t('text.search.keyword')}</label>
                  <input type="text" className="form-control form-contorl-lg mt-1" placeholder={`${t('text.product.item')} ${t('text.search.keyword')}`}
                    onKeyDown={handleKeyDown}
                    onChange={e => {
                      setCanFetchProducts(true);
                      setSearchKeyword(e.target.value);
                    }} />
                </div>
                <div className="col-xl-3" />
                <div className="col-xl-3">
                  <label>{t('text.ctrl_no')}</label>
                  <input type="text" className="form-control form-contorl-lg mt-1" placeholder={t('text.ctrl_no')} title={t('text.ctrl_no')}
                    value={searchCtrlNo}
                    onKeyDown={handleKeyDown}
                    onChange={e => {
                      setCanFetchProducts(true);
                      setSearchCtrlNo(e.target.value);
                    }} />
                </div>
                <div className="col-xl-3">
                  <label>{t('text.chemical.casno')}</label>
                  <input type="text" className="form-control form-contorl-lg mt-1" placeholder={t('text.chemical.casno_format_msg')} title={t('text.chemical.casno_format_msg')}
                    onKeyDown={handleKeyDown}
                    onChange={e => {
                      setCanFetchProducts(true);
                      setSearchCasNo(e.target.value);
                    }} />
                </div>
                <SearchMethod className="col-xl-3" searchMethod={searchMethod} handleSearchMethodChange={e => {
                  setCanFetchProducts(true);
                  setSearchMethod(e.target.value);
                }} />
                <div className="col-xl-3">
                  <button type="button" className="btn btn-primary" onClick={() => clickSearch()}>
                    <i className="fas fa-magnifying-glass me-1" />
                    <span className="fs-5">{t('button.search.item')}{notChinese && " "}{t('text.product.item')}</span>
                  </button>
                </div>
              </div>}
            </div>
          </div>
          {
            /* <!-- 化學品採購清單 --> */
          }
          {hasAddProduct && <div className="card mt-3">
            <div className="card-body">
              <h4>{t('text.chemical.purchase_list')}</h4>
              {
                /* <!-- 化學品採購清單開始 --> */
              }
              <div className="table-responsive">
                <table className="table mt-3 text-center align-middle table-hover fs-5">
                  <thead className="align-middle bg-lime-200">
                    <tr>
                      <th>{t('text.product_item_number')}</th>
                      <th>{t('text.name')}</th>
                      <th>{t('table.title.concentration')}(%)</th>
                      <th>{t('text.chemical.phase_state')}</th>
                      {needLabApproval && <>
                        <th>{tableTitleLabInventory}</th>
                        <th>{t('table.title.chemical.in_transit_qty')}<br />({tableTitleLabInTransitQtyDesc})</th>
                      </>}
                      <th>{t('table.title.single_bottle_weight')}(kg)</th>
                      <th>{t('table.title.purchase_bottle_quantity')}</th>
                      <th>{t('table.title.purchase_quantity_total')}(kg)</th>
                      <th>{needLabApproval ? labText : t('text.org.item')}{notChineseSpace}{t('table.title.purchase_avaliable_quantity')}(kg)</th>
                      <th>{t('table.title.purchase_remaining_quantity')}(kg)</th>
                      <th>{t('table.title.action')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {carts.map((item, index) => {
                      const {
                        productId,
                        chemConId,
                        productNo,
                        concentration,
                        weight,
                        quantity,
                        phaseState,
                        canPurchaseQuantity,
                        chemNameList,
                        chemClassLv,
                      } = item;
                      const {
                        firstName,
                        firstEnName
                      } = getFirstChemNames(chemNameList, i18n.language);
                      let actualMaxOptions = PURCHASE_QTY_LIMIT_MAX;

                      if (canPurchaseQuantity !== null) {
                        // 計算根據重量和剩餘可購買數量得出的最大選項數
                        const maxOptionsBasedOnWeight = Math.floor(accDiv(canPurchaseQuantity, weight)); // 確定下拉選項的實際上限

                        actualMaxOptions = Math.min(maxOptionsBasedOnWeight, PURCHASE_QTY_LIMIT_MAX);
                      }

                      let existingChemical = chemicalQtyMap[chemConId];
                      return <tr key={index}>
                        <td data-title={t('text.product_item_number')}>{productNo}</td>
                        <td data-title={t('text.name')}>{firstName ? firstName : firstEnName}
                          {firstName && <><br /> {firstEnName}</>}</td>
                        <td data-title={t('table.title.concentration') + '(%)'}>{concentration}</td>
                        <td data-title={t('text.chemical.phase_state')}>{configMap[phaseState]?.configName}</td>
                        {needLabApproval && <>
                          <td data-title={tableTitleLabInventory}></td>
                          <td data-title={t('table.title.chemical.in_transit_qty')}></td>
                        </>}
                        <td data-title={t('table.title.single_bottle_weight') + '(kg)'}>{weight}</td>
                        <td data-title={t('table.title.purchase_bottle_quantity')}>
                          <select className="form-select form-select-lg" value={quantity} onChange={event => {
                            const newQty = parseInt(event.target.value);
                            updateQuantity(index, newQty);

                            if (isChemClassLvCtrl(chemClassLv)) {
                              if (existingChemical) {
                                const updatedNowQuantities = existingChemical.nowQuantities.map(item => item.productId === productId ? {
                                  ...item,
                                  quantity: accMul(weight, newQty)
                                } : item);
                                const remainQty = parseFloat(accSub(existingChemical.avalibleQuantity, calculateTotalNowQuantity(updatedNowQuantities)));
                                setChemicalQtyMap(prevMap => {
                                  return {
                                    ...prevMap,
                                    [chemConId]: {
                                      ...existingChemical,
                                      canPurchaseQuantity,
                                      nowQuantities: updatedNowQuantities,
                                      remainQty
                                    }
                                  };
                                });
                              }
                            }
                          }}>
                            {Array.from({
                              length: actualMaxOptions
                            }, (_, index) => index + 1).map(option => <option key={'qty_' + productId + '_' + option} value={option}>
                              {option}
                            </option>)}
                          </select>
                        </td>
                        <td data-title={t('table.title.purchase_quantity_total') + '(kg)'}>{accMul(weight, quantity)}</td>
                        <td data-title={needLabApproval ? labText : t('text.org.item') + (notChinese ? ' ' : '') + t('table.title.purchase_avaliable_quantity')}>{canPurchaseQuantity || t('text.chemical.no_limit_quantity')}</td>
                        <td data-title={t('table.title.purchase_remaining_quantity') + '(kg)'}>{canPurchaseQuantity ? existingChemical?.remainQty : t('text.chemical.no_limit_quantity')}
                          {existingChemical && existingChemical.isOverLimit && <><br /><label className="text-danger">{t('message.purchase.over_limit_qty')}</label></>}</td>
                        <td data-title={t('table.title.action')}>
                          <i className="fas fa-trash-can fa-lg text-danger" onClick={() => removeFromShoppingList(item)}></i>
                        </td>
                      </tr>;
                    })}
                  </tbody>
                </table>
              </div>
              <hr />
              <h4 className="mt-3 me-3 d-inline-block">{notifyTitle}</h4>
              {!notifyPurchaseUnit && <button type="button" className="btn btn-warning" onClick={() => setShowRecipientEdit(true)} title={t('button.edit') + notChineseSpace + t('text.recipient.manufacturer')}>
                <i className="fas fa-user-tie fa-lg"></i> {RecipientEditTitle}
              </button>}
              {notEmptyRecipient && <div className="table-responsive mt-3">
                <table className="table fs-5 text-center align-middle">
                  <thead className="bg-lime-200">
                    <tr>
                      <th>{t('text.vendor')}</th>
                      <th>{notifyTitle}</th>
                      <th>{t('text.email')}</th>
                      <th>{t('text.phone')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {recipientArray.map((recipient, index) => <tr key={'recipient' + index}>
                      {index === 0 && <td rowSpan={recipientArray.length}>{selectedVendorContent}</td>}
                      <td>{recipient.name}</td>
                      <td>{recipient.email}</td>
                      <td>{recipient.phone}</td>
                    </tr>)}
                  </tbody>
                </table>
              </div>}

              <h4 className="mt-3">{t('text.purchase.note')}<span className="text-danger ms-1">*</span></h4><br />
              <textarea
                className="form-control"
                id="note"
                rows={4}
                cols={50}
                onChange={(e) => {
                  setPurchaseNote(e.target.value)
                }}
              />
              {
                /* <!-- 送出採購button --> */
              }
              {showPurchaseBtn && <div className="row justify-content-center py-3">
                <div className={`${notChinese ? 'col-xl-2' : 'col-xl-1'} d-grid`}>
                  <button type="button" className="btn btn-success fs-5" onClick={() => submitPurchase()}>
                    <i className="fas fa-cart-shopping"></i> {t('button.submit_purchase')}</button>
                </div>
              </div>}
            </div>
          </div>}
          {
            /* <!-- 廠商販賣商品清單 --> */
          }
          {isQuery && <div className="card mt-3" id="productList">
            <div className="card-body">
              <h5>{t('text.chemical.selling_product_list')}</h5>
              <span className="text-danger">{t('text.chemical.purchase_product_limit')}</span>
              {
                /* <!-- 商品清單開始 --> */
              }
              <div className="row mt-3 px-3">
                {!isArrayEmpty(products) && products.map(product => {
                  const {
                    productId,
                    manufacturerId,
                    productNo,
                    productName,
                    chemClassLv,
                    chemCtrlNo,
                    chemStatus,
                    concentration,
                    weight,
                    productDescription,
                    isFutures,
                    futuresDay,
                    phaseState,
                    casnoList,
                    chemNameList,
                    chemClassList,
                    containsMix
                  } = product;
                  const {
                    firstName,
                    firstEnName
                  } = getFirstChemNames(chemNameList, i18n.language);
                  const compareChemClassList = isArrayEmpty(chemClassList) ? [] : chemClassList.filter(item => item.configValue !== CONFIG_VAL_UNCATEGORIZED).map(item => item.configId);
                  const noChemClassExist = !compareChemClassList.every(item => labChemClassify.includes(item));
                  if (noChemClassExist) {
                    console.log('noChemClassExist:', noChemClassExist);
                    console.log('compareChemClassList:', compareChemClassList);
                    console.log('labChemClassify:', labChemClassify);
                  }
                  return <div className="col-xl-3 border rounded-3 shadow p-3 my-3" key={productId}>
                    <div className="text-start position-relative ps-3">
                      {
                        /* <!-- 若為列管，就出現此行 ↓ --> */
                      }
                      {chemClassLv && <label className={`badge bg-danger d-block col-3`} data-toggle="tooltip" data-placement="top" title={t('text.chemical.class_status')}>{configMap[chemClassLv]?.configName}</label>}
                      <div className="mt-1"></div>
                      {chemStatus !== CHEMICAL_STATUS_ENABLE && <label className={`badge bg-danger d-block col-6`} data-toggle="tooltip" data-placement="top" title={t('text.chemical.stop_add')}> {t('text.chemical.stop_add')}</label>}
                      <div className="mt-1"></div>
                      {noChemClassExist && <label className={`badge bg-danger d-block col-6`} data-toggle="tooltip" data-placement="top" title={labAttributeNoMatchChem}> {labAttributeNoMatchChem}</label>}
                      {containsMix ?
                        <>
                          <div className="mt-1"></div>
                          <label className={`badge bg-primary d-block col-3`} data-toggle="tooltip" data-placement="top" title={t('text.product.is_mixture')}>{t('text.product.is_mixture')}</label>
                        </>
                        : null}
                      {
                        /* <!-- 列管編號，有才出現 ↓ --> */
                      }
                      {chemCtrlNo && <h5 className="text-danger mt-1">{t('text.ctrl_no')}：{chemCtrlNo}</h5>}
                      {
                        /* <!--CAS_NO編號，只出現第一筆 ↑ --> */
                      }
                      <h5 className="text-primary">{t('text.chemical.casno')}：{casnoList.find(casno => casno.casnoSeq === 1)?.casno}</h5>
                      <h3>{firstName ? firstName : firstEnName} </h3>
                      {firstName && <div className="fs-5 text-gray">{firstEnName}</div>}
                      <button type="button" className="btn btn-success shopping-cart" data-toggle="tooltip" data-placement="top" title={t('button.add_shopping_list')} onClick={() => addShoppingList(product, noChemClassExist)}>
                        <i className="fas fa-cart-plus fa-2x"></i>
                      </button>
                    </div>
                    <div className="pt-1 ps-3">
                      <div className="fs-5">{t('text.vendor')}：<span>{vendorMap[manufacturerId].manufacturerName}</span> </div>
                      <div className="fs-5">{t('text.product_item_number')}：<span>{productNo}</span></div>
                      <div className="fs-5">{t('text.product.name')}：<span>{productName}</span> </div>
                      <div className="fs-5">{t('text.weight')}(kg)：<span>{weight}</span>
                      </div>
                      <div className="fs-5">{t('text.concentration')}(%)：<span>{concentration}</span>
                      </div>
                      <div className="fs-5">{t('text.chemical.phase_state')}：<span>{configMap[phaseState]?.configName}</span>
                      </div>
                      <div className="fs-6">{t('text.product_description')}：<span>{productDescription}</span>
                      </div>
                      <div className="fs-6">{t('text.futures')}/{t('text.days')}：<span>{isFutures}/{futuresDay}</span>
                      </div>
                      <button type="button" className="btn btn-secondary mt-1 me-3 " title={t('button.detail')} data-toggle="tooltip" data-placement="top" onClick={() => {
                        setShowProductDetail(true);
                        setProductDetail(product);
                        fetchAndStoreProductCategories(product);
                        fetchAndStoreProductSubstances(product);
                      }}>
                        <i className="fas fa-file-lines fa-lg me-1"></i>{t('button.detail')}
                      </button>
                    </div>
                  </div>;
                })}
              </div>
            </div>
          </div>}
        </div>
        {  /* BEGIN #footer */}
        <Footer />
        {   /* END #footer */}
      </div>
    </BlockUi>
  </StlyedPurchaseRequest>;
}

const StlyedPurchaseRequest = styled.div`
  padding-bottom:150px;

  .badge{
    cursor:default;
    white-space: normal;
  }
  
  .shopping-cart {
      position: absolute;
      top: 5%;
      right: 0%;
  }

  /* 商品明細包裝表格垂直文字 */
  table tr td.w-25 {
      -webkit-writing-mode: vertical-lr;
      writing-mode: vertical-lr;
      vertical-align: middle;
      letter-spacing: .5rem;
  }


  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }  
  table {
    position:relative;
    th {
      text-align: center;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 110px;
        text-align:left;
        min-height:50px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 15px;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }
    }
    
  }
`;
export default PurchaseRequest;