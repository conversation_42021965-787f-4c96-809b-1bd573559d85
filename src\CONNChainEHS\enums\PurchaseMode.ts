/**
 * @typedef {Object} purchaseMode
 * @property {string} SIGNOFF - SIGNOFF mode.
 * @property {string} DETAIL - DETAIL mode.
 * @property {string} ARRIVAL - ARRIVAL mode.
 * @property {string} INSPECTION - INSPECTION mode. 
 * @property {string} RETURN - RETURN mode. 
 * @property {string} CANCEL - CANCEL mode. 
 */
export enum PurchaseMode {
  SIGNOFF,
  DETAIL,
  ARRIVAL,
  INSPECTION,
  RETURN,
  CANCEL,
}
