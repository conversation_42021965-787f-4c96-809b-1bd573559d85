import React from 'react';
import styled from 'styled-components';
import { EhsConfigParam } from '../../../models/EhsConfigParam';
import { getChemClassifyBadgeStyle } from '../../../utils/chemicalUtil';
import { useTranslation } from 'react-i18next';
import { EhsChemicalClassRule, initEhsChemicalClassRule } from '../../../models/EhsChemicalClassRule';
import ChemicalClassificationBadge from 'ehs/common/chemical/ChemicalClassificationBadge';
interface ChemClassifyEditItemProps {
    chemClass: EhsConfigParam;
    selectedOptions: EhsConfigParam[];
    handleChemClassChange: (e: React.ChangeEvent<HTMLInputElement>, chemClass: EhsConfigParam) => void;
    isCheckedOptionById: (options: EhsConfigParam[], configId: string) => boolean;
    chemClassReadonly?: Record<string, boolean>;
    protectChemClass?: string[];
    isEdit?: boolean;
    isStrictChemClass?: boolean;
    isAllDisabled?: boolean;
    chemClassRuleTypeOption?: EhsConfigParam[];
    chemClassRuleCompareOption?: EhsConfigParam[];
    chemClassRule?: EhsChemicalClassRule;
    setChemClassRuleObj?: (rule: EhsChemicalClassRule) => void;//設定分類規則
}

const ChemClassifyEditItem = ({
    chemClass,
    selectedOptions = [],
    isEdit = false,
    chemClassReadonly = {},
    protectChemClass = [],
    isStrictChemClass = false,
    isAllDisabled = false,
    chemClassRuleTypeOption = [],
    chemClassRuleCompareOption = [],
    chemClassRule = initEhsChemicalClassRule,
    handleChemClassChange = () => { },
    isCheckedOptionById,
    setChemClassRuleObj = () => { },
}: ChemClassifyEditItemProps) => {
    const { t } = useTranslation();
    const bgColor = getChemClassifyBadgeStyle(chemClass.configValue);
    const borderColor = bgColor ? bgColor.replaceAll('bg', 'border') : '';
    const isDisabled = isAllDisabled || chemClassReadonly[chemClass.configValue];
    const isChecked = isCheckedOptionById(selectedOptions, chemClass.configId);
    const needStrictClass = isStrictChemClass && !isDisabled && isChecked;

    return (!protectChemClass.includes(chemClass.configValue) || isEdit) && (
        <StlyedChemClassifyEditItem
            as="div"
            className={`d-inline-flex flex-column form-check mx-1 ${needStrictClass ? 'border ' + borderColor : ''} ${!isStrictChemClass ? '' : 'py-1 col-md-3 col-12 my-1'}`}
            key={'chemclass-' + chemClass.configId}
        >
            <div className={`d-flex align-items-center ms-1 pt-1 col-12`} title={`${isDisabled ? t('text.disabled.chem_class_checkbox') : ''}`}>
                <input
                    className="form-check-input"
                    type="checkbox"
                    id={chemClass.configId}
                    onChange={(e) => handleChemClassChange(e, chemClass)}
                    checked={isChecked}
                    disabled={isDisabled}
                />
                <label
                    className={`form-check-label ps-2 ${chemClassReadonly[chemClass.configValue] ? 'label-disabled' : ''}`}
                    htmlFor={chemClass.configId}
                >
                    <ChemicalClassificationBadge item={chemClass} />
                </label>
            </div>

            {/* 規則設定部分 */}
            <div
                className={`mt-2 ${needStrictClass ? '' : 'd-none'} col-12`}
                id={`ruleConfig${chemClass.configId}`}
            >
                {chemClassRuleTypeOption.map((ruleType) => {
                    const { configId, configName, configValue, configIvalue } = ruleType;
                    return (
                        <div className="form-check form-check-inline" key={'ruletype-' + configId}>
                            <input
                                type="radio"
                                className="form-check-input"
                                name={`rule${chemClass.configId}`}
                                id={`${configValue}-${chemClass.configId}`}
                                value={configIvalue}
                                checked={(chemClassRule.ruleType || 1) === configIvalue}
                                onChange={(e) => {
                                    setChemClassRuleObj({ ...chemClassRule, ruleType: parseInt(e.target.value) });
                                }}
                            />
                            <label className="form-check-label" htmlFor={`${configValue}-${chemClass.configId}`}>{configName}</label>
                        </div>
                    )
                })}
                <div
                    id={`mixtureOptions${chemClass.configId}`}
                    className="mt-2"
                >
                    <label>{t('text.chemical.chem_class_rule_condition')}:</label><br />
                    {chemClassRuleCompareOption.map((ruleCompare) => {
                        const { configId, configName, configValue, configIvalue } = ruleCompare;
                        const isChecked = (chemClassRule.ruleCompare || 1) === configIvalue;
                        return (
                            <div className="d-inline-flex align-items-center me-3" key={'rulecompare-' + configId}>
                                <div className="form-check form-check-inline me-2">
                                    <input
                                        type="radio"
                                        className="form-check-input"
                                        name={`condition${chemClass.configId}`}
                                        id={`${configValue}-${chemClass.configId}`}
                                        value={configIvalue}
                                        checked={isChecked}
                                        onChange={(e) => {
                                            setChemClassRuleObj({ ...chemClassRule, ruleCompare: parseInt(e.target.value) });
                                        }}
                                    />
                                    <label className="form-check-label" htmlFor={`${configValue}-${chemClass.configId}`}>{configName}</label>
                                </div>
                                {isChecked && <input
                                    type="number"
                                    className="form-control form-control-sm item-width-20 me-2"
                                    value={chemClassRule.ruleConcen || ""}
                                    onChange={(e) => { }}
                                />}
                                {isChecked && <span>%</span>}
                            </div>
                        )
                    })}
                </div>
            </div>
        </StlyedChemClassifyEditItem>
    );
};


const StlyedChemClassifyEditItem = styled.div`
  input[type="checkbox"]:disabled:not(:checked) {
      background-color: #d3d3d3; /* 灰色背景 */
      border-color: #d3d3d3; /* 灰色邊框 */
      opacity: 0.6; /* 透明度 */
      cursor: not-allowed; /* 禁止游標 */
  }
  .label-disabled {
    opacity: 1 !important; /* 确保标签不透明 */ 
  }
`

export default ChemClassifyEditItem;
