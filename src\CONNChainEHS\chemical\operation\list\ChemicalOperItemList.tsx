import BlockUi from "@availity/block-ui";
import { ChemicalAPI } from "api/chemicalAPI";
import { ConfigAPI } from "api/configAPI";
import { OptionAPI } from "api/optionAPI";
import { SelectListAPI } from "api/selectListAPI";
import BlockuiMsg from "ehs/common/BlockuiMsg";
import CcbViewDialog from "ehs/common/chemical/CcbViewDialog";
import ChemicalClassificationBadge from "ehs/common/chemical/ChemicalClassificationBadge";
import SubstanceInfo from "ehs/common/chemical/SubstanceInfo";
import Dialog from "ehs/common/Dialog";
import ExpandableRwdTable from "ehs/common/ExpandableRwdTable";
import ExpandRwdButton from "ehs/common/ExpandRwdButton";
import GhsImage from "ehs/common/GhsImage";
import InputDate from "ehs/common/input/InputDate";
import Loader from "ehs/common/Loader";
import NoDataRow from "ehs/common/NoDataRow";
import SearchConditionButton from "ehs/common/search/SearchConditionButton";
import SearchDivBuilding from "ehs/common/search/SearchDivBuilding";
import SearchDivLab from "ehs/common/search/SearchDivLab";
import SearchDivUnit from "ehs/common/search/SearchDivUnit";
import SearchDropdownSwitch from "ehs/common/search/SearchDropdownSwitch";
import SortIcon from "ehs/common/SortIcon";
import { confirmMsg, errorMsg } from "ehs/common/SwalMsg";
import { showSuccessToast } from "ehs/common/Toast";
import { CONFIG_TYPE_CHEM, CONFIG_TYPE_CONCEN_TYPE, CONFIG_TYPE_CONCERNED, CONFIG_TYPE_CTRL_CONCEN_TYPE, CONFIG_TYPE_GHS_IMG, CONFIG_TYPE_MIX_CONCEN_TYPE, CONFIG_TYPE_ORGANIC_SOLVENT, CONFIG_TYPE_PRIORITY, CONFIG_TYPE_PUBLIC_HAZARD, CONFIG_TYPE_SPECIFIC_CHEM, CONFIG_TYPE_STATE, CONFIG_TYPE_TOXIC, CONFIG_TYPE_WORK_ENV_MONITOR, CONFIG_TYPE_WORK_ENV_MONITOR_FREQ, OPTION_CHEM_REPORT_MONTH_EARLIEST, OPTION_CHEM_REPORT_MONTH_LONG, OPTION_CHEM_REPORT_MONTH_START_DF, OPTION_SHOW_SEARCH_ADVANCE, OPTION_SHOW_SEARCH_BUILDING, OPTION_SHOW_SEARCH_UNIT } from "ehs/constant/constants";
import { BtnType } from "ehs/enums/BtnType";
import { ChemicalCcbLinkType } from "ehs/enums/ChemicalCcbLinkType";
import { InventoryStatus } from "ehs/enums/InventoryStatus";
import { OperateType } from "ehs/enums/OperateType";
import { OperDetailPageMode } from "ehs/enums/OperDetailPageMode";
import { SearchCondition } from "ehs/enums/SearchCondition";
import { useExpandedRwdTableRow } from "ehs/hooks/useExpandedRwdTableRow";
import useLoginUser from "ehs/hooks/useLoginUser";
import useServerNowDate from "ehs/hooks/useServerNowDate";
import Breadcrumbs from "ehs/layout/Breadcrumbs";
import Footer from "ehs/layout/Footer";
import PageSizeSelector from "ehs/layout/PageSizeSelector";
import Pagination from "ehs/layout/Pagination";
import { EhsChemicalInventory } from "ehs/models/EhsChemicalInventory";
import { initEhsChemicalOperRecord } from "ehs/models/EhsChemicalOperRecord";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsOptions } from "ehs/models/EhsOptions";
import { PageInfo, initPageInfo } from "ehs/models/PageInfo";
import { ReactSelectOption } from "ehs/models/ReactSelectOption";
import { SelectSearch } from "ehs/models/SearchLabInfo";
import { SelectItem } from "ehs/models/SelectItem";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { checkBtnAuth, checkTimeoutAction, getBasicLoginUserInfo } from "ehs/utils/authUtil";
import { getChemicalClassifications, getDetailCategoryByType } from "ehs/utils/chemicalUtil";
import { ExcelColumnConfig, formatChemicalInfo, generateExcel } from "ehs/utils/excelUtil";
import { getUserUnitTextObj } from "ehs/utils/langUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { getFormatDateDash, getSplitUnitByIndex, isContainEnglish } from "ehs/utils/stringUtil";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import Select from "react-select";
import styled from "styled-components";
import OperDetailButton from "./OperDetailButton";

interface ConditionType {
  keyword: string;
  currentPage: number;
  pageSize: number;
  orgLevel: number | null;
  areaId: string;
  orgType: string;
  unit: { [key: number]: string };
  queryOrgId: string;
  buildId: string;
  floor: string;
  houseNum: string;
  queryLabIds: string[];
  chemConId: string;
  phaseState: string;
  toxicClass: string;
  svhcClass: string;
  priorityClass: string;
  organicClass: string;
  specificChemClass: string;
  envMonitorClass: string;
  envMonitorFreq: string;
  pubhazClass: string;
  casNo: string;
  operStartDate: string | undefined;
  operEndDate: string | undefined;
  inventoryStatus: number;
}

function ChemicalOperItemList() {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const allOptionValue = "all"
  const allSelectOption = { value: allOptionValue, label: t('text.all') }
  const { loginUser } = useLoginUser();
  const [nowDate] = useServerNowDate(loginUser);
  const { itemText } = getUserUnitTextObj(loginUser, t);
  const [isLinkSelect, setIsLinkSelect] = useState<boolean>(true);
  const [showUnitCondition, setShowUnitCondition] = useState<boolean>(false);
  const [showBuildCondition, setShowBuildCondition] = useState<boolean>(false);
  const [showAdvCondition, setShowAdvCondition] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingBlock, setLoadingBlock] = useState<boolean>(false);
  const [showCCBView, setShowCCBView] = useState<boolean | null>(null);
  const [ccbLinkId, setCcbLinkId] = useState<string>("");
  const [inventoryList, setInventoryList] = useState<EhsChemicalInventory[]>([]);
  const [searchSelectList, setSearchSelectList] = useState<SelectSearch[]>([]);
  const [chemClassOptions, setChemClassOptions] = useState<any[]>([]);
  const [configSelectOptions, setConfigSelectOptions] = useState<{ [key: string]: EhsConfigParam[] }>({});
  const [selectedChemClasses, setSelectedChemClasses] = useState<ReactSelectOption[]>([]);
  const [configMap, setConfigMap] = useState<{ [configId: string]: EhsConfigParam }>({});// c
  const [ctrlNoOptionList, setCtrlNoOptionList] = useState<SelectItem[]>([]); // 列管編號選項onfig對應
  const [svhcCtrlNoOptionList, setSvhcCtrlNoOptionList] = useState<SelectItem[]>([]); // 關注性編號選項onfig對應
  const [selectedCtrlNoOption, setSelectedCtrlNoOption] = useState<ReactSelectOption>(allSelectOption);
  const [selectedSvhcCtrlNoOption, setSelectedSvhcCtrlNoOption] = useState<ReactSelectOption>(allSelectOption);
  const [optionsMap, setOptionsMap] = useState<{ [key: string]: EhsOptions }>({});
  const initCondition = {
    keyword: "",
    currentPage: 1,
    pageSize: 50,
    orgLevel: null,
    areaId: "",
    orgType: "",
    unit: {},
    queryOrgId: "",
    buildId: "",
    floor: "",
    houseNum: "",
    queryLabIds: [],
    chemConId: "",
    phaseState: "",
    toxicClass: "",
    svhcClass: "",
    priorityClass: "",
    organicClass: "",
    specificChemClass: "",
    envMonitorClass: "",
    envMonitorFreq: "",
    pubhazClass: "",
    casNo: "",
    operStartDate: nowDate ? getFormatDateDash(new Date(nowDate.getFullYear(), nowDate.getMonth() - 6, nowDate.getDate())) : undefined,
    operEndDate: nowDate ? getFormatDateDash(nowDate) : undefined,
    inventoryStatus: 1,
  };
  const [condition, setCondition] = useState<ConditionType>(initCondition);
  const [pageInfo, setPageInfo] = useState<PageInfo>(initPageInfo);
  const { orgLevel } = condition;
  const showAdvConditionMarginTop = showAdvCondition ? 'mt-3' : '';

  // 使用 ref 來管理所有暫存的搜尋條件
  const searchRef = useRef({
    casNo: "",
    phaseState: "",
    toxicClass: "",
    concernedClass: "",
    priorityClass: "",
    organicClass: "",
    specificChemClass: "",
    envMonitorClass: "",
    envMonitorFreq: "",
    pubhazClass: "",
    operStartDate: condition.operStartDate,
    operEndDate: condition.operEndDate,
    inventoryStatus: condition.inventoryStatus
  });
  const selectedChemClassesConfig = selectedChemClasses.map((option) => configMap[option.value]);
  const phaseStateOptions = configSelectOptions[CONFIG_TYPE_STATE] || [];
  const toxicClassOptions = configSelectOptions[CONFIG_TYPE_TOXIC] || [];
  const concernedClassOptions = configSelectOptions[CONFIG_TYPE_CONCERNED] || [];
  const priorityClassOptions = configSelectOptions[CONFIG_TYPE_PRIORITY] || [];
  const organicClassOptions = configSelectOptions[CONFIG_TYPE_ORGANIC_SOLVENT] || [];
  const specificChemClassOptions = configSelectOptions[CONFIG_TYPE_SPECIFIC_CHEM] || [];
  const envMonitorClassOptions = configSelectOptions[CONFIG_TYPE_WORK_ENV_MONITOR] || [];
  const envMonitorFreqOptions = configSelectOptions[CONFIG_TYPE_WORK_ENV_MONITOR_FREQ] || [];
  const pubhazClassOptions = configSelectOptions[CONFIG_TYPE_PUBLIC_HAZARD] || [];
  const chemicalClassTypes = [
    { type: CONFIG_TYPE_TOXIC, key: 'hasSelectedToxicClass' },
    { type: CONFIG_TYPE_CONCERNED, key: 'hasSelectedConcernedClass' },
    { type: CONFIG_TYPE_PRIORITY, key: 'hasSelectedPriorityClass' },
    { type: CONFIG_TYPE_ORGANIC_SOLVENT, key: 'hasSelectedOrganicClass' },
    { type: CONFIG_TYPE_SPECIFIC_CHEM, key: 'hasSelectedSpecificChemClass' },
    { type: CONFIG_TYPE_WORK_ENV_MONITOR, key: 'hasSelectedEnvMonitorClass' },
    { type: CONFIG_TYPE_PUBLIC_HAZARD, key: 'hasSelectedPubhazClass' },
  ];
  const selectedClassFlags = chemicalClassTypes.reduce<Record<string, boolean>>((acc, { type, key }) => {
    acc[key] = selectedChemClassesConfig.some((config) => config && config.configValue === type);
    return acc;
  }, {});
  const keyToTypeMap = chemicalClassTypes.reduce<Record<string, string>>((acc, { type, key }) => {
    acc[key] = type;
    return acc;
  }, {});


  const { hasSelectedToxicClass, hasSelectedConcernedClass, hasSelectedPriorityClass, hasSelectedOrganicClass, hasSelectedSpecificChemClass,
    hasSelectedEnvMonitorClass, hasSelectedPubhazClass } = selectedClassFlags;

  const createDeselectClearClassHandler = (refKey: string) => ({
    onSelect: () => { /* 可以在這裡添加選擇時的邏輯 */ },
    onDeselect: () => {
      (searchRef.current as any)[refKey] = "";
    }
  });

  const classHandlers = useMemo(() => ({
    [CONFIG_TYPE_TOXIC]: {
      onSelect: () => {
        if (loginUser && isArrayEmpty(ctrlNoOptionList)) {
          fetchCtrlNoList();
        }
      },
      onDeselect: () => {
        searchRef.current.toxicClass = "";
        setSelectedCtrlNoOption(allSelectOption);
      }
    },
    [CONFIG_TYPE_CONCERNED]: {
      onSelect: () => {
        if (loginUser && isArrayEmpty(svhcCtrlNoOptionList)) {
          fetchCtrlNoList();
        }
      },
      onDeselect: () => {
        searchRef.current.concernedClass = "";
        setSelectedSvhcCtrlNoOption(allSelectOption);
      }
    },
    [CONFIG_TYPE_PRIORITY]: createDeselectClearClassHandler('priorityClass'),
    [CONFIG_TYPE_ORGANIC_SOLVENT]: createDeselectClearClassHandler('organicClass'),
    [CONFIG_TYPE_SPECIFIC_CHEM]: createDeselectClearClassHandler('specificChemClass'),
    [CONFIG_TYPE_WORK_ENV_MONITOR]: createDeselectClearClassHandler('envMonitorClass'),
    [CONFIG_TYPE_PUBLIC_HAZARD]: createDeselectClearClassHandler('pubhazClass'),
  }), [loginUser, ctrlNoOptionList, svhcCtrlNoOptionList]);

  useEffect(() => {
    Object.entries(selectedClassFlags).forEach(([key, isSelected]) => {
      const type = keyToTypeMap[key];
      const handler = classHandlers[type as keyof typeof classHandlers];

      if (handler) {
        if (isSelected) {
          handler.onSelect();
        } else {
          handler.onDeselect();
        }
      }
    });
  }, [selectedClassFlags, classHandlers]);

  useEffect(() => {
    if (loginUser) {
      fetchData();
    }
  }, [loginUser, condition.currentPage, condition.pageSize, condition.orgLevel, i18n.language]);

  useEffect(() => {
    if (loginUser) {
      fetchOption();
    }
  }, [loginUser])

  useEffect(() => {
    if (loginUser) {
      fetchSelectListData();
      fetchConfig();
    }
  }, [loginUser, i18n.language]);

  // 根據選項設定是否顯示搜尋條件
  useEffect(() => {
    if (!isArrayEmpty(Object.keys(optionsMap))) {
      const unitOption = optionsMap[OPTION_SHOW_SEARCH_UNIT];
      const buildOption = optionsMap[OPTION_SHOW_SEARCH_BUILDING];
      const advOption = optionsMap[OPTION_SHOW_SEARCH_ADVANCE];

      setShowUnitCondition(unitOption?.optionEnabled);
      setShowBuildCondition(buildOption?.optionEnabled);
      setShowAdvCondition(advOption?.optionEnabled);
    }
  }, [optionsMap]);


  const fetchData = (searchCondition = condition) => {
    setLoading(true);
    const { queryLabIds } = searchCondition;
    if (queryLabIds) {
      searchCondition.queryLabIds = Array.isArray(queryLabIds) ? queryLabIds : [queryLabIds];
    }
    fetchChemicalItemList(searchCondition);
  };

  const fetchChemicalItemList = (searchCondition = condition) => {
    ChemicalAPI.getChemicalInventoryItemList({
      ...getBasicLoginUserInfo(loginUser),
      ...searchCondition,
    }).then((result) => {
      handleFetchDataSuccess(result);
    }).catch(err => {
      checkTimeoutAction(err, navigate, t);
      console.error(err);
    }).finally(() => {
      setLoading(false)
    });
  };

  const handleFetchDataSuccess = (result: any) => {
    if (isApiCallSuccess(result)) {
      setPageInfo(result.pageinfo);
      setInventoryList(result.results);
    } else {
      if (result && result.message) {
        errorMsg(result.message)
      } else {
        errorMsg(t('text.error'))
      }
    }
  }

  const fetchSelectListData = () => {
    SelectListAPI.getSelectLabSearchView({
      ...getBasicLoginUserInfo(loginUser),
    }).then((result) => {
      if (result) {
        setSearchSelectList(result.results);
      }
    });
  };

  const fetchConfig = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser),
      configTypeList: [CONFIG_TYPE_CHEM, CONFIG_TYPE_STATE, CONFIG_TYPE_CTRL_CONCEN_TYPE, CONFIG_TYPE_CONCEN_TYPE, CONFIG_TYPE_GHS_IMG,
        CONFIG_TYPE_MIX_CONCEN_TYPE, CONFIG_TYPE_TOXIC, CONFIG_TYPE_CONCERNED, CONFIG_TYPE_PRIORITY, CONFIG_TYPE_ORGANIC_SOLVENT,
        CONFIG_TYPE_SPECIFIC_CHEM, CONFIG_TYPE_WORK_ENV_MONITOR, CONFIG_TYPE_WORK_ENV_MONITOR_FREQ, CONFIG_TYPE_PUBLIC_HAZARD]
    }).then((result) => {
      if (result) {
        const configMap = new Map<string, EhsConfigParam>();
        const options: { value: string; label: string }[] = [];
        const configSelectOptions: { [key: string]: EhsConfigParam[] } = {};

        result.results.forEach((config: EhsConfigParam) => {
          if (config.configType === CONFIG_TYPE_CTRL_CONCEN_TYPE) {
            configMap.set(String(config.configIvalue), config);
          } else {
            configMap.set(config.configId, config);
            if (config.configType === CONFIG_TYPE_CHEM) {
              options.push({
                value: config.configId,
                label: config.configName
              });
            } else {
              if (!configSelectOptions[config.configType]) {
                configSelectOptions[config.configType] = [];
              }
              configSelectOptions[config.configType].push(config);
            }
          }
        });

        setConfigMap(Object.fromEntries(configMap)); // 將 Map 轉換為物件
        setChemClassOptions(options);
        setConfigSelectOptions(configSelectOptions);
      }
    });
  };

  const fetchCtrlNoList = () => {
    SelectListAPI.getSelectChemicalCtrlnoList({
      ...getBasicLoginUserInfo(loginUser),
    }).then(result => {
      if (isApiCallSuccess(result)) {
        // 將 "全部" 選項添加到結果中
        const { updatedSvhcResults, updatedCtrlNoResults } = result.results.reduce((acc: any, item: SelectItem) => {
          if (isContainEnglish(item.label)) {
            acc.updatedSvhcResults.push(item);
          } else {
            acc.updatedCtrlNoResults.push(item);
          }
          return acc;
        }, { updatedSvhcResults: [], updatedCtrlNoResults: [] });

        setCtrlNoOptionList([allSelectOption, ...updatedCtrlNoResults]);
        setSvhcCtrlNoOptionList([allSelectOption, ...updatedSvhcResults]);
      }
    })
  }

  const fetchOption = () => {
    const optionIds = [OPTION_SHOW_SEARCH_UNIT, OPTION_SHOW_SEARCH_BUILDING, OPTION_SHOW_SEARCH_ADVANCE];
    OptionAPI.getOptionListByIds({
      ...getBasicLoginUserInfo(loginUser),
      optionIdList: optionIds
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const options = result.results;
        const newOptionsMap: {
          [key: string]: EhsOptions;
        } = {};
        options.forEach((item: any) => {
          if (optionIds.includes(item.optionId)) {
            newOptionsMap[item.optionId] = item;
          }
        });
        setOptionsMap(newOptionsMap);
      }
    });
  };

  const getShowList = () => {
    return inventoryList || [];
  };

  const clickSearch = () => {
    const chemCtrlNo = selectedCtrlNoOption.value === allOptionValue ? "" : selectedCtrlNoOption.value;
    const svhcCtrlNo = selectedSvhcCtrlNoOption.value === allOptionValue ? "" : selectedSvhcCtrlNoOption.value;
    // 從 searchRef.current 解構所需資料，避免重複引用
    const {
      casNo, phaseState, operStartDate, operEndDate, inventoryStatus,
      toxicClass, concernedClass, priorityClass, organicClass,
      specificChemClass, envMonitorClass, envMonitorFreq, pubhazClass
    } = searchRef.current;
    const newCondition = {
      ...condition,
      casNo,
      chemCtrlNo,
      svhcCtrlNo,
      phaseState,
      currentPage: 1,
      chemConId: "",
      orgLevel: null,
      operStartDate,
      operEndDate,
      inventoryStatus,
      chemClassIdList: selectedChemClasses.map((option) => option.value),
      chemCtrlNoList: [chemCtrlNo, svhcCtrlNo].filter(Boolean),
      chemSubCategoryIdList: [
        ...(toxicClass ? [toxicClass] : []),
        ...(concernedClass ? [concernedClass] : []),
        ...(priorityClass ? [priorityClass] : []),
        ...(organicClass ? [organicClass] : []),
        ...(specificChemClass ? [specificChemClass] : []),
        ...(envMonitorClass ? [envMonitorClass] : []),
        ...(envMonitorFreq ? [envMonitorFreq] : []),
      ],
      purchaseDetailConfigIdList: [...(pubhazClass ? [pubhazClass] : [])]
    };

    setCondition(newCondition);
    fetchData(newCondition);
  };

  const handleStartDateChange = (value: Date | null) => {
    const startDate = value ? getFormatDateDash(value) : undefined;
    searchRef.current.operStartDate = startDate;

    // 如果結束日早於起始日,將結束日設為起始日
    if (startDate && searchRef.current.operEndDate && searchRef.current.operEndDate < startDate) {
      searchRef.current.operEndDate = startDate;
    }
  };

  const handleEndDateChange = (value: Date | null) => {
    const endDate = value ? getFormatDateDash(value) : undefined;
    searchRef.current.operEndDate = endDate;

    // 如果結束日早於起始日,將結束日設為起始日
    if (endDate && searchRef.current.operStartDate && endDate < searchRef.current.operStartDate) {
      searchRef.current.operEndDate = searchRef.current.operStartDate;
    }
  };

  const exportToExcel = async () => {
    // 定義欄位配置
    let columns: ExcelColumnConfig[] = [
      {
        header: t('table.title.item'),
        key: 'itemNo',
        width: 8
      }
    ];

    // 只在有單位層級時添加單位欄位
    if (orgLevel !== null) {
      columns.push({
        header: t('table.title.org.item'),
        key: 'orgName',
        width: 15,
        formatter: (_, row) => {
          return getSplitUnitByIndex(row.orgNames, orgLevel || 0);
        }
      });
    }

    // 添加其他欄位
    columns = [
      ...columns,
      {
        header: t('table.title.chemical.information'),
        key: 'chemicalInfo',
        width: 30,
        formatter: (_, row) => formatChemicalInfo(row.substList || [], configMap, t)
      },
      {
        header: t('table.title.chem_class'),
        key: 'chemClasses',
        width: 15,
        formatter: (_, row) => {
          const substList = row.substList || [];
          const chemClassIdList = Array.from(
            new Set(
              substList.flatMap((subst: any) => (subst.categoryList || []).map((category: any) => category.configId))
            )
          );
          return chemClassIdList.map((item: unknown) => {
            const itemId = item as string;
            return configMap[itemId]?.configName;
          }).join(', ');
        }
      },
      {
        header: t('table.title.chemical.phase_state'),
        key: 'phaseState',
        width: 15,
        formatter: (value, _, configMap) => configMap[value]?.configName
      },
      {
        header: t('table.title.chemical.unit_ctrl_qty_total', { unit: itemText }),
        key: 'ctrlTotalQty',
        width: 15,
        formatter: (_, row, configMap, t) => {
          const { chemical, chemicalCon } = row;
          const { chemGradeHandQty } = chemical || {};
          const { ctrlTotalQty, ctrlTotalQtyType } = chemicalCon || {};
          const showCtrlTotalQty = ctrlTotalQtyType === 1 ? chemGradeHandQty : ctrlTotalQty;
          return showCtrlTotalQty === null ? t('text.chemical.no_ctrL_limit') : `${showCtrlTotalQty} kg`;
        }
      },
      {
        header: t('table.title.max_oper_qty'),
        key: 'maxOperQty',
        width: 15,
        formatter: (value) => `${value || 0} kg`
      },
      {
        header: t('table.title.year_max_storage_qty'),
        key: 'maxStorageQty',
        width: 15,
        formatter: (value) => `${value || 0} kg`
      },
      {
        header: t('table.title.inventory_quantity'),
        key: 'inventoryQty',
        width: 15,
        formatter: (value) => `${value || 0} kg`
      }
    ];

    // 調用通用工具函數
    await generateExcel(
      getShowList(),
      columns,
      configMap,
      t,
      'Chemical Operational List.xlsx',
      'Chemical Operations'
    );
  };

  return (
    <StlyedChemicalOperItemList $loading={loading}>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {
            <Dialog
              content={
                <CcbViewDialog
                  onClose={() => {
                    setShowCCBView(null);
                  }}
                  setLoadingBlock={setLoadingBlock}
                  mode={showCCBView}
                  linkType={ChemicalCcbLinkType.OPER}
                  linkId={ccbLinkId!}
                />
              }
              show={showCCBView !== null}
            />
          }
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.chemical.manage") },
                { label: t("func.chemical.operation") },
              ]}
            />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{t("func.chemical.operation")} </h1>
            {/* END page-header */}

            <div className="card">
              <div className="card-body p-4">
                <SearchDropdownSwitch checked={isLinkSelect} onChangeChecked={setIsLinkSelect} onChangeCondition={() => setCondition(initCondition)} />
                <SearchConditionButton buttonType={SearchCondition.UNIT} onClick={() => { setShowUnitCondition((pre) => !pre) }} />
                <SearchConditionButton buttonType={SearchCondition.BUILDING} onClick={() => { setShowBuildCondition((pre) => !pre) }} />
                <SearchConditionButton buttonType={SearchCondition.ADVANCEd} onClick={() => { setShowAdvCondition((pre) => !pre) }} />
                <button type="button" className="btn btn-secondary ms-1 my-2" title={t('button.reset_search')} onClick={() => {
                  setCondition(initCondition);
                  searchRef.current = {
                    casNo: initCondition.casNo,
                    phaseState: initCondition.phaseState,
                    toxicClass: initCondition.toxicClass,
                    concernedClass: initCondition.svhcClass,
                    priorityClass: initCondition.priorityClass,
                    organicClass: initCondition.organicClass,
                    specificChemClass: initCondition.specificChemClass,
                    envMonitorClass: initCondition.envMonitorClass,
                    envMonitorFreq: initCondition.envMonitorFreq,
                    pubhazClass: initCondition.pubhazClass,
                    operStartDate: initCondition.operStartDate,
                    operEndDate: initCondition.operEndDate,
                    inventoryStatus: initCondition.inventoryStatus
                  };
                  setSelectedCtrlNoOption(allSelectOption);
                  setSelectedChemClasses([]);
                }}><i className="fas fa-undo mx-1"></i>{t('button.reset_search')}</button>
                <div className="row my-3">
                  <SearchDivUnit dataList={searchSelectList} show={showUnitCondition} isLinkSelect={isLinkSelect}
                    condition={condition} setCondition={setCondition} showAll loginUser={loginUser} />
                  <SearchDivBuilding dataList={searchSelectList} show={showBuildCondition} isLinkSelect={isLinkSelect} condition={condition} setCondition={setCondition} showAll />
                  <div className="row my-2">
                    <SearchDivLab dataList={searchSelectList} isLinkSelect={isLinkSelect} condition={condition} setCondition={setCondition} />
                    {showAdvCondition &&
                      <>
                        <div className={`col-xl-3 d-flex align-items-center`}>
                          <label className="w-25">{t('text.chemical.casno')}</label>
                          <input
                            type="text"
                            className="form-control form-control-lg"
                            defaultValue={searchRef.current.casNo}
                            onChange={(e) => searchRef.current.casNo = e.target.value.trim()}
                          />
                        </div>
                        <div className={`col-xl-3 d-flex align-items-center`}>
                          <label className="w-25">{t('text.chemical.phase_state')}</label>
                          <select
                            className="form-select form-select-lg"
                            defaultValue={searchRef.current.phaseState}
                            onChange={(e) => searchRef.current.phaseState = e.target.value}
                          >
                            <option value="">{t('text.all')}</option>
                            {phaseStateOptions.map((option) => (
                              <option key={option.configId} value={option.configId}>{option.configName}</option>
                            ))}
                          </select>
                        </div>
                        <div className={`col-xl-3 d-flex align-items-center`}>
                          <label className="w-25">{t('text.status')}</label>
                          <select
                            className="form-select form-select-lg"
                            defaultValue={searchRef.current.inventoryStatus}
                            onChange={(e) => searchRef.current.inventoryStatus = Number(e.target.value)}
                          >
                            <option value={InventoryStatus.ENABLE} title={t('text.enabled')}>{t('text.enabled')}</option>
                            <option value={InventoryStatus.SIGNING} title={t('text.signoff.under_review')}>{t('text.signoff.under_review')}</option>
                            <option value={InventoryStatus.PURCHASING} title={t('text.purchasing')}>{t('text.purchasing')}</option>
                            <option value={InventoryStatus.DISABLED} title={t('text.disabled.item')}>{t('text.disabled.item')}</option>
                            <option value={InventoryStatus.DISPOSAL} title={t('text.chemical.disposal')}>{t('text.chemical.disposal')}</option>
                          </select>
                        </div>
                        <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                          <label className="w-25">{t("text.chemical.class")}</label>
                          <Select
                            className="w-75"
                            isMulti
                            isSearchable
                            menuPosition="fixed"
                            options={chemClassOptions}
                            placeholder={t('text.all')}
                            value={selectedChemClasses}
                            onChange={(selectedOptions: any) => {
                              setSelectedChemClasses(selectedOptions || []);
                            }}
                            noOptionsMessage={() => t('text.select_no_option')}
                          />
                        </div>
                        {hasSelectedToxicClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.chemical.toxic_class')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.toxicClass}
                              onChange={(e) => searchRef.current.toxicClass = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {toxicClassOptions.map((option) => (
                                <option key={option.configId} value={option.configId}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {hasSelectedToxicClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.ctrl_no')}</label>
                            <Select options={ctrlNoOptionList || []} isSearchable menuPosition="fixed" placeholder={t('text.all')} className="w-100"
                              menuPortalTarget={document.body}
                              onChange={(selectedOption: any) => {
                                setSelectedCtrlNoOption(selectedOption);
                              }} value={selectedCtrlNoOption}
                              noOptionsMessage={() => t('message.search.non_ctrl_no')}
                            />
                          </div>
                        )}
                        {hasSelectedConcernedClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.chemical.concerned_class')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.concernedClass}
                              onChange={(e) => searchRef.current.concernedClass = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {concernedClassOptions.map((option) => (
                                <option key={option.configId} value={option.configId}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {hasSelectedConcernedClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.chemical.concerned_ctrl_no')}</label>
                            <Select options={svhcCtrlNoOptionList || []} isSearchable menuPosition="fixed" placeholder={t('text.all')} className="w-75"
                              menuPortalTarget={document.body}
                              onChange={(selectedOption: any) => {
                                setSelectedSvhcCtrlNoOption(selectedOption);
                              }} value={selectedSvhcCtrlNoOption}
                              noOptionsMessage={() => t('message.search.non_ctrl_no')}
                            />
                          </div>
                        )}
                        {hasSelectedPriorityClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.chemical.prority_class_title')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.priorityClass}
                              onChange={(e) => searchRef.current.priorityClass = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {priorityClassOptions.map((option) => (
                                <option key={option.configId} value={option.configId}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {hasSelectedOrganicClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.chemical.organic_solvent_class')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.organicClass}
                              onChange={(e) => searchRef.current.organicClass = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {organicClassOptions.map((option) => (
                                <option key={option.configId} value={option.configId}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {hasSelectedSpecificChemClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.chemical.specific_chem_class')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.specificChemClass}
                              onChange={(e) => searchRef.current.specificChemClass = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {specificChemClassOptions.map((option) => (
                                <option key={option.configId} value={option.configId}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {hasSelectedEnvMonitorClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25 me-1">{t('text.chemical.work_env_monitor_class')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.envMonitorClass}
                              onChange={(e) => searchRef.current.envMonitorClass = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {envMonitorClassOptions.map((option) => (
                                <option key={option.configId} value={option.configId} title={option.configName}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {hasSelectedEnvMonitorClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25 me-1">{t('text.chemical.work_env_monitor_freq')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.envMonitorFreq}
                              onChange={(e) => searchRef.current.envMonitorFreq = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {envMonitorFreqOptions.map((option) => (
                                <option key={option.configId} value={option.configId}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {hasSelectedPubhazClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.chemical.public_hazard_classify')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.pubhazClass}
                              onChange={(e) => searchRef.current.pubhazClass = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {pubhazClassOptions.map((option) => (
                                <option key={option.configId} value={option.configId}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        <div className={`col-xl-6 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                          <label className="w-25">{t('text.chemical.operation_date')}</label>
                          <div className="d-flex align-items-center">
                            <InputDate
                              defaultValue={searchRef.current.operStartDate}
                              isClearable={false}
                              onChange={handleStartDateChange}
                              maxDate={searchRef.current.operEndDate ? getFormatDateDash(new Date(searchRef.current.operEndDate)) : undefined}
                            />
                            <span className="mx-2">~</span>
                            <InputDate
                              defaultValue={searchRef.current.operEndDate}
                              isClearable={false}
                              onChange={handleEndDateChange}
                              minDate={searchRef.current.operStartDate ? getFormatDateDash(new Date(searchRef.current.operStartDate)) : undefined}
                            />
                          </div>
                        </div>
                      </>
                    }
                    <div className={`col-xl-3 ${showAdvConditionMarginTop}`}>
                      <button type="button" className="btn btn-primary mt-1" title={t('button.search.item')} onClick={clickSearch}><i className="fas fa-magnifying-glass"></i> {t('button.search.item')}</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="card pt-3">
              <div className="row topFunctionRow">
                <div className="col-sm-12 col-md-6 left">
                  <PageSizeSelector pageSize={condition.pageSize} setCondition={setCondition} />
                </div>
                <div className="col-sm-12 col-md-6 right">
                  <button
                    type="button"
                    className="btn btn-green float-end"
                    onClick={exportToExcel}
                  >
                    <i className="fas fa-file-excel me-1"></i>
                    {t('button.export.operation_list')}
                  </button>
                </div>
              </div>
              <div className="card-body">
                {loading && <Loader />}
                <ExpandableRwdTable breakpoint={1400} expandOnMedium={true}>
                  <div className="table-container mt-1">
                    <table
                      id="data-table-default"
                      className={
                        "table table-hover align-middle dt-responsive nowrap"
                      }
                    >
                      <thead className="text-center fs-4 fw-bold bg-lime-200">
                        <tr>
                          <th className="item-width-sm-5 item-width-md-10 item-width-xl-5">{t("table.title.item")} </th>
                          {
                            orgLevel !== null &&
                            <th className='text-start'>
                              {t("table.title.org.item")}
                            </th>
                          }
                          <th className='text-start item-width-xl-18'>
                            {t("table.title.chemical.information")}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"chemical.chemCtrlNo"}
                              setFunction={setInventoryList}
                            />
                          </th>
                          <th className="responsive-header">{t("table.title.ghs_img")}</th>
                          <th className='text-start responsive-header'>{t("table.title.chem_class")}</th>
                          <th className='text-center item-width-sm-5 item-width-md-15 item-width-xl-8'>{t("table.title.chemical.phase_state")}</th>
                          <th className='text-end item-width-10 responsive-header'> {t("table.title.chemical.unit_ctrl_qty_total", { unit: itemText })}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"chemical.chemGradeHandQty"}
                              setFunction={setInventoryList}
                            />
                          </th>
                          <th className='text-end item-width-10 responsive-header'>{t("table.title.max_oper_qty")}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"maxOperQty"}
                              setFunction={setInventoryList}
                            />
                          </th>
                          <th className='text-end item-width-10 responsive-header'>{t("table.title.year_max_storage_qty")}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"avgUseQty"}
                              setFunction={setInventoryList}
                            />
                          </th>
                          <th className='text-end item-width-sm-5 item-width-md-20 item-width-xl-10'>{t("table.title.inventory_quantity")}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"inventoryQty"}
                              setFunction={setInventoryList}
                            />
                          </th>
                          <th data-orderable="false" className="text-start responsive-header">{t("text.chemical.ccb.item")}</th>
                          <th data-orderable="false" className="text-start item-width-sm-5 item-width-md-20 item-width-xl-8">{t("table.title.action")}</th>
                        </tr>
                      </thead>
                      <tbody className="text-center fs-5">
                        {!loading && getShowList() && !isArrayEmpty(getShowList()) ?
                          getShowList().map((data, idx) => {
                            return <Row
                              key={'inventory_' + idx}
                              index={idx + 1}
                              inventory={data}
                              nowDate={nowDate}
                              configMap={configMap}
                              setInventoryList={setInventoryList}
                              setLoadingBlock={setLoadingBlock}
                              setShowCCBView={setShowCCBView}
                              setCcbLinkId={setCcbLinkId}
                            />;
                          }) : (!loading && <NoDataRow />)}
                      </tbody>
                    </table>
                  </div>
                </ExpandableRwdTable>
                <Pagination pageInfo={pageInfo} setCondition={setCondition} />
              </div>
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </BlockUi>
    </StlyedChemicalOperItemList >
  );
}

interface RowProps {
  index: number;
  inventory: EhsChemicalInventory;
  nowDate: Date;
  configMap: { [configId: string]: EhsConfigParam };
  setInventoryList: React.Dispatch<React.SetStateAction<EhsChemicalInventory[]>>;
  setLoadingBlock: React.Dispatch<React.SetStateAction<boolean>>;
  setShowCCBView: React.Dispatch<React.SetStateAction<boolean | null>>;
  setCcbLinkId: React.Dispatch<React.SetStateAction<string>>;
}

const Row = (props: RowProps) => {
  const {
    index,
    inventory,
    configMap,
    nowDate,
    setInventoryList,
    setLoadingBlock,
    setShowCCBView,
    setCcbLinkId
  } = props;
  const { chemical, chemicalCon, detailCategoryList, substList, phaseState,
    inventoryId, inventoryQty, inventoryStatus,
    maxOperQty, maxStorageQty, ccbLevel } = inventory;
  const { chemGradeHandQty } = chemical;
  const { ctrlTotalQty, ctrlTotalQtyType } = chemicalCon;
  const chemClasses = getChemicalClassifications(substList, configMap);
  const ghsImages = getDetailCategoryByType(detailCategoryList, configMap, CONFIG_TYPE_GHS_IMG);
  const { isExpanded, toggleExpanded } = useExpandedRwdTableRow();
  const { loginUser } = useLoginUser();
  const { t } = useTranslation();
  const { itemText } = getUserUnitTextObj(loginUser, t);
  const isChemCancelDisableRole = checkBtnAuth(BtnType.CHEM_CANCEL_DISABLE);
  const isChemDisposeRole = checkBtnAuth(BtnType.CHEM_DISPOSE);
  // const { currentLangNames, enNames, chNames } = splitChemNameListByLang(nameList, i18n.language);
  const showCtrlTotalQty = ctrlTotalQtyType === 1 ? chemGradeHandQty : ctrlTotalQty;
  const isDisable = inventoryStatus === InventoryStatus.DISABLED;

  const cancelDisable = async () => {
    const confirmed = await confirmMsg(t('message.confirm.chemical_oper_disable_cancel'), t);
    if (!confirmed) return;
    setLoadingBlock(true);
    const response = await updateInventoryOperate(OperateType.CANCEL_DISABLE, 0);
    handleUpdateInventoryOperResult(response);
  }

  const doDisposal = async () => {
    const confirmed = await confirmMsg(t('message.confirm.chemical.dispose'), t);
    if (!confirmed) return;
    setLoadingBlock(true);
    const response = await updateInventoryOperate(OperateType.DISPOSAL, inventoryQty);
    handleUpdateInventoryOperResult(response);
  }

  const updateInventoryOperate = (operType: OperateType, operQty: number) => {
    return ChemicalAPI.addChemicalOperation({
      ...getBasicLoginUserInfo(loginUser)!,
      chemicalOperRecord: {
        ...initEhsChemicalOperRecord,
        inventoryId: inventoryId,
        operDate: getFormatDateDash(nowDate),
        operType: operType,
        chemRecordNote: '',
        inventoryPre: inventoryQty,
        operQty: operQty
      }
    })
  }

  const handleUpdateInventoryOperResult = (response: any) => {
    if (isApiCallSuccess(response)) {
      showSuccessToast(t('message.success'));
      setInventoryList(prevList => prevList.filter(item => item.inventoryId !== inventoryId));
    } else {
      errorMsg(t('message.error'));
    }
    setLoadingBlock(false);
  }

  const ghsImagesFragment = ghsImages.map((item) => {
    const { configId, configValue, configName } = item || {};
    return (<GhsImage key={'ghs_img_' + configId} src={configValue} alt={configName} title={configName} extraClassName="ghs-img" />);
  });

  const chemClassesFragment = chemClasses.map((item) => {
    const { configId } = item || {};
    const categoryConfig = configMap[configId];
    return (
      <React.Fragment key={'category_' + configId}>
        <span className="d-inline-block mb-1">
          <ChemicalClassificationBadge item={categoryConfig} />
        </span>
      </React.Fragment>
    );
  });

  const ctrlTotalQtyFragment = showCtrlTotalQty === null ? t('text.chemical.no_ctrL_limit') : showCtrlTotalQty + ' kg';

  // 定義響應式內容映射
  const responsiveContentMap = {
    "ghs_img": {
      title: t("table.title.ghs_img"),
      content: ghsImagesFragment
    },
    "chem_class": {
      title: t("table.title.chem_class"),
      content: chemClassesFragment
    },
    "unit_ctrl_qty": {
      title: t("table.title.chemical.unit_ctrl_qty_total", { unit: itemText }),
      content: ctrlTotalQtyFragment
    },
    "max_oper_qty": {
      title: t("table.title.max_oper_qty"),
      content: `${maxOperQty} kg`
    },
    "year_max_storage_qty": {
      title: t("table.title.year_max_storage_qty"),
      content: `${maxStorageQty} kg`
    },
    "ccb_item": {
      title: t("text.chemical.ccb.item"),
      content: ccbLevel && (
        <span
          className="custom-link"
          onClick={() => {
            setCcbLinkId(inventoryId);
            setShowCCBView(true);
          }}
        >
          {ccbLevel}
        </span>
      )
    }
  };

  return (
    <>
      <tr>
        <td data-title={t("table.title.item")}><label className="my-3 me-2">{index}</label></td>
        <td data-title={t("table.title.chemical.information")} className='text-start'>
          <SubstanceInfo
            substList={substList}
            configMap={configMap}
          />
        </td>
        <td data-title={responsiveContentMap.ghs_img.title} className="responsive-cell">{responsiveContentMap.ghs_img.content}</td>
        <td data-title={responsiveContentMap.chem_class.title} className='text-start responsive-cell'>{responsiveContentMap.chem_class.content}</td>
        <td data-title={t("table.title.chemical.phase_state")} className='text-center'>{configMap[phaseState]?.configName}</td>
        <td data-title={responsiveContentMap.unit_ctrl_qty.title} className='text-end responsive-cell'>{responsiveContentMap.unit_ctrl_qty.content}</td>
        <td data-title={responsiveContentMap.max_oper_qty.title} className='text-end responsive-cell'>{maxOperQty} kg</td>
        <td data-title={responsiveContentMap.year_max_storage_qty.title} className='text-end responsive-cell'>{maxStorageQty} kg</td>
        <td data-title={t("table.title.inventory_quantity")} className='text-end'>
          <label>{inventoryQty} kg</label>
        </td>
        <td data-title={responsiveContentMap.ccb_item.title} className='text-center responsive-cell'>
          {responsiveContentMap.ccb_item.content}
        </td>
        <td data-title={t("table.title.action")} className="action-cell">
          <OperDetailButton inventoryItem={inventory} operDetailPageMode={OperDetailPageMode.OPER_ITEM} />
          {isDisable && <>
            {isChemCancelDisableRole &&
              <button type="button" className="btn btn-sm btn-danger my-1" title={t('button.chemical.operation_disable_cancel')}
                onClick={() => cancelDisable()}>
                <i className="fas fa-times fa-lg me-1" />{t('button.chemical.operation_disable_cancel')}
              </button>}
            {isChemDisposeRole &&
              <button type="button" className="btn btn-sm btn-danger my-1" title={t('button.dispose')} onClick={() => doDisposal()}>
                <i className="fas fa-trash-can fa-lg me-1" />{t('button.dispose')}
              </button>}
          </>}
          <ExpandRwdButton isExpanded={isExpanded} onClick={toggleExpanded} />
        </td>
      </tr>
      {isExpanded && (
        <tr className="expanded-row">
          <td colSpan={5} className="p-0">
            <div className="expanded-content">
              <table className="expanded-table">
                <tbody>
                  {/* 使用映射物件動態生成展開行 */}
                  {Object.entries(responsiveContentMap).map(([key, { title, content }]) => (
                    <tr key={key}>
                      <td className="expanded-label">{title}</td>
                      <td className="expanded-value">{content}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </td>
        </tr>
      )}
    </>
  );
};

const StlyedChemicalOperItemList = styled.div<{ $loading?: boolean }>`
  padding-bottom:150px; 

  .custom-link {
    color: blue;
    cursor: pointer;
  }

  .custom-link:hover {
    color: #551A8B;
  }

  .ghs-img{
    width:40px;
  }

  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }

  .table-container {
      border: 1px solid #ccc;
      border-radius: 20px;
      border-collapse: separate;
      padding-bottom: 0;
      display: flex;
      margin-bottom: 1rem;
      flex-direction: column;
      
      @media screen and (min-width: 1400px) {
          overflow-x: auto !important;  // 強制覆蓋原本的 hidden
      }
  }

  table {
    position: relative;
    min-height: ${props => props.$loading ? "300px" : "auto"};
    width: 100%;
    
    @media screen and (min-width: 1400px) {
        min-width: 1400px;
    }
    
    th {
      text-align: center;
      height: auto;
      white-space: normal;
      vertical-align: middle;
      padding: 10px;
    }
    td {
      .form-check {
        justify-content: center;  
      }
    }
  }

  @media (max-width: 600px){
  
    .ghs-img{
      width:30%;
    }
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 125px;
        text-align:left;
        min-height:100px; // rwd後 td最小高度
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          
    padding-bottom: 10px;
    min-height: auto; /* 重置最小高度 */
    height: auto; /* 重置高度 */ 
    white-space: normal; /* 讓長標題能夠換行 */
    word-wrap: break-word; /* 在需要時強斷詞 */
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
`;

export default ChemicalOperItemList;
