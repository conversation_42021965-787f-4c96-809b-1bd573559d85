import { useState } from "react";

// 定義元件的 Props 型別
interface CheckboxWithInputProps {
    label: string;             // checkbox 的標籤
    checkboxId: string;        // checkbox 的 id
    checkboxValue: string;      // checkbox 的 value
    inputValue?: string;        // 輸入框的初始值
    checkedValue?: boolean;     // 預設的勾選狀態
    inputPlaceholder?: string;  // 輸入框的 placeholder
    onCheckboxChange?: (checked: boolean) => void; // 勾選狀態變更時的 callback
    onInputChange?: (value: string) => void;  // 輸入框內容變更時的 callback
}

const CheckboxWithInput: React.FC<CheckboxWithInputProps> = ({
    label,
    inputPlaceholder,
    checkboxId,
    checkboxValue,
    checkedValue = false,
    inputValue = "",
    onCheckboxChange = (checked: boolean) => { },
    onInputChange = (value: string) => { },
}) => {
    const [isChecked, setIsChecked] = useState<boolean>(checkedValue); // 控制 checkbox 狀態
    const [inputText, setInputText] = useState<string>(inputValue); // 控制 input 的值

    // 處理 checkbox 變更
    const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const checked = e.target.checked;
        setIsChecked(checked);
        onCheckboxChange(checked); // 將變更的狀態傳遞給父元件
        if (!checked) {
            setInputText("");
        }
    };

    // 處理輸入框變更
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setInputText(value);
        onInputChange(value); // 將變更的值傳遞給父元件
    };

    return (
        <div className="form-check mb-3 me-3">
            <input
                className="form-check-input"
                type="checkbox"
                id={checkboxId}
                value={checkboxValue}
                checked={isChecked} // 綁定狀態
                onChange={handleCheckboxChange} // 勾選變更事件
            />
            <label className="form-check-label" htmlFor={checkboxId}>
                {label}
            </label>

            {/* 當 checkbox 被勾選時顯示輸入框 */}
            {isChecked && (
                <input
                    type="text"
                    className="form-control mt-2"
                    placeholder={inputPlaceholder}
                    value={inputText} // 綁定輸入框的值
                    onChange={handleInputChange} // 輸入變更事件
                />
            )}
        </div>
    );
};

export default CheckboxWithInput;
