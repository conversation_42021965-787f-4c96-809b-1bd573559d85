import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const GroupAPI = {
  getGroupList: async (parms: BaseParams) => {
    return apiRequest(getApiAddress + "group/list", createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  getGroupDetail: async (parms: BaseParams & {
    groupId: string;
  }) => {
    return apiRequest(getApiAddress + "group/detail", createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  addGroup: async (parms: BaseParams & {
    groupName: string;
    groupDescription: string;
  }) => {
    return apiRequest(getApiAddress + "group/add", createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  editGroup: async (parms: BaseParams & {
    groupId: string;
    groupName: string;
    groupDescription: string;
  }) => {
    return apiRequest(getApiAddress + "group/edit", createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  delGroup: async (parms: BaseParams & {
    groupId: string;
  }) => {
    return apiRequest(getApiAddress + "group/del", createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  editGroupFunc: async (parms: BaseParams & {
    groupId: string;
    groupFuncId: string[];
  }) => {
    return apiRequest(getApiAddress + "groupfunc/edit", createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
};
