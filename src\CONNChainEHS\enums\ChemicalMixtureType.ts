// ChemicalMixtureType.ts

export enum ChemicalMixtureType {
  general = "general", // 一般化學品（未被列管）
  controlled = "controlled", // 受控化學品（有特定規範） 管制性
  svhc = "svhc", // 高關注物質 關注性
  toxic = "toxic", // 有毒物質 毒化物
}

export const getChemicalMixtureType = (
  value: string
): ChemicalMixtureType | null => {
  switch (value) {
    case ChemicalMixtureType.general:
      return ChemicalMixtureType.general; // 返回一般化學品類型
    case ChemicalMixtureType.controlled:
      return ChemicalMixtureType.controlled; // 返回受控化學品類型
    case ChemicalMixtureType.svhc:
      return ChemicalMixtureType.svhc; // 返回高關注物質類型
    case ChemicalMixtureType.toxic:
      return ChemicalMixtureType.toxic; // 返回有毒物質類型
    default:
      return null; // 如果數字不匹配，返回 null
  }
};
