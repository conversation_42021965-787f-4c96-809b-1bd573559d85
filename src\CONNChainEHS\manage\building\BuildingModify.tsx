import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { BuildAPI } from "../../../api/buildAPI";
import { FileAPI } from "../../../api/fileAPI";
import InputFileUpload from "../../common/InputFileUpload";
import IntegerInput from "../../common/input/InputNumInt";
import { errorMsg } from "../../common/SwalMsg";
import { FILE_TYPE_BUILD_RESCUE_IMAGE_CLASS_A, LANGUAGE_MAPPING_TYPE_BUILDING, UPLOAD_ACCEPT_TYPE_IMAGE } from "../../constant/constants";
import { ActionMode } from "../../enums/ActionMode";
import useLoginUser from "../../hooks/useLoginUser";
import { EhsBuild, initEhsBuild } from "../../models/EhsBuild";
import { EhsConfigParam } from "../../models/EhsConfigParam";
import { EhsFile, initEhsFile } from "../../models/EhsFile";
import { EhsLanguage, initEhsLanguage } from "../../models/EhsLanguage";
import { getBasicLoginUserInfo } from "../../utils/authUtil";
import { getLabTextObj, sortLanguagesListByCurrent } from "../../utils/langUtil";
import { isApiCallSuccess } from "../../utils/resultUtil";
import LoadingSpinner from "../../common/LoadingSpinner";

const initReqShowMsgValues = {
    areaId: null as boolean | null,
    buildNo: null as boolean | null,
    buildName: null as boolean | null
};

export default function BuildingModify(props: {
    onClose: () => void;
    onActionSuccess: () => void;
    setLoadingBlock: (block: boolean) => void;
    mode: ActionMode | null;
    modifyData: EhsBuild;
    areaSelectOption: JSX.Element[];
    buildTypeList: EhsConfigParam[];
    showBuildType: boolean;
}) {
    const { onClose, onActionSuccess, setLoadingBlock, mode, modifyData, areaSelectOption, buildTypeList, showBuildType } = props;
    const { loginUser } = useLoginUser();
    const { t } = useTranslation();
    const [editValues, setEditValues] = useState(initEhsBuild);
    const [reqShowMsgValues, setReqShowMsgValues] = useState(initReqShowMsgValues);
    const sortedLangList = loginUser ? sortLanguagesListByCurrent(loginUser!.langType) : [];
    const hasMultiLang = sortedLangList.length > 1;
    const [buildNameList, setBuildNameList] = useState<EhsLanguage[]>([]);
    const [delFileIdList, setDelFileIdList] = useState<string[]>([]);
    const [buildRescueImgClassA, setBuildRescueImgClassA] = useState<EhsFile | null>(null);//A類救援圖 甲類
    const [buildRescueImgClassAContent, setBuildRescueImgClassAContent] = useState<File | null>(null);
    const [showOtherLangName, setShowOtherLangName] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const { buildTypeText } = getLabTextObj(loginUser, t);
    const isAddMode = mode === ActionMode.ADD;
    const actBtnColor = isAddMode ? "purple" : "warning";
    const actTitle = mode === null ? "" : isAddMode ? t("text.add") : t("text.edit");
    const nameTitle = t("text.name");
    let firstLangValue = "";
    const isModifyLoading = isLoading && !isAddMode;

    useEffect(() => {
        setEditValues(isAddMode ? initEhsBuild : modifyData);
        setReqShowMsgValues(initReqShowMsgValues);
        setShowOtherLangName(false);
        setBuildNameList([]);
    }, [isAddMode, modifyData]);

    useEffect(() => {
        if (loginUser && mode && !isAddMode && editValues.buildId) {
            setIsLoading(true);

            Promise.all([
                BuildAPI.getBuildDetail({
                    ...getBasicLoginUserInfo(loginUser)!,
                    buildId: editValues.buildId,
                }),
                fetchFiles()
            ]).then(([result]) => {
                if (isApiCallSuccess(result)) {
                    setBuildNameList(result.results.buildNameList);
                } else {
                    errorMsg(result.message)
                }
            }).catch((err) => {
                console.error(err);
                alert(err);
            }).finally(() => {
                setIsLoading(false);
            });
        }
    }, [loginUser, editValues.buildId, mode]);

    const fetchFiles = () => {
        FileAPI.getFileList({
            ...getBasicLoginUserInfo(loginUser),
            fileTypeList: [FILE_TYPE_BUILD_RESCUE_IMAGE_CLASS_A],
            fileMappingIdList: [editValues.buildId],
        }).then((result) => {
            if (isApiCallSuccess(result)) {
                const resultFileList: EhsFile[] = result.results;
                resultFileList.forEach((file: EhsFile) => {
                    if (file.fileType === FILE_TYPE_BUILD_RESCUE_IMAGE_CLASS_A) {
                        setBuildRescueImgClassA(file);
                    }
                });
            }
        });
    };

    const onAction = () => {
        if (!loginUser) {
            return;
        }

        const { areaId, buildNo, buildName } = editValues;
        const showMsgValues = {
            areaId: !areaId,
            buildNo: !buildNo,
            buildName: !buildName,
        };

        setReqShowMsgValues(showMsgValues);

        if (Object.values(showMsgValues).some(value => value)) {
            return;
        }

        setLoadingBlock(true);
        let newBuildNameList: EhsLanguage[] = [...buildNameList].map(build => ({
            ...build,
            langValue: build.langValue.trim()
        }));
        sortedLangList.forEach((item, idx) => {
            const isFirst = idx === 0;
            if (isFirst) {
                const firstLang = newBuildNameList.find(lang => lang.langType === item.code);
                firstLangValue = firstLang ? firstLang.langValue : ""; // 確保 firstLang 存在，避免錯誤
            } else {
                const exists = newBuildNameList.some((lang) => lang.langType === item.code);
                if (exists) {
                    // 如果存在，且 langValue 為空，則將其設置為 firstLangValue
                    const existingLangIndex = newBuildNameList.findIndex(lang => lang.langType === item.code);
                    if (existingLangIndex !== -1 && !newBuildNameList[existingLangIndex].langValue) {
                        newBuildNameList[existingLangIndex].langValue = firstLangValue;
                    }

                } else {
                    newBuildNameList = [
                        ...newBuildNameList,
                        {
                            ...initEhsLanguage,
                            langType: item.code,
                            langValue: firstLangValue,
                            mappingType: LANGUAGE_MAPPING_TYPE_BUILDING
                        }
                    ];
                }
            }
        });

        if (isAddMode) {
            editValues.buildStatus = 1;
            BuildAPI.addBuild({
                ...editValues,
                ...getBasicLoginUserInfo(loginUser)!,
                rescueImageClassA: buildRescueImgClassAContent,
                buildNameList: newBuildNameList,
            }).then((result) => {
                if (isApiCallSuccess(result)) {
                    onActionSuccess();
                } else {
                    errorMsg(result.message)
                }
                setLoadingBlock(false);
            }).catch((err) => {
                debugger;
                errorMsg(err);
                setLoadingBlock(false);
            });
        } else {
            BuildAPI.editBuild({
                ...editValues,
                ...getBasicLoginUserInfo(loginUser)!,
                rescueImageClassA: buildRescueImgClassAContent,
                buildNameList: newBuildNameList,
                delFileIdList
            }).then((result) => {
                if (isApiCallSuccess(result)) {
                    onActionSuccess();
                } else {
                    errorMsg(result.message)
                }
                setLoadingBlock(false);
            }).catch((err) => {
                debugger;
                errorMsg(err);
                setLoadingBlock(false);
            });
        }
    };

    const clickClose = () => {
        onClose();
        setDelFileIdList([]);
    }

    return (
        <StyledModifyBuild>
            <div className="modifyBuild">
                <div className="modifyBuild-header">
                    <h4 className="modal-title">{actTitle}</h4>
                    <button
                        type="button"
                        className="btn-close"
                        aria-hidden="true"
                        onClick={clickClose}
                    ></button>
                </div>
                <div className="modifyBuild-body">
                    {isModifyLoading ? (
                        <LoadingSpinner />
                    ) : (
                        <div className="row mb-4 justify-content-center">
                            <div className="col-md-10 mb-3">
                                <label className="fw-bold mb-1">
                                    {t('text.area.item')}：<span className="text-danger">*</span>
                                </label>
                                <select className={`form-select w-100 ${reqShowMsgValues.areaId && 'is-invalid'}`} value={editValues.areaId}
                                    onChange={(e) => {
                                        setEditValues({
                                            ...editValues,
                                            areaId: e.target.value,
                                        });
                                        setReqShowMsgValues({ ...reqShowMsgValues, areaId: !e.target.value })
                                    }}>
                                    {areaSelectOption}
                                </select>
                                {reqShowMsgValues.areaId &&
                                    <div className="invalid-feedback">{t("message.select")}</div>
                                }
                            </div>
                            <div className="col-md-10 mb-3">
                                <label className="fw-bold mb-1">
                                    {t('text.no')}：<span className="text-danger">*</span>
                                </label>
                                <input
                                    value={editValues.buildNo}
                                    type="text"
                                    className={`form-control form-control-lg ${reqShowMsgValues.buildNo && 'is-invalid'}`}
                                    data-parsley-required="true"
                                    onChange={(e) => {
                                        setEditValues({
                                            ...editValues,
                                            buildNo: e.target.value,
                                        });
                                        setReqShowMsgValues({ ...reqShowMsgValues, buildNo: !e.target.value })
                                    }}
                                />
                                {reqShowMsgValues.buildNo &&
                                    <div className="invalid-feedback">{t("message.enter")}</div>
                                }
                            </div>

                            {loginUser && sortedLangList.map((item, idx) => {
                                const isFirst = idx === 0;
                                const buildNameVal = buildNameList.find((lang) => lang.langType === item.code)?.langValue || "";
                                return (
                                    <div className={`col-md-10 mb-3 ${!isFirst && !showOtherLangName && 'd-none'}`} key={'buildname' + item.code}>
                                        <label className="fw-bold mb-1">
                                            {nameTitle}：{isFirst && <span className="text-danger me-1">*</span>} {hasMultiLang && item.language}
                                        </label>
                                        <input
                                            value={buildNameVal}
                                            type="text"
                                            className={`form-control form-control-lg ${isFirst && reqShowMsgValues.buildName && 'is-invalid'}`}
                                            data-parsley-required="true"
                                            onChange={(e) => {
                                                const newVal = e.target.value;
                                                if (isFirst) {
                                                    setEditValues({
                                                        ...editValues,
                                                        buildName: e.target.value,
                                                    });
                                                    setReqShowMsgValues({ ...reqShowMsgValues, buildName: !e.target.value })
                                                }
                                                setBuildNameList((prevList) => {
                                                    const index = prevList.findIndex((lang) => lang.langType === item.code);
                                                    if (index !== -1) {
                                                        // 找到匹配的項目，更新其 langValue
                                                        return prevList.map((lang, idx) =>
                                                            idx === index ? { ...lang, langValue: newVal } : lang
                                                        );
                                                    } else {
                                                        // 未找到匹配的項目，新增一個新的項目
                                                        return [
                                                            ...prevList,
                                                            {
                                                                ...initEhsLanguage,
                                                                langType: item.code,
                                                                langValue: newVal,
                                                                mappingType: LANGUAGE_MAPPING_TYPE_BUILDING
                                                            }
                                                        ];
                                                    }
                                                });
                                            }}
                                        />
                                        {isFirst && reqShowMsgValues.buildName &&
                                            <div className="invalid-feedback">{t("message.enter")}</div>
                                        }
                                        {isFirst && hasMultiLang && <button type="button" className="btn btn-primary mt-2" onClick={() => {
                                            setShowOtherLangName(!showOtherLangName);
                                        }}>{t('button.enter_other_lang')} {nameTitle}</button>}
                                    </div>
                                )
                            })}
                            <div className="col-md-10 mb-3">
                                <label className="fw-bold mb-1">
                                    {t('text.building.floor.item')}：
                                </label>
                                <IntegerInput className="form-control form-control-lg" value={editValues.buildFloors} maxLength={3}
                                    onChange={(e) => {
                                        setEditValues({
                                            ...editValues,
                                            buildFloors: parseInt(e.target.value),
                                        });
                                    }} />
                            </div>
                            <div className="col-md-10 mb-3">
                                <label className="fw-bold mb-1">
                                    {t('text.building.floor.basement')}：
                                </label>
                                <IntegerInput className="form-control form-control-lg" value={editValues.buildUnfloors} maxLength={2}
                                    onChange={(e) => {
                                        setEditValues({
                                            ...editValues,
                                            buildUnfloors: parseInt(e.target.value),
                                        });
                                    }} allowNegative
                                />
                            </div>
                            <div className="col-md-10 mb-3">
                                <label className="fw-bold mb-1">
                                    {t('text.building.structure')}：
                                </label>
                                <input
                                    value={editValues.buildStructure || ""}
                                    type="text"
                                    className={`form-control form-control-lg`}
                                    data-parsley-required="true"
                                    onChange={(e) => {
                                        setEditValues({
                                            ...editValues,
                                            buildStructure: e.target.value,
                                        });
                                    }}
                                />
                            </div>
                            <div className="col-md-10 mb-3">
                                <label className="fw-bold mb-1">
                                    {t('text.building.oper_time')}：
                                </label>
                                <input
                                    value={editValues.buildOperTime || ""}
                                    type="text"
                                    className={`form-control form-control-lg`}
                                    data-parsley-required="true"
                                    onChange={(e) => {
                                        setEditValues({
                                            ...editValues,
                                            buildOperTime: e.target.value,
                                        });
                                    }}
                                />
                            </div>
                            {showBuildType && <div className="col-md-10 mb-3">
                                <label className="fw-bold mb-1">
                                    {buildTypeText}
                                </label><br />
                                {buildTypeList.map((item) => {
                                    const id = 'radio-' + item.configId;
                                    return (
                                        <div className="form-check form-check-inline" key={id}>
                                            <input className="form-check-input" type="radio" name="buildType" id={id}
                                                data-parsley-mincheck="1"
                                                onChange={(e) => {
                                                    setEditValues({
                                                        ...editValues,
                                                        buildType: item.configIvalue,
                                                    });
                                                }} value={item.configIvalue}
                                                checked={editValues.buildType === item.configIvalue} />
                                            <label className="form-check-label" htmlFor={id}>{item.configName}</label>
                                        </div>
                                    );
                                })
                                }
                            </div>}

                            <div className="col-md-10 mb-3">
                                <label className="fw-bold mb-1">
                                    {t('text.building.rescue_image_class_a')}：
                                    <InputFileUpload inputId={'allBuildingImage'}
                                        defaultFile={buildRescueImgClassA || undefined}
                                        defaultFileInfo={buildRescueImgClassA || undefined}
                                        onFileChange={(file) => {
                                            const newInfoFile = file ? {
                                                ...initEhsFile,
                                                fileName: file ? file.name : "",
                                                fileMappingId: editValues.buildId,
                                            } : null;
                                            if (file === null && buildRescueImgClassA) {
                                                setDelFileIdList([...delFileIdList, buildRescueImgClassA.fileId]);
                                            }
                                            setBuildRescueImgClassA(newInfoFile)
                                            setBuildRescueImgClassAContent(file);
                                        }}
                                        acceptedFileTypes={UPLOAD_ACCEPT_TYPE_IMAGE} />
                                </label>
                            </div>
                        </div>
                    )}
                </div>
                {actTitle && <div className="modifyBuild-footer">
                    <div className="btn btn-white" aria-hidden="true" onClick={clickClose}>
                        <i className="fas fa-times me-1" />
                        {t("button.close")}
                    </div>
                    {!isModifyLoading && <div className={`btn btn-${actBtnColor} ms-2`} onClick={onAction}>
                        <i className="fas fa-cubes" /> {actTitle}
                    </div>}
                </div>}
            </div>
        </StyledModifyBuild>
    );
}

const StyledModifyBuild = styled.div`
  background: white;
  width: 100%;
  max-width: 500px;
  display: flex;
  flex-direction: column;
  
  .modifyBuild-header {
    background: rgb(251, 205, 165);
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ced4da;
    margin-bottom: 5px;
    width: 100%;
    box-sizing: border-box;
    flex-shrink: 0;
  }
  
  .modifyBuild-body {
    padding: 15px 15px 0 15px;
    margin-bottom: 15px;
    overflow-y: auto;
    flex: 1;
  }
  
  .modifyBuild-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: flex-end;
    width: 100%;
    box-sizing: border-box;
    flex-shrink: 0;
    
    .btn.btn-purple {
      margin-left: 10px;
    }
  }
  
    @media (max-width: 1024px) {
        width: 100%;  // 在小螢幕時使用 100% 寬度
        max-width: 100%;
        height: 100%;
        
        .modifyBuild-body {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 20px; // 添加底部間距，避免內容被 footer 遮擋
            max-height: 700px;
        }
        
        .modifyBuild-footer {
            position: sticky;
            bottom: 0;
            background: white;
            padding: 15px;
            border-top: 1px solid #ced4da;
            display: flex;
            justify-content: center;
        }
    }
`;
