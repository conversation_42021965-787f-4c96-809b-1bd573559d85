import React from 'react';
import { useTranslation } from "react-i18next";

interface PageSizeSelectorProps<T> {
    pageSize: number;
    setCondition: React.Dispatch<React.SetStateAction<T>>;
    condition?: T;
    specialCondition?: {
        orgList?: {
            orglvId: string;
        };
        tabPagination?: {
            tabId: string;
            updateTabPageSize?: (tabId: string, pageSize: number) => void;
        };
    };
}

const PageSizeSelector: React.FC<PageSizeSelectorProps<any>> = ({ pageSize, setCondition, condition, specialCondition }) => {
    const { t } = useTranslation();
    const pageSizeList = [10, 25, 50, 100, 200];

    const handlePageSizeChange = (e: any) => {
        const newPageSize = parseInt(e.target.value);

        // 處理分頁籤頁面大小變更
        if (specialCondition?.tabPagination) {
            // 先更新外部的 conditionMap
            setCondition((prevCondition: any) => {
                // 如果 condition 是一個映射，則更新特定分頁籤的條件
                if (typeof prevCondition === 'object' && prevCondition !== null) {
                    return {
                        ...prevCondition,
                        [specialCondition.tabPagination!.tabId]: {
                            ...prevCondition[specialCondition.tabPagination!.tabId],
                            pageSize: newPageSize,
                            currentPage: 1
                        }
                    };
                }
                // 否則，更新一般條件
                return {
                    ...prevCondition,
                    pageSize: newPageSize,
                    currentPage: 1
                };
            });

            // 如果提供了更新函數，調用它
            if (specialCondition.tabPagination.updateTabPageSize) {
                specialCondition.tabPagination.updateTabPageSize(
                    specialCondition.tabPagination.tabId,
                    newPageSize
                );
            }
            return;
        }

        // 處理組織列表分頁
        if (specialCondition?.orgList) {
            const newCondition = condition?.[specialCondition.orgList.orglvId];
            if (newCondition) {
                newCondition.pageSize = newPageSize;
                newCondition.currentPage = 1;
                setCondition((prevCondition: any) => ({
                    ...prevCondition,
                    [specialCondition.orgList!.orglvId]: newCondition,
                }));
            }
            return;
        }

        // 一般分頁邏輯
        setCondition((prevCondition: any) => ({
            ...prevCondition,
            pageSize: newPageSize,
            currentPage: 1
        }));
    };

    return (
        <div className="d-flex align-items-center">
            {t('table.pagination.pagesize_start')}
            <select
                value={pageSize}
                onChange={handlePageSizeChange}
                name="data-table-default_length"
                aria-controls="data-table-default"
                className="form-select form-select-sm"
            >
                {pageSizeList.map((pageSize, idx) => {
                    return <option key={pageSize} value={pageSize}>{pageSize}</option>
                })}
            </select>
            {t('table.pagination.pagesize_end')}
        </div>
    );
};

export default PageSizeSelector;
