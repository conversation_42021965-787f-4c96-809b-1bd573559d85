import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import styled from "styled-components";
import { RoleAPI } from '../../../../api/roleAPI';
import { RouAPI } from '../../../../api/rouAPI';
import { SelectListAPI } from '../../../../api/selectListAPI';
import { UserAPI } from '../../../../api/userAPI';
import { AppPaths } from '../../../../config/app-paths';
import BackButton from '../../../common/button/BackButton';
import { errorMsg } from '../../../common/SwalMsg';
import { OPTION_SHOW_USER_BIRTHDAY_AGE, ROU_TYPE_LAB, ROU_TYPE_ORG } from '../../../constant/constants';
import { ActionMode } from '../../../enums/ActionMode';
import useLoginUser from '../../../hooks/useLoginUser';
import Breadcrumbs from '../../../layout/Breadcrumbs';
import { EhsOrg } from '../../../models/EhsOrg';
import { EhsOrgLevel } from '../../../models/EhsOrgLevel';
import { EhsRole } from '../../../models/EhsRole';
import { EhsRou } from '../../../models/EhsRou';
import { EhsUser, initEhsUser } from '../../../models/EhsUser';
import { findItemByLangType, getLabTextObj } from '../../../utils/langUtil';
import { isApiCallSuccess } from '../../../utils/resultUtil';
import BasicInfo from './BasicInfo';
import Lab from './Lab';
import OtherAuths from './OtherAuths';
import { getBasicLoginUserInfo, navigateToHome } from '../../../utils/authUtil';
import { AreaAPI } from '../../../../api/areaAPI';
import { EhsArea } from '../../../models/EhsArea';
import { OptionAPI } from '../../../../api/optionAPI';
import { EhsOptions } from '../../../models/EhsOptions';

function UserDetail() {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const { labAffiliated } = getLabTextObj(loginUser, t);
  const [showBirthdayAge, setShowBirthdayAge] = useState<boolean>(false);
  const [user, setUser] = useState<EhsUser>(initEhsUser);
  const [ownerLabList, setOwnerLabList] = useState<EhsRou[]>([])
  const [otherAuthList, setOtherAuthList] = useState<EhsRou[]>([])
  const [areaList, setAreaList] = useState<EhsArea[]>([]);
  const [roleList, setRoleList] = useState<EhsRole[]>([]);
  const [orgLevelList, setOrgLevelList] = useState<EhsOrgLevel[]>([]);
  const [orgList, setOrgList] = useState<EhsOrg[]>([]);
  const { state } = useLocation();
  const { userId, roleId, mode } = state || {};
  const isEditMode = mode === ActionMode.EDIT;
  const modeObj = {
    [ActionMode.ADD]: {
      funcName: t('func.add')
    },
    [ActionMode.EDIT]: {
      funcName: t('func.edit')
    },
    [ActionMode.DETAIL]: {
      funcName: t('func.detail')
    }
  };
  const funcName = (mode !== undefined && mode in modeObj)
    ? modeObj[mode as keyof typeof modeObj].funcName
    : t('func.detail');
  const orgSelectInfo = {
    orgList: orgList,
    orgLevelList: orgLevelList,
  };
  const isEditNoUser = isEditMode && (!userId || !roleId);

  useEffect(() => {
    if (isEditNoUser) {
      navigateToHome(navigate);
      return;
    }
    if (!loginUser) {
      return;
    }
    fetchOption();
  }, [loginUser])

  useEffect(() => {
    if (isEditNoUser) {
      navigateToHome(navigate);
      return;
    }
    if (!!userId && !!roleId && loginUser) {
      UserAPI.getUserDetail({
        ...getBasicLoginUserInfo(loginUser),
        userId: userId,
        roleId: roleId
      }).then(result => {
        if (isApiCallSuccess(result)) {
          const userDetail = result.results;
          const userNameObject = findItemByLangType(userDetail.userNameList, loginUser.langType);
          const userName = userNameObject ? userNameObject.userName : '';
          setUser({ ...userDetail, userName: userName });
        }
      })
      fetchUserRole();
      fetchAreaData();
      if (isEditMode) {
        RoleAPI.getRoleList({
          ...getBasicLoginUserInfo(loginUser),
        }).then(result => {
          if (isApiCallSuccess(result)) {
            setRoleList(result.results);
          } else {
            errorMsg(result.message);
          }
        }).catch(err => {
          errorMsg(err);
        })
        fetchOrgData();
      }
    }
  }, [loginUser, i18n.language])

  const fetchUserRole = () => {
    RouAPI.getUserRoleList({
      ...getBasicLoginUserInfo(loginUser),
      userId: userId!,
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const rs = result.results;
        const ownerLabList = rs.filter((item: EhsRou) => item.rouType === ROU_TYPE_LAB);
        setOwnerLabList(ownerLabList);
        const otherAuthList = rs.filter((item: EhsRou) => item.rouType === ROU_TYPE_ORG);
        setOtherAuthList(otherAuthList);
      } else {
        errorMsg(result.message);
      }
    }).catch(err => {
      errorMsg(err);
    })
  }

  const fetchOrgData = () => {
    SelectListAPI.getSelectOrgList({
      ...getBasicLoginUserInfo(loginUser),
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const orgData = result.results;
        setOrgLevelList(orgData.orgLevelList);
        setOrgList(orgData.orgList);
      }
    })
  }

  const fetchAreaData = () => {
    AreaAPI.getAreaList({
      ...getBasicLoginUserInfo(loginUser),
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        setAreaList(result.results);
      }
    });
  };

  const fetchOption = () => {
    const optionIds = [OPTION_SHOW_USER_BIRTHDAY_AGE];
    OptionAPI.getOptionListByIds({
      ...getBasicLoginUserInfo(loginUser)!,
      optionIdList: optionIds
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const options = result.results;
        const optionsMap: {
          [key: string]: EhsOptions;
        } = {};
        options.forEach((item: any) => {
          optionsMap[item.optionId] = item;
        });
        setShowBirthdayAge(optionsMap[OPTION_SHOW_USER_BIRTHDAY_AGE].optionEnabled);
      }
    });
  };

  return (
    <StyledUserDetail>
      <div id="content" className="d-flex flex-column p-0">
        {/* BEGIN scrollbar */}
        <div className="app-content-padding flex-grow-1">
          {/* BEGIN breadcrumb */}
          <Breadcrumbs items={[
            { label: t("func.org_user_manage") },
            { label: t("func.user_manage"), path: AppPaths.manage.userList },
            { label: funcName },
          ]} />
          {/* END breadcrumb */}
          {/* BEGIN page-header */}
          <h1 className="page-header">{funcName}</h1>
          {/* END page-header */}

          {/* BEGIN row */}
          <BackButton />
          <div className="row mt-3">
            {/* 左邊基本資料 */}
            <div className="col-12 col-xl-4 mb-4 mb-xl-0">
              {<BasicInfo userInfo={user} onChange={(data) => { setUser({ ...user, ...data }) }} isEditMode={isEditMode}
                showBirthdayAge={showBirthdayAge}
                roleList={roleList}
                orgInfo={orgSelectInfo}
              />}
            </div>
            {/* 右邊分頁 */}
            <div className="col-12 col-xl-8">
              <ul className="nav nav-tabs fs-5">
                <li className="nav-item">
                  <a href="#user-tab-1" data-bs-toggle="tab" className="nav-link active"><i className="fas fa-house-user me-1"></i>{labAffiliated}</a>
                </li>
                <li className="nav-item">
                  <a href="#user-tab-2" data-bs-toggle="tab" className="nav-link"><i className="fa-solid fa-user-shield me-1"></i>{t('text.role.other')}</a>
                </li>
                {/* <li className="nav-item">
                <a href="#user-tab-3" data-bs-toggle="tab" className="nav-link"><i className="fa-solid fa-graduation-cap"></i> 教育訓練紀錄</a>
              </li> */}
              </ul>
              <div className="tab-content panel p-3 rounded-0 rounded-bottom">
                {/* 所屬實驗室場所 */}
                <Lab isEditMode={isEditMode} userId={userId} ownerLabList={ownerLabList} areaList={areaList} roleList={roleList} fetchUserRole={fetchUserRole} />
                {/* 其他權限 */}
                <OtherAuths isEditMode={isEditMode} userId={userId} otherAuthList={otherAuthList} areaList={areaList} roleList={roleList} fetchUserRole={fetchUserRole}
                  orgInfo={orgSelectInfo} />
                {/* 教育訓練紀錄 之後做功能在顯示*/}
                {/* <EduRecords isEditMode={isEditMode} /> */}
              </div>
            </div>
          </div>
          {/* END row */}
        </div>
        {/* END scrollbar */}
      </div>
    </StyledUserDetail>
  );
}

const StyledUserDetail = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;

  #content {
    flex: 1;
    min-height: 0;
  }

  .app-content-padding {
    height: 100%;
    overflow-y: auto;
    padding: 20px;
  }

  .tab-content {
    overflow-y: auto;
    padding: 1rem;
  }

  @media (max-width: 1200px) {
    .app-content-padding {
      height: auto;
    }

    .tab-content {
      height: calc(100vh - 320px);
      overflow-y: auto;
    }

    .col-12 {
      margin-bottom: 1rem;
    }
  }

  @media (max-width: 600px){
    ul{
      margin-top: 1em;
    }
    
    .tab-content {
      height: calc(100vh - 360px);
    }
  }
`
export default UserDetail;
