import { getChemClassifyBadgeStyle } from 'ehs/utils/chemicalUtil';
import React from 'react';

interface Item {
    configValue: string;
    configName: string;
    // Add more properties as needed
}

interface Props {
    item: Item;
    extClassName?: string;
}

const ChemicalClassificationBadge: React.FC<Props> = ({ item = { configValue: '', configName: '' }, extClassName }) => {
    return (
        <label className={`badge me-1 mt-5 user-select-none ${getChemClassifyBadgeStyle(item.configValue)} ${extClassName}`}>
            {item.configName}
        </label>
    );
};

export default ChemicalClassificationBadge;
