import InputMixConcen from 'ehs/common/input/inputMixConcen';
import { EhsChemical } from 'ehs/models/EhsChemical';
import { EhsConfigParam } from 'ehs/models/EhsConfigParam';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface AddSubstanceDivProps {
    chemInfo: EhsChemical;//化學物質資訊
    mixConcenTypeOptions: EhsConfigParam[]; // 混合物濃度選項
    phaseStateOptions: EhsConfigParam[];//相態選項
    selectedPhaseState?: string;//已選相態
    defaultExpanded?: boolean;//預設展開
    setExpendId?: (id: string) => void;//設定要展開的化學物質
    setPhaseState?: (phaseState: string) => void;//設定相態
    handleAdd?: () => void;//添加化學物質動作
}

const AddSubstanceDiv: React.FC<AddSubstanceDivProps> = ({
    chemInfo,
    mixConcenTypeOptions,
    phaseStateOptions,
    selectedPhaseState = "",
    defaultExpanded = false,
    setExpendId = () => { },
    setPhaseState = () => { },
    handleAdd = () => { },
}) => {
    const { t } = useTranslation();
    const { chemId } = chemInfo;
    const [isExpanded, setIsExpanded] = useState(false); // 控制展開狀態

    useEffect(() => {
        setIsExpanded(defaultExpanded);
    }, [defaultExpanded])

    const handleExpand = () => {
        setExpendId(chemId);
        setIsExpanded(true); // 按下後展開
    }; 
    
    const addBtnTitle = isExpanded ? t('button.join') : t('button.add');
    const addBtn = <button
        type="button"
        className="btn btn-purple d-block fs-5 mt-1"
        title={addBtnTitle}
        onClick={isExpanded ? handleAdd : handleExpand}
    >
        <i className="fas fa-flask fa-lg me-1"></i>
        {addBtnTitle}
    </button>

    return isExpanded ?
        (
            <div className="container">
                <div className="card">
                    <div className="card-body">
                        <div className="row">
                            <div className="col-12">
                                <InputMixConcen mixConcenTypeOptions={mixConcenTypeOptions} />
                                <div className="mt-2">
                                    <label className="d-flex align-items-center fw-bold">{t('table.title.chemical.substance_phase_state')}</label>
                                    {phaseStateOptions.map((item) => {
                                        const { configId, configName } = item;
                                        const id = `mix-phasestate-radio_${configId}`;
                                        return (
                                            <div className="form-check form-check-inline" key={configId}>
                                                <input
                                                    className="form-check-input"
                                                    type="radio"
                                                    name="phasestate"
                                                    id={id}
                                                    value={configId}
                                                    data-parsley-mincheck="1"
                                                    checked={selectedPhaseState === configId}
                                                    onChange={(e) => {
                                                        setPhaseState(e.target.value);
                                                    }}
                                                />
                                                <label className="form-check-label" htmlFor={id}>
                                                    {configName}
                                                </label>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                            <div className="col-md-6">
                                {addBtn}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        ) : addBtn;
};

export default AddSubstanceDiv;
