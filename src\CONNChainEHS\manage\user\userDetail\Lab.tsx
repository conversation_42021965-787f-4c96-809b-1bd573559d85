import BlockUi from "@availity/block-ui";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { LabAPI } from "../../../../api/labAPI";
import { RouAPI } from "../../../../api/rouAPI";
import BlockuiMsg from "../../../common/BlockuiMsg";
import NamesSplitFragment from "../../../common/NamesSplitFragment";
import { confirmMsg, errorMsg } from "../../../common/SwalMsg";
import { showSuccessToast, showWarnToast } from "../../../common/Toast";
import { ORG_SPLIT_FLAG, ROLE_LAB_MAX_LEVEL, ROLE_MIN_LEVEL, ROU_TYPE_LAB } from "../../../constant/constants";
import useLoginUser from "../../../hooks/useLoginUser";
import { EhsArea } from "../../../models/EhsArea";
import { EhsRole } from "../../../models/EhsRole";
import { EhsRou } from "../../../models/EhsRou";
import { isApiCallSuccess } from "../../../utils/resultUtil";
import { getBasicLoginUserInfo } from "../../../utils/authUtil";
import { isArrayEmpty } from "../../../utils/arrayUtil";
import NoDataRow from "../../../common/NoDataRow";
import { compareStrings } from "../../../utils/stringUtil";
import { getLabTextObj } from "../../../utils/langUtil";

interface LabData {
    unit: string;
    labNo: string;
    labName: string;
    labId: string;
    orgId: string;
}

export default function Lab(props: { isEditMode: boolean, userId: string, ownerLabList: EhsRou[], areaList: EhsArea[], roleList: EhsRole[], fetchUserRole: () => void }) {
    const { isEditMode, userId, ownerLabList, areaList, roleList, fetchUserRole } = props;
    const { t, i18n } = useTranslation();
    const { loginUser } = useLoginUser()
    const [keyword, setKeyword] = useState<string>("");
    const [selectedAreaId, setSelectedAreaId] = useState<string>("")
    const [selectedLabRole, setSelectedLabRole] = useState<string>("")
    const [searchLabDataList, setSearchLabDataList] = useState<Array<LabData>>([])
    const [hasSearch, setHasSearch] = useState<boolean>(false)
    const [loadingBlock, setLoadingBlock] = useState(false);
    const labRoleList = roleList.filter((role: EhsRole) => role.roleLevel >= ROLE_LAB_MAX_LEVEL && role.roleLevel < ROLE_MIN_LEVEL);
    const isCurrentUser = compareStrings(loginUser?.loginUserId ?? "", userId);
    const { tableTitleLabNo, tableTitleLabName } = getLabTextObj(loginUser, t);

    useEffect(() => {
        if (loginUser && hasSearch) {
            fetchSearchLabs()
        }
    }, [i18n.language])

    const fetchSearchLabs = () => {
        LabAPI.getLabList({
            ...getBasicLoginUserInfo(loginUser),
            areaId: selectedAreaId,
            keyword: keyword.trim()
        }).then(result => {
            if (isApiCallSuccess(result)) {
                if (keyword && result.results.length > 20) {
                    showWarnToast(t('message.search.result_over_limit'))
                    return;
                }
                setHasSearch(true);
                setSearchLabDataList(result.results.map((data: any) => {
                    return {
                        unit: data.orgName,
                        labNo: data.labNo,
                        labName: data.labName,
                        orgId: data.orgId,
                        labId: data.labId
                    }
                }))
            } else {
                errorMsg(result.message);
            }
        }).catch(err => {
            errorMsg(err);
        })
    }

    const joinLab = (labData: LabData) => {
        setLoadingBlock(true);
        const firstLabRoleId = labRoleList[0].roleId || "";
        if (!selectedLabRole) {
            setSelectedLabRole(firstLabRoleId)
        }
        if (!firstLabRoleId) {
            return;
        }
        RouAPI.addSingleRou({
            ...getBasicLoginUserInfo(loginUser),
            labId: labData.labId,
            orgId: labData.orgId,
            userId: userId!,
            roleId: selectedLabRole! || firstLabRoleId,
            rouType: ROU_TYPE_LAB
        }).then(result => {
            if (isApiCallSuccess(result)) {
                showSuccessToast(t('message.success'));
                fetchUserRole();
            } else {
                errorMsg(result.message);
            }
            setLoadingBlock(false);
        }).catch(err => {
            errorMsg(err);
            setLoadingBlock(false);
        })
    }
    return (
        <div className="tab-pane fade active show" id="user-tab-1">
            <StyledLab>
                <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
                    {/* 查詢實驗室 */}
                    {isEditMode && !isCurrentUser && <div className="card mb-3">
                        <div className="card-body">
                            <div className="row align-items-end">
                                <div className="col-md-3">
                                    <h5>{t('table.title.role.item')}</h5>
                                    <select className="form-select" data-parsley-required="true" value={selectedLabRole} onChange={(e) => { setSelectedLabRole(e.target.value) }}>
                                        {labRoleList.map((data: EhsRole) => <option key={'labrole_opt' + data.roleId} value={data.roleId}>{data.roleName}</option>)}
                                    </select>
                                </div>
                                <div className="col-md-3">
                                    <h5>{t('text.area.item')}</h5>
                                    <select className="form-select" data-parsley-required="true" value={selectedAreaId} onChange={(e) => { setSelectedAreaId(e.target.value) }}>
                                        <option value="">{t('text.all')}</option>
                                        {areaList.map((data: EhsArea) => <option key={'area_opt' + data.areaId} value={data.areaId}>{data.areaName}</option>)}
                                    </select>
                                </div>
                                <div className="col-md-3">
                                    <h5>{t('text.search.keyword')}</h5>
                                    <input value={keyword} type="text" className="form-control" onChange={e => setKeyword(e.target.value)} />
                                </div>
                                <div className="col-md-3">
                                    <button type="button" className="btn btn-primary" title={t('button.search.item')} onClick={fetchSearchLabs}>
                                        <i className="fas fa-magnifying-glass me-1"></i>{t('button.search.item')}
                                    </button>
                                </div>
                            </div>
                            <hr />
                            {/* 按下查詢後出現表格 */}
                            {hasSearch &&
                                <div className="table-responsive" style={{ maxHeight: '400px', overflowY: 'auto' }}>
                                    <table className="table table-hover text-center align-middle">
                                        <thead className="fs-4 fw-bold table-fixed-header table-info">
                                            <tr>
                                                <th>{t('table.title.org.item')}</th>
                                                <th>{tableTitleLabNo}</th>
                                                <th>{tableTitleLabName}</th>
                                                <th>{t('table.title.action')}</th>
                                            </tr>
                                        </thead>
                                        <tbody className="fs-5">
                                            {isArrayEmpty(searchLabDataList) ?
                                                <NoDataRow trClass="alert alert-danger text-center" />
                                                : searchLabDataList.map(data => (
                                                    <RowSearchLab key={'search_lab_' + data.labId} lab={data} onClickJoin={() => joinLab(data)} />
                                                ))}
                                        </tbody>
                                    </table>
                                </div>}
                        </div>
                    </div>}
                    {/* 所屬實驗室表格 */}
                    <table className="table table-hover text-center align-middle">
                        <thead className="fs-4 fw-bold table-bordered">
                            <tr>
                                <th className="text-start">{t('table.title.role.item')}</th>
                                <th className="text-start">{t('table.title.org.item')}</th>
                                <th className="text-start">{tableTitleLabName}</th>
                                {isEditMode && !isCurrentUser && <th>{t('table.title.action')}</th>}
                            </tr>
                        </thead>
                        <tbody className="text-center fs-5">
                            {ownerLabList.map(data => <RowOwnerLab key={'ownerlab_rou' + data.rouId} ownerLab={{ ...data }} isEditMode={isEditMode} isCurrentUser={isCurrentUser} fetchUserRole={fetchUserRole} setLoadingBlock={setLoadingBlock} />)}
                        </tbody>
                    </table>
                </BlockUi>
            </StyledLab>
        </div>
    );
}

const RowSearchLab = (props: {
    lab: LabData, onClickJoin: () => void,
}) => {
    const { t } = useTranslation();
    const { loginUser } = useLoginUser()
    const { lab, onClickJoin } = props;
    const { btnLabJoinText, tableTitleLabNo, tableTitleLabName } = getLabTextObj(loginUser, t);
    return (
        <tr>
            <td data-title={t('table.title.org.item')}>{lab.unit}</td>
            <td data-title={tableTitleLabNo}>{lab.labNo}</td>
            <td data-title={tableTitleLabName}>{lab.labName}</td>
            <td data-title={t('table.title.action')}>
                <button type="button" className="btn btn-purple btn-sm" title={btnLabJoinText} onClick={onClickJoin}>
                    <i className="fas fa-plus me-1" />{btnLabJoinText}
                </button>
            </td>
        </tr>
    );
}
const RowOwnerLab = (props: {
    ownerLab: EhsRou, isEditMode: boolean, fetchUserRole: () => void,
    setLoadingBlock: (block: boolean) => void,
    isCurrentUser?: boolean
}) => {
    const { t } = useTranslation();
    const { loginUser } = useLoginUser()
    const { tableTitleLabName } = getLabTextObj(loginUser, t);
    const { ownerLab, isEditMode, isCurrentUser, fetchUserRole, setLoadingBlock } = props;
    const clickDelete = () => {
        confirmMsg(t('message.confirm.delete'), t).then((value) => {
            if (value) {
                setLoadingBlock(true);
                RouAPI.delRou({
                    ...getBasicLoginUserInfo(loginUser)!,
                    rouId: ownerLab.rouId,
                    deleteBasicRou: true
                }).then(result => {
                    if (isApiCallSuccess(result)) {
                        fetchUserRole();
                        showSuccessToast(t('message.success'));
                    } else {
                        errorMsg(result.message);
                    }
                    setLoadingBlock(false);
                }).catch(err => {
                    errorMsg(err);
                    setLoadingBlock(false);
                })
            }
        })
    }

    return (
        <tr>
            <td className="text-start" data-title={t('table.title.role.item')}>{ownerLab.roleName}</td>
            <td className="text-start" data-title={t('table.title.org.item')}><NamesSplitFragment names={ownerLab.orgNames} separator={ORG_SPLIT_FLAG} /></td>
            <td className="text-start" data-title={tableTitleLabName}>{ownerLab.labName}</td>
            {isEditMode && !isCurrentUser && <td data-title={t('table.title.action')}>
                <button type="button" className="btn btn-danger fs-5" title={t('button.delete')} onClick={clickDelete}><i className="fas fa-trash-can me-1" />{t('button.delete')}</button>
            </td>}
        </tr>
    );
}

const StyledLab = styled.div`

    .table-fixed-header th {
    position: sticky;
    top: 0; 
    z-index: 100;
    }
 
  thead {
    background:rgb(251, 205, 165);
  }

  .fa-trash-can {
    cursor:pointer;
  }
   @media (max-width: 600px){
        label {
            width:200px;
        }
        .buttonPanel {
            margin-top:10px;
            display:flex;
            justify-content: flex-end;
        }
        thead {
            display:none;
        }
        tbody, td, tr {
            display:block;
            background: #fff !important;
            box-shadow: inset 0 0 0 9999px white;

        }
        tr {
            border: 1px solid #ccc;
            margin-bottom: 10px;
            background: #fff !important;
        }
        td {
            min-height:30px;
            background: #fff!important;
            position:relative;
            padding-left: 100px;
            text-align:left;
        }
        td::before {
            content: attr(data-title);
            position: absolute;
            top: 6px;
            left: 6px;
            width: 30%;
            padding-right: 10px;
            white-space: nowrap;
            text-align: left;
            font-weight: bold;
            color: #1a1a1a;
        }
    
    }
`