import { AreaAPI } from "api/areaAPI";
import { FileAPI } from "api/fileAPI";
import InputDate from "ehs/common/input/InputDate";
import InputFileUpload from "ehs/common/InputFileUpload";
import LoadingSpinner from "ehs/common/LoadingSpinner";
import { errorMsg } from "ehs/common/SwalMsg";
import { showWarnToast } from "ehs/common/Toast";
import { CONFIG_TYPE_CHEM_AREA_APPROVE_TYPE, CONFIG_TYPE_CITY, FILE_TYPE_AREA_ALL_BUILDING_IMAGE, FILE_TYPE_AREA_CHEMICAL_APPROVAL_INFO, LANGUAGE_MAPPING_TYPE_AREA, UPLOAD_ACCEPT_TYPE_IMAGE, UPLOAD_ACCEPT_TYPE_PDF } from "ehs/constant/constants";
import { ActionMode } from "ehs/enums/ActionMode";
import useLoginUser from "ehs/hooks/useLoginUser";
import useServerNowDate from "ehs/hooks/useServerNowDate";
import { EhsArea, initEhsArea } from "ehs/models/EhsArea";
import { EhsAreaChemicalApproveInfo, initEhsAreaChemicalApproveInfo } from "ehs/models/EhsAreaChemicalApproveInfo";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsFile, initEhsFile } from "ehs/models/EhsFile";
import { EhsLanguage, initEhsLanguage } from "ehs/models/EhsLanguage";
import { getBasicLoginUserInfo } from "ehs/utils/authUtil";
import { getApprovalNoPre } from "ehs/utils/chemicalUtil";
import { isChineseLang, sortLanguagesListByCurrent } from "ehs/utils/langUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { formatAlphanumeric, formatPhoneNum, getFormatDateSlash } from "ehs/utils/stringUtil";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
const initReqShowMsgValues = {
    areaNo: null as boolean | null,
    areaName: null as boolean | null,
    areaCity: null as boolean | null,
    areaAddress: null as boolean | null,
};

export default function AreaModify(props: {
    onClose: () => void;
    onActionSuccess: () => void;
    setLoadingBlock: (block: boolean) => void;
    mode: ActionMode | null;
    modifyData: EhsArea,
    configMap: { [key: string]: EhsConfigParam[] },
}) {
    const { onClose, onActionSuccess, setLoadingBlock, mode, modifyData, configMap } = props;
    const navigate = useNavigate();
    const { loginUser } = useLoginUser();
    const { t, i18n } = useTranslation();
    const [nowDate] = useServerNowDate(loginUser);
    const [editValues, setEditValues] = useState(initEhsArea);
    const [reqShowMsgValues, setReqShowMsgValues] = useState(initReqShowMsgValues);
    const [isLoading, setIsLoading] = useState(false);
    const [showOtherLangName, setShowOtherLangName] = useState(false);
    const [areaNameList, setAreaNameList] = useState<EhsLanguage[]>([]);
    const [delFileIdList, setDelFileIdList] = useState<string[]>([]);
    const [chemApproveInfoMap, setChemApproveInfoMap] = useState<{ [type: string]: EhsAreaChemicalApproveInfo }>({});
    const [chemApproveInfoFile, setChemApproveInfoFile] = useState<{ [type: string]: EhsFile | null }>({});
    const [chemApproveInfoFileContent, setChemApproveInfoFileContent] = useState<{ [type: string]: File | null }>({});
    const [allBuildingImage, setAllBuildingImage] = useState<EhsFile | null>(null);
    const [allBuildingImageContent, setAllBuildingImageContent] = useState<File | null>(null);
    const [areaNoDuplicated, setAreaNoDuplicated] = useState<boolean>(false);
    const chemApprovalTypeList = configMap[CONFIG_TYPE_CHEM_AREA_APPROVE_TYPE] || [];
    const cityList = configMap[CONFIG_TYPE_CITY] || [];
    const cityMap: { [key: string]: EhsConfigParam } = cityList.reduce((acc, city) => {
        acc[city.configId] = city;
        return acc;
    }, {} as { [key: string]: EhsConfigParam });
    const cityName = cityMap[editValues.areaCity]?.configName || "";
    const isAddMode = mode === ActionMode.ADD;
    const actBtnColor = isAddMode ? "purple" : "warning";
    const actTitle = mode === null ? "" : isAddMode ? t("text.add") : t("text.edit");
    const areaData = isAddMode ? initEhsArea : modifyData;
    const { areaId } = areaData;
    const nameTitle = t("text.name");
    const sortedLangList = loginUser ? sortLanguagesListByCurrent(loginUser!.langType) : [];
    const hasMultiLang = sortedLangList.length > 1;
    let firstLangValue = "";
    const approvalFileInfo = {
        ...initEhsFile,
        fileType: FILE_TYPE_AREA_CHEMICAL_APPROVAL_INFO,
        fileMappingId: areaId,
    }
    const isChinese = isChineseLang(i18n.language)
    const langSpace = isChinese ? '' : ' ';
    const isModifyLoading = isLoading && !isAddMode;

    useEffect(() => {
        setEditValues(areaData);
        setReqShowMsgValues(initReqShowMsgValues);
        setShowOtherLangName(false);
        setAreaNameList([]);
        setChemApproveInfoMap({});
        setChemApproveInfoFile({});
    }, [isAddMode, modifyData]);

    useEffect(() => {
        if (loginUser && mode && !isAddMode && areaId) {
            setIsLoading(true);

            Promise.all([
                AreaAPI.getAreaDetail({
                    ...getBasicLoginUserInfo(loginUser)!,
                    areaId: areaId,
                }),
                AreaAPI.getAreaChemicalApproveInfoByAreaId({
                    ...getBasicLoginUserInfo(loginUser),
                    areaId: areaId
                }),
                FileAPI.getFileList({
                    ...getBasicLoginUserInfo(loginUser),
                    fileTypeList: [FILE_TYPE_AREA_CHEMICAL_APPROVAL_INFO, FILE_TYPE_AREA_ALL_BUILDING_IMAGE],
                    fileMappingIdList: [areaId],
                })
            ]).then(([areaResult, chemResult, fileResult]) => {
                if (isApiCallSuccess(areaResult)) {
                    setAreaNameList(areaResult.results.areaNameList);
                } else {
                    errorMsg(areaResult.message);
                }

                if (isApiCallSuccess(chemResult)) {
                    const approveInfoMap = chemResult.results.reduce((map: { [type: string]: EhsAreaChemicalApproveInfo }, item: EhsAreaChemicalApproveInfo) => {
                        map[item.approvalType] = item;
                        return map;
                    }, {});
                    setChemApproveInfoMap(approveInfoMap);
                }

                if (isApiCallSuccess(fileResult)) {
                    const resultFileList: EhsFile[] = fileResult.results;
                    const fileInfos: Record<string, EhsFile> = {};
                    resultFileList.forEach((file: EhsFile) => {
                        if (file.fileType === FILE_TYPE_AREA_CHEMICAL_APPROVAL_INFO) {
                            fileInfos[file.fileSubtype] = file;
                        } else if (file.fileType === FILE_TYPE_AREA_ALL_BUILDING_IMAGE) {
                            setAllBuildingImage(file);
                        }
                    });
                    setChemApproveInfoFile(fileInfos);
                }
            }).catch((error) => {
                errorMsg(t('message.system_error'));
            }).finally(() => {
                setIsLoading(false);
            });
        }
    }, [loginUser, areaId, mode]);

    useEffect(() => {
        if (!mode) {
            setAllBuildingImage(null);
        }
    }, [mode]);

    const checkAreaNoDuplicate = () => {
        if (editValues.areaNo && loginUser) {
            AreaAPI.checkAreaNoDuplicate({
                ...getBasicLoginUserInfo(loginUser),
                areaId: areaId,
                areaNo: editValues.areaNo
            }).then((result) => {
                if (isApiCallSuccess(result)) {
                    const isDuplicated = result.results || false;
                    setAreaNoDuplicated(isDuplicated);
                }
            });
        }
    };

    const onAction = () => {
        if (!loginUser) {
            return;
        }

        const { areaNo, areaName, areaCity, areaAddress } = editValues;
        const showMsgValues = {
            areaNo: !areaNo,
            areaName: !areaName,
            areaCity: !areaCity,
            areaAddress: !areaAddress,
        };

        setReqShowMsgValues(showMsgValues);

        if (Object.values(showMsgValues).some(value => value) || areaNoDuplicated) {
            return;
        }

        const hasNullInApproveInfo = Object.values(chemApproveInfoMap).some(info => !info.approvalNo || !info.approvalExpiryDate);
        if (hasNullInApproveInfo) {
            showWarnToast(t('message.area.chem_approval_info_enter'));
            return;
        }
        // 檢查 chemApproveInfoFileContent 中是否有 null 值 
        const hasNullInFiles = Object.keys(chemApproveInfoMap).some(key => !chemApproveInfoFile[key]);
        if (hasNullInFiles) {
            showWarnToast(t('message.area.chem_approval_file_upload'));
            return;
        }

        setLoadingBlock(true);
        let newAreaNameList: EhsLanguage[] = [...areaNameList].map(area => ({
            ...area,
            langValue: area.langValue.trim()
        }));
        sortedLangList.forEach((item, idx) => {
            const isFirst = idx === 0;
            if (isFirst) {
                const firstLang = newAreaNameList.find(lang => lang.langType === item.code);
                firstLangValue = firstLang ? firstLang.langValue : ""; // 確保 firstLang 存在，避免錯誤
            } else {
                const exists = newAreaNameList.some((lang) => lang.langType === item.code);
                if (exists) {
                    // 如果存在，且 langValue 為空，則將其設置為 firstLangValue
                    const existingLangIndex = newAreaNameList.findIndex(lang => lang.langType === item.code);
                    if (existingLangIndex !== -1 && !newAreaNameList[existingLangIndex].langValue) {
                        newAreaNameList[existingLangIndex].langValue = firstLangValue;
                    }

                } else {
                    newAreaNameList = [
                        ...newAreaNameList,
                        {
                            ...initEhsLanguage,
                            langType: item.code,
                            langValue: firstLangValue,
                            mappingType: LANGUAGE_MAPPING_TYPE_AREA
                        }
                    ];
                }
            }
        });

        const approveInfos = Object.values(chemApproveInfoMap).filter((info): info is EhsAreaChemicalApproveInfo => info !== null);
        const files = Object.values(chemApproveInfoFileContent).filter((file): file is File => file !== null);
        if (isAddMode) {
            editValues.areaStatus = 1;
            AreaAPI.addArea({
                ...editValues,
                ...getBasicLoginUserInfo(loginUser),
                allBuildingImage: allBuildingImageContent,
                areaNameList: newAreaNameList,
                approveTypeList: Object.keys(chemApproveInfoMap),
                approveInfoList: approveInfos,
                approveInfoFileList: files,
            }).then((result) => {
                if (isApiCallSuccess(result)) {
                    onActionSuccess();
                } else {
                    errorMsg(result.message)
                }
                setLoadingBlock(false);
            }).catch((err) => {
                errorMsg(t('message.system_error'));
                setLoadingBlock(false);
            });

        } else {
            AreaAPI.editArea({
                ...editValues,
                ...getBasicLoginUserInfo(loginUser),
                allBuildingImage: allBuildingImageContent,
                areaNameList: newAreaNameList,
                approveTypeList: Object.keys(chemApproveInfoMap),
                approveInfoList: approveInfos,
                approveFileTypeList: Object.keys(chemApproveInfoFileContent),
                approveInfoFileList: files,
                delFileIdList
            }).then((result) => {
                if (isApiCallSuccess(result)) {
                    onActionSuccess();
                } else {
                    errorMsg(result.message)
                }
                setLoadingBlock(false);
            }).catch((err) => {
                errorMsg(t('message.system_error'));
                setLoadingBlock(false);
            });
        }
    };

    const handleApprovalTypeChange = (configId: string, checked: boolean) => {
        setChemApproveInfoMap(prevMap => {
            const newMap = { ...prevMap };
            if (checked) {
                newMap[configId] = { ...initEhsAreaChemicalApproveInfo, ...prevMap[configId], areaId };
            } else {
                // Remove the item from the map
                delete newMap[configId];
            }
            return newMap;
        });
        setChemApproveInfoFile(prev => {
            const newMap = { ...prev };
            if (!checked) {
                delete newMap[configId];
            }
            return newMap;
        })
    };

    const clickClose = () => {
        onClose();
        setDelFileIdList([]);
    }

    return (
        <StyledAreaModify>
            <div className="addArea">
                <div className="addArea-header">
                    <h4 className="modal-title">{actTitle}</h4>
                    <button
                        type="button"
                        className="btn-close"
                        onClick={clickClose}
                    ></button>
                </div>
                {isModifyLoading ? (
                    <LoadingSpinner />
                ) : (
                    <div className="addArea-body">
                        <div className="d-flex align-items-start flex-wrap">
                            <div className="row mb-4 col-lg-6 col-12 justify-content-center mt-3 ps-3">
                                <div className="col-md-10 mb-3">
                                    <h3>{t('text.area_info')}</h3>
                                </div>
                                <div className="col-md-10 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.no')}：<span className="text-danger">*</span>
                                    </label>
                                    <input
                                        value={editValues.areaNo}
                                        type="text"
                                        className={`form-control form-control-lg ${(reqShowMsgValues.areaNo || areaNoDuplicated) && 'is-invalid'}`}
                                        data-parsley-required="true"
                                        onChange={(e) => {
                                            setEditValues({
                                                ...editValues,
                                                areaNo: e.target.value,
                                            });
                                            setReqShowMsgValues({ ...reqShowMsgValues, areaNo: !e.target.value });
                                            setAreaNoDuplicated(false);
                                        }}
                                        onBlur={checkAreaNoDuplicate}
                                    />
                                    {reqShowMsgValues.areaNo &&
                                        <div className="invalid-feedback">{t("message.enter")}</div>
                                    }
                                    {areaNoDuplicated &&
                                        <div className="invalid-feedback">{t("message.area.number_duplicate")}</div>
                                    }
                                </div>
                                {loginUser && sortedLangList.map((item, idx) => {
                                    const isFirst = idx === 0;
                                    const areaNameVal = areaNameList.find((lang) => lang.langType === item.code)?.langValue || "";
                                    return (
                                        <div className={`col-md-10 mb-3 ${!isFirst && !showOtherLangName && 'd-none'}`} key={'areaname' + item.code}>
                                            <label className="fw-bold mb-1">
                                                {nameTitle}：{isFirst && <span className="text-danger me-1">*</span>} {hasMultiLang && item.language}
                                            </label>
                                            <input
                                                value={areaNameVal}
                                                type="text"
                                                className={`form-control form-control-lg ${isFirst && reqShowMsgValues.areaName && 'is-invalid'}`}
                                                data-parsley-required="true"
                                                onChange={(e) => {
                                                    const newVal = e.target.value;
                                                    if (isFirst) {
                                                        setEditValues({
                                                            ...editValues,
                                                            areaName: newVal,
                                                        });
                                                        setReqShowMsgValues({ ...reqShowMsgValues, areaName: !newVal })
                                                    }
                                                    setAreaNameList((prevList) => {
                                                        const index = prevList.findIndex((lang) => lang.langType === item.code);
                                                        if (index !== -1) {
                                                            // 找到匹配的項目，更新其 langValue
                                                            return prevList.map((lang, idx) =>
                                                                idx === index ? { ...lang, langValue: newVal } : lang
                                                            );
                                                        } else {
                                                            // 未找到匹配的項目，新增一個新的項目
                                                            return [
                                                                ...prevList,
                                                                {
                                                                    ...initEhsLanguage,
                                                                    langType: item.code,
                                                                    langValue: newVal,
                                                                    mappingType: LANGUAGE_MAPPING_TYPE_AREA
                                                                }
                                                            ];
                                                        }
                                                    });
                                                }}
                                            />
                                            {isFirst && reqShowMsgValues.areaName &&
                                                <div className="invalid-feedback">{t("message.enter")}</div>
                                            }
                                            {isFirst && hasMultiLang && <button type="button" className="btn btn-primary mt-2" onClick={() => {
                                                setShowOtherLangName(!showOtherLangName);
                                            }}>{t('button.enter_other_lang')} {nameTitle}</button>}
                                        </div>
                                    )
                                })}
                                <div className="col-md-10 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.address')}：<span className="text-danger">*</span>
                                    </label>
                                    <div className="d-flex flex-wrap mb-1">
                                        <div className="flex-grow-0 me-1">
                                            <select className={`form-select form-select-lg w-auto align-self-start ${reqShowMsgValues.areaCity && 'is-invalid'}`}
                                                value={editValues.areaCity}
                                                onChange={(e) => {
                                                    setEditValues({
                                                        ...editValues,
                                                        areaCity: e.target.value,
                                                    });
                                                    setReqShowMsgValues({ ...reqShowMsgValues, areaCity: !e.target.value })
                                                }}>
                                                <option value="" title={t('message.select')}>{t('message.select')}</option>
                                                {cityList.map((item) => {
                                                    const { configId, configName } = item;
                                                    return <option key={configId} value={configId} title={configName}>{configName}</option>
                                                })}
                                            </select>
                                            {reqShowMsgValues.areaCity &&
                                                <div className="invalid-feedback">{t("message.select")}</div>
                                            }
                                        </div>
                                        <div className="flex-grow-1">
                                            <input
                                                value={editValues.areaAddress}
                                                type="text"
                                                className={`form-control form-control-lg ${reqShowMsgValues.areaAddress && 'is-invalid'}`}
                                                data-parsley-required="true"
                                                onChange={(e) => {
                                                    setEditValues({
                                                        ...editValues,
                                                        areaAddress: e.target.value,
                                                    });
                                                    setReqShowMsgValues({ ...reqShowMsgValues, areaAddress: !e.target.value })
                                                }}
                                            />
                                            {reqShowMsgValues.areaAddress &&
                                                <div className="invalid-feedback">{t("message.enter")}</div>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div className="col-md-10 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.phone')}：
                                    </label>
                                    <input
                                        value={editValues.areaPhone || ""}
                                        type="text"
                                        className="form-control form-control-lg"
                                        data-parsley-required="true"
                                        placeholder={t('message.format.phone')}
                                        title={t('message.format.phone')}
                                        onChange={(e) => {
                                            setEditValues({
                                                ...editValues,
                                                areaPhone: formatPhoneNum(e.target.value),
                                            });
                                        }}
                                    />
                                </div>
                                <div className="col-md-10 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.area.ctrl_no')}：
                                    </label>
                                    <input
                                        value={editValues.areaCno || ""}
                                        type="text"
                                        className="form-control form-control-lg"
                                        data-parsley-required="true"
                                        onChange={(e) => {
                                            setEditValues({
                                                ...editValues,
                                                areaCno: e.target.value,
                                            });
                                        }}
                                    />
                                </div>
                                <div className="col-md-10 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.area.use')}：
                                    </label>
                                    <input
                                        value={editValues.areaUse || ""}
                                        type="text"
                                        className="form-control form-control-lg"
                                        data-parsley-required="true"
                                        onChange={(e) => {
                                            setEditValues({
                                                ...editValues,
                                                areaUse: e.target.value,
                                            });
                                        }}
                                    />
                                </div>
                                <div className="col-md-10 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.area.resp_name')}：
                                    </label>
                                    <input
                                        value={editValues.areaRespName || ""}
                                        type="text"
                                        className="form-control form-control-lg"
                                        data-parsley-required="true"
                                        onChange={(e) => {
                                            setEditValues({
                                                ...editValues,
                                                areaRespName: e.target.value,
                                            });
                                        }}
                                    />
                                </div>
                                <div className="col-md-10 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.area.image')}：
                                        <InputFileUpload inputId={'allBuildingImage'}
                                            defaultFile={allBuildingImage || undefined}
                                            defaultFileInfo={allBuildingImage || undefined}
                                            onFileChange={(file) => {
                                                const newAreaImage = file ? { ...initEhsFile, ...approvalFileInfo, fileName: file ? file.name : "", fileMappingId: areaId } : null;
                                                if (file === null && allBuildingImage) {
                                                    setDelFileIdList([...delFileIdList, allBuildingImage.fileId]);
                                                }
                                                setAllBuildingImage(newAreaImage);
                                                setAllBuildingImageContent(file);
                                            }}
                                            acceptedFileTypes={UPLOAD_ACCEPT_TYPE_IMAGE} />
                                    </label>
                                </div>
                            </div>

                            <div className="row mb-4 col-lg-6 col-12 justify-content-center mt-3 ps-3">
                                <div className="col-md-10 mb-3">
                                    <h3>{t('text.chemical.area_cert_info')}</h3>
                                </div>
                                <div className="col-md-10 mb-3 chem-info-div">
                                    {chemApprovalTypeList.map((item) => {
                                        const { configId, configName } = item
                                        const approvalNoPre = getApprovalNoPre(cityName, configName, isChinese, t)
                                        const ApproveInfo = chemApproveInfoMap[configId];
                                        const approvalFile = chemApproveInfoFile[configId];
                                        const { approvalNo, approvalExpiryDate } = ApproveInfo || {};
                                        const isChecked = !!ApproveInfo;
                                        const id = 'approvalType' + configId
                                        return (<React.Fragment key={'approvalType-div' + configId}>
                                            <div className="form-check mb-3 me-3">
                                                <input className="form-check-input" type="checkbox" id={id}
                                                    checked={isChecked} onChange={(e) => handleApprovalTypeChange(configId, e.target.checked)} />
                                                <label className="form-check-label" htmlFor={id}>{configName}</label>
                                            </div>
                                            {isChecked && <div className="border border-info p-3">
                                                <div className="col-md-12 mb-2 row">
                                                    {/* <!-- 核可證號 --> */}
                                                    {<div className="col-xl-6 col-lg-12 mb-2">
                                                        <label className="h5">{t('text.chemical.certification_no')}<span className="text-danger">*</span></label><br />
                                                        <div className="d-flex align-items-center">
                                                            <label className="h5">{approvalNoPre}</label>
                                                            <input type="text" className="form-control item-width-30 mx-1" data-parsley-required="true" value={approvalNo}
                                                                onChange={(e) => {
                                                                    setChemApproveInfoMap({
                                                                        ...chemApproveInfoMap,
                                                                        [configId]: {
                                                                            ...chemApproveInfoMap[configId],
                                                                            approvalNo: formatAlphanumeric(e.target.value),
                                                                            approvalType: configId
                                                                        }
                                                                    })
                                                                }} />
                                                            <label className="h5">{langSpace}{t('text.area.chem_approval_no_suffix')}</label>
                                                        </div>
                                                    </div>}
                                                    {/* <!-- 核可證號到期日期 --> */}
                                                    {<div className="col-xl-6 col-lg-12">
                                                        <label className="h5">{t('text.chemical.certification_exp_date')}<span className="text-danger">*</span></label>
                                                        <InputDate defaultValue={getFormatDateSlash(approvalExpiryDate)} minDate={getFormatDateSlash(nowDate)} onChange={(date) => {
                                                            setChemApproveInfoMap({
                                                                ...chemApproveInfoMap,
                                                                [configId]: {
                                                                    ...chemApproveInfoMap[configId],
                                                                    approvalExpiryDate: date,
                                                                    approvalType: configId
                                                                }
                                                            })
                                                        }} />
                                                    </div>}
                                                </div>
                                                <div className="col-md-12 mb-3 ps-xl-2 file-upload-row">
                                                    <label className="h5">{t('text.chemical.certification_file')}<span className="text-danger">*</span></label>
                                                    <InputFileUpload inputId={'approval-file' + configId}
                                                        defaultFile={approvalFile || undefined}
                                                        defaultFileInfo={approvalFile || undefined}
                                                        onFileChange={(file) => {
                                                            const newInfoFile = file ? {
                                                                ...initEhsFile, ...approvalFileInfo,
                                                                fileName: file ? file.name : "",
                                                                fileSubtype: configId,
                                                                fileMappingId: areaId,
                                                            } : null;
                                                            if (file === null) {
                                                                const fileId = approvalFile?.fileId;
                                                                if (fileId) {
                                                                    setDelFileIdList([...delFileIdList, fileId]);
                                                                }
                                                            }

                                                            setChemApproveInfoFile({
                                                                ...chemApproveInfoFile,
                                                                [configId]: newInfoFile
                                                            })
                                                            setChemApproveInfoFileContent({
                                                                ...chemApproveInfoFileContent,
                                                                [configId]: file
                                                            });
                                                        }}
                                                        acceptedFileTypes={UPLOAD_ACCEPT_TYPE_PDF} />
                                                </div>
                                            </div>}
                                            <hr />
                                        </React.Fragment>);
                                    })}
                                </div>
                            </div>
                        </div>
                    </div>
                )}
                {actTitle && <div className="addArea-footer">
                    <div className="btn btn-white" onClick={clickClose}>
                        <i className="fas fa-close" /> {t("button.close")}
                    </div>
                    {!isModifyLoading && <div className={`btn btn-${actBtnColor} ms-3`} onClick={onAction}>
                        <i className="fas fa-cubes" /> {actTitle}
                    </div>}
                </div>}
            </div>
        </StyledAreaModify >
    );
}

const StyledAreaModify = styled.div`
  background: white; 
  width: 1300px;

  .download-button:hover {
    text-decoration: underline; /* 滑鼠懸停時顯示底線 */
    }

  .chem-info-div { 
    overflow-y: auto;
    height: 100%;
  }
  
  label{
    user-select: none;
  }

  .addArea-header {
    background:rgb(251, 205, 165);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  } 

    .addArea-body {
        padding-top: 15px;
        
        .row {
            width: 50%;
        }

        .file-upload-row {
            width: 100% !important; /* 覆蓋 .row 的 50% 寬度設定 */
        }
        
        @media (max-width: 1024px) {
            .row {
                width: 100%;
            }
        }
    }

  .addArea-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-purple {
      margin-left: 10px;
    }
  } 
    
    @media (max-width: 1024px) {
        width: 100%;  // 在小螢幕時使用 100% 寬度
        max-width: 100%;
        height: 100%;
        
        .addArea-body {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 20px; // 添加底部間距，避免內容被 footer 遮擋
            max-height: 700px;
        }
        
        .addArea-footer {
            position: sticky;
            bottom: 0;
            background: white;
            padding: 15px;
            border-top: 1px solid #ced4da;
            display: flex;
            justify-content: center;
        }
    }
    
    @media (min-width: 1025px) {
        .border.border-info {
            display: flex;
            flex-wrap: wrap;
            
            .col-md-12.mb-2.row {
                width: 100%;
                margin: 0;
                display: flex;
                flex-wrap: wrap;
            }
            
            .col-xl-6 {
                padding: 0 10px;
            }
        }
    }
`;
