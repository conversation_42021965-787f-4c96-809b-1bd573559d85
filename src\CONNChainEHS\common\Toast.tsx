// Toast.js
import { toast } from 'react-toastify';

/**
 * 警示訊息
 * @param {*} message 
 * @param {*} options 
 */
export const showWarnToast = (message: React.ReactNode | string, options: any = {}) => {
    const defaultOptions = {
        position: "top-center",
        autoClose: 5000,
        closeOnClick: false,
        hideProgressBar: true,
    };

    toast.warning(message, {
        ...defaultOptions,
        ...options,
    });
};

/**
 * 警示訊息 避免重複顯示
 */
export const showWarnToastWithId = (message: string, toastId: string) => {
    showWarnToast(message, { toastId });
}

/**
 * 成功訊息
 */
export const showSuccessToast = (message: string, options: any = {}) => {
    const defaultOptions = {
        position: "top-center",
        autoClose: 5000,
        hideProgressBar: true,
    };

    toast.success(message, {
        ...defaultOptions,
        ...options,
    });
};