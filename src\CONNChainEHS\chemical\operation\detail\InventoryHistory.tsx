import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { EhsChemicalInventory } from "../../../models/EhsChemicalInventory";
import { EhsConfigParam } from "../../../models/EhsConfigParam";
import InventoryItem from "./InventoryItem";
import { EhsFile } from "ehs/models/EhsFile";


function InventoryHistory(props: {
  onClose: () => void;
  historyList: EhsChemicalInventory[];
  setHistoryList: React.Dispatch<React.SetStateAction<EhsChemicalInventory[]>>;
  nowDate: Date;// 現在日期
  stopOperDate: Date; // 停止操作日期
  storageLocationConfigs: EhsConfigParam[];
  configMap: { [configId: string]: EhsConfigParam };
  sdsFileMap: { [inventoryId: string]: EhsFile };
  operItemConfigs: EhsConfigParam[];
}) {
  const { onClose, historyList, setHistoryList, nowDate, stopOperDate, storageLocationConfigs, configMap
    , sdsFileMap, operItemConfigs } = props;
  const { t } = useTranslation();
  return (
    <StlyedInventoryHistory >
      <div className="modifyLabUser">
        <div className="modal-header">
          <h4 className="modal-title">{t('func.chemical.inventory_history')}</h4>
          <button type="button" className="btn-close" aria-hidden="true" title={t("button.close")} onClick={onClose}></button>
        </div>
        <div className="modal-body">
          <div className="accordion mt-3 px-3" id="accordion">
            {historyList.map((item, idx) => {
              const { inventoryId, sdsFileId } = item;
              return (<InventoryItem key={inventoryId + '_' + idx} inventory={item} historyList={historyList} setHistoryList={setHistoryList}
                nowDate={nowDate} defStopOperDate={stopOperDate} sdsFile={sdsFileMap[sdsFileId]}
                idx={idx} operItemConfigs={operItemConfigs} configMap={configMap} storageLocationConfigs={storageLocationConfigs} isHistory={true} />);
            })}
          </div>
        </div>
        <div className="modal-footer">
          <button className="btn btn-white" aria-hidden="true" onClick={onClose} title={t("button.close")}>
            <i className="fas fa-times me-1" />
            {t("button.close")}
          </button>
        </div>
      </div>
    </StlyedInventoryHistory>
  );
}


const StlyedInventoryHistory = styled.div`
  font-size: 1.2em;
  background: white;
  width: 1400px;

  .accordion-button{
    cursor:default;
  }

  .modal-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  .modal-body {
    padding-top: 15px;
    .table-responsive{
      max-height: 650px; /* 设置容器的最大高度 */
      overflow-y: auto;
    }
    thead {
      /* 将标题行固定在顶部 */
      position: sticky;
      top: 0;
      background-color: white; /* 使标题行在滚动时可见 */
      z-index: 2; /* 设置标题行的层级高于其他内容 */
    } 
  }
  .modal-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-purple {
      margin-left: 10px;
    }
  }

  
  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 100px;
        text-align:left;
        min-height:39.5px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          white-space: nowrap;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
`;

export default InventoryHistory;
