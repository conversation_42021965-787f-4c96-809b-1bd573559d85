import React from 'react';
import styled from 'styled-components';
import { PageInfo } from '../models/PageInfo';
import { useTranslation } from 'react-i18next';

interface PaginationProps<T> {
    pageInfo: PageInfo;
    setCondition: React.Dispatch<React.SetStateAction<T>>;
    condition?: T;
    specialCondition?: {
        orgList?: {
            orglvId: string;
        };
        tabPagination?: {
            tabId: string;
            updateTabPage: (tabId: string, page: number) => void;
        };
    };
}

const Pagination: React.FC<PaginationProps<any>> = ({ pageInfo, setCondition, condition, specialCondition }) => {
    const { t } = useTranslation();
    const { currentPage = 1, pageSize = 50, totalRows = 0, totalPages = 0 } = pageInfo || {};

    const getPageEntryInfo = () => {
        let firstNumber = (currentPage - 1) * pageSize ? ((currentPage - 1) * pageSize + 1) : 1;
        let finalNumber = ((firstNumber + pageSize - 1) > totalRows) ? totalRows : (firstNumber + pageSize - 1);
        return t('table.pagination.entries_info', { start: firstNumber, end: finalNumber, total: totalRows });
    }
    const getPageInfo = () => {
        return t('table.pagination.page_info', { currentPage, totalPages });
    }

    // 處理頁碼變更的通用函數
    const handlePageChange = (newPage: number) => {
        // 處理分頁籤分頁
        if (specialCondition?.tabPagination) {
            // 先更新外部的 conditionMap
            if (condition) {
                setCondition((prevCondition: any) => {
                    // 如果 condition 是一個映射，則更新特定分頁籤的條件
                    if (typeof prevCondition === 'object' && prevCondition !== null) {
                        return {
                            ...prevCondition,
                            [specialCondition.tabPagination!.tabId]: {
                                ...prevCondition[specialCondition.tabPagination!.tabId],
                                currentPage: newPage
                            }
                        };
                    }
                    // 否則，更新一般條件
                    return {
                        ...prevCondition,
                        currentPage: newPage
                    };
                });
            }

            // 然後調用 updateTabPage
            specialCondition.tabPagination.updateTabPage(
                specialCondition.tabPagination.tabId,
                newPage
            );
            return;
        }

        // 處理組織列表分頁 (原有邏輯)
        if (specialCondition?.orgList) {
            if (condition) {
                const newCondition = condition[specialCondition.orgList.orglvId];
                if (newCondition) {
                    newCondition.currentPage = newPage;
                    setCondition((prevCondition: any) => ({
                        ...prevCondition,
                        [specialCondition.orgList!.orglvId]: newCondition,
                    }));
                }
            }
            return;
        }

        // 一般分頁邏輯
        setCondition((prevCondition: any) => ({
            ...prevCondition,
            currentPage: newPage,
        }));
    };

    // 生成頁碼選項
    const generatePageOptions = () => {
        const pageOptions = [];
        for (let i = 1; i <= totalPages; i++) {
            pageOptions.push(
                <option key={i} value={i}>
                    {i}
                </option>
            );
        }
        return pageOptions;
    };

    const renderPageBtns = () => {
        let isFirstPage = currentPage === 1;
        let isLastPage = currentPage === totalPages;

        return (
            <ul className="pagination">
                <li className={"paginate_button page-item previous" + (isFirstPage ? " disabled" : "")} id="data-table-default_previous" onClick={() => {
                    if (isFirstPage) {
                        return;
                    }
                    handlePageChange(currentPage - 1);
                }}>
                    <button aria-controls="data-table-default" data-dt-idx="0" className="page-link">
                        {t('table.pagination.previous_page')}
                    </button>
                </li>
                <li className="paginate_button page-item active">
                    <select
                        className="page-select page-link ms-1"
                        value={currentPage}
                        onChange={(e) => handlePageChange(Number(e.target.value))}
                        aria-controls="data-table-default"
                    >
                        {generatePageOptions()}
                    </select>
                </li>
                <li className={"paginate_button page-item next" + (isLastPage && totalPages > 0 ? " disabled" : "")} id="data-table-default_next" onClick={() => {
                    if (isLastPage && totalPages > 0) {
                        return;
                    }
                    handlePageChange(currentPage + 1);
                }}>
                    <button aria-controls="data-table-default" data-dt-idx="2" className="page-link">
                        {t('table.pagination.next_page')}
                    </button>
                </li>
            </ul>
        )
    }

    return (
        <StyledPagination>
            <div className="row bottomFunctionRow">
                <div className="col-sm-12 col-md-5">
                    <div className="dataTables_info" role="status" aria-live="polite">
                        {getPageInfo()}
                        <br />
                        {getPageEntryInfo()}
                    </div>
                </div>
                <div className="col-sm-12 col-md-7">
                    <div className="dataTables_paginate paging_simple_numbers">
                        {renderPageBtns()}
                    </div>
                </div>
            </div>
        </StyledPagination>
    );
};

const StyledPagination = styled.div`
  .bottomFunctionRow {
    .pagination {
      justify-content:right;
    }
  }
  .page-item.active:hover {
    cursor: default;
  }
  .page-select {
    border: none;
    border-radius: 0.25rem;
    padding: 0.375rem 0.75rem;
    height: 100%;
    min-width: 3rem;
    text-align: center;
    cursor: pointer;
    appearance: auto;
    color: white;
    font-weight: 400;
    vertical-align: middle;
  }  

  .page-select option {
    background-color: white;
    color: #212529;
    padding: 8px;
  }
  .page-select:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
  }
`

export default Pagination;
