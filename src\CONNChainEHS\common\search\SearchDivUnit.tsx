import { ORG_SPLIT_FLAG } from "ehs/constant/constants";
import { SelectSearch } from "ehs/models/SearchLabInfo";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { getSplitValueByIndexReg } from "ehs/utils/stringUtil";
import { useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
interface SearchUnitDivProps {
    dataList: Array<SelectSearch>
    show: boolean;
    showAll?: boolean;
    showArea?: boolean;
    showUnitCategory?: boolean;
    showUnit?: boolean;
    isLinkSelect?: boolean;
    condition?: any;
    setCondition?: React.Dispatch<React.SetStateAction<any>>;
    loginUser?: any;
    autoSetDefaultArea?: boolean;
}
const SearchDivUnit = ({ dataList = [], show, showAll, showArea, showUnitCategory,
    showUnit, isLinkSelect, condition = {}, setCondition, loginUser, autoSetDefaultArea = true }: SearchUnitDivProps) => {
    const { t } = useTranslation();
    const hasSetDefaultArea = useRef(false);
    const { areaId: selectedAreaValues, orgType: selectedUnitCateValues, unit: selectedUnitValues = {} } = condition

    useEffect(() => {
        // 如果有登入者資訊且區域為空，則設置登入者的區域作為預設值
        if (autoSetDefaultArea && loginUser?.userInfo?.areaId && !selectedAreaValues && setCondition && !hasSetDefaultArea.current) {
            setCondition((prevCondition: any) => ({
                ...prevCondition,
                areaId: loginUser.userInfo.areaId
            }));
            hasSetDefaultArea.current = true;
        }
    }, [loginUser, selectedAreaValues, setCondition, autoSetDefaultArea]);

    useEffect(() => {

        if (selectedUnitValues) {
            const selectedKeys = Object.keys(selectedUnitValues);
            if (selectedKeys.length > 0) {
                const maxKey = Math.max(...selectedKeys.map(Number));
                let maxValue = selectedUnitValues[maxKey];

                // 如果最大值是空的，找出次大的值
                if (!maxValue) {
                    const secondMaxKey = selectedKeys
                        .map(Number)
                        .filter(key => key !== maxKey) // 排除最大值的鍵
                        .reduce((prevKey: number, currKey: number) => {
                            if (prevKey === null || currKey > prevKey) {
                                return currKey;
                            } else {
                                return prevKey;
                            }
                        }, -1);

                    if (secondMaxKey !== -1) {
                        maxValue = selectedUnitValues[secondMaxKey];
                        // console.log('第二大鍵的值為：', maxValue);
                        if (setCondition) {
                            setCondition((prevCondition: any) => ({
                                ...prevCondition,
                                queryOrgId: maxValue || ""
                            }));
                        }
                    } else {
                        // console.log('第二大鍵的值都是空的');
                        if (setCondition) {
                            setCondition((prevCondition: any) => ({
                                ...prevCondition,
                                queryOrgId: ''
                            }));
                        }
                    }
                } else {
                    // console.log('最大鍵的值為：', maxValue);
                    if (setCondition) {
                        setCondition((prevCondition: any) => ({
                            ...prevCondition,
                            queryOrgId: maxValue || ""
                        }));
                    }
                }
            } else {
                if (setCondition) {
                    setCondition((prevCondition: any) => ({
                        ...prevCondition,
                        queryOrgId: ""
                    }));
                }
                // console.log('最大鍵的值為空：', maxValue);
            }
        }
    }, [selectedUnitValues])

    /* -------------------paragraph change method ------------------- */
    const handleSelectAreaChange = (event: any) => {
        const newValue = event.target.value;
        if (setCondition) {
            setCondition((prevCondition: any) => ({
                ...prevCondition,
                areaId: newValue || ""
            }));
        }
        if (newValue !== "") {
            if (setCondition) {
                setCondition((prevCondition: any) => ({
                    ...prevCondition,
                    unit: {}
                }));
            }
        }
    }

    const handleSelectUnitCateChange = (event: any) => {
        const newValue = event.target.value;
        if (setCondition) {
            setCondition((prevCondition: any) => ({
                ...prevCondition,
                orgType: newValue || ""
            }));
        }
        if (newValue !== "") {
            if (setCondition) {
                setCondition((prevCondition: any) => ({
                    ...prevCondition,
                    unit: {}
                }));
            }
        }
    }

    const handleSelectUnitChange = (level: number, event: any) => {
        const target = event.target;
        const newValue = target.value;
        const selectedOption = target.options[target.selectedIndex];
        const infoString = selectedOption.dataset.info;
        const info = infoString ? JSON.parse(infoString) : {};
        const areaId = info.areaId;
        const orgType = info.orgType;

        if (setCondition) {
            setCondition((prevCondition: any) => {
                const updatedCondition = { ...prevCondition };
                // 创建 unit 的副本
                const updatedValues = { ...updatedCondition.unit };
                // Clear any higher level values 更換單位 清除所有下層選中值
                Object.keys(updatedValues).forEach(key => {
                    const numericKey = parseInt(key);
                    if (!isNaN(numericKey) && numericKey > level) {
                        delete updatedValues[numericKey];
                    }
                });
                updatedValues[level] = newValue;

                // Update the value for the current level
                updatedCondition.unit = updatedValues;
                return updatedCondition;
            });
        }

        //更新區域 單位分類
        if (setCondition) {
            setCondition((prevCondition: any) => ({
                ...prevCondition,
                areaId: areaId || "",
                orgType: orgType || ""
            }));
        }
    };

    /* -------------------paragraph area ------------------- */
    // 創建一個新的 Map 對象，用於存儲唯一的 areaId 和對應的 areaName
    const uniqueAreaMap = new Map();
    dataList?.forEach(item => {
        if (item.areaId) {
            uniqueAreaMap.set(item.areaId, item.areaName);
        }
    });

    // 使用 Map 的 entries() 方法，將鍵值對轉換為數組，並進行 map() 轉換為 <option> 元素
    const areaOption = Array.from(uniqueAreaMap.entries()).map(([areaId, areaName]) => {
        return (<option key={areaId} value={areaId} title={areaName}>
            {areaName}
        </option>);
    });

    /* -------------------paragraph unit level ------------------- */
    const uniqueUnitLevelMap = new Map();
    dataList?.forEach(item => {
        if (item.orgLevelId) {
            uniqueUnitLevelMap.set(item.orgLevel, item.orgLevelName);
        }
    });
    // 獲取鍵的數組，並按照升序排序
    const sortedOrgLevels = Array.from(uniqueUnitLevelMap.keys()).sort();


    /* -------------------paragraph unit category ------------------- */
    const uniqueUnitCategoryMap = new Map();
    dataList?.forEach(item => {
        if (item.orgType) {
            uniqueUnitCategoryMap.set(item.orgType, item.orgTypeName);
        }
    });

    const unitCategoryOption = Array.from(uniqueUnitCategoryMap.entries()).map(([orgType, orgTypeName]) => {
        return (
            <option key={orgType} value={orgType} title={orgTypeName}>
                {orgTypeName}
            </option>
        );
    });


    /* -------------------paragraph unit ------------------- */
    // 根據排好序的鍵生成 unitSelectList
    const unitSelectList = sortedOrgLevels.map(orgLevel => {
        const orgLevelName = uniqueUnitLevelMap.get(orgLevel);

        const index = orgLevel - 1;
        const isFirstLevel = index === 0;
        const preIndex = index - 1;
        const visitedOrgIds = new Set();
        // 使用 filter() 方法過濾 dataList 中符合當前 orgLevel 的數據
        const filteredData = dataList.filter(item => {
            const orgNameOld = getSplitValueByIndexReg(item.orgNames, index, ORG_SPLIT_FLAG);
            if (item.orgLevel >= orgLevel && !visitedOrgIds.has(orgNameOld)) {
                visitedOrgIds.add(orgNameOld); // 将新的 orgName 添加到 Set 中
                return true; // 如果 orgName 是第一次出现，则返回 true，保留该项
            }
            return false;
        }).sort((a, b) => {
            const orgNameOld = getSplitValueByIndexReg(a.orgNames, index, ORG_SPLIT_FLAG);
            const orgNameNew = getSplitValueByIndexReg(b.orgNames, index, ORG_SPLIT_FLAG);
            return orgNameOld.localeCompare(orgNameNew);
        });
        const prevOrgLevel = sortedOrgLevels[index - 1];
        const prevOrgLevelSelectedValue = selectedUnitValues && selectedUnitValues[prevOrgLevel as keyof typeof selectedUnitValues];

        // 為每個符合條件的數據創建一個 <option> 元素
        const options = filteredData.map(item => {
            const orgId = getSplitValueByIndexReg(item.orgIds, index, ORG_SPLIT_FLAG);
            const orgName = getSplitValueByIndexReg(item.orgNames, index, ORG_SPLIT_FLAG);
            const preOrgId = getSplitValueByIndexReg(item.orgIds, preIndex, ORG_SPLIT_FLAG);
            //區域判斷
            if (!selectedAreaValues || selectedAreaValues === item.areaId) {
                //單位分類判斷
                if (!selectedUnitCateValues || selectedUnitCateValues === item.orgType) {
                    //單位層級判斷
                    if ((isFirstLevel || (prevOrgLevelSelectedValue === preOrgId && !visitedOrgIds.has(orgId))) && orgName) {
                        visitedOrgIds.add(orgId);
                        return (
                            <option key={orgId} value={orgId} data-info={JSON.stringify(item)}>
                                {orgName}
                            </option>
                        );
                    }
                }
            }
            return null;
        });

        const hasOption = !isArrayEmpty(options.filter(option => option !== null));
        //非第一層 且前一層沒選中值 且沒選項 不顯示下拉
        if (index > 0 && !prevOrgLevelSelectedValue && !hasOption) {
            return null;
        }

        //有選項才能顯示下拉
        return (hasOption &&
            <div key={orgLevel} className="col-xl-3 d-flex align-items-center my-2">
                <label className="w-25 me-1">{orgLevelName}</label>
                <select className="form-select w-75" value={selectedUnitValues[orgLevel] || ''} onChange={(e) => handleSelectUnitChange(orgLevel, e)}>
                    <option key="all" value="">{t('text.all')}</option>
                    {options}
                </select>
            </div>
        );
    });

    /* -------------------paragraph content ------------------- */
    return (
        show && <div className="row my-2">
            {(showAll || showArea) && <div className="col-xl-3 d-flex align-items-center">
                <label className="w-25 me-1">{t('text.area.item')}</label>
                <select className="form-select w-75" value={selectedAreaValues} onChange={(e) => handleSelectAreaChange(e)}>
                    <option key="all" value="">{t('text.all')}</option>
                    {areaOption}
                </select>
            </div>}
            {(showAll || showUnitCategory) && <div className="col-xl-3 d-flex align-items-center">
                <label className="w-25 me-1">{t('text.org.classification')}</label>
                <select className="form-select w-75" value={selectedUnitCateValues} onChange={(e) => handleSelectUnitCateChange(e)}>
                    <option key="all" value="">{t('text.all')}</option>
                    {unitCategoryOption}
                </select>
            </div>}
            {(showAll || showUnit) && unitSelectList}
        </div>
    );
};

export default SearchDivUnit;
