import { ChemicalAPI } from "api/chemicalAPI";
import { AgentNameTag } from "ehs/common/AgentNameTag";
import InputDate from "ehs/common/input/InputDate";
import { showWarnToast } from "ehs/common/Toast";
import { CONFIG_TYPE_OPER_TYPE, CONFIG_VAL_CONCERNED, CONFIG_VAL_TOXIC, OPTION_CHEM_REPORT_MONTH_EARLIEST, OPTION_CHEM_REPORT_MONTH_LONG, OPTION_CHEM_REPORT_MONTH_START_DF } from "ehs/constant/constants";
import useLoginUser from "ehs/hooks/useLoginUser";
import { EhsChemicalOperRecord } from "ehs/models/EhsChemicalOperRecord";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsOptions } from "ehs/models/EhsOptions";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { getBasicLoginUserInfo } from "ehs/utils/authUtil";
import { getLabTextObj } from "ehs/utils/langUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { getFormatDateDash, getFormatDateSlash } from "ehs/utils/stringUtil";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

export interface OperRecordDialogData {
    casNo: string;
    chemCtrlNo: string;
    chemName: string;
    chemId: string;
    chemConId: string;
    phaseState: string;
    // 可以根據需要添加更多屬性
}

enum ReportType {
    Excel = 'Excel',
    Word = 'Word',
}

function OperRecordDialog(props: {
    onClose: () => void;
    setLoadingBlock: (block: boolean) => void;
    mode: boolean | null;
    nowDate: Date;
    configMap: { [key: string]: EhsConfigParam };
    optionsMap: { [key: string]: EhsOptions };
    activeTab?: string;
    areaId: string;
    chemData: OperRecordDialogData;
}) {
    const { onClose, setLoadingBlock, mode, nowDate = new Date(), configMap, optionsMap,
        activeTab, areaId, chemData,
    } = props;
    const { casNo, chemCtrlNo, chemName, chemId, chemConId, phaseState } = chemData;
    const { loginUser } = useLoginUser();
    const { t, i18n } = useTranslation();
    const { tableTitleOperationLab } = getLabTextObj(loginUser, t);
    const [startMonth, setStartMonth] = useState<Date | null>(null);
    const [endMonth, setEndMonth] = useState<Date | null>(nowDate);
    const [operRecordList, setOperRecordList] = useState<EhsChemicalOperRecord[]>([]);
    const [hasSearched, setHasSearched] = useState<boolean>(false);

    const dfStartMonthOption = optionsMap[OPTION_CHEM_REPORT_MONTH_START_DF]?.optionIntValue || 0;
    const dfStartMonths = dfStartMonthOption > 1 ? dfStartMonthOption - 1 : dfStartMonthOption;//需包含當月
    const queryMonthLongOption = optionsMap[OPTION_CHEM_REPORT_MONTH_LONG]?.optionIntValue || 0;
    const queryMonthEarliestOption = optionsMap[OPTION_CHEM_REPORT_MONTH_EARLIEST]?.optionIntValue || 0;
    const hasExportReportChemClass = [CONFIG_VAL_TOXIC, CONFIG_VAL_CONCERNED];
    // 判斷當前標籤是否為毒化物或關注物質
    const isExportAvailable = hasExportReportChemClass.includes(activeTab || '');

    // 抽取一個函數來計算日期
    const calculateBeforeDate = (monthsToSubtract: number) => {
        return new Date(nowDate.getFullYear(), nowDate.getMonth() - monthsToSubtract, 1);
    };

    const defaultStartMonth = useMemo(() => {
        return calculateBeforeDate(dfStartMonths); // 預設開始是三個月前
    }, [dfStartMonths]);

    const minStartMonth = useMemo(() => {
        return calculateBeforeDate(queryMonthEarliestOption); // 最早查詢月份
    }, [queryMonthEarliestOption]);

    const operTypeConfigMap = useMemo(() => {
        return Object.values(configMap).reduce((acc, config) => {
            if (config.configType === CONFIG_TYPE_OPER_TYPE) {
                acc[config.configIvalue] = config;
            }
            return acc;
        }, {} as Record<string, EhsConfigParam>);
    }, [configMap]);


    useEffect(() => {
        if (!mode) {
            setOperRecordList([]);
            setHasSearched(false);
        } else {
            // 當對話框開啟時(mode = true)，自動執行第一次查詢
            if (startMonth && endMonth && chemConId) {
                searchRecord();
            }
        }
    }, [mode]);

    useEffect(() => {
        setStartMonth(defaultStartMonth);
    }, [defaultStartMonth])

    const searchRecord = async () => {
        if (startMonth && endMonth) {
            // 用於計算月份差的日期（統一為每月第一天）
            const monthDiffEndDate = new Date(endMonth.getFullYear(), endMonth.getMonth() + 1, 1); // 下個月第一天

            // 計算兩個日期之間的月份差
            const monthDiff = (monthDiffEndDate.getFullYear() - startMonth.getFullYear()) * 12 +
                (monthDiffEndDate.getMonth() - startMonth.getMonth());

            // 判斷是否超過限制
            if (monthDiff > queryMonthLongOption) {
                showWarnToast(t('text.search.chemical_report_month_limit', { months: queryMonthLongOption }));
                return;
            }

            // 實際查詢用的日期範圍
            const queryStartDate = new Date(startMonth.getFullYear(), startMonth.getMonth(), 1); // 該月第一天
            const queryEndDate = new Date(endMonth.getFullYear(), endMonth.getMonth() + 1, 0); // 該月最後一天

            // 在這裡進行查詢的實際邏輯 
            console.log('開始查詢記錄...', getFormatDateDash(queryStartDate), getFormatDateDash(queryEndDate));

            const res = await ChemicalAPI.getChemicalOperRecordList({
                ...getBasicLoginUserInfo(loginUser),
                operRecordStatus: 1,
                operStartDate: queryStartDate ? getFormatDateDash(queryStartDate) : '',
                operEndDate: queryEndDate ? getFormatDateDash(queryEndDate) : '',
                chemConId: chemConId,
                phaseState: phaseState,
            });
            if (isApiCallSuccess(res)) {
                setOperRecordList(res.results);
                setHasSearched(true);
            }
        } else {
            console.warn('請選擇查詢月份');
        }
    }

    const clickReport = (type: ReportType) => {
        setLoadingBlock(true);
        try {
            if (!areaId) {
                showWarnToast(t('message.area_required_for_report'));
                setLoadingBlock(false);
                return;
            }
            const requestParams = {
                ...getBasicLoginUserInfo(loginUser),
                reportType: activeTab || '',
                operStartDate: startMonth ? getFormatDateDash(startMonth) : '',
                operEndDate: endMonth ? getFormatDateDash(endMonth) : '',
                operRecordStatus: 1,
                areaId: areaId,
                chemId: chemId,
                chemConId: chemConId,
                chemCtrlNo: chemCtrlNo,
                phaseState: phaseState,
            };
            if (type === ReportType.Excel) {
                ChemicalAPI.downloadChemicalOperReportExcel(requestParams);
            } else if (type === ReportType.Word && isExportAvailable) {
                // 假設 API 有一個 downloadChemicalOperReportWord 方法
                // 由於目前的 API 中沒有找到此方法，這裡只是一個示例
                // 實際上，請使用真實的 API 調用
                if (activeTab === CONFIG_VAL_TOXIC) {
                    ChemicalAPI.downloadChemicalOperReportWordToxic(requestParams);
                } else if (activeTab === CONFIG_VAL_CONCERNED) {
                    ChemicalAPI.downloadChemicalOperReportWordConcern(requestParams);
                }
            }
        } finally {
            setLoadingBlock(false);
        }
    }

    // 顯示化學品資訊的標題
    const dialogTitle = casNo && chemName
        ? `${casNo} ${chemName} ${t('text.oper_record')}`
        : t('text.oper_record');

    return (
        <StyledOperRecordDialog>
            <div className="modifyStopDate">
                <div className="modifyStopDate-header">
                    <h4 className="modal-title">{dialogTitle}</h4>
                    <button
                        type="button"
                        className="btn-close"
                        aria-hidden="true"
                        onClick={onClose}
                    ></button>
                </div>
                <div className="modifyStopDate-body">
                    <div className="row mb-3">
                        <div className="col-md-6">
                            <div className="d-flex align-items-center">
                                <div className="d-flex align-items-center">
                                    <label className="me-2">{t('text.operation_month')}</label>
                                </div>
                                <InputDate
                                    minDate={minStartMonth && getFormatDateSlash(minStartMonth)}
                                    defaultValue={(startMonth && getFormatDateSlash(startMonth)) || undefined}
                                    onChange={(date) => setStartMonth(date)}
                                    showMonthYearPicker
                                    isClearable={false}
                                    className="me-2"
                                />
                                <span className="mx-2">~</span>
                                <InputDate
                                    maxDate={(nowDate && getFormatDateSlash(nowDate)) || undefined}
                                    defaultValue={(endMonth && getFormatDateSlash(endMonth)) || undefined}
                                    onChange={(date) => setEndMonth(date)}
                                    showMonthYearPicker
                                    isClearable={false}
                                    className="me-2"
                                />
                                <button
                                    type="button"
                                    className="btn btn-primary ms-2"
                                    title={t('button.search.item')}
                                    onClick={() => { searchRecord(); }}
                                >
                                    <i className="fas fa-magnifying-glass"></i> {t('button.search.item')}
                                </button>
                            </div>
                        </div>
                        {isExportAvailable && <div className="col-md-6 text-end">
                            <button
                                type="button"
                                className="btn btn-success"
                                aria-hidden="true"
                                title="Excel"
                                onClick={() => clickReport(ReportType.Excel)}
                            >
                                <i className="fa fa-lg fa-file-excel me-1" />Excel
                            </button>
                            <button
                                type="button"
                                className="btn btn-primary ms-3"
                                aria-hidden="true"
                                title="Word"
                                onClick={() => clickReport(ReportType.Word)}
                            >
                                <i className="fa fa-lg fa-file-word me-1" />Word
                            </button>
                        </div>}
                    </div>
                    <div className="row mb-3">
                        <div className="col-md-12">
                            <div className="table-responsive">
                                <table className="table">
                                    <thead className="bg-lime-200">
                                        <tr>
                                            <th>{t('table.title.item')}</th>
                                            <th>{t('table.title.chemical.operation_item')}</th>
                                            <th className="text-end">{t('table.title.chemical.weight_operation_before')}(kg)</th>
                                            <th className="text-end">{t('table.title.chemical.weight_operation')}(kg)</th>
                                            <th className="text-end">{t('table.title.chemical.weight_operation_after')}(kg)</th>
                                            <th>{t('table.title.chemical.operation_date')}</th>
                                            <th>{t('table.title.last_modified_at')}</th>
                                            <th>{t('table.title.registrant')}</th>
                                            <th>{t('table.title.note')}</th>
                                            <th>{tableTitleOperationLab}</th>
                                            <th>{t('table.title.vendor_ctrl_no')}</th>
                                            <th>{t('table.title.vendor_name')}</th>
                                            <th>{t('table.title.product_permit_no')}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {!isArrayEmpty(operRecordList) ?
                                            operRecordList.map((record, index) => (
                                                <tr key={'operRecordList' + index}>
                                                    <td data-title={t('table.title.item')} className="text-center">{index + 1}</td>
                                                    <td data-title={t('table.title.chemical.operation_item')} className="text-center">{operTypeConfigMap[record.operType]?.configName}</td>
                                                    <td data-title={t('table.title.chemical.weight_operation_before')} className="text-end">{record.inventoryPre}</td>
                                                    <td data-title={t('table.title.chemical.weight_operation')} className="text-end">{record.operQty}</td>
                                                    <td data-title={t('table.title.chemical.weight_operation_after')} className="text-end">{record.inventory}</td>
                                                    <td data-title={t('table.title.chemical.operation_date')} className="text-center">{getFormatDateSlash(record.operDate)}</td>
                                                    <td data-title={t('table.title.last_modified_at')} className="text-center">{getFormatDateSlash(record.editDate)}</td>
                                                    <td data-title={t('table.title.registrant')} className="text-center">{record.operatorName}
                                                        <AgentNameTag agentId={record.operatorAgentId} agentName={record.operatorAgentName} withBreak />
                                                    </td>
                                                    <td data-title={t('table.title.note')} className="text-center">{record.chemRecordNote}</td>
                                                    <td data-title={tableTitleOperationLab} className="text-center">{record.labName}</td>
                                                    <td data-title={t('table.title.vendor_ctrl_no')} className="text-center">{record.approvalNo}</td>
                                                    <td data-title={t('table.title.vendor_name')} className="text-center">{record.manufacturerName}</td>
                                                    <td data-title={t('table.title.product_permit_no')} className="text-center">
                                                        {/* 縣市字第00000000號 */}
                                                    </td>
                                                </tr>
                                            )) :
                                            <tr>
                                                <td colSpan={99} className="text-center">{t('message.no_data')}</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="modifyStopDate-footer">
                    <div className="btn btn-white" aria-hidden="true" onClick={onClose}>
                        <i className="fas fa-times me-1" />
                        {t("button.close")}
                    </div>
                </div>
            </div>
        </StyledOperRecordDialog>
    );
}

const StyledOperRecordDialog = styled.div`
  background: white;
  width: 1400px;

  .modifyStopDate-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  
  .modifyStopDate-body {
    padding: 15px;
  }
  
  .modifyStopDate-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-purple {
      margin-left: 10px;
    }
  }

  table {
    position:relative;  
    th {
      text-align: center;
    } 
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 100px;
        text-align:left;
        min-height:39.5px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }

  @media (max-width: 768px) {
    width: 100%;
    
    .d-flex {
      flex-direction: column;
      align-items: flex-start;
      
      .btn {
        margin-top: 10px;
        margin-left: 0 !important;
      }
      
      span {
        margin: 5px 0;
      }
    }
    
    .text-end {
      text-align: left !important;
      margin-top: 15px;
      
      .btn {
        width: 100%;
        margin-bottom: 10px;
      }
    }
  }
`;

export default OperRecordDialog;