import { handleFileDownload } from "ehs/utils/fileUtil";
import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { ChemicalClassMatch } from "../CONNChainEHS/models/ChemicalClassMatch";
import { EhsChemical } from "../CONNChainEHS/models/EhsChemical";
import { EhsChemicalCasno } from "../CONNChainEHS/models/EhsChemicalCasno";
import { EhsChemicalCategory } from "../CONNChainEHS/models/EhsChemicalCategory";
import { EhsChemicalCon } from "../CONNChainEHS/models/EhsChemicalCon";
import { EhsChemicalInfoArea } from "../CONNChainEHS/models/EhsChemicalInfoArea";
import { EhsChemicalName } from "../CONNChainEHS/models/EhsChemicalName";
import { EhsChemicalOperRecord } from "../CONNChainEHS/models/EhsChemicalOperRecord";
import { EhsOtherInfo } from "../CONNChainEHS/models/EhsOtherInfo";
import { EhsPurchase } from "../CONNChainEHS/models/EhsPurchase";
import { EhsPurchaseDetail } from "../CONNChainEHS/models/EhsPurchaseDetail";
import { EhsPurchaseDetailCategory } from "../CONNChainEHS/models/EhsPurchaseDetailCategory";
import { appendToFormData } from "../CONNChainEHS/utils/formDataUtil";
import { EhsChemicalConCategory } from "./../CONNChainEHS/models/EhsChemicalConCategory";
import { EhsPurchaseSubst } from './../CONNChainEHS/models/EhsPurchaseSubst';
import { apiRequest, createLoginPostFormDataConfig, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";
import { ChemicalCcbLinkType } from "ehs/enums/ChemicalCcbLinkType";

export const ChemicalAPI = {
  getChemicalList: async (parms: BaseParams) => {
    return apiRequest(getApiAddress + "chemical/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalDetail: async (
    parms: BaseParams & {
      chemId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/detail", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalSearch: async (
    parms: BaseParams & {
      areaId: string;
      chemCtrlNo?: string;
      casNo?: string;
      chemName?: string;
      searchMethod?: string;
      limitQty?: number;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/search", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalByCasNo: async (
    parms: BaseParams & {
      casNo?: string;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/search/casno", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalByCtrlNo: async (
    parms: BaseParams & {
      chemCtrlNo?: string;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/search/ctrlno", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalClassByChemId: async (
    parms: BaseParams & {
      chemId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/class/rule/chemid", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalCategoryByChemConId: async (
    parms: BaseParams & {
      chemConId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/category/chemconid", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalInventorySum: async (parms: BaseParams) => {
    return apiRequest(getApiAddress + "chemical/inventory/list/sum", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalInventoryItemList: async (parms: BaseParams) => {
    return apiRequest(getApiAddress + "chemical/inventory/list/item", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalInventoryReportList: async (parms: BaseParams) => {
    return apiRequest(getApiAddress + "chemical/inventory/list/report", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalInventoryListByLab: async (
    parms: BaseParams & {
      labId: string;
      chemConId: string;
      phaseState: string;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/inventory/list/lab", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalInventoryTotalByLab: async (
    parms: BaseParams & {
      labId: string;
      chemConId: string;
      phaseState: string;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/inventory/total/lab", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalCasnoListByCategoryId: async (
    parms: BaseParams & {
      categoryId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/casno/list/categoryid", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalCtrlnoListByCategoryId: async (
    parms: BaseParams & {
      categoryId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/ctrlno/list/categoryid", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemConInfoByConcen: async (
    parms: BaseParams & {
      chemConId: string;
      concen: number;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/chemconinfo/concen", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalOperRecordList: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "chemical/operate/record/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalStopOperList: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "chemical/operate/stop/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalStopOperByChemConId: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "chemical/operate/stop/chemconid", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalClassMatch: async (parms: BaseParams & {
    mixtureFlag: boolean;//是否比對混合
    matchConcenFlag: boolean;//是否比對濃度
    matchList: ChemicalClassMatch[];//比對條件
  }) => {
    return apiRequest(getApiAddress + "chemical/class/match", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getCcbDetail: async (parms: BaseParams & {
    linkType: ChemicalCcbLinkType;
    linkId: string;
  }) => {
    return apiRequest(getApiAddress + "chemical/ccb/detail", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  checkDuplicateCasNumber: async (
    parms: BaseParams & {
      casno: string;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/casno/check/duplicate", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addChemical: async (
    parms: BaseParams & {
      chemical: EhsChemical;
      areaInfos: EhsChemicalInfoArea[];
      casnos: EhsChemicalCasno[];
      chemNames: EhsChemicalName[];
      categories?: EhsChemicalCategory[];
      concens?: EhsChemicalCon[];
      concenCategories?: EhsChemicalConCategory[];
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/add", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  editChemical: async (
    parms: BaseParams & {
      chemical: EhsChemical;
      areaInfos: EhsChemicalInfoArea[];
      casnos: EhsChemicalCasno[];
      chemNames: EhsChemicalName[];
      categories?: EhsChemicalCategory[];
      concens?: EhsChemicalCon[];
      concenCategories?: EhsChemicalConCategory[];
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/edit", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  editChemicalStatus: async (
    parms: BaseParams & {
      chemId: string;
      chemStatus: number;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/status/edit", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addChemicalItem: async (
    parms: BaseParams & {
      manufacturerId: string;
      inventoryLocation: string;
      quantity: number;
      purchase: EhsPurchase;
      purchaseDetail: EhsPurchaseDetail;
      sdsFile: File;
      substanceList: EhsPurchaseSubst[];
      weightList: number[];
      detailCategoryList: EhsPurchaseDetailCategory[];
      otherInfoList: EhsOtherInfo[];
    }
  ) => {
    const formData = new FormData(); 
    const customKey = ["sdsFile"];

    const jsonParams = Object.keys(parms).reduce((acc, key) => {
      if (!customKey.includes(key)) {
        acc[key] = (parms as any)[key]; // 使用類型斷言
      }
      return acc;
    }, {} as any);

    formData.append("jsonString", JSON.stringify(jsonParams));
    if (parms.sdsFile) {
      formData.append("sdsFile", parms.sdsFile);
    }

    return apiRequest(getApiAddress + "chemical/item/add", createLoginPostFormDataConfig(parms, formData)).then((res) => handleFetchResponse(res));
  },
  editChemicalInventoryLocation: async (
    parms: BaseParams & {
      inventoryId: string;
      inventoryLocation: string;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/inventory/location/update", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  editChemicalInventoryNote: async (
    parms: BaseParams & {
      inventoryId: string;
      inventoryNote: string;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/inventory/note/update", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  editChemicalInventorySds: async (
    parms: BaseParams & {
      inventoryId: string;
      purchaseDetailId: string;
      sdsFile: File;
      sdsExpDate: string;
      modifyFileNote: string;
    }
  ) => {
    const formData = new FormData();
    const customKey = ["sdsFile"];
    const jsonParams = Object.keys(parms).reduce((acc, key) => {
      if (!customKey.includes(key)) {
        acc[key] = (parms as any)[key]; // 使用類型斷言
      }
      return acc;
    }, {} as any);

    formData.append("jsonString", JSON.stringify(jsonParams));

    if (parms.sdsFile) {
      formData.append("sdsFile", parms.sdsFile);
    }

    return apiRequest(getApiAddress + "chemical/inventory/sds/update", createLoginPostFormDataConfig(parms, formData)).then((res) => handleFetchResponse(res));
  },
  updateChemicalInventoryTransfer: async (
    parms: BaseParams & {
      currentLabId: string;
      newLabId: string;
      modifyNote: string;
      inventoryIdList: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/inventory/transfer/update", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addChemicalOperation: async (
    parms: BaseParams & {
      chemicalOperRecord: EhsChemicalOperRecord;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/operate/add", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addChemicalRePurchase: async (
    parms: BaseParams & {
      chemicalOperRecord: EhsChemicalOperRecord;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/operate/repurchase/inventory/add", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addChemicalOperRecordNoChange: async (
    parms: BaseParams & {
      inventoryIdList: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/operate/nochange/add", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addChemicalOperRecordDisposal: async (
    parms: BaseParams & {
      inventoryIdList: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/operate/disposal/add", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  modifyChemicalOperation: async (
    parms: BaseParams & {
      chemicalOperRecord: EhsChemicalOperRecord;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/operate/modify", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  deleteChemicalRecord: async (
    parms: BaseParams & {
      inventoryId: string;
      chemRecordId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/record/delete", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  editChemicalStopOperMonth: async (
    parms: BaseParams & {
      conList: EhsChemicalCon[];
    }
  ) => {
    return apiRequest(getApiAddress + "chemical/operate/stop/month/edit", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  downloadChemicalOperReportExcel: async (
    parms: BaseParams & {
      // reportType?: string;
      // startDate?: string;
      // endDate?: string;
      // exportType?: string;
    }
  ) => {
    try {
      const response = await apiRequest(getApiAddress + "chemical/report/excel", createLoginPostJsonConfig(parms));
      await handleFileDownload(response, 'chemical_report.xlsx'); 
    } catch (error) {
      console.error('下載過程中發生錯誤:', error);
    }
  },
  downloadChemicalOperReportWordToxic: async (
    parms: BaseParams & {
      // reportType?: string;
      // startDate?: string;
      // endDate?: string;
      // exportType?: string;
    }
  ) => {
    try {
      const response = await apiRequest(getApiAddress + "chemical/report/word/toxic", createLoginPostJsonConfig(parms));
      await handleFileDownload(response, 'chemical_report_toxic.doc');
    } catch (error) {
      console.error('下載過程中發生錯誤:', error);
    }
  },
  downloadChemicalOperReportWordConcern: async (
    parms: BaseParams & {
      // reportType?: string;
      // startDate?: string;
      // endDate?: string;
      // exportType?: string;
    }
  ) => {
    try {
      const response = await apiRequest(getApiAddress + "chemical/report/word/concern", createLoginPostJsonConfig(parms));
      await handleFileDownload(response, 'chemical_report_concern.doc');
    } catch (error) {
      console.error('下載過程中發生錯誤:', error);
    }
  },
  downloadCcbExecutionRecord: async (
    parms: BaseParams & {
      ccbDetailId: string;
    }
  ) => {
    try {
      const response = await apiRequest(getApiAddress + "chemical/report/ccb/execution/record", createLoginPostJsonConfig(parms));
      await handleFileDownload(response, 'ccb_execution_record.docx');
    } catch (error) {
      console.error('下載過程中發生錯誤:', error);
    }
  },
  downloadCcbCheckList: async (
    parms: BaseParams & {}
  ) => {
    try {
      const response = await apiRequest(getApiAddress + "chemical/report/ccb/checklist", createLoginPostJsonConfig(parms));
      await handleFileDownload(response, 'ccb_checklist.docx');
    } catch (error) {
      console.error('下載過程中發生錯誤:', error);
    }
  },
};
