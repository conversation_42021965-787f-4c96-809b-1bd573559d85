import { useMenu } from 'ehs/hooks/useMenu';
import { useTranslation } from 'react-i18next';
import useLoginUser from '../CONNChainEHS/hooks/useLoginUser';

/**
 * menu 
 * 邏輯移至funcUtil處理
 * @returns 
 */
const Menu = () => {
  const { t } = useTranslation();
  const { loginUser } = useLoginUser();
  const { menuList } = useMenu();
  // const userMenuList = loginUser?.userInfo.menuList || [];

  // const menuItems = [
  //   // { path: '/', icon: 'fa fa-sitemap', title: t('func.home') },
  //   {
  //     path: 'system', icon: 'fa fa-gear', title: t('func.system_manage_setting'),
  //     children: [
  //       { path: AppPaths.system.funcList, title: t('func.func_list') },
  //       { path: AppPaths.system.roleList, title: t('func.role_manage') },
  //       { path: AppPaths.system.funcGroupList, title: t('func.func_group_manage') },
  //       { path: AppPaths.system.orgLevelList, title: t('func.org_level_manage') },
  //     ]
  //   },
  //   {
  //     path: 'manage', icon: 'fa fa-sitemap', title: t('func.org_user_manage'),
  //     children: [
  //       { path: AppPaths.manage.areaList, title: t('func.area_manage') },
  //       { path: AppPaths.manage.buildingList, title: t('func.building_manage') },
  //       { path: AppPaths.manage.orgList, title: t('func.org_manage') },
  //       { path: AppPaths.manage.labList, title: t('func.lab_manage') },
  //       { path: AppPaths.manage.userList, title: t('func.user_manage') },
  //     ]
  //   },
  //   {
  //     path: 'signoff', icon: 'fa fa-clipboard-check', title: t('func.signoff.manage'),
  //     children: [
  //       { path: AppPaths.signoff.purchaseList, title: '化學品採購簽核' },
  //     ]
  //   },
  //   {
  //     path: 'chemical', icon: 'fa fa-flask', title: t('func.chemical.manage'),
  //     children: [
  //       { path: AppPaths.chemical.informationList, title: t('func.chemical.info') },
  //       { path: AppPaths.chemical.itemAdd, title: t('func.chemical.item_add') },
  //       { path: AppPaths.chemical.purchaseRequest, title: t('func.chemical.purchase.request') },
  //       { path: AppPaths.chemical.purchaseList, title: t('func.chemical.purchase.list') },
  //       { path: AppPaths.chemical.operationList, title: t('func.chemical.operation') },
  //     ]
  //   }
  // ];

  // // 遞迴函數,用於遍歷所有層級的子菜單
  // function traverseSubMenus(menus, map) {
  //   menus.forEach(menu => {
  //     if (menu.funcType === 'F') {
  //       map.set(menu.funcUrl, menu.funcUrl);
  //     }
  //     if (menu.subMenuList) {
  //       traverseSubMenus(menu.subMenuList, map);
  //     }
  //   });
  // }

  // // 將 userMenuList 轉換為以 funcUrl 為鍵的映射
  // const userMenuMap = new Map();
  // userMenuList?.forEach(userMenu => {
  //   if (userMenu.funcType === 'F') {
  //     userMenuMap.set(userMenu.funcUrl, userMenu.funcUrl);
  //   }
  //   if (userMenu.subMenuList) {
  //     traverseSubMenus(userMenu.subMenuList, userMenuMap);
  //   }
  // });

  // // 過濾 menuItems
  // const filteredMenuItems = menuItems.map(item => {
  //   if (item.children) {
  //     const filteredChildren = item.children.filter(child => { 
  //       return userMenuMap.has(child.path); // 使用 map 進行檢查
  //     });

  //     // 如果至少有一個子菜單存在於用戶菜單中，則保留該項目
  //     if (filteredChildren.length > 0) {
  //       return {
  //         ...item,
  //         children: filteredChildren // 返回過濾後的子菜單
  //       };
  //     } else {
  //       return null; // 如果不是所有子菜單都存在，則返回 null
  //     }
  //   } else {
  //     // 如果沒有子菜單，檢查當前項目是否在用戶菜單中
  //     return userMenuMap.has(item.path) ? item : null; // 使用 map 進行檢查
  //   }
  // }).filter(item => item !== null); // 過濾掉 null 值

  // console.log("🚀 ~ Menu ~ filteredMenuItems:", filteredMenuItems);
  return menuList;
}

export default Menu;
