import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const ProductAPI = {
  getProductList: async (
    parms: BaseParams & {
      areaId: string;
      manufacturerId?: string;
      searchMethod?: string;
      keyword?: string;
      chemCtrlNo?: string;
      casNo?: string;
    }
  ) => {
    return apiRequest(getApiAddress + "product/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getProductCategoryId: async (
    parms: BaseParams & {
      productId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "product/category/id", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getProductSubstanceById: async (
    parms: BaseParams & {
      productId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "product/substance", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  }
};
