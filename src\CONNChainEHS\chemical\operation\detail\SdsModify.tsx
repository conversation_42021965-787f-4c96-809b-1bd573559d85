import InputDate from "ehs/common/input/InputDate";
import InputFileUpload from "ehs/common/InputFileUpload";
import { UPLOAD_ACCEPT_TYPE_PDF } from "ehs/constant/constants";
import { EhsFile, initEhsFile } from "ehs/models/EhsFile";
import { getFormatDateDash, getFormatDateSlash } from "ehs/utils/stringUtil";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import styled from "styled-components";
import { ChemicalAPI } from "api/chemicalAPI";
import { getBasicLoginUserInfo } from "ehs/utils/authUtil";
import useLoginUser from "ehs/hooks/useLoginUser";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { errorMsg } from "ehs/common/SwalMsg";
import { showWarnToast } from "ehs/common/Toast";

interface SdsFormData {
    sdsFile: EhsFile | null;
    sdsExpDate: Date | null;
    modifyFileNote: string;
}

interface TempFileState {
    file: File | null;
    fileInfo: EhsFile | null;
}

export default function SdsModify({
    onClose, onActionSuccess, setLoadingBlock,
    inventoryId,
    purchaseDetailId,
    sdsFile, sdsExpDate, nowDate }: {
        onClose: () => void;
        onActionSuccess: (file: EhsFile | null, newSdsExpDate: Date | null, inventoryStatus: number) => void;
        setLoadingBlock: (block: boolean) => void;
        inventoryId: string,
        purchaseDetailId: string,
        sdsFile: EhsFile | null,
        sdsExpDate: Date | null,
        nowDate: Date, 
    }) {
    const { t } = useTranslation();
    const {loginUser} = useLoginUser();
    const [tempFile, setTempFile] = useState<TempFileState>({
        file: null,
        fileInfo: sdsFile
    });
    const { fileInfo: templateFileInfo } = tempFile;

    const getDefaultValues = () => ({
        sdsFile: sdsFile || null,
        sdsExpDate: sdsExpDate || null,
        modifyFileNote: ''
    });

    const { register, handleSubmit, formState: { errors }, setValue, watch, reset } = useForm<SdsFormData>({
        defaultValues: getDefaultValues(),
        resolver: (values) => {
            const errors: any = {};

            if (!values.sdsFile) {
                errors.sdsFile = {
                    type: 'required'
                };
            }

            if (!values.sdsExpDate) {
                errors.sdsExpDate = {
                    type: 'required'
                };
            }

            return {
                values,
                errors: !isArrayEmpty(Object.keys(errors)) ? errors : {}
            };
        }
    });
    const sdsExpDateValue = watch('sdsExpDate');

    const resetForm = () => {
        setTempFile({
            file: null,
            fileInfo: sdsFile
        });
        reset(getDefaultValues());
    };

    useEffect(() => {
        resetForm();
    }, [sdsFile, sdsExpDate]);

    const actTitle = t('func.edit');

    const clickClose = () => {
        resetForm();
        onClose();
    }

    const isFormChanged = () => {
        // 檢查檔案是否有更動
        const isFileChanged = tempFile.fileInfo?.fileName !== sdsFile?.fileName;

        // 檢查日期是否有更動
        const originalDate = sdsExpDate ? new Date(sdsExpDate).getTime() : null;
        const currentDate = sdsExpDateValue ? new Date(sdsExpDateValue).getTime() : null;
        const isDateChanged = originalDate !== currentDate;

        return isFileChanged || isDateChanged;
    };

    const onSubmit = handleSubmit(async (data) => {
        if (!isFormChanged()) {
            showWarnToast(t('message.no_change'),{toastId: 'no_change'});
            return;
        }
        setLoadingBlock(true);
        try {
            const requestData = {
                ...getBasicLoginUserInfo(loginUser),
                inventoryId: inventoryId,
                purchaseDetailId,
                sdsExpDate: getFormatDateDash(data.sdsExpDate),
                modifyFileNote: data.modifyFileNote
            };

            // 如果有新檔案才加入檔案相關資訊
            if (tempFile.file) {
                Object.assign(requestData, {
                    sdsFile: tempFile.file,
                });
            }
            const res = await ChemicalAPI.editChemicalInventorySds(requestData);
            if (isApiCallSuccess(res)) {
                const rsObj = res.results;
                onActionSuccess(rsObj.sdsFile, data.sdsExpDate, rsObj.inventoryStatus);
                resetForm(); 
            }else{
                errorMsg(t('message.error'));
            }
        } catch (error) {
            errorMsg(t('message.error'));
            console.error(error);
        } finally {
            setLoadingBlock(false);
        }
    });
 
    return (
        <StyledSdsModify>
            <form onSubmit={onSubmit}>
                <div className="sdsModify">
                    <div className="sdsModify-header">
                        <h4 className="modal-title">{actTitle}</h4>
                        <button
                            type="button"
                            className="btn-close"
                            onClick={clickClose}
                        ></button>
                    </div>
                    <div className="sdsModify-body d-flex align-items-start flex-wrap">
                        <div className="row col-12 justify-content-center">
                            <div className="col-10 mb-4">
                                <label className="fw-bold">{t('table.title.sds_file')}<span className="text-danger">*</span></label>
                                <InputFileUpload
                                    defaultFile={templateFileInfo || undefined}
                                    defaultFileInfo={templateFileInfo || undefined}
                                    acceptedFileTypes={UPLOAD_ACCEPT_TYPE_PDF}
                                    onFileChange={(file) => {
                                        const newFileInfo = file ? {
                                            ...initEhsFile,
                                            fileName: file.name,
                                            fileMappingId: purchaseDetailId
                                        } : null;

                                        setTempFile({
                                            file: file,
                                            fileInfo: newFileInfo
                                        });
                                        setValue('sdsFile', newFileInfo, { shouldValidate: true });
                                    }}
                                />
                                {errors.sdsFile && <div className="invalid-feedback d-block">{t('message.select')}</div>}
                            </div>
                            <div className="col-10 mb-4">
                                <label className="fw-bold">{t('text.chemical.sds_file_exp_date')}<span className="text-danger">*</span></label>
                                <InputDate
                                    className={`form-control ${errors.sdsExpDate ? 'is-invalid' : ''}`}
                                    defaultValue={sdsExpDateValue ? getFormatDateSlash(sdsExpDateValue) : ''}
                                    minDate={getFormatDateSlash(nowDate)}
                                    onChange={date => setValue('sdsExpDate', date, { shouldValidate: true })}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                    }}
                                />
                                {errors.sdsExpDate && <div className="invalid-feedback d-block">{t('message.select')}</div>}
                            </div>
                            <div className="col-10 mb-4">
                                <label className="fw-bold">{t('text.note')}</label>
                                <textarea
                                    className="form-control"
                                    value={watch('modifyFileNote')}
                                    onChange={e => setValue('modifyFileNote', e.target.value)}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="sdsModify-footer">
                        <button type="button" className="btn btn-white" onClick={clickClose}>
                            <i className="fas fa-close" /> {t("button.close")}
                        </button>
                        <button type="submit" className="btn btn-success ms-3">
                            <i className="fas fa-pencil-alt" /> {actTitle}
                        </button>
                    </div>
                </div>
            </form>
        </StyledSdsModify>
    );
}

const StyledSdsModify = styled.div`
  background: white;
  width: 100%;
  max-width: 500px;

  .download-button:hover {
    text-decoration: underline; /* 滑鼠懸停時顯示底線 */
    }

  .chem-info-div { 
      overflow-y: auto;
      height: 675px;
  }
  
  label{
    user-select: none;
  }

  .sdsModify-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  .sdsModify-body {
    padding-top: 15px;
  }
  .sdsModify-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-purple {
      margin-left: 10px;
    }
  }
  
  @media (max-width: 600px){
    .chem-info-div { 
        min-height: 0; 
    }
    
    .sdsModify-footer {
        border-top: 1px solid #ced4da;
        padding: 15px;
        margin-top: 140px; 
        justify-content: center;
        .btn.btn-purple {
        margin-left: 10px;
        position: relative;  
        }
    }
  }
`;
