// @ts-nocheck
import swal from 'sweetalert';

const swalMode = {
    Confirm: 'confirm',
    Success: 'success',
    Error: 'error',
    Warning: 'warning'
};

export function doSwal(text, showConfirm, t, mode, inputTitle) {
    let icon = mode === swalMode.Confirm ? swalMode.Warning : mode
    const swalOptions = {
        title: text,
        icon: icon,
        dangerMode: true,
        ...(inputTitle && {
            content: {
                element: "input",
                attributes: {
                    placeholder: inputTitle,
                    type: "text",
                },
            },
        }),
        ...(showConfirm && {
            buttons: {
                Btn: false,
                cancel: {
                    text: t('button.no'),
                    visible: true
                },
                confirm: {
                    text: t('button.yes'),
                    visible: true
                }
            }
        })
    };
    return swal(swalOptions);
}

export function confirmMsg(text, t) {
    return doSwal(text, true, t, swalMode.Confirm);
}

export function confirmMsgInput(text, t, inputTitle) {
    return doSwal(text, true, t, swalMode.Confirm, inputTitle);
}

//先用 toast 避免成功 還要user 一直點
// export function SuccessMsg({ text = '', t }) {
//     return DoSwal(text || t('message.success'), false, t, SwalMode.Success);
// }

//先用 toast 避免成功 還要user 一直點
// export function WarningMsg(text, t) {
//     return DoSwal(text, false, t, SwalMode.Warning);
// }

export function errorMsg(text = "") {
    doSwal(text, false, () => { }, swalMode.Error);
}
