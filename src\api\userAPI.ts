import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { EhsUserName } from "../CONNChainEHS/models/EhsUserName";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const UserAPI = {
  getUserManageList: async (parms: BaseParams & {
    queryOrgId?: string;
    keyword: string;
    queryLabId?: string;
    currentPage?: number;
    pageSize: number;
  }) => {
    return apiRequest(getApiAddress + "user/list/manage", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getUserSearchList: async (parms: BaseParams & {
    keyword: string;
    pageSize: number;
  }) => {
    return apiRequest(getApiAddress + "user/list/search", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getUserOrgManager: async (parms: BaseParams & {
  }) => {
    return apiRequest(getApiAddress + "user/org/manager", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  deleteUser: async (parms: BaseParams & {
    userId: string;
  }) => {
    return apiRequest(getApiAddress + "user/del", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getUserDetail: async (parms: BaseParams & {
    userId: string;
    roleId: string;
  }) => {
    return apiRequest(getApiAddress + "user/detail", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addUserDetail: async (parms: BaseParams & {
    userId: string;
    pwd: string;
    jobTitle: string;
    userEmail: string;
    userEmail2: string;
    userExt: string;
    userPhone: string;
    orgId: string;
    userBirthday: Date | null;
    userGender: string;
    origUnit: string;
    roleId: string;
    entryDate: Date | null;
    userNameList: EhsUserName[];
  }) => {
    return apiRequest(getApiAddress + "user/add", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  editUserDetail: async (parms: BaseParams & {
    userId: string;
    jobTitle: string;
    userEmail: string;
    userEmail2: string;
    userExt: string;
    userPhone: string;
    orgId: string;
    userBirthday: Date | null;
    userGender: string;
    origUnit: string;
    roleId: string;
    entryDate: Date | null;
    userNameList: EhsUserName[];
  }) => {
    console.log("🚀 ~ entryDate:", parms.entryDate);

    return apiRequest(getApiAddress + "user/edit", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  changeUserStatus: async (parms: BaseParams & {
    userId: string;
    userStatus: string;
  }) => {
    return apiRequest(getApiAddress + "user/update/status", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};
