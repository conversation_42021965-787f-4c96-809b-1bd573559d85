import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
interface BackButtonProps<T> {
    condition?: T;
    fromPath?: string; // 如果需要
}

const BackButton = <T,>({ condition, fromPath }: BackButtonProps<T>) => {
    const { t } = useTranslation();
    const navigate = useNavigate();

    return (
        <button
            type="button"
            className="btn btn-secondary fs-5"
            title={t('button.back')}
            onClick={() => {
                if (condition && fromPath) {
                    navigate(fromPath, { state: { condition } }); // 帶上 condition 
                } else {
                    navigate(-1); // 沒有 condition 時的預設行為
                }
            }}
        >
            <i className="fas fa-arrow-left"></i>
            &nbsp;{t('button.back')}
        </button>
    );
};

export default BackButton;
