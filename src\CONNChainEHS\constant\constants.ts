import { OperateType } from "ehs/enums/OperateType";

// ----- web_config -----
export const WEB_IS_ONLINE = false;
export const WEB_IS_TEST_CUSTOMER = false;
export const WEB_IS_TEST_UNIFORM_NUM = "34567890";
export const WEB_IS_TEST_API_BASE_URL = "https://iehsapi.connchain.net/ehsapi/api/";

export const GOOGLE_RECAPTCHA_KEY = "6LcvzoAfAAAAAN8wQcxSkMcp7pprfSyoNKZMcOE3";
export const SYSTEM_TITLE = "智能環安衛";
export const TIMEOUT_MINUTES = 120;

// ----- Ehs_Config_Param -----
export const CONFIG_TYPE_BARCODE_FIELD = "barcode_field";
export const CONFIG_TYPE_BUILD_TYPE = "build_type";
export const CONFIG_TYPE_CCB_PROCESS_TEMP = "ccb_process_temp";
export const CONFIG_TYPE_CHEM = "chem";
export const CONFIG_TYPE_CHEM_AREA_APPROVE_TYPE = "chem_area_approve_type";
export const CONFIG_TYPE_CHEM_STATUS = "chem_status";
export const CONFIG_TYPE_CHEM_STORAGE_LOCATION = "chem_storage_location";
export const CONFIG_TYPE_CHEM_TEMPRATURE_TYPE = "chem_temprature_type";
export const CONFIG_TYPE_CHEM_MIXTURE_TYPE = "chem_mixture_type";
export const CONFIG_TYPE_CHEM_CLASS_LV = "chem_class_lv";
export const CONFIG_TYPE_CHEM_CLASS_RULE_TYPE = "chem_class_rule_type";
export const CONFIG_TYPE_CHEM_CLASS_RULE_COMPARE = "chem_class_rule_compare";
export const CONFIG_TYPE_CONCEN_TYPE = "concen_type";
export const CONFIG_TYPE_CONCERNED = "concerned";
export const CONFIG_TYPE_CTRL_CONCEN_TYPE = "ctrl_concen_type";
export const CONFIG_TYPE_CTRL_TOTAL_QTY_TYPE = "ctrl_total_qty_type";
export const CONFIG_TYPE_CITY = "city";
export const CONFIG_TYPE_FLOOR_DESC_TYPE = "floor_desc_type";
export const CONFIG_TYPE_GENDER = "gender";
export const CONFIG_TYPE_GHS_CLASS = "ghs_class";
export const CONFIG_TYPE_GHS_IMG = "ghs_img";
export const CONFIG_TYPE_JOBTITLE = "jobtitle";
export const CONFIG_TYPE_LAB_STATUS = "lab_status";
export const CONFIG_TYPE_MIX_CONCEN_TYPE = "mix_concen_type";
export const CONFIG_TYPE_MIX_CONCEN_TYPE_SINGLE = "mix_con_type_single";
export const CONFIG_TYPE_MIX_CONCEN_TYPE_RANGE = "mix_con_type_range";
export const CONFIG_TYPE_NEWS = "news";
export const CONFIG_TYPE_NEWS_STATUS = "news_status";
export const CONFIG_TYPE_OPER_ITEM = "oper_item";
export const CONFIG_TYPE_OPER_TYPE = "oper_type";
export const CONFIG_TYPE_ORGANIC_SOLVENT = "organic_solvent";
export const CONFIG_TYPE_OSHMS = "ohsms_type";
export const CONFIG_TYPE_PACKTYPE = "packType";
export const CONFIG_TYPE_PACKMAL = "packMal";
export const CONFIG_TYPE_POWDER_LEVEL = "powder_level";
export const CONFIG_TYPE_PRIORITY = "priority";
export const CONFIG_TYPE_PUBLIC_HAZARD = "public_hazard";
export const CONFIG_TYPE_PURCHASE_STATUS = "purchase_status";
export const CONFIG_TYPE_SPECIFIC_CHEM = "specific_chem";
export const CONFIG_TYPE_STATE = "state";
export const CONFIG_TYPE_TOXIC = "toxic";
export const CONFIG_TYPE_UNIT = "unit";
export const CONFIG_TYPE_WORK_ENV_MONITOR = "work_env_monitor";
export const CONFIG_TYPE_WORK_ENV_MONITOR_FREQ = "work_env_monitor_freq";
export const CONFIG_SUB_TYPE_TOP = "top";
export const CONFIG_SUB_TYPE_CCB_GROUP_S = "ccb_group_s";
export const CONFIG_SUB_TYPE_CCB_SECURITY_ENV = "ccb_security_env";
export const CONFIG_VAL_CHEM = "chem";
export const CONFIG_VAL_TOXIC = "toxic";
export const CONFIG_VAL_CONCERNED = "concerned";
export const CONFIG_VAL_GHS = "ghs";
export const CONFIG_VAL_PRIORITY = "priority";
export const CONFIG_VAL_INDUSTRIAL_PRECHEM_A = "Industrial_prechem_A";
export const CONFIG_VAL_INDUSTRIAL_PRECHEM_B = "Industrial_prechem_B";
export const CONFIG_VAL_CONTROLLED = "controlled";
export const CONFIG_VAL_UNCATEGORIZED = "uncategorized";
export const CONFIG_VAL_CHEM_WEAP_CTRL_DRUGS = "ChemWeapCtrlDrugs";
export const CONFIG_VAL_NEW_CHEM = "newchem";
export const CONFIG_VAL_PUBLIC_HAZARD = "public_hazard";
export const CONFIG_VAL_OTHER = "other";
export const CONFIG_ID_START_PUB_HAZ = "pub_haz";
export const CONFIG_ID_CONCEN_TYPE_SINGLE = "concen_type_single";
export const CONFIG_ID_CONCEN_TYPE_RANGE = "concen_type_range";
export const CONFIG_ID_CONCEN_TYPE_ABOVE = "concen_type_above";
export const CONFIG_ID_CONCEN_TYPE_BELOW = "concen_type_below";
export const CONFIG_ID_CONCEN_TYPE_ALL = "concen_type_all";
export const CONFIG_ID_CHEM_CLASS_LV_CTRL = "chem_class_lv_ctrl";
export const CONFIG_ID_CHEM_CLASS_LV_GENERAL = "chem_class_lv_gener";
export const CONFIG_ID_CHEM_CLASS_LV_SPEC1 = "chem_class_lv_spec1";
export const CONFIG_ID_GENDER_FEMALE = "gender_female";

// ----- Ehs_Options -----
export const OPTION_MEMBER_TYPE = "member_type";
export const OPTION_UNIT_TYPE = "unit_type";
export const OPTION_NEED_APPROVAL_QTY_TYPE = "need_approval_qty_type";
export const OPTION_NOTIFY_PURCHASE_RECIPIENT = "notify_purchase_recipient";
export const OPTION_NOTIFY_PURCHASE_RECIPIENT_TYPE =
  "notify_purchase_recipient_type";
export const OPTION_CHEM_INFO_MODIFY_FIRST = "chem_info_modify_first";
export const OPTION_CHEM_CLASSIFY_MODE = "chem_classify_mode";
export const OPTION_CHEM_STOP_OPER_MONTHS_CTRL = "stop_oper_months_ctrl";
export const OPTION_CHEM_STOP_OPER_MONTHS_SPEC1 = "stop_oper_months_spec1";
export const OPTION_CHEM_STOP_OPER_MONTHS_GENER = "stop_oper_months_gener";
export const OPTION_CHEM_REPORT_MONTH_START_DF = "chem_report_month_start_df";
export const OPTION_CHEM_REPORT_MONTH_LONG = "chem_report_month_long";
export const OPTION_CHEM_REPORT_MONTH_EARLIEST = "chem_report_month_earliest";
export const OPTION_CHEM_ITEM_ADD_PURCHASE = "chem_item_add_purchase";
export const OPTION_CHEM_ITEM_ADD_INCREASE = "chem_item_add_increase";
export const OPTION_CHEM_ITEM_ADD_INITIAL = "chem_item_add_initial";
export const OPTION_CHEM_ADD_SUBSTANCE_MAX_LIMIT = "add_substance_max_limit";
export const OPTION_LAB_STATUS_ROLE_LV_EDIT = "edit_lab_status_lv";
export const OPTION_SHOW_BUILDING_TYPE = "show_building_type";
export const OPTION_SHOW_USER_BIRTHDAY_AGE = "show_user_birthday_age";
export const OPTION_SHOW_SEARCH_UNIT = "show_search_unit";
export const OPTION_SHOW_SEARCH_BUILDING = "show_search_building";
export const OPTION_SHOW_SEARCH_ADVANCE = "show_search_advance";
export const OPTION_SUBTYPE_ARRIVAL = "arrival";
export const OPTION_SUBTYPE_STOP_OPER_MONTHS = "stop_oper_months";
export const OPTION_USE_AREA = "use_area";
export const OPTION_VAL_ENABLE = "1";
export const OPTION_VAL_NEED_APPROVAL_QTY_TYPE_LAB = "1";
export const OPTION_VAL_NEED_NOTIFY_PURCHASE_UNIT = "1";
export const OPTION_VAL_CHEM_CLASSIFY_MODE_STRICT = "1";
export const OPTION_VAL_SCHOOL = "S";
export const OPTION_VAL_COMPANY = "C";
export const OPTION_VAL_GOVERNMENT = "G";

// ----- Ehs_File -----
export const FILE_TYPE_AREA_CHEMICAL_APPROVAL_INFO = "area_chemical_approval_info";
export const FILE_TYPE_AREA_ALL_BUILDING_IMAGE = "area_all_builing_image";
export const FILE_TYPE_BUILD_RESCUE_IMAGE_CLASS_A = "build_rescue_image_a";
export const FILE_TYPE_BUILD_FLOOR_PLAN = "build_floor_plan";
export const FILE_TYPE_BUILD_FLOOR_RESCUE_IMAGE = "build_floor_rescue_image_b";
export const FILE_TYPE_BUILD_FLOOR_HAZARD_CARD = "build_floor_hazard_card";
export const FILE_TYPE_CCB_FORM = "ccb_form";
export const FILE_TYPE_INFORMATION_NEWS = "informataion_news";
export const FILE_TYPE_LAB = "lab";
export const FILE_TYPE_NEWS = "news";
export const FILE_TYPE_OSHMS = "oshms";
export const FILE_TYPE_PURCHASE_DETAIL_SDS = "purchase_detail_sds";
export const FILE_SUB_TYPE_LAB_FLOORPLAN = "lab_floorplan";

// ----- Ehs_Language -----
export const LANGUAGE_MAPPING_TYPE_AREA = "area";
export const LANGUAGE_MAPPING_TYPE_BUILDING = "building";
export const LANGUAGE_MAPPING_TYPE_ORG = "org";
export const LANGUAGE_MAPPING_TYPE_ORGLEVEL = "orglevel";
export const LANGUAGE_MAPPING_TYPE_ROLE = "role";
export const LANGUAGE_MAPPING_TYPE_LAB = "lab";
export const LANGUAGE_MAPPING_TYPE_USER = "user";

// ----- Ehs_Lab -----
export const LAB_STATUS_CAN_OPERATION = [1, 2];

// ----- Ehs_Role -----
export const ROLE_CAN_MODIFY_MIN_LEVEL = 2;
export const ROLE_MANAGER_MAX_LEVEL = 3;
export const ROLE_LAB_MAX_LEVEL = 7;
export const ROLE_MIN_LEVEL = 11;

// ----- Ehs_Rou -----
export const ROU_ALL = "all";
export const ROU_TYPE_LAB = "L";
export const ROU_TYPE_ORG = "O";

// ----- chemical -----
export const CHEMICAL_WEIGHT_DECIMAL_PLACES = 10; //小數點位數
export const CHEMICAL_WEIGHT_MAX = 999999;
export const CHEMICAL_WEIGHT_MIN = 0.0000000001;
export const CHEMICAL_CONCEN_DECIMAL_PLACES = 6; //小數點位數
export const CHEMICAL_CONCEN_MAX = 100;
export const CHEMICAL_CONCEN_MIN = 0.000001;
export const CHEMICAL_CONCEN_TYPE_RANGE = 2;
export const CHEMICAL_CONCEN_TYPE_ALL = 5;
export const CHEMICAL_TEMPERATURE_DECIMAL_PLACES = 3; //小數點位數
export const CHEMICAL_TEMPERATURE_MAX = 9999;
export const CHEMICAL_TEMPERATURE_MIN = 0.001;
export const CHEMICAL_TEMPERATURE_BOILING = "boiling";
export const CHEMICAL_TEMPERATURE_MELTING = "melting";
export const CHEMICAL_PHASE_SOLID = "solid";
export const CHEMICAL_PHASE_LIQUID = "Liquid";
export const CHEMICAL_STATUS_ENABLE = 1;
export const CHEMICAL_NEW_CHEM_CON_ID_PREFIX = "new";
export const CHEMICAL_OPER_ITEM_NO_WEIGHT = [OperateType.DISPOSAL, OperateType.NO_CHANGE, OperateType.DISABLE];
export const CHEMICAL_OPER_ITEM_NO_DATE = [OperateType.DISABLE];
export const CHEMICAL_CASNO_MAX_LENGTH = 13;
export const CHEMICAL_CTRL_TOTAL_QTY_TYPE_NONE = 0;
export const CHEMICAL_CTRL_TOTAL_QTY_TYPE_REQ = 1;
export const CHEMICAL_CTRL_TOTAL_QTY_TYPE_ANNC = 2;
export const CHEMICAL_CONCEN_CTRL_NONE = 0;
export const CHEMICAL_CONCEN_CTRL_ALL = 5;

// ----- purchase -----
export const PURCHASE_QTY_LIMIT_MAX = 100;

// ----- purchase_detail -----
export const PURCHASE_DETAIL_STATUS_CANCEL = "cancel";
export const PURCHASE_DETAIL_STATUS_RETURN_GOODS = "return_goods";
export const PURCHASE_DETAIL_STATUS_INSPECTION_COMPLETE = "inspection_complete";
export const PURCHASE_DETAIL_STATUS_PENDING_INSPECTION = "pending_inspection";
export const PURCHASE_DETAIL_STATUS_PURCHASING = "purchasing";

// ----- page -----
export const PAGE_SOURCE = {
  SIGN_OFF_PURCHASE_LIST: "purchaseListSignOff",
  CHEMICAL_PURCHASE_LIST: "purchaseListChemical"
} as const;

// 定義 type 方便使用
export type PageSource = typeof PAGE_SOURCE[keyof typeof PAGE_SOURCE];

// ----- sort direction -----
export const SORT_DIRECTION = {
  ASC: 'asc',
  DESC: 'desc'
} as const;

export type SortDirection = typeof SORT_DIRECTION[keyof typeof SORT_DIRECTION];

// ----- news -----
export const NEWS_STATUS_SHOW = 1;
export const NEWS_PREVIEW_MAX_RESULT = 5;

// ----- storage -----
export const STORAGE_USER_PREFERENCES = "userPreferences";
export const STORAGE_PAGE_LANGUAGE = "pageLanguage";
export const STORAGE_AUTH_MESSAGE = "authMessage";
export const STORAGE_USAGE_POLICY_AGREED = "usagePolicyAgreed";

// ----- 顯示單位分隔換行代號 -----
export const ORG_SPLIT_FLAG = "[spt]";

// ----- upload file type flag -----
export const UPLOAD_FILE_TYPE_IMAGE = "image";
export const UPLOAD_FILE_TYPE_PDF = "pdf";

// ----- upload file size -----
export const UPLOAD_FILE_Size_PDF = 10;
export const UPLOAD_FILE_Size_IMAGE = 1;

// ----- upload accept type -----
export const UPLOAD_ACCEPT_TYPE_IMAGE = ".jpg,.jpeg,.png";
export const UPLOAD_ACCEPT_TYPE_PDF = ".pdf";
export const UPLOAD_ACCEPT_TYPE_IMAGE_PDF = ".jpg,.jpeg,.png,.pdf";

// ----- date format -----
export const MONTH_FORMAT_SLASH = "yyyy/MM";
export const DATE_FORMAT_SLASH = "yyyy/MM/dd";
export const DATE_FORMAT_DASH = "yyyy-MM-dd";
export const TIME_FORMAT_SLASH = "yyyy/MM/dd HH:mm:ss";
export const ISO_TIME_FORMAT_DASH = "yyyy-MM-dd'T'HH:mm:ss";

// -----search method -----
export const SEARCH_METHOD_EXACT = "exact_match";
export const SEARCH_METHOD_FUZZY = "fuzzy";

// -----search limit qty -----
export const SEARCH_LIMIT_QTY_CHEM_ITEM_ADD = 200;

// ----- TOSHMS -----
export const TOSHMS_DOWNLOAD_ENABLED = "1";
export const TOSHMS_DOWNLOAD_NOT_ENABLED = "0";
export const TOSHMS_FILE_TYPE = "file";
export const TOSHMS_LEVEL_ROOT= 1;
export const TOSHMS_MANAGE_PAGE = "manage";
export const TOSHMS_PARENT_ID_ROOT = "0";
export const TOSHMS_STATUS_ENABLED= "1";
export const TOSHMS_STATUS_NOT_ENABLED = "0";

// -----pwd limit -----
export const PWD_MIN_LENGTH = 8;

// -----devtool navigation url -----
export const DEVTOOL_NAVIGATION_URL = "https://www.connchain.net/";

// -----react query key -----
export const REACT_QUERY_CURRENT_USER = "currentUser";
export const REACT_QUERY_USER_MENU = "userMenu";

// ----- usage policy text -----
export const USAGE_POLICY_TEXT = `壹　　一般聲明
一　本網站「iEHS智能環安衛」資訊，由本公司自行開發，資料係自各級政府機關蒐集而來，若與各主管機關公布之文字有所不同，仍以各主管機關公布之資料為準。
二　非經本公司正式書面同意，不得下載、重製本網站之內容他用。

貳　　權利聲明

一　標的
「iEHS智能環安衛」之內容，包括（但不限於）本網站內所有之影像、圖片、動畫、視訊、音效、音樂、文字、資料以及全部應用程式...等。
二　權利保護
「iEHS智能環安衛」之著作權及其他權利，均受著作權法、公平交易法或其他法令，和國際著作權條約及其他相關法令與條約之保護。
三　使用權限
「iEHS智能環安衛」僅限於在電腦、手機、平板...等載具，利用瀏覽器使用本網站所提供之功能查詢、檢閱或列印在紙上，但不包括以下權限：

    將「iEHS智能環安衛」之內容列印、存檔、複製、剪貼，不論是否經過編輯，進一步利用於其他印刷物或電子媒體。但僅引用為論文、文件之內容或為該等文件附件之一部分，以作為其法律見解之說明者，不在此限。
    將「iEHS智能環安衛」之內容，重製於磁碟、光碟、硬碟、電子儲存媒體或其他載體，有償或無償提供自己或他人使用。
    以程式或自動化方式執行查詢、下載、擷取內容…等方式，使用本網站。
    將「iEHS智能環安衛」之內容，載入自行或他人開發之系統中，充作資料庫之部分或全部。

四　使用授權

    除非事先經過「雲集科技行銷有限公司」以正式書面特別同意，否則禁止對「iEHS智能環安衛」所提供之資料重製、販售、出租、互易、出借、散布、出版、改作、改篡割裂、公開展示、公開傳輸及其他方式對外公佈其內容或為其他足以侵害「雲集科技行銷有限公司」權益之行為。
    使用者若要求下載、重製或其他使用授權，請洽雲集科技行銷有限公司。
    
免責聲明：
本化學品分類查詢結果係本公司依據相關政府機關發布之法規及資料所進行之彙整與判斷，僅供參考之用。使用者應自行判斷其適用性，並自行承擔使用本查詢結果所生之一切風險。本公司對於使用者因直接或間接依據本查詢結果所為之任何行為、或因此所致之損害、損失或法律責任，概不負責。`;