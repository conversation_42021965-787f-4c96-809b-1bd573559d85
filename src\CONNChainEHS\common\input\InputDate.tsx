import { isAfter, isBefore } from 'date-fns';
import { enUS, vi, zhTW } from "date-fns/locale";
import { DATE_FORMAT_SLASH, MONTH_FORMAT_SLASH } from 'ehs/constant/constants';
import { Language } from 'ehs/enums/Language';
import PropTypes from 'prop-types';
import React, { useEffect, useRef, useState } from 'react';
import DatePicker, { CalendarContainer, registerLocale } from 'react-datepicker';
import { useTranslation } from 'react-i18next';

interface InputDateProps {
    defaultValue?: string;
    disabled?: boolean;
    minDate?: string;
    maxDate?: string;
    className?: string;
    readOnly?: boolean;
    isClearable?: boolean;
    showMonthYearPicker?: boolean;
    onChange?: (date: Date | null) => void;
    onClick?: (e: React.MouseEvent<HTMLInputElement>) => void;
}

const InputDate: React.FC<InputDateProps> = ({
    defaultValue, //預設值
    disabled, //是否禁用
    minDate, //最小日期
    maxDate, //最大日期
    className, //自定義類名
    readOnly, //是否只讀
    isClearable = true, //是否可清除
    showMonthYearPicker = false, //是否顯示月份選擇器
    onChange, //輸入日期時的事件
    onClick, //點擊日期時的事件
}) => {
    const { t, i18n } = useTranslation();
    const [startDate, setStartDate] = useState<Date | null>(null);
    const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
    const [parsedMinDate, setParsedMinDate] = useState<Date | null>(null);
    const [parsedMaxDate, setParsedMaxDate] = useState<Date | null>(null);
    const [isTodayInRange, setIsTodayInRange] = useState<boolean>(false);
    const yearSelectRef = useRef<HTMLSelectElement>(null);
    const monthSelectRef = useRef<HTMLSelectElement>(null);
    const [years, setYears] = useState<number[]>([]);
    const range = (start: number, end: number, step: number): number[] => {
        const result: number[] = [];
        for (let i = start; i < end; i += step) {
            result.push(i);
        }
        return result;
    };

    type MonthsObj = { [key: number]: string };
    const monthsObj: MonthsObj = {
        0: t('text.month.jan'),
        1: t('text.month.feb'),
        2: t('text.month.mar'),
        3: t('text.month.apr'),
        4: t('text.month.may'),
        5: t('text.month.jun'),
        6: t('text.month.jul'),
        7: t('text.month.aug'),
        8: t('text.month.sep'),
        9: t('text.month.oct'),
        10: t('text.month.nov'),
        11: t('text.month.dec'),
    };

    // 篩選可選擇的月份
    const availableMonths = Object.keys(monthsObj).filter((key) => {
        const monthIndex = parseInt(key);
        // 檢查是否超出最小日期的範圍
        if (parsedMinDate && parsedMinDate.getFullYear() === selectedYear) {
            if (monthIndex < parsedMinDate.getMonth()) {
                return false;
            }
        }
        // 檢查是否超出最大日期的範圍
        if (parsedMaxDate && parsedMaxDate.getFullYear() === selectedYear) {
            if (monthIndex > parsedMaxDate.getMonth()) {
                return false;
            }
        }
        return true;
    });

    //預設值
    useEffect(() => {
        if (defaultValue) {
            // 解析defaultValue為Date對象
            setStartDate(new Date(defaultValue));
        }
    }, [defaultValue]);

    //轉換最小日
    useEffect(() => {
        if (minDate) {
            const parsedDate = new Date(minDate);
            setParsedMinDate(parsedDate);
        } else {
            setParsedMinDate(null);
        }
    }, [minDate]);

    //轉換最大日
    useEffect(() => {
        if (maxDate) {
            const parsedDate = new Date(maxDate);
            setParsedMaxDate(parsedDate);
        } else {
            setParsedMaxDate(null);
        }
    }, [maxDate]);

    // 判斷今天按鈕是否在合法的限制範圍
    useEffect(() => {
        const today = new Date();
        if (parsedMinDate && parsedMaxDate) {
            setIsTodayInRange(today >= parsedMinDate && today <= parsedMaxDate);
        } else if (parsedMinDate) {
            setIsTodayInRange(today >= parsedMinDate);
        } else if (parsedMaxDate) {
            setIsTodayInRange(today <= parsedMaxDate);
        } else {
            setIsTodayInRange(true);
        }
    }, [parsedMinDate, parsedMaxDate]);

    // 生成年份範圍 
    useEffect(() => {
        const currentYear: number = new Date().getFullYear();
        let startYear = currentYear - 100;
        let endYear = currentYear + 11;
        if (parsedMinDate) {
            startYear = Math.max(startYear, parsedMinDate.getFullYear());
        }
        if (parsedMaxDate) {
            endYear = Math.min(endYear, parsedMaxDate.getFullYear() + 1);
        }
        setYears(range(startYear, endYear, 1));
    }, [parsedMinDate, parsedMaxDate]);

    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
        // 阻止使用者通過鍵盤輸入來更改日期
        event.preventDefault();
    };

    const dayClassName = (date: Date | null): string | null => {
        if (!date) {
            return null; // 如果 date 為 null，返回 null
        }
        if ((parsedMinDate && isBefore(date, parsedMinDate)) ||
            (parsedMaxDate && isAfter(date, parsedMaxDate))) {
            return "bg-gray-500"; // 超出範圍的日期添加 CSS 類名
        }
        return null; // 在範圍內的日期不添加 CSS 類名
    };

    interface MyContainerProps {
        className?: string;
        children: React.ReactNode;
    }

    const MyContainer: React.FC<MyContainerProps> = ({ className, children }) => {
        return (
            <div style={{ padding: "10px", background: "#348fe2", color: "#fff" }}>
                <CalendarContainer className={className}>
                    <div style={{ position: "relative" }}>{children}</div>
                </CalendarContainer>
            </div>
        );
    };

    registerLocale(Language.ZHTW, zhTW);
    registerLocale(Language.ENUS, enUS);
    registerLocale(Language.VIVN, vi);
    return (
        <DatePicker
            locale={i18n.language}
            selected={startDate}
            dateFormat={showMonthYearPicker ? MONTH_FORMAT_SLASH : DATE_FORMAT_SLASH}
            todayButton={isTodayInRange && (showMonthYearPicker ? t('button.thisMonth') : t('button.today'))}
            placeholderText={showMonthYearPicker ? t('message.format.select_month') : t('message.format.select_date')}
            className={className}
            calendarContainer={MyContainer}
            disabled={disabled}
            readOnly={readOnly}
            minDate={parsedMinDate}
            maxDate={parsedMaxDate}
            popperProps={{ strategy: 'fixed' }}
            showMonthYearPicker={showMonthYearPicker}
            isClearable={isClearable}
            onChange={(date) => {
                setStartDate(date)
                if (onChange) {
                    onChange(date);
                }
            }}
            onKeyDown={handleKeyDown}
            dayClassName={dayClassName}
            renderCustomHeader={({
                date,
                changeYear,
                changeMonth,
                decreaseMonth,
                increaseMonth,
                prevMonthButtonDisabled,
                nextMonthButtonDisabled,
            }) => (
                <div
                    style={{
                        margin: 10,
                        display: "flex",
                        justifyContent: "center",
                    }}
                >
                    <select className='form-select'
                        ref={yearSelectRef}
                        value={new Date(date).getFullYear()}
                        onChange={({ target: { value } }) => {
                            const year = parseInt(value);
                            setSelectedYear(year);
                            changeYear(year)
                        }}
                    >
                        {years.map((option) => (
                            <option key={option} value={option}>
                                {option}
                            </option>
                        ))}
                    </select>
                    <span className='mx-1 mt-2 fs-5'>{t('text.year')}</span>
                    <br />
                    <button type="button" className='btn btn-blue mx-1' onClick={(e) => {
                        decreaseMonth();
                        const year = parseInt(yearSelectRef.current?.value || '');
                        const month = parseInt(monthSelectRef.current?.value || '');
                        if (month === 0) {
                            setSelectedYear(year - 1);
                        }
                    }} disabled={prevMonthButtonDisabled}>
                        {"<"}
                    </button>
                    <select className='form-select w-auto'
                        ref={monthSelectRef}
                        value={new Date(date).getMonth()}
                        onChange={({ target: { value } }) =>
                            changeMonth(parseInt(value))
                        }
                    >
                        {availableMonths.map((key) => (
                            <option key={key} value={key}>
                                {monthsObj[parseInt(key)]}
                            </option>
                        ))}
                    </select>
                    <button type="button" className='btn btn-blue mx-1' onClick={(e) => {
                        increaseMonth();
                        const year = parseInt(yearSelectRef.current?.value || '');
                        const month = parseInt(monthSelectRef.current?.value || '');
                        if (month === 11) {
                            setSelectedYear(year + 1);
                        }
                    }} disabled={nextMonthButtonDisabled}>
                        {">"}
                    </button>
                </div>
            )}
        />
    );
};

InputDate.propTypes = {
    defaultValue: PropTypes.string,
    disabled: PropTypes.bool,
    minDate: PropTypes.string,
    maxDate: PropTypes.string,
    className: PropTypes.string,
    readOnly: PropTypes.bool,
    onChange: PropTypes.func,
};

InputDate.defaultProps = {
    defaultValue: '',
    disabled: false,
    minDate: undefined,
    maxDate: undefined,
    className: 'form-control',
    readOnly: false,
    onChange: undefined,
};

export default InputDate;
