import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { EhsPurchaseDetail } from "../CONNChainEHS/models/EhsPurchaseDetail";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const PurchaseAPI = {
  getPurchaseList: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "purchase/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getPurchaseDetail: async (
    parms: BaseParams & {
      purchaseId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "purchase/detail", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  checkPurchaseProduct: async (
    parms: BaseParams & {
      needLabApproval: boolean;
      ctrlTotalQtyType: number;
      chemId?: string;
      chemConId?: string;
      areaId: string;
      labId?: string;
    }
  ) => {
    return apiRequest(getApiAddress + "purchase/product/add/check", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getPurchaseRecipient: async (
    parms: BaseParams & {
      manufacturerId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "purchase/recipient", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  modifyPurchaseRecipient: async (
    parms: BaseParams & {
      userId: string;
      manufacturerId: string;
      accountIds: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "purchase/recipient/modify", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addPurchaseRequest: async (
    parms: BaseParams & {
      chemClassLv: string;
      manafacturerId: string;
      labId: string;
      note: string;
      productIdList: string[];
      quantityList: number[];
    }
  ) => {
    return apiRequest(getApiAddress + "purchase/add", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  checkPurchaseArrival: async (
    parms: BaseParams & {
      purchaseId: string;
      purchaseDetailIdList: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "purchase/arrival/check", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  checkPurchaseInspection: async (
    parms: BaseParams & {
      purchaseId: string;
      purchaseDetailIdList: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "purchase/inspection/check", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  checkPurchaseCancel: async (
    parms: BaseParams & {
      purchaseId: string;
      purchaseDetailIdList: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "purchase/cancel/check", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  checkPurchaseReturn: async (
    parms: BaseParams & {
      purchaseId: string;
      purchaseDetailIdList: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "purchase/return/check", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  updatePurchaseArrival: async (
    parms: BaseParams & {
      purchaseId: string;
      purchaseDetailIdList: string[];
      detailList: EhsPurchaseDetail[];
      note: string;
    }
  ) => {
    return apiRequest(getApiAddress + "purchase/arrival/update", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  updatePurchaseInspection: async (
    parms: BaseParams & {
      purchaseId: string;
      purchaseDetailIdList: string[];
      detailList: EhsPurchaseDetail[];
      note: string;
    }
  ) => {
    return apiRequest(getApiAddress + "purchase/inspection/update", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  updatePurchaseCancel: async (
    parms: BaseParams & {
      purchaseId: string;
      purchaseDetailIdList: string[];
      note: string;
    }
  ) => {
    return apiRequest(getApiAddress + "purchase/cancel/update", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  updatePurchaseReturn: async (
    parms: BaseParams & {
      purchaseId: string;
      purchaseDetailIdList: string[];
      note: string;
    }
  ) => {
    return apiRequest(getApiAddress + "purchase/return/update", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};
