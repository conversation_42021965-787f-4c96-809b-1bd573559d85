import { EhsOrgLevel } from '../models/EhsOrgLevel';
import { EhsOrg } from '../models/EhsOrg';
import { useTranslation } from 'react-i18next';
import { isArrayEmpty } from '../utils/arrayUtil';
import React from 'react';

interface FilterCondition {
    areaId?: string;
    // 如果有其他篩選條件，也可以在這裡定義
}

interface Props {
    orglv: EhsOrgLevel;
    orgList: EhsOrg[];
    selectOrgIds: Record<string, string>;
    setSelectOrgIds: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
    className?: string;
    labelClassName?: string;
    selectClassName?: string;
    componentStyle?: number;
    defaultOptionType?: number;
    defaultOptionName?: string;
    required?: boolean;
    filterCondition?: FilterCondition;
}

const SelectOrg: React.FC<Props> = ({ orglv, orgList, selectOrgIds, setSelectOrgIds,
    className, labelClassName, selectClassName, defaultOptionType, defaultOptionName, componentStyle, required, filterCondition = {} }) => {
    const { t } = useTranslation();
    const level = orglv.orglvLevel
    const isFirstLevel = level === 1;
    const selectParentOrgId = selectOrgIds[level - 1];
    let selectOrgId = selectOrgIds[level] || "";
    let optionList = selectParentOrgId
        ? orgList.filter(item => item.orgPid === selectParentOrgId)
        : orgList.filter(item => item.orglvId === orglv.orglvId);

    // 根據 filterCondition 進一步過濾 optionList
    if (filterCondition.areaId) {
        optionList = optionList.filter(item => item.areaId === filterCondition.areaId);
    }

    const noOption = isArrayEmpty(optionList);
    // 修改 isHide 的計算方式，使用布林值而非 CSS 類別
    const shouldHide = !isFirstLevel && (!selectParentOrgId || noOption);
    const hideClass = shouldHide ? 'd-none' : '';

    const onChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        const value = e.target.value;
        // 清空比當前 level 大的所有項目 且設置當前level 值
        const newSelectOrgIds = Object.keys(selectOrgIds)
            .filter(key => parseInt(key) < level)
            .reduce((acc, key) => {
                acc[key] = selectOrgIds[key];
                return acc;
            }, {} as Record<string, string>);
        newSelectOrgIds[level] = value;
        setSelectOrgIds(newSelectOrgIds);
    }

    const defaultOptions: Record<number, string> = {
        0: t('message.select'),
        1: t('text.all'),
    };

    const getDefaultOptionNameByType = (defaultOptionType: number) => {
        return defaultOptions[defaultOptionType] || defaultOptions[0];
    }

    if (componentStyle) {
        if (componentStyle === 1) {
            //標題排橫
            return (
                <React.Fragment key={"orglv-" + orglv.orglvId}>
                    <div className={`col-md-3 mb-3 ${hideClass}`}>{orglv.orglvName}{required && <span className="text-danger me-1">*</span>}</div>
                    <div className={`col-md-9 fs-5 mb-3 ${hideClass}`}>
                        {<select className={selectClassName || "form-select"} value={selectOrgId} onChange={(e) => onChange(e)}>
                            <option value="">{defaultOptionType ? getDefaultOptionNameByType(defaultOptionType) : defaultOptionName}</option>
                            {optionList.map(org => (
                                <option key={"orgopt-" + org.orgId} value={org.orgId}>{org.orgName}</option>
                            ))}
                        </select>
                        }
                    </div>
                </React.Fragment>
            );
        }
    }

    // 修改標準版本的渲染方式，確保響應式佈局正確
    return (
        <div className={`${className || ''} ${hideClass}`} key={"orglv-" + orglv.orglvId}>
            <label className={labelClassName || ''}>{orglv.orglvName}{required && <span className="text-danger me-1">*</span>}</label>
            <div className={selectClassName ? selectClassName.split(' ').filter(c => !c.startsWith('col-')).join(' ') : ''}>
                <select className="form-select w-100" value={selectOrgId} onChange={(e) => onChange(e)}>
                    <option value="">{defaultOptionType ? getDefaultOptionNameByType(defaultOptionType) : defaultOptionName}</option>
                    {optionList.map(org => (
                        <option key={"orgopt-" + org.orgId} value={org.orgId}>{org.orgName}</option>
                    ))}
                </select>
            </div>
        </div>
    );
};

export default SelectOrg;