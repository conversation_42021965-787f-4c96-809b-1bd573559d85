import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { CONFIG_SUB_TYPE_TOP, CONFIG_VAL_CHEM } from "../../../constant/constants";
import { ActionMode } from "../../../enums/ActionMode";
import useLoginUser from "../../../hooks/useLoginUser";
import { EhsConfigParam } from "../../../models/EhsConfigParam";
import { isArrayEmpty } from "../../../utils/arrayUtil";
import { isCtrlChemClassify } from "../../../utils/chemicalUtil";

function LabModifyAttr(props: {
  onClose: () => void;
  onActionSuccess: () => void;
  mode: ActionMode | null;
  labAttributes: EhsConfigParam[];
  checkLabAttributesMap: { [key: string]: string };
  setCheckLabAttributesMap: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
}) {
  const { onClose, onActionSuccess, mode, labAttributes, checkLabAttributesMap, setCheckLabAttributesMap } = props;
  const { t } = useTranslation();
  const { loginUser } = useLoginUser();
  const [labAttrMap, setLabAttrMap] = useState<Record<string, EhsConfigParam[]>>({});
  const [checkLabAttributesTempMap, setCheckLabAttributesTempMap] = useState<{ [key: string]: string }>({});
  const topLabAttrList = labAttrMap[CONFIG_SUB_TYPE_TOP];
  const chemItem = topLabAttrList?.find(item => item.configValue === CONFIG_VAL_CHEM);
  const chemConfigId = chemItem ? chemItem.configId : "";

  useEffect(() => {
    if (labAttributes && !isArrayEmpty(labAttributes)) {
      const updatedLabAttrMap: Record<string, EhsConfigParam[]> = {};
      // 遍歷原始數組，將對象按照 configSubType 分組
      labAttributes.forEach(param => {
        // 如果已存在該 configSubType 的鍵，則將當前對象添加到對應的數組中
        if (updatedLabAttrMap[param.configSubType]) {
          updatedLabAttrMap[param.configSubType].push(param);
        } else {
          // 如果不存在，則創建一個新數組，並將當前對象添加進去
          updatedLabAttrMap[param.configSubType] = [param];
        }
      });
      // 更新 labAttrMap 狀態
      setLabAttrMap(updatedLabAttrMap);
      setCheckLabAttributesTempMap(checkLabAttributesMap);
    }
  }, [labAttributes]);

  const modifyFinish = () => {
    onActionSuccess();
    setCheckLabAttributesMap(checkLabAttributesTempMap);
  }

  return (
    <StlyedLabModifyAttr >
      <div className="modifyLabAttr">
        <div className="modal-header">
          <h4 className="modal-title">{t('button.edit')}</h4>
          <button type="button" className="btn-close" aria-hidden="true" onClick={onClose}></button>
        </div>
        <div className="modal-body">
          {labAttrMap && topLabAttrList &&
            topLabAttrList.map((attr) => (
              <div key={attr.configId}>
                <div className="row justify-content-center mb-3">
                  <div className="col-md-4">
                    <div className="form-check">
                      <input className="form-check-input" type="checkbox" id={attr.configId} checked={checkLabAttributesTempMap.hasOwnProperty(attr.configId)} onChange={() => {
                        setCheckLabAttributesTempMap(prevState => {
                          // 複製原本的狀態
                          const newState = { ...prevState };
                          // 如果已經存在則移除，否則添加
                          if (newState.hasOwnProperty(attr.configId)) {
                            delete newState[attr.configId];
                            Object.entries(newState).forEach(([key, value]) => {
                              // 如果值等於 attr.configId，則刪除這個鍵值對
                              if (value === attr.configId) {
                                delete newState[key];
                              }
                            });
                          } else {
                            newState[attr.configId] = attr.configSubType;
                          }
                          return newState;
                        });
                      }} />
                      <label className="form-check-label" htmlFor={attr.configId}>{attr.configName}</label>
                    </div>
                  </div><div className="col-md-5 d-flex flex-wrap">
                    {checkLabAttributesTempMap.hasOwnProperty(attr.configId) &&
                      labAttrMap[attr.configId] &&
                      labAttrMap[attr.configId].map((subAttr, index) => (
                        <div key={subAttr.configId}>
                          <div className="form-check me-3">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              id={subAttr.configId}
                              checked={checkLabAttributesTempMap.hasOwnProperty(subAttr.configId)}
                              onChange={() => {
                                setCheckLabAttributesTempMap((prevState) => {
                                  // 複製原本的狀態
                                  const newState = { ...prevState };
                                  // 如果已經存在則移除，否則添加
                                  if (newState.hasOwnProperty(subAttr.configId)) {
                                    delete newState[subAttr.configId];
                                  } else {
                                    newState[subAttr.configId] = subAttr.configSubType;
                                    if (isCtrlChemClassify.includes(subAttr.configValue)) {
                                      //勾列管屬性 化學性下的要全勾
                                      labAttrMap[chemConfigId].filter((chemAttr) => chemAttr.configSeq > subAttr.configSeq).forEach((chemAttr) => {
                                        newState[chemAttr.configId] = chemAttr.configSubType;
                                      });
                                    }
                                  }
                                  return newState;
                                });
                              }}
                            />
                            <label className="form-check-label" htmlFor={subAttr.configId}>{subAttr.configName}</label>
                          </div>
                          {(index + 1) % 5 === 0 && <div className="w-100 mt-1"></div>}
                        </div>
                      ))}
                  </div>

                </div>
                <hr />
              </div>
            ))
          }
        </div>
        <div className="modal-footer">
          <div className="btn btn-white" aria-hidden="true" onClick={onClose}>
            <i className="fas fa-times me-1" />
            {t("button.close")}
          </div>
          <div className="btn btn-warning" onClick={modifyFinish}>
            <i className="fas fa-cubes" /> {t('button.edit')}
          </div>
        </div>
      </div>
    </StlyedLabModifyAttr>
  );
}


const StlyedLabModifyAttr = styled.div`
  font-size: 1.2em;
  background: white;
  width: 1250px; 
  label{
    user-select: none;
  }
  .modal-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  .modal-body {
    padding: 15px;
  }
  .modal-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-warning {
      margin-left: 10px;
    }
  }
  
  @media (max-width: 1024px) {
      width: 100%;  // 在小螢幕時使用 100% 寬度
      max-width: 100%;
      height: 100%;
      
      .modal-body {
          flex: 1;
          overflow-y: auto;
          padding-bottom: 20px; // 添加底部間距，避免內容被 footer 遮擋
          max-height: 700px;
      }
      
      .modal-footer {
          position: sticky;
          bottom: 0;
          background: white;
          padding: 15px;
          border-top: 1px solid #ced4da;
          display: flex;
          justify-content: center;
      }
  }
`;

export default LabModifyAttr;
