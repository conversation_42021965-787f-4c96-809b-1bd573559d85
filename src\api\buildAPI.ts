import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { EhsBuildingFloorInfo } from "../CONNChainEHS/models/EhsBuildingFloorInfo";
import { EhsLanguage } from "../CONNChainEHS/models/EhsLanguage";
import { EhsMultipleDetail } from "../CONNChainEHS/models/EhsMultipleDetail";
import { appendToFormData } from "../CONNChainEHS/utils/formDataUtil";
import { apiRequest, createLoginPostFormDataConfig, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const BuildAPI = {
  getBuildList: async (
    parms: BaseParams & {
      areaId: string;
      buildType?: number | null;
      currentPage: number;
      pageSize: number;
    }
  ) => {
    return apiRequest(getApiAddress + "build/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getBuildDetail: async (
    parms: BaseParams & {
      buildId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "build/detail", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getBuildFloorInfoList: async (
    parms: BaseParams & {
      buildId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "build/floor/info/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  editBuildStatus: async (
    parms: BaseParams & {
      buildId: string;
      buildStatus: number;
    }
  ) => {
    return apiRequest(getApiAddress + "build/status/edit", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addBuild: async (
    parms: BaseParams & {
      areaId: string;
      buildNo: string;
      buildName: string;
      buildFloors: number;
      buildUnfloors: number;
      rescueImageClassA: File;
      buildNameList: EhsLanguage[];
    }
  ) => {
    const formData = new FormData();
    const customKey = ["rescueImageClassA"];
    const jsonParams = Object.keys(parms).reduce((acc, key) => {
      if (!customKey.includes(key)) {
        acc[key] = (parms as any)[key]; // 使用類型斷言
      }
      return acc;
    }, {} as any);

    formData.append("jsonString", JSON.stringify(jsonParams));
    if (parms.rescueImageClassA) {
      formData.append("rescueImageClassA", parms.rescueImageClassA);
    }
    return apiRequest(getApiAddress + "build/add", createLoginPostFormDataConfig(parms, formData)).then((res) => handleFetchResponse(res));
  },
  editBuild: async (
    parms: BaseParams & {
      areaId: string;
      buildNo: string;
      buildName: string;
      buildFloors: number;
      buildUnfloors: number;
      rescueImageClassA: File;
      buildNameList: EhsLanguage[];
      delFileIdList: string[];
    }
  ) => {
    const formData = new FormData();
    const customKey = ["rescueImageClassA"];
    const jsonParams = Object.keys(parms).reduce((acc, key) => {
      if (!customKey.includes(key)) {
        acc[key] = (parms as any)[key]; // 使用類型斷言
      }
      return acc;
    }, {} as any);

    formData.append("jsonString", JSON.stringify(jsonParams));
    if (parms.rescueImageClassA) {
      formData.append("rescueImageClassA", parms.rescueImageClassA);
    }
    return apiRequest(getApiAddress + "build/edit", createLoginPostFormDataConfig(parms, formData)).then((res) => handleFetchResponse(res));
  },
  deleteBuild: async (
    parms: BaseParams & {
      buildId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "build/del", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  modifyBuildFloorInfo: async (
    parms: BaseParams & {
      floorInfo: EhsBuildingFloorInfo;
      floorPlan: File;
      rescueImageClassB: File;
      hazardCard: File;
      delFileIdList: string[];
      floorDetailMultiList: EhsMultipleDetail[],
    }
  ) => {
    const formData = new FormData();
    const customKey = ["floorPlan", "rescueImageClassB", "hazardCard"];
    const jsonParams = Object.keys(parms).reduce((acc, key) => {
      if (!customKey.includes(key)) {
        acc[key] = (parms as any)[key]; // 使用類型斷言
      }
      return acc;
    }, {} as any);

    formData.append("jsonString", JSON.stringify(jsonParams));
    customKey.forEach((key) => {
      appendToFormData((parms as any)[key], formData, key);
    })
    return apiRequest(getApiAddress + "build/floor/info/modify", createLoginPostFormDataConfig(parms, formData)).then((res) => handleFetchResponse(res));
  },
};
