import { downloadBase64File } from 'ehs/utils/fileUtil';
import React from 'react';

interface DownloadButtonProps {
    content?: string; // base64 編碼的檔案內容
    fileName: string; // 檔案名稱
    onDownload?: () => Promise<{
        content: string;
        fileName: string;
    }>;
}

const DownloadButton: React.FC<DownloadButtonProps> = ({ content, fileName, onDownload }) => {

    const handleClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
        try {
            let fileContent = content;
            let downloadFileName = fileName;

            if (!fileContent && onDownload) {
                const result = await onDownload();
                fileContent = result.content;
                downloadFileName = result.fileName || fileName;
            }

            if (fileContent) {
                downloadBase64File(fileContent, downloadFileName);
            }
        } catch (error) {
            console.error('下載檔案時發生錯誤:', error);
            // 這裡可以加入錯誤處理，例如顯示錯誤訊息給使用者
        }

        stopPropagation(event);
    };
    const stopPropagation = (event: React.MouseEvent<HTMLButtonElement> | React.MouseEvent<HTMLTextAreaElement, MouseEvent>) => {
        event.stopPropagation();
    };
    
    return (
        <button
            className="download-button mt-md-0 mt-1"
            onClick={handleClick}
            style={{
                background: "none",
                color: "blue",
                border: "none",
                cursor: "pointer",
                padding: 0,
            }}
            title={fileName}
        >
            {fileName}
        </button>
    );
};

export default DownloadButton;
