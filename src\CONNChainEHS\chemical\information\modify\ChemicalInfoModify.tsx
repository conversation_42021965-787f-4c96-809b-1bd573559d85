import BlockUi from "@availity/block-ui";
import { AreaAPI } from "api/areaAPI";
import { ChemicalAPI } from "api/chemicalAPI";
import { ConfigAPI } from "api/configAPI";
import { FileAPI } from "api/fileAPI";
import { OptionAPI } from "api/optionAPI";
import { AppPaths } from "config/app-paths";
import BlockuiMsg from "ehs/common/BlockuiMsg";
import BackButton from "ehs/common/button/BackButton";
import DownloadButton from "ehs/common/button/DownloadButton";
import ChemicalTemperatureObj from "ehs/common/chemical/ChemicalTemperatureObj";
import GhsImage from "ehs/common/GhsImage";
import InputNumFloat from "ehs/common/input/InputNumFloat";
import { showSuccessToast, showWarnToast } from "ehs/common/Toast";
import { CHEMICAL_CASNO_MAX_LENGTH, CHEMICAL_CONCEN_DECIMAL_PLACES, CHEMICAL_CONCEN_MAX, CHEMICAL_NEW_CHEM_CON_ID_PREFIX, CHEMICAL_PHASE_LIQUID, CHEMICAL_TEMPERATURE_BOILING, CHEMICAL_TEMPERATURE_MELTING, CONFIG_ID_START_PUB_HAZ, CONFIG_TYPE_CHEM, CONFIG_TYPE_CHEM_AREA_APPROVE_TYPE, CONFIG_TYPE_CHEM_CLASS_RULE_COMPARE, CONFIG_TYPE_CHEM_CLASS_RULE_TYPE, CONFIG_TYPE_CHEM_STATUS, CONFIG_TYPE_CHEM_TEMPRATURE_TYPE, CONFIG_TYPE_CITY, CONFIG_TYPE_CONCEN_TYPE, CONFIG_TYPE_CONCERNED, CONFIG_TYPE_CTRL_CONCEN_TYPE, CONFIG_TYPE_CTRL_TOTAL_QTY_TYPE, CONFIG_TYPE_GHS_CLASS, CONFIG_TYPE_GHS_IMG, CONFIG_TYPE_PRIORITY, CONFIG_TYPE_PUBLIC_HAZARD, CONFIG_TYPE_STATE, CONFIG_TYPE_TOXIC, CONFIG_VAL_GHS, CONFIG_VAL_PRIORITY, CONFIG_VAL_PUBLIC_HAZARD, CONFIG_VAL_CONCERNED, CONFIG_VAL_TOXIC, FILE_TYPE_AREA_CHEMICAL_APPROVAL_INFO, OPTION_CHEM_CLASSIFY_MODE, OPTION_CHEM_INFO_MODIFY_FIRST, OPTION_VAL_CHEM_CLASSIFY_MODE_STRICT, OPTION_VAL_ENABLE } from "ehs/constant/constants";
import { ChemicalTemperatureType, getChemicalTemperatureType } from "ehs/enums/ChemicalTempratureType";
import { Language } from "ehs/enums/Language";
import useGhsMapping from "ehs/hooks/useGhsMapping";
import useLoginUser from "ehs/hooks/useLoginUser";
import Breadcrumbs from "ehs/layout/Breadcrumbs";
import Footer from "ehs/layout/Footer";
import { ChemicalClassMatch, initChemicalClassMatch } from "ehs/models/ChemicalClassMatch";
import { EhsArea } from "ehs/models/EhsArea";
import { EhsAreaChemicalApproveInfo } from "ehs/models/EhsAreaChemicalApproveInfo";
import { EhsChemical, initEhsChemical } from "ehs/models/EhsChemical";
import { EhsChemicalCasno } from "ehs/models/EhsChemicalCasno";
import { EhsChemicalCategory, initEhsChemicalCategory } from "ehs/models/EhsChemicalCategory";
import { EhsChemicalClassRule, initEhsChemicalClassRule } from "ehs/models/EhsChemicalClassRule";
import { EhsChemicalInfoArea, initEhsChemicalInfoArea } from "ehs/models/EhsChemicalInfoArea";
import { EhsChemicalName } from "ehs/models/EhsChemicalName";
import { EhsConfigParam, initEhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsFile } from "ehs/models/EhsFile";
import { EhsOptions, initEhsOptions } from "ehs/models/EhsOptions";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { getBasicLoginUserInfo, navigateToHome } from "ehs/utils/authUtil";
import { getApprovalNoPre, getProtectChemClassReadonly, protectChemClass } from "ehs/utils/chemicalUtil";
import { isChineseLang, isEnglishLang, splitChemNameListByLang } from "ehs/utils/langUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { checkFormatCasno, checkFormatNumDesh, getFormatDateSlash } from "ehs/utils/stringUtil";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from 'react-router-dom';
import styled from "styled-components";
import ChemicalInfoConcen from "../ChemicalInfoConcen";
import ChemClassifyEditItem from "./ChemClassifyEditItem";
import { errorMsg } from "ehs/common/SwalMsg";
import ChemicalAreaApprovalSection from "../ChemicalAreaApprovalSection";

//分類選項類型
type OptionState = {
  chemClassify: EhsConfigParam[];
  chemPhaseStates: EhsConfigParam[];
  chemStatus: EhsConfigParam[];
  toxicClassify: EhsConfigParam[];
  concernedClassify: EhsConfigParam[];
  priorityClassify: EhsConfigParam[];
  concenType: EhsConfigParam[];
  chemConcType: EhsConfigParam[];
  ghsClassify: EhsConfigParam[];
  ghsImg: EhsConfigParam[];
  publicHazardClassify: EhsConfigParam[];
  areaApprovalType: EhsConfigParam[];
  cityList: EhsConfigParam[];
  ctrlTotalQtyType: EhsConfigParam[];
  chemClassRuleType: EhsConfigParam[];
  chemClassRuleCompare: EhsConfigParam[];
  tempratureTypeOptions: EhsConfigParam[];
};

//分類選項類型對應
const configTypeMap = {
  chemClassify: CONFIG_TYPE_CHEM,
  chemPhaseStates: CONFIG_TYPE_STATE,
  chemStatus: CONFIG_TYPE_CHEM_STATUS,
  toxicClassify: CONFIG_TYPE_TOXIC,
  concernedClassify: CONFIG_TYPE_CONCERNED,
  priorityClassify: CONFIG_TYPE_PRIORITY,
  concenType: CONFIG_TYPE_CONCEN_TYPE,
  chemConcType: CONFIG_TYPE_CTRL_CONCEN_TYPE,
  ghsClassify: CONFIG_TYPE_GHS_CLASS,
  ghsImg: CONFIG_TYPE_GHS_IMG,
  publicHazardClassify: CONFIG_TYPE_PUBLIC_HAZARD,
  areaApprovalType: CONFIG_TYPE_CHEM_AREA_APPROVE_TYPE,
  cityList: CONFIG_TYPE_CITY,
  ctrlTotalQtyType: CONFIG_TYPE_CTRL_TOTAL_QTY_TYPE,
  chemClassRuleType: CONFIG_TYPE_CHEM_CLASS_RULE_TYPE,
  chemClassRuleCompare: CONFIG_TYPE_CHEM_CLASS_RULE_COMPARE,
  tempratureTypeOptions: CONFIG_TYPE_CHEM_TEMPRATURE_TYPE
};//取得的config type
const initOption = {
  chemClassify: [],
  chemPhaseStates: [],
  chemStatus: [],
  toxicClassify: [],
  concernedClassify: [],
  priorityClassify: [],
  concenType: [],
  chemConcType: [],
  ghsClassify: [],
  ghsImg: [],
  publicHazardClassify: [],
  areaApprovalType: [],
  cityList: [],
  ctrlTotalQtyType: [],
  chemClassRuleType: [],
  chemClassRuleCompare: [],
  tempratureTypeOptions: [],
};

//多輸入框型態
const multiValType = {
  casno: "casno",
  name: "name",
  chName: "chName",
  enName: "enName",
}

function ChemicalInfoModify() {
  const { t, i18n } = useTranslation();
  const { state } = useLocation();
  const { isEdit, initChemId } = state || {};
  const { loginUser } = useLoginUser();
  const ghsMappingObj = useGhsMapping(loginUser);//ghs分類勾選對應
  const navigate = useNavigate();
  const [isEditPage, setIsEditPage] = useState<boolean>(false);//判斷新增 或修改
  const [loadingBlock, setLoadingBlock] = useState(false);
  const [hasDuplicateCasno, setHasDuplicateCasno] = useState<boolean[]>([false]);//檢查casno 是否在系統內有重複
  const [needGrandHandQty, setNeedGrandHandQty] = useState<boolean>(false);//是否需要分級運作量
  const [initGhsClass, setInitGhsClass] = useState<boolean>(false);//是否初始過ghs分類
  const [initChemInfoArea, setInitChemInfoArea] = useState<boolean>(false);//是否初始過化學品區域設定狀態
  const [boilType, setBoilType] = useState(ChemicalTemperatureType.Single); //沸點類型 1:單一 2:區間
  const [meltType, setMeltType] = useState(ChemicalTemperatureType.Single); //熔點類型 1:單一 2:區間
  const [firstValidCasno, setFirstValidCasno] = useState<string>("");//暫存第一個有效格式casno 避免未修改重複call api判斷
  const [originEditCasno, setOriginEditCasno] = useState<string>("");//修改暫存第一個修改前的casno 避免未修改重複call api判斷卡到
  const [checkedChemClass, setCheckedChemClass] = useState<{ [key: string]: EhsConfigParam }>({});//勾選的化學品分類 (判斷用)
  const [ghsClassObj, setGhsClassObj] = useState<{ [key: string]: string[] }>({});//ghs分類相關內容(公共危險分類 & 圖片)
  const [casnoVaild, setCasnoVaild] = useState<(boolean | null)[]>([null]);//判斷casno格式是否正確
  const [grandHandQtyTemp, setGrandHandQtyTemp] = useState<(number | null)>(null);//暫存分級運作量
  const [chemClassifyMode, setChemClassifyMode] = useState<(EhsOptions)>(initEhsOptions);//化學品分類模式 0:簡單版 1:需針對每個分類設定
  const [canModifyFirstInfo, setCanModifyFirstInfo] = useState<(EhsOptions)>(initEhsOptions);//化學品基本資訊能否修改比對過分類的第一筆(毒化 關注等)
  const [chemicalInfo, setChemicalInfo] = useState<EhsChemical>(initEhsChemical);//化學品資訊 
  const [option, setOption] = useState<OptionState>(initOption);//checkbox radio option
  const [selectedOptions, setSelectedOptions] = useState<OptionState>(initOption);//checkbox radio selected option
  const [areaList, setAreaList] = useState<EhsArea[]>([]);
  const [modifiedConcens, setModifiedConcens] = useState<string[]>([]);
  const [deletedConcens, setDeletedConcens] = useState<string[]>([]);
  const [chemApproveInfoMap, setChemApproveInfoMap] = useState<{ [type: string]: EhsAreaChemicalApproveInfo }>({});
  const [chemApproveInfoFile, setChemApproveInfoFile] = useState<{ [type: string]: EhsFile | null }>({});
  const [chemInfoArea, setChemInfoArea] = useState<{ [areaId: string]: EhsChemicalInfoArea }>({});
  const [chemClassRuleObj, setChemClassRuleObj] = useState<{ [configId: string]: EhsChemicalClassRule }>({});
  const { conList } = chemicalInfo;
  const reqValType = {
    chemClass: "chemClass",
  }
  const errorConditions: { [key: string]: () => boolean } = {
    chemClass: () => Object.keys(checkedChemClass).length === 0,//化學品分類必選
    // 可以添加更多的條件
  };//必填判斷條件
  const initialReqState: { [key: string]: boolean | null } = {};
  Object.keys(reqValType).forEach((key) => {
    initialReqState[key] = null;
  });
  const [reqField, setReqField] = useState(initialReqState);//判斷必填欄位
  const { chemClassify, chemPhaseStates, chemStatus, toxicClassify, concernedClassify,
    priorityClassify, chemConcType, ghsClassify, ghsImg, publicHazardClassify,
    concenType, areaApprovalType, cityList, ctrlTotalQtyType, chemClassRuleType,
    chemClassRuleCompare, tempratureTypeOptions } = option;
  const ghsItem = chemClassify.find(item => item.configValue === CONFIG_VAL_GHS) || initEhsConfigParam;//危害性化學品分類選項
  const hasProtectChemClass = selectedOptions.chemClassify.some(item =>
    protectChemClass.includes(item.configValue)
  );
  const isCanModifyFirstInfo = canModifyFirstInfo.optionValue === OPTION_VAL_ENABLE;
  const canModifyProtectChemClass = !hasProtectChemClass || isCanModifyFirstInfo
  const selectedGhsImg = ghsImg.filter(item => selectedOptions.ghsImg.some(selected => selected.configId === item.configId));
  const selectedPubHazClass = publicHazardClassify.filter(item => selectedOptions.publicHazardClassify.some(selected => selected.configId === item.configId));
  const hasSelectedPubHazClass = !isArrayEmpty(selectedPubHazClass);
  const mainGhsClass = ghsClassify.filter(item => !item.configSubType);
  const subGhsClass = ghsClassify.filter(item => item.configSubType);
  const cityMap: { [key: string]: EhsConfigParam } = cityList.reduce((acc, item) => {
    acc[item.configId] = item;
    return acc;
  }, {} as { [key: string]: EhsConfigParam });

  interface SubGhsClassObject {
    [key: string]: EhsConfigParam[]; // 或者根据实际情况定义更具体的类型
  }
  const subGhsClassObject: SubGhsClassObject = subGhsClass.reduce((acc, curr) => {
    const { configSubType } = curr;
    if (!acc[configSubType]) {
      acc[configSubType] = [];
    }
    acc[configSubType].push(curr);
    return acc;
  }, {} as SubGhsClassObject);
  const [multiInputs, setMultiInputs] = useState({
    [multiValType.casno]: [''],
    [multiValType.name]: [''],
    [multiValType.chName]: [''],
    [multiValType.enName]: [''],
  }); // 儲存輸入框的內容
  const [isRepeatInputs, setIsRepeatInputs] = useState({
    [multiValType.casno]: [false],
    [multiValType.name]: [false],
    [multiValType.chName]: [false],
    [multiValType.enName]: [false],
  });//判斷多個輸入框是否有填入重複的內容
  const isChinese = isChineseLang(i18n.language);
  const isEnglish = isEnglishLang(i18n.language);
  const showSmartMatchChemClassBtn = () => {
    const firstCasno = multiInputs.casno[0]?.trim();
    const firstName = multiInputs.name[0]?.trim();
    const firstChName = multiInputs.chName[0]?.trim();
    const firstEnName = multiInputs.enName[0]?.trim();

    if (casnoVaild.includes(false) || firstCasno === '') return false; // casno第一個不能空 且不能有誤

    if (isChinese) {
      return firstName && firstEnName; // 中文時，name和enName不能空
    } else if (isEnglish) {
      return firstChName && firstName; // 英文時，chName和name不能空
    } else {
      return firstName && firstChName && firstEnName; // 其他語言時，name不能空
    }
  };

  const isAreaEnabledApproveInfo = (chemInfoArea: { [key: string]: any }, areaId: string): boolean => {
    return Object.entries(chemInfoArea)
      .filter(([key]) => key.endsWith(`-${areaId}`))
      .some(([_, value]) => value.chemStatus !== 0);
  };

  const isDisplayApproveinfoSection = areaList.some((area) => {
    const { areaId } = area;
    const enabledArea = isAreaEnabledApproveInfo(chemInfoArea, areaId);
    return enabledArea;
  });
  const hasMissingApproveInfo = isDisplayApproveinfoSection ? areaApprovalType.some((item) => {
    const { configId, configValue } = item;
    const isChecked = checkedChemClass[configValue];
    if (!isChecked) return false;

    return areaList.some((area) => {
      const { areaId } = area;
      const enabledArea = isAreaEnabledApproveInfo(chemInfoArea, areaId);
      if (!enabledArea) return false; // 如果 enabledArea 為 false，則忽略
      const key = areaId + '-' + configId;
      const chemApproveInfo = chemApproveInfoMap[key];
      const { approvalNo, approvalExpiryDate } = chemApproveInfo || {};
      const approveInfoFile = chemApproveInfoFile[key];
      const { fileContentBase64 } = approveInfoFile || {};
      const isShow = approvalNo && approvalExpiryDate && fileContentBase64;
      return !isShow; // 只要有一個 isShow 為 false，即返回 true
    });
  }) : false;
  const isStrictChemClass = chemClassifyMode.optionValue === OPTION_VAL_CHEM_CLASSIFY_MODE_STRICT;
  const selectedPhaseStates = selectedOptions.chemPhaseStates;
  const hasLiquid = selectedPhaseStates.some(phaseState => phaseState.configValue === CHEMICAL_PHASE_LIQUID);

  useEffect(() => {
    if (loginUser) {
      fetchOption();
    }
  }, [loginUser]);

  useEffect(() => {
    if (loginUser) {
      setIsEditPage(isEdit);
      fetchConfigData();
      clearMultiValEnName();
      fetchAreaData();
      fetchAreaChemApproveInfoList();
      fetchChemicalApprovalFile();
      if (isEdit) {
        fetchChemData();
      } else if (isEdit === undefined && !initChemId) {
        navigateToHome(navigate);
        return;
      }
    }
  }, [loginUser, i18n.language]);

  //化學品勾選後切換必填提示訊息
  useEffect(() => {
    if (reqField[reqValType.chemClass] != null) {
      setReqField({
        ...reqField,
        [reqValType.chemClass]: errorConditions[reqValType.chemClass]()
      });
    }
  }, [checkedChemClass])

  useEffect(() => {
    if (grandHandQtyTemp) {
      changeChemInfo({ chemGradeHandQty: grandHandQtyTemp });
    }
  }, [grandHandQtyTemp])

  useEffect(() => {
    if (!initGhsClass && !isArrayEmpty(Object.keys(ghsMappingObj)) && !isArrayEmpty(selectedOptions.ghsClassify)) {
      setInitGhsClass(true);
      const newGhsClassObj = getNewGhsClassObj(selectedOptions.ghsClassify.map(item => item.configId), true);
      setGhsClassObj(newGhsClassObj);
    }
  }, [ghsMappingObj, selectedOptions.ghsClassify])

  /**
   * db 沒資料時 初始化化學品區域資訊
   */
  useEffect(() => {
    if (!isArrayEmpty(areaList) && !isArrayEmpty(conList) && initChemInfoArea) {
      const initialChemInfoArea = conList.reduce((conAcc, con) => {
        // 對每個濃度區間，初始化所有區域的狀態
        const areaStates = areaList.reduce((areaAcc, area) => {
          const key = `${con.chemConId}-${area.areaId}`;
          areaAcc[key] = {
            ...initEhsChemicalInfoArea,
            chemConId: con.chemConId,  // 加入濃度區間ID
            areaId: area.areaId,       // 加入區域ID
          };
          return areaAcc;
        }, {} as { [key: string]: EhsChemicalInfoArea });

        return { ...conAcc, ...areaStates };
      }, {} as { [key: string]: EhsChemicalInfoArea });

      setChemInfoArea(initialChemInfoArea);
      setInitChemInfoArea(false);
    }
  }, [areaList, conList, initChemInfoArea]);

  /**
   * 需濃度控管分類時要抓設定的值
   */
  useEffect(() => {
    if (loginUser && isStrictChemClass) {
      fetchChemClassRule();
    }
  }, [loginUser, isStrictChemClass])

  useEffect(() => {

    // 根據 hasPubHazClass 的值來決定是否加入或移除 CONFIG_VAL_PUBLIC_HAZARD
    const publicHazardOption = chemClassify.find(item => item.configValue === CONFIG_VAL_PUBLIC_HAZARD);
    if (publicHazardOption) {
      handleChemClassChange({} as React.ChangeEvent<HTMLInputElement>, publicHazardOption, hasSelectedPubHazClass);
    }
  }, [hasSelectedPubHazClass])


  const clickMatchChemClass = () => {
    setLoadingBlock(true);
    const casnos: EhsChemicalCasno[] = getCurrentCasnos();
    const newChNames = createChemicalNames(multiInputs[isChinese ? multiValType.name : multiValType.chName], Language.ZHTW);
    const newEnNames = createChemicalNames(multiInputs[isEnglish ? multiValType.name : multiValType.enName], Language.ENUS);
    const chemicalClassMatches: ChemicalClassMatch[] = [];

    // 取得三個陣列的最大長度
    const maxLength = Math.max(casnos.length, newChNames.length, newEnNames.length);

    for (let i = 0; i < maxLength; i++) {
      const casno = casnos[i]?.casno || ''; // 確保不會因為 undefined 而出錯
      const chName = newChNames[i]?.chemName || ''; // 中名
      const enName = newEnNames[i]?.chemName || ''; // 英名

      // 創建 ChemicalClassMatch 物件並添加到陣列中
      chemicalClassMatches.push({
        ...initChemicalClassMatch,
        casno: casno,
        chName: chName,
        enName: enName,
      });
    }

    ChemicalAPI.getChemicalClassMatch({
      ...getBasicLoginUserInfo(loginUser),
      mixtureFlag: false,
      matchConcenFlag: false,
      matchList: chemicalClassMatches,
    }).then((rs) => {
      if (isApiCallSuccess(rs)) {
        const matchRs: ChemicalClassMatch[] = rs.results;
        matchRs.forEach((match, i) => {
          // 根據 match.configId 找到對應的 chemClassify
          const matchedClass = chemClassify.find(classify => classify.configId === match.configId);
          if (matchedClass) {
            // 如果找到了對應的 chemClassify，則呼叫 handleChemClassChange
            handleChemClassChange({} as React.ChangeEvent<HTMLInputElement>, matchedClass, true);
          }
        });
      }
      setLoadingBlock(false);
      showSuccessToast(t('message.success'));
    }).catch((err) => {
      console.error(err);
      setLoadingBlock(false);
    })
  }

  const changeChemInfo = (obj: object) => {
    setChemicalInfo({ ...chemicalInfo, ...obj })
  }

  //判斷是否選取
  const isCheckedOptionById = (configList: EhsConfigParam[], targetConfigId: string): boolean => {
    return configList.some((item) => item.configId === targetConfigId);
  };

  //切換語系如果是英文 須清除英文名稱
  const clearMultiValEnName = () => {
    multiInputs[multiValType.enName] = [""];
    isRepeatInputs[multiValType.enName] = [false];
  }

  //變更化學品分類時的動作
  const handleChemClassChange = (event: React.ChangeEvent<HTMLInputElement>, chemClass: EhsConfigParam, checked?: boolean) => {
    const isChecked = checked !== undefined ? checked : event.target.checked;

    setCheckedChemClass(prevState => {
      const updatedState = { ...prevState };

      // 更新 CheckedChemClass 狀態
      if (isChecked) {
        updatedState[chemClass.configValue] = chemClass; // 添加選中的化學分類     
        // 如果有ghsItem且不是GHS本身,則也添加GHS
        if (ghsItem && chemClass.configValue !== CONFIG_VAL_GHS) {
          updatedState[CONFIG_VAL_GHS] = ghsItem;
        }
      } else {
        delete updatedState[chemClass.configValue]; // 如果取消勾選，移除對應項目
        // 檢查是否還有其他非GHS的選項
        const hasOtherChecked = Object.values(updatedState).some(
          item => item.configValue !== CONFIG_VAL_GHS
        );

        // 如果沒有其他選項且當前項不是GHS,則也移除GHS
        if (!hasOtherChecked && chemClass.configValue !== CONFIG_VAL_GHS) {
          delete updatedState[CONFIG_VAL_GHS];
        }
      }

      return updatedState;
    });

    // 調用處理選項變更的函數
    handleCheckboxOptionChange(event, chemClass, configTypeMap.chemClassify, checked);
  };

  //變更選項時的動作
  const handleCheckboxOptionChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    config: EhsConfigParam,
    option: string,
    checked?: boolean
  ) => {
    const isChecked = checked !== undefined ? checked : event.target.checked;
    const classifyKey = Object.keys(configTypeMap).find(
      (key) => configTypeMap[key as keyof typeof configTypeMap] === option
    ) as keyof OptionState; // 使用型別斷言告訴 TypeScript classifyKey 是 OptionState 的合法屬性名稱
    const isChemClassify = classifyKey === 'chemClassify';

    if (classifyKey !== undefined) {
      // 初始化 selectedOptions 對象
      setSelectedOptions(prevSelectedOptions => {
        const newSelectedOptions = { ...prevSelectedOptions };

        if (isChecked) {
          // 如果選中，添加 config 到選項中
          newSelectedOptions[classifyKey] = [...newSelectedOptions[classifyKey], config];

          // 判斷是否為 chemClassify 並添加 ghsItem
          if (isChemClassify) {
            if (ghsItem) {
              newSelectedOptions[classifyKey] = [...newSelectedOptions[classifyKey], ghsItem];
            }
          }
        } else {
          // 如果取消勾選，從選項中移除 config
          newSelectedOptions[classifyKey] = newSelectedOptions[classifyKey].filter(param => param.configId !== config.configId);

          // 確保非 GHS 的鍵的數量，決定是否移除 ghsItem
          const nonGhsCount = newSelectedOptions[classifyKey].filter(param => param.configValue !== CONFIG_VAL_GHS).length;
          if (isChemClassify && nonGhsCount === 0) {
            newSelectedOptions[classifyKey] = newSelectedOptions[classifyKey].filter(param => param.configValue !== CONFIG_VAL_GHS);
          }
        }

        return newSelectedOptions; // 返回更新後的選項
      });
    }
  };

  // 更新GHS选项的通用函数
  const getNewGhsClassObj = (configIds: string[], checked: boolean) => {
    const newGhsClassObj: { [key: string]: string[] } = { ...ghsClassObj };
    if (!isArrayEmpty(configIds)) {
      configIds.forEach(configId => {
        const ghsMappingArray = ghsMappingObj[configId];
        if (ghsMappingArray) {
          ghsMappingArray.forEach((key) => {
            if (checked) {
              // 勾选逻辑
              if (newGhsClassObj[key]) {
                newGhsClassObj[key].push(configId);
              } else {
                newGhsClassObj[key] = [configId];
              }
            } else {
              // 取消勾选逻辑
              if (newGhsClassObj[key]) {
                newGhsClassObj[key] = newGhsClassObj[key].filter((id) => id !== configId);
                if (isArrayEmpty(newGhsClassObj[key])) {
                  delete newGhsClassObj[key];
                }
              }
            }
          });
        }
      });
    }

    const objKeys = Object.keys(newGhsClassObj);
    // 直接創建一個新的物件，不需要透過 reduce 操作
    const updatedOptions: Partial<OptionState> = {
      ghsImg: [],
      publicHazardClassify: []
    };

    // 檢查是否有符合條件的 key
    const hasGhsImg = objKeys.some(key => key.startsWith(CONFIG_TYPE_GHS_IMG));
    const hasPubHaz = objKeys.some(key => key.startsWith(CONFIG_ID_START_PUB_HAZ));

    // 僅在有符合條件的 key 時才進行篩選
    if (hasGhsImg) {
      updatedOptions.ghsImg = option.ghsImg.filter(item =>
        objKeys.some(key => key.startsWith(CONFIG_TYPE_GHS_IMG) && key === item.configId)
      );
    }

    if (hasPubHaz) {
      updatedOptions.publicHazardClassify = option.publicHazardClassify.filter(item =>
        objKeys.some(key => key.startsWith(CONFIG_ID_START_PUB_HAZ) && key === item.configId)
      );
    }

    // 更新 selectedOptions
    setSelectedOptions(prevOptions => ({
      ...prevOptions,
      ...updatedOptions,
    }));
    return newGhsClassObj;
  };


  //變更GHS選項時的動作
  const handleGhsCheckboxOptionChange = (event: React.ChangeEvent<HTMLInputElement>, config: EhsConfigParam) => {
    const { configId } = config;
    const newGhsClassObj = getNewGhsClassObj([configId], event.target.checked);
    setGhsClassObj(newGhsClassObj);
  }

  const checkedToxic = !!checkedChemClass[CONFIG_VAL_TOXIC];
  const checkedSvhc = !!checkedChemClass[CONFIG_VAL_CONCERNED];
  const checkedPriority = !!checkedChemClass[CONFIG_VAL_PRIORITY];

  // 新增輸入框
  const addInput = (field: keyof typeof multiInputs) => {
    setMultiInputs({ ...multiInputs, [field]: [...multiInputs[field], ''] });
    setIsRepeatInputs({ ...isRepeatInputs, [field]: [...isRepeatInputs[field], false] })
    if (field === multiValType.casno) {
      setCasnoVaild([...casnoVaild, null]);
    }
  };

  // 刪除指定索引的輸入框
  const removeInput = (field: keyof typeof multiInputs, index: number) => {
    const newInputs = [...multiInputs[field]];
    newInputs.splice(index, 1);
    setMultiInputs({ ...multiInputs, [field]: newInputs });
    const newIsRepeatInputs = isRepeatInputs[field];
    newIsRepeatInputs.splice(index, 1);
    setIsRepeatInputs({ ...isRepeatInputs, [field]: newIsRepeatInputs })
    if (field === multiValType.casno) {
      const newCasnoVaild = [...casnoVaild];
      newCasnoVaild.splice(index, 1);
      setCasnoVaild(newCasnoVaild);
    }
  };

  // 更新輸入框的值
  const handleInputChange = (field: keyof typeof multiInputs, index: number, value: string) => {
    const trimValue = value.trim(); // 移除前後空格
    setMultiInputs(prevState => {
      const newInputs = { ...prevState };
      newInputs[field][index] = trimValue;
      return newInputs;
    });

    //檢查casno格式
    if (field === multiValType.casno) {
      setCasnoVaild(prevState => {
        const newState = [...prevState];
        newState[index] = trimValue ? checkFormatCasno(trimValue) : null;
        return newState;
      });
    }
    checkRepeatVal(field, index, trimValue);
  };

  //失焦判斷casno是否重複
  const handleInputBlur = (field: keyof typeof multiInputs, index: number, value: string) => {
    const isCasnoField = field === multiValType.casno;
    if (isCasnoField) {
      setFirstValidCasno(value);
      if (value && checkFormatCasno(value) && value !== firstValidCasno && value !== originEditCasno) {
        //casno 檢查第一個 是否 DB 重複
        ChemicalAPI.checkDuplicateCasNumber({
          ...getBasicLoginUserInfo(loginUser),
          casno: value
        }).then((result) => {
          if (isApiCallSuccess(result)) {
            setHasDuplicateCasno((prev) => {
              const newHasDuplicate = [...prev]; // 創建前一狀態的副本
              newHasDuplicate[index] = result.results; // 根據 index 更新狀態
              return newHasDuplicate; // 返回新的狀態
            });
          }
        });
      }
    }
  };

  //檢查有無重複內容
  const checkRepeatVal = (field: keyof typeof multiInputs, index: number, value: string) => {
    const newIsRepeatInputs = isRepeatInputs[field];
    const isDuplicate = multiInputs[field].some((inputValue, idx) => inputValue === value && idx !== index);
    newIsRepeatInputs[index] = isDuplicate;
    setIsRepeatInputs({ ...isRepeatInputs, [field]: newIsRepeatInputs })
  }

  //新增輸入框按鈕
  const addMultiValBtn = (type: keyof typeof multiInputs) =>
    <button className="btn btn-purple ms-3" onClick={() => addInput(type)}><i className="fas fa-plus me-1" />{t('button.add')}</button>

  //儲存按鈕
  const saveBtn = <button className="btn btn-success ms-4 fs-5" onClick={() => doSave()}><i className="fas fa-floppy-disk me-1" />{t('button.save')}</button>

  const fetchChemData = () => {

    ChemicalAPI.getChemicalDetail({
      ...getBasicLoginUserInfo(loginUser),
      chemId: initChemId
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const chemInfo = result.results;
        const { infoAreaList, casnoList, nameList, categoryList, chemGradeHandQty,
          chemBoilPoint, chemBoilPointMax, chemMeltPoint, chemMeltPointMax } = chemInfo;
        setNeedGrandHandQty(chemGradeHandQty !== null);
        setChemicalInfo(chemInfo);
        if (chemBoilPoint && chemBoilPointMax) {
          setBoilType(ChemicalTemperatureType.Range);
        }
        if (chemMeltPoint && chemMeltPointMax) {
          setMeltType(ChemicalTemperatureType.Range);
        }
        const newSelectedOptions = { ...selectedOptions };
        for (const key in configTypeMap) {
          if (configTypeMap.hasOwnProperty(key)) {
            const configType = configTypeMap[key as keyof typeof configTypeMap];
            const filteredCategoryList = categoryList.filter((item: EhsConfigParam) => item.configType === configType);

            // 使用 Set 來去除重複項目
            const existingIds = new Set(newSelectedOptions[key as keyof OptionState].map(item => item.configId));
            const uniqueNewItems = filteredCategoryList.filter((item: EhsConfigParam) => !existingIds.has(item.configId));

            newSelectedOptions[key as keyof OptionState] = [
              ...newSelectedOptions[key as keyof OptionState],
              ...uniqueNewItems
            ];
          }
        }

        const chemClassObject = newSelectedOptions.chemClassify.reduce((acc: { [key: string]: EhsConfigParam }, item: EhsConfigParam) => {
          acc[item.configValue] = item;
          return acc;
        }, {} as { [key: string]: EhsConfigParam });
        setCheckedChemClass(chemClassObject);
        setSelectedOptions({
          ...newSelectedOptions
        });
        const { currentLangNames, enNames, chNames } = splitChemNameListByLang(nameList, i18n.language);
        const casnoArray: string[] = casnoList.map((item: EhsChemicalCasno) => item.casno);

        //沒有名稱時需預設第一個空的input
        const getNameArray = (shouldBeEmpty: boolean, arr: EhsChemicalName[]) =>
          shouldBeEmpty ? [""] : (isArrayEmpty(arr) ? [""] : arr.map(item => item.chemName));

        let nameArray: string[] = getNameArray(false, isEnglish ? enNames : currentLangNames);
        let chNameArray: string[] = getNameArray(isChinese, chNames);
        let enNameArray: string[] = getNameArray(isEnglish, enNames);

        const initialIsRepeatArrays = {
          [multiValType.casno]: Array(casnoArray.length).fill(false),
          [multiValType.name]: Array(nameArray.length).fill(false),
          [multiValType.chName]: Array(chNameArray.length).fill(false),
          [multiValType.enName]: Array(enNameArray.length).fill(false),
        };
        if (!isArrayEmpty(casnoArray)) {
          setOriginEditCasno(casnoArray[0] || "");
        }

        setMultiInputs({
          ...multiInputs,
          [multiValType.casno]: casnoArray,
          [multiValType.name]: nameArray,
          [multiValType.chName]: chNameArray,
          [multiValType.enName]: enNameArray,
        })
        setIsRepeatInputs({
          ...isRepeatInputs,
          ...initialIsRepeatArrays,
        })

        if (!isArrayEmpty(infoAreaList)) {
          const initialChemInfoArea = infoAreaList.reduce((acc: { [areaId: string]: EhsChemicalInfoArea }, areaInfo: EhsChemicalInfoArea) => {
            acc[areaInfo.chemConId + "-" + areaInfo.areaId] = areaInfo;
            return acc;
          }, {});
          setChemInfoArea(initialChemInfoArea);
        } else {
          setInitChemInfoArea(true);
        }
      }
    });
  };

  const fetchConfigData = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser),
      configTypeList: Object.values(configTypeMap)
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const rs: EhsConfigParam[] = result.results;
        const newOptions = (Object.keys(configTypeMap) as Array<keyof typeof configTypeMap>).reduce((acc, key) => {
          acc[key] = rs.filter((item: EhsConfigParam) => item.configType === configTypeMap[key]);
          return acc;
        }, {} as { [K in keyof typeof configTypeMap]: EhsConfigParam[] });

        setOption(newOptions);
      }
    });
  };

  const fetchAreaData = () => {
    AreaAPI.getAreaList({
      ...getBasicLoginUserInfo(loginUser),
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        setAreaList(result.results);
      }
    });
  };

  const fetchAreaChemApproveInfoList = () => {
    AreaAPI.getAreaChemicalApproveInfoList({
      ...getBasicLoginUserInfo(loginUser),
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const approveInfoMap = result.results.reduce((map: { [type: string]: EhsAreaChemicalApproveInfo }, item: EhsAreaChemicalApproveInfo) => {
          map[item.areaId + '-' + item.approvalType] = item;
          return map;
        }, {} as { [type: string]: EhsAreaChemicalApproveInfo });
        setChemApproveInfoMap(approveInfoMap);
      }
    });
  }

  const fetchChemicalApprovalFile = () => {
    FileAPI.getFileList({
      ...getBasicLoginUserInfo(loginUser),
      fileTypeList: [FILE_TYPE_AREA_CHEMICAL_APPROVAL_INFO],
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const resultFileList: EhsFile[] = result.results;
        const fileInfos: Record<string, EhsFile> = {};
        resultFileList.forEach((file: EhsFile) => {
          fileInfos[file.fileMappingId + '-' + file.fileSubtype] = file;
        });
        setChemApproveInfoFile(fileInfos);
      }
    });
  };

  const fetchOption = () => {
    const optionIds = [OPTION_CHEM_CLASSIFY_MODE, OPTION_CHEM_INFO_MODIFY_FIRST];
    OptionAPI.getOptionListByIds({
      ...getBasicLoginUserInfo(loginUser),
      optionIdList: optionIds
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const options = result.results;
        const optionsMap: {
          [key: string]: EhsOptions;
        } = {};
        options.forEach((item: any) => {
          if (optionIds.includes(item.optionId)) {
            optionsMap[item.optionId] = item;
          }
        });
        setChemClassifyMode(optionsMap[OPTION_CHEM_CLASSIFY_MODE]);
        setCanModifyFirstInfo(optionsMap[OPTION_CHEM_INFO_MODIFY_FIRST]);
      }
    });
  };

  const fetchChemClassRule = () => {
    ChemicalAPI.getChemicalClassByChemId({
      ...getBasicLoginUserInfo(loginUser),
      chemId: initChemId
    }).then(result => {
      if (isApiCallSuccess(result)) {
        setChemClassRuleObj(result.results.reduce((acc: { [key: string]: EhsChemicalClassRule }, item: EhsChemicalClassRule) => {
          acc[item.configId] = item;
          return acc;
        }, {}));
      }
    });
  }

  const mapChemConId = (item: any) => {
    return {
      ...item,
      chemConId: item.chemConId.replace(CHEMICAL_NEW_CHEM_CON_ID_PREFIX, '')
    };
  }
  const createChemicalNames = (names: string[], langType: string) => {
    return names.filter(name => name).map((name, index) => ({
      chemNameId: '', // 每個對象生成一個唯一的 chemCasnoId(db生成)
      chemId: chemicalInfo.chemId,
      chemName: name,
      chemNameSeq: index + 1, // casnoSeq 是基於數組索引，從1開始
      langType: langType,
    }));
  };

  const getCurrentCasnos = () => {
    return multiInputs[multiValType.casno].filter(casno => casno).map((casno, index) => ({
      chemCasnoId: ``, // 每个对象生成一个唯一的 chemCasnoId(db生成)
      chemId: chemicalInfo.chemId,
      casno: casno,
      casnoSeq: index + 1, // casnoSeq 是基于数组索引，从1开始
    }));
  }

  const getCurrentChemicalNames = () => {
    const newNames = createChemicalNames(multiInputs[multiValType.name], i18n.language);
    const newChNames = createChemicalNames(multiInputs[multiValType.chName], Language.ZHTW);
    const newEnNames = createChemicalNames(multiInputs[multiValType.enName], Language.ENUS);
    const chemNames = [...newNames, ...newChNames, ...newEnNames];
    return chemNames;
  };

  // 在 ChemicalInfoConcen 組件中添加處理函數的傳遞
  const handleConcentrationModified = (chemConId: string) => {
    if (!modifiedConcens.includes(chemConId) && !chemConId.includes(CHEMICAL_NEW_CHEM_CON_ID_PREFIX)) {
      setModifiedConcens([...modifiedConcens, chemConId]);
    }
  };

  const handleConcentrationDeleted = (chemConId: string) => {
    if (!chemConId.includes(CHEMICAL_NEW_CHEM_CON_ID_PREFIX)) {
      // 添加到刪除列表
      setDeletedConcens([...deletedConcens, chemConId]);

      // 從修改列表中移除（如果存在）
      if (modifiedConcens.includes(chemConId)) {
        setModifiedConcens(modifiedConcens.filter(id => id !== chemConId));
      }
    }
  };

  const handleValidationError = (errorMessage: string) => {
    showWarnToast(errorMessage);
    setLoadingBlock(false);
  };

  const doSave = () => {
    setLoadingBlock(true);
    const updatedReqField: { [key: string]: boolean } = {};
    Object.keys(reqValType).forEach((key) => {
      updatedReqField[key] = errorConditions[key]();
    });
    setReqField(updatedReqField);
    const hasRequireEnterField = Object.values(updatedReqField).includes(true);
    if (hasRequireEnterField) {
      setLoadingBlock(false);
      return;
    }

    if (isArrayEmpty(selectedPhaseStates)) {
      handleValidationError(t('message.chemical.choose_substance_phase'));
      return;
    }

    // 檢查勞動部公告之優先管理化學品分類是否有選取
    if (checkedPriority && isArrayEmpty(selectedOptions.priorityClassify)) {
      handleValidationError(t('message.priority_class_no_select'));
      return;
    }

    if (casnoVaild.includes(false)) {
      handleValidationError(t('message.chemical.casno_invalid'));
      return;
    }

    if (hasMissingApproveInfo) {
      handleValidationError(t('message.chemical.area_miss_approval_info'));
      return;
    }

    if (chemicalInfo.chemUnNo && !chemicalInfo.respHandPrinciple) {
      handleValidationError(t('message.chemical.error.chemical_unno_non_resp_hand_principle'));
      return;
    }

    if (!isArrayEmpty(selectedPhaseStates) && hasLiquid && !chemicalInfo.chemBoilPoint) {
      handleValidationError(t('message.chemical.liquid_enter_boiling_point'));
      return;
    }

    const casnos: EhsChemicalCasno[] = getCurrentCasnos();
    const chemNames = getCurrentChemicalNames();

    if (isArrayEmpty(casnos)) {
      handleValidationError(t('message.chemical.casno_no_enter'));
      return;
    }
    if (isArrayEmpty(chemNames)) {
      handleValidationError(t('message.chemical.name_no_enter'));
      return;
    }
    if (isArrayEmpty(conList)) {
      handleValidationError(t('message.chemical.concen_no_enter'));
      return;
    }

    // 使用 Object.values() 获取 option 对象中的所有数组，并将它们合并为一个数组
    const categories = Object.values(selectedOptions).reduce((acc, array) => {
      const categoryArray = array.map(optionItem => ({
        ...initEhsChemicalCategory,
        chemId: chemicalInfo.chemId,
        configId: optionItem.configId,
        configValue: optionItem.configValue
      }));
      const filteredCategoryArray = categoryArray.filter(item => !protectChemClass.includes(item.configValue));
      return acc.concat(filteredCategoryArray);
    }, [] as EhsChemicalCategory[]);

    const concenCategories = conList.flatMap(con => con.categoryList);
    const areaInfos = Object.values(chemInfoArea).map(mapChemConId);

    if (isEditPage) {
      const editConcens = conList
        .filter(concen => concen.chemConId.includes(CHEMICAL_NEW_CHEM_CON_ID_PREFIX))
        .map(mapChemConId);

      const updatedConcens = conList
        .filter(concen => modifiedConcens.includes(concen.chemConId))
        .map(mapChemConId);

      const deleteConcens = deletedConcens.map(chemConId => ({
        chemConId,
      }));

      const editConcenCategories = concenCategories
        .filter(cate => cate.chemConId.includes(CHEMICAL_NEW_CHEM_CON_ID_PREFIX))
        .map(mapChemConId);

      const updateConcenCategories = concenCategories
        .filter(cate => modifiedConcens.includes(cate.chemConId))
        .map(mapChemConId);

      ChemicalAPI.editChemical({
        ...getBasicLoginUserInfo(loginUser),
        chemical: chemicalInfo,
        areaInfos,
        casnos: casnos,
        chemNames: chemNames,
        categories: categories,
        concens: editConcens,
        concenCategories: editConcenCategories,
        updatedConcens: updatedConcens,
        deleteConcens: deleteConcens,
        updateConcenCategories: updateConcenCategories,
      }).then((result) => {
        if (isApiCallSuccess(result)) {
          showSuccessToast(t('message.success'));
          goToChemicalInfoListPage();
        } else {
          errorMsg(result.message);
        }
      }).catch(() => {
        errorMsg(t('message.error'));
      }).finally(() => {
        setLoadingBlock(false);
      });
    } else {
      const addConcens = conList.map(mapChemConId);
      const addConcenCategories = concenCategories.map(mapChemConId);
      ChemicalAPI.addChemical({
        ...getBasicLoginUserInfo(loginUser),
        chemical: chemicalInfo,
        areaInfos,
        casnos: casnos,
        chemNames: chemNames,
        categories: categories,
        concens: addConcens,
        concenCategories: addConcenCategories,
      }).then((result) => {
        if (isApiCallSuccess(result)) {
          showSuccessToast(t('message.success'));
          goToChemicalInfoListPage();
        } else {
          errorMsg(result.message);
        }
      }).catch(() => {
        errorMsg(t('message.error'));
      }).finally(() => {
        setLoadingBlock(false);
      });
    }
  }

  const goToChemicalInfoListPage = () => {
    navigate('/' + AppPaths.chemical.informationList);
  }

  const infoAreaFragment = (
    <ChemicalAreaApprovalSection
      isDisplayApproveinfoSection={isDisplayApproveinfoSection}
      areaApprovalType={areaApprovalType}
      checkedChemClass={checkedChemClass}
      areaList={areaList}
      chemApproveInfoMap={chemApproveInfoMap}
      chemApproveInfoFile={chemApproveInfoFile}
      chemInfoArea={chemInfoArea}
      cityMap={cityMap}
      isChinese={isChinese}
      className="mt-1" // 可以自定義寬度
    />
  );

  return (
    <StlyedChemicalInfoModify>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.chemical.manage") },
                { label: t("func.chemical.info"), path: AppPaths.chemical.informationList },
                { label: isEditPage ? t('func.edit') : t('func.add') },
              ]}
            />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{isEditPage ? t('func.edit') : t('func.add')} </h1>
            {/* END page-header */}
            <BackButton />
            {saveBtn}
            <div className="card mt-3">
              <div className="card-body">
                <div className="row justify-content-center">
                  <div className="col-xl-12 px-5">
                    <div className="fs-5 ms-1 d-inline h5"> {t("text.chemical.class")}<span className="text-danger mx-1">*</span>
                      {showSmartMatchChemClassBtn()
                        && <button type="button" className="btn btn-primary ms-3" title={t('button.smart_match_chem_class')} onClick={clickMatchChemClass}>
                          <i className="fas fa-check me-1"></i>{t('button.smart_match_chem_class')}
                        </button>}
                    </div>
                    <br /><br />
                    {chemClassify.map((chemClass: EhsConfigParam) =>
                      <ChemClassifyEditItem
                        key={`chemclass-edit-item-${chemClass.configId}`}
                        chemClass={chemClass}
                        chemClassReadonly={getProtectChemClassReadonly}
                        protectChemClass={protectChemClass}
                        isEdit={isEdit}
                        isStrictChemClass={isStrictChemClass}
                        selectedOptions={selectedOptions.chemClassify}
                        chemClassRuleTypeOption={chemClassRuleType}
                        chemClassRuleCompareOption={chemClassRuleCompare}
                        chemClassRule={chemClassRuleObj[chemClass.configId] || initEhsChemicalClassRule}
                        handleChemClassChange={handleChemClassChange}
                        isCheckedOptionById={isCheckedOptionById}
                        setChemClassRuleObj={(rule: EhsChemicalClassRule) => {
                          setChemClassRuleObj({ ...chemClassRuleObj, [chemClass.configId]: rule });
                        }}
                      />
                    )}
                    <br />
                    {reqField[reqValType.chemClass] === true && <div className="alert alert-danger mt-3 col-12">{t('message.checked')}{t("text.chemical.class")}</div>}
                  </div>
                </div>
                <hr />
                <div className="row align-items-start mt-4">
                  {/* <!-- CAS NO --> */}
                  <div className="col-xl-12 px-5 mb-2">
                    <label className="col-1 ms-1 h5">{t('text.chemical.casno')}<span className="text-danger mx-1">*</span></label>
                    {canModifyProtectChemClass && addMultiValBtn(multiValType.casno)}<br />
                    <span className="text-danger ms-2">※ {t('text.chemical.casno_format_msg')}</span>
                    <div className="row d-flex align-items-center">
                      {multiInputs.casno.map((input, index) => {
                        const inValidCasno = casnoVaild[index] === false;
                        const hasRepeatCasno = isRepeatInputs[multiValType.casno][index] === true;
                        const isDuplicate = hasDuplicateCasno[index];
                        return (
                          <div key={'casno' + index} className="col-xl-3 col-md-6 col-sm-12">
                            <div className="d-flex align-items-center" >
                              <span className="mx-3 multi-inputs">{index + 1 + '.'}</span>
                              {canModifyProtectChemClass ? <input
                                type="text"
                                className={`form-control my-1 item-width-80 ${(inValidCasno || hasRepeatCasno || isDuplicate) && 'is-invalid'}`}
                                data-parsley-required="true"
                                value={input}
                                maxLength={CHEMICAL_CASNO_MAX_LENGTH}
                                onChange={(e) => {
                                  const inputEvent = e.nativeEvent as InputEvent;
                                  const char = inputEvent.data || ''; // 处理新输入的字符
                                  if (!checkFormatNumDesh(char)) { // 检查当前输入字符是否合法
                                    e.preventDefault(); // 阻止不合法字符输入
                                  } else {
                                    handleInputChange(multiValType.casno, index, e.target.value)
                                  }
                                }}
                                onBlur={(e) => handleInputBlur(multiValType.casno, index, e.target.value)}
                              /> : <label className="my-1">{input}</label>}
                              {/* 刪除按鈕 */}
                              {canModifyProtectChemClass && index > 0 && <i className="fas fa-trash fa-lg text-danger ps-2" title={t('button.delete')} onClick={() => removeInput(multiValType.casno, index)} />}
                            </div>
                            {inValidCasno && <div className="text-danger ms-5">{t("message.format.error")}</div>}
                            {hasRepeatCasno && <div className="text-danger ms-5">{t("message.repeat_content")}</div>}
                            {isDuplicate && <div className="text-danger ms-5">{t("message.repeat_casno")}</div>}
                          </div>
                        )
                      })}</div>
                  </div>
                  <hr />
                  {/* <!-- 名稱 --> */}
                  <div className="col-xl-12 px-5 mb-2">
                    <label className="col-1 ms-1 h5">{t('text.name')}<span className="text-danger mx-1">*</span></label>
                    {addMultiValBtn(multiValType.name)}
                    <span>&nbsp;</span>
                    <div className="row d-flex align-items-center">
                      {multiInputs.name.map((input, index) => (
                        <div key={'name' + index} className="col-xl-3 col-md-6 col-sm-12">
                          <div className="d-flex align-items-center" >
                            <span className="mx-3 multi-inputs">{index + 1 + '.'}</span>
                            {(!canModifyProtectChemClass && index === 0) ? <label className="my-1">{input}</label> : <input
                              type="text"
                              className={`form-control my-1 item-width-80 ${isRepeatInputs[multiValType.name][index] === true && 'is-invalid'}`}
                              data-parsley-required="true"
                              value={input}
                              onChange={(e) => handleInputChange(multiValType.name, index, e.target.value)}
                              onBlur={(e) => handleInputBlur(multiValType.name, index, e.target.value)}
                            />}
                            {/* 刪除按鈕 */}
                            {index > 0 && <i className="fas fa-trash fa-lg text-danger ps-2" title={t('button.delete')} onClick={() => removeInput(multiValType.name, index)} />}
                          </div>
                          {isRepeatInputs[multiValType.name][index] === true && <div className="text-danger ms-5">{t("message.repeat_content")}</div>}
                        </div>
                      ))}</div>
                  </div>
                  <hr />
                  {/* <!-- 非中文語系時都要 中文名稱 --> */}
                  {!isChinese && <div className="col-xl-12 px-5 mb-2">
                    <label className="col-1 ms-1 h5">{t('text.ch_name')}<span className="text-danger mx-1">*</span></label>
                    {addMultiValBtn(multiValType.chName)}
                    <span>&nbsp;</span>
                    <div className="row d-flex align-items-center">
                      {multiInputs.chName.map((input, index) => (
                        <div key={'name' + index} className="col-xl-3 col-md-6 col-sm-12">
                          <div className="d-flex align-items-center" >
                            <span className="mx-3 multi-inputs">{index + 1 + '.'}</span>
                            {(!canModifyProtectChemClass && index === 0) ? <label className="my-1">{input}</label> : <input
                              type="text"
                              className={`form-control my-1 item-width-80 ${isRepeatInputs[multiValType.chName][index] === true && 'is-invalid'}`}
                              data-parsley-required="true"
                              value={input}
                              onChange={(e) => handleInputChange(multiValType.chName, index, e.target.value)}
                              onBlur={(e) => handleInputBlur(multiValType.chName, index, e.target.value)}
                            />}
                            {/* 刪除按鈕 */}
                            {index > 0 && <i className="fas fa-trash fa-lg text-danger ps-2" title={t('button.delete')} onClick={() => removeInput(multiValType.chName, index)} />}
                          </div>
                          {isRepeatInputs[multiValType.chName][index] === true && <div className="text-danger ms-5">{t("message.repeat_content")}</div>}
                        </div>
                      ))}</div>
                    <hr />
                  </div>
                  }
                  {/* <!-- 英文名稱 --> */}
                  {!isEnglish &&
                    <div className="col-xl-12 px-5 mb-2">
                      <label className="col-1 ms-1 h5">{t('text.en_name')}<span className="text-danger mx-1">*</span></label>
                      {addMultiValBtn(multiValType.enName)}
                      <span>&nbsp;</span>
                      <div className="row d-flex align-items-center">
                        {multiInputs.enName.map((input, index) => (
                          <div key={'enName' + index} className="col-xl-3 col-md-6 col-sm-12">
                            <div className="d-flex align-items-center" >
                              <span className="mx-3 multi-inputs">{index + 1 + '.'}</span>
                              {(!canModifyProtectChemClass && index === 0) ? <label className="my-1">{input}</label> : <input
                                type="text"
                                className={`form-control my-1 item-width-80 ${isRepeatInputs[multiValType.enName][index] === true && 'is-invalid'}`}
                                data-parsley-required="true"
                                value={input}
                                onChange={(e) => handleInputChange(multiValType.enName, index, e.target.value)}
                                onBlur={(e) => handleInputBlur(multiValType.enName, index, e.target.value)}
                              />}
                              {/* 刪除按鈕 */}
                              {index > 0 && <i className="fas fa-trash fa-lg text-danger ps-2" title={t('button.delete')} onClick={() => removeInput(multiValType.enName, index)} />}
                            </div>
                            {isRepeatInputs[multiValType.enName][index] === true && <div className="text-danger ms-5">{t("message.repeat_content")}</div>}
                          </div>
                        ))}
                      </div>
                    </div>}
                </div>
                <hr />
                <div className="row align-items-start mt-1">
                  <ChemicalInfoConcen param={{
                    page: "modify",
                    chemical: chemicalInfo,
                    setChemical: setChemicalInfo,
                    setShowGrandHandQty: (show) => {
                      setNeedGrandHandQty(show)
                    },
                    ctrlTotalQtyTypeOption: ctrlTotalQtyType,
                    concenTypeOption: concenType,
                    checkedChemClass: checkedChemClass,
                    infoAreaFragment: infoAreaFragment,
                    areaList: areaList,
                    chemInfoArea: chemInfoArea,
                    chemStatus: chemStatus,
                    setChemInfoArea: setChemInfoArea,
                    onConcentrationModified: handleConcentrationModified,
                    onConcentrationDeleted: handleConcentrationDeleted
                  }} />
                </div>
                <hr />
                <div className="row align-items-start mt-1">
                  <label className="ps-4 mb-3 ms-4 h5">{t('text.chemical.ctrl_class_info')}<span className="text-danger mx-1">*</span></label><br />
                  {/* <!-- 勞動部公告之優先管理化學品 --> */}
                  {checkedPriority && <div className="col-xl-4 ps-5 mb-3">
                    <label className="h5">{t('text.chemical.prority_class_title')}<span className="text-danger mx-1">*</span></label><br />
                    {priorityClassify.map((priorityClass: EhsConfigParam) =>
                      <div className="form-check form-check-inline" key={'check' + priorityClass.configId}>
                        <input className="form-check-input" type="checkbox"
                          data-parsley-mincheck="1" id={'check' + priorityClass.configId}
                          checked={isCheckedOptionById(selectedOptions.priorityClassify, priorityClass.configId)} // 檢查該項是否被勾選 
                          onChange={(e) => handleCheckboxOptionChange(e, priorityClass, configTypeMap.priorityClassify)}
                        />
                        <label className="form-check-label" htmlFor={'check' + priorityClass.configId}>{priorityClass.configName}</label>
                      </div>
                    )}
                  </div>}
                  {/* <!-- 相態 --> */}
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="h5">{t('text.chemical.phase_state')}<span className="text-danger mx-1">*</span></label><br />
                    {chemPhaseStates.map((phaseState: EhsConfigParam) => (
                      <div className="form-check form-check-inline" key={'checkbox-' + phaseState.configId}>
                        <input className="form-check-input" type="checkbox" name="ohaseState" id={phaseState.configId} value={phaseState.configId}
                          data-parsley-mincheck="1" checked={isCheckedOptionById(selectedPhaseStates, phaseState.configId)}
                          onChange={(e) => handleCheckboxOptionChange(e, phaseState, configTypeMap.chemPhaseStates)} />
                        <label className="form-check-label" htmlFor={phaseState.configId}>{phaseState.configName}</label>
                      </div>
                    ))}
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="h5" title={t('text.chemical.un_no_desc')}>{t('text.chemical.un_no')}</label><br />
                    <input type="text" className="form-control" value={chemicalInfo.chemUnNo || ''} maxLength={10}
                      onChange={(e) => changeChemInfo({ chemUnNo: e.target.value })}
                    />

                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="h5">{t('text.chemical.response_handling_principal')}{chemicalInfo.chemUnNo && <span className="text-danger mx-1">*</span>}</label><br />
                    <input type="text" className="form-control" value={chemicalInfo.respHandPrinciple || ''}
                      onChange={(e) => changeChemInfo({ respHandPrinciple: e.target.value })}
                    />
                  </div>

                  {/* <!-- 列管編號 --> */}
                  {(checkedToxic || checkedSvhc) && <div className="col-xl-4 px-5 mb-3">
                    <label className="h5">{t('text.ctrl_no')}</label><br />
                    {/* <input type="text" className="form-control" data-parsley-required="true" value={chemicalInfo.chemCtrlNo} disabled /> */}
                    <label className="">{chemicalInfo.chemCtrlNo}</label>
                  </div>}
                  {checkedToxic && <div className="col-xl-4 px-5 mb-3">
                    <label className="h5">{t('text.chemical.toxic_class')}</label><br />
                    <div className="d-md-flex">
                      {toxicClassify.map((toxClass: EhsConfigParam) => {
                        const isChecked = isCheckedOptionById(selectedOptions.toxicClassify, toxClass.configId);
                        return (isChecked && <div className="me-3" key={'check' + toxClass.configId}>
                          {/* <input className="form-check-input d-none" type="checkbox" value={toxClass.configId}
                          data-parsley-mincheck="1" id={'check' + toxClass.configId}
                          defaultChecked={isCheckedOptionById(selectedOptions.toxicClassify, toxClass.configId)} /> */}
                          <label className="form-check-label" htmlFor={'check' + toxClass.configId}>{toxClass.configName}</label>
                        </div>)
                      })}
                    </div>
                  </div>}
                  {checkedSvhc && <div className="col-xl-4 px-5 mb-3">
                    <label className="h5">{t('text.chemical.concerned_class')}</label><br />
                    {concernedClassify.map((concernedClass: EhsConfigParam) => {
                      const isChecked = isCheckedOptionById(selectedOptions.concernedClassify, concernedClass.configId);
                      return (isChecked && <div className="me-1" key={'check' + concernedClass.configId}>
                        {/* <input className="form-check-input" type="checkbox" value={concernedClass.configId}
                          data-parsley-mincheck="1" id={'check' + concernedClass.configId}
                          defaultChecked={isCheckedOptionById(selectedOptions.concernedClassify, concernedClass.configId)} disabled /> */}
                        <label className="form-check-label" htmlFor={'check' + concernedClass.configId}>{concernedClass.configName}</label>
                      </div>)
                    })}
                  </div>}
                  {(checkedToxic || checkedSvhc) && <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.chemical.ctrl_concen')}</label><br />
                    <label>{chemicalInfo.chemCtrlConcen > 0 ? chemicalInfo.chemCtrlConcen + '%' : ''}</label>
                    {/* <input type="text" className="form-control d-inline item-width-90" value={chemicalInfo.chemCtrlConcen} disabled /> */}
                    {/* <span className="fs-4">&ensp;%</span> */}
                    {chemConcType.map((concenType: EhsConfigParam) => {
                      const isChecked = chemicalInfo.chemCtrlConcenType === concenType.configIvalue;
                      return (isChecked && <label className="form-check-label" key={'radio' + concenType.configId}>{concenType.configName}</label>
                        // <div className="form-check form-check-inline mt-3" key={'radio' + concenType.configId}>
                        // <input className="form-check-input" type="radio" value={concenType.configIvalue}
                        //   data-parsley-mincheck="1" id={'radio' + concenType.configId}
                        //   checked={chemicalInfo.chemCtrlConcenType === concenType.configIvalue}
                        //   disabled
                        //   onChange={(e) => { changeChemInfo({ chemCtrlConcenType: concenType.configIvalue }) }} />
                        // <label className="form-check-label" htmlFor={'radio' + concenType.configId}>{concenType.configName}</label>
                        // </div>
                      )
                    })}
                  </div>}
                  {/* <!-- 管制總量 分級運作量--> */}
                  {needGrandHandQty && <div className="col-xl-4 px-5 mb-3">
                    <label className="h5">{t('text.chemical.ctrl_qty')}</label><br />
                    <label>
                      {chemicalInfo.chemGradeHandQty !== null
                        ? `${chemicalInfo.chemGradeHandQty}Kg`
                        : t('text.chemical.no_ctrL_limit')}
                    </label>
                    {/* <div className="form-check form-check-inline">
                      <input className="form-check-input" type="radio" name="gradedHandlingQuantity" id="no_gradedHandlingQuantity"
                        data-parsley-mincheck="1" onChange={(e) => handleNeedGrandHandQtyChange(e)} value="n" checked={!needGrandHandQty} />
                      <label className="form-check-label" htmlFor="no_gradedHandlingQuantity">{t('text.chemical.no_ctrL_limit')}</label>
                    </div>
                    <div className="form-check form-check-inline mb-3">
                      <input className="form-check-input" type="radio" name="gradedHandlingQuantity" id="has_gradedHandlingQuantity"
                        data-parsley-mincheck="1" onChange={(e) => handleNeedGrandHandQtyChange(e)} value="y" checked={needGrandHandQty} />
                      <label className="form-check-label" htmlFor="has_gradedHandlingQuantity">{t('text.chemical.need_ctrL_limit')}</label>
                    </div> */}
                    {/* {
                      <>
                        <InputNumFloat placeholder={t('text.chemical.ctrl_qty_placeholder')} maxValue={CHEMICAL_WEIGHT_MAX} minValue={CHEMICAL_WEIGHT_MIN}
                          value={needGrandHandQty && (chemicalInfo.chemGradeHandQty || undefined)}
                          decimalPlaces={CHEMICAL_WEIGHT_DECIMAL_PLACES}
                          onChange={(event, num) => { }}
                          onBlur={(num) => {
                            const nowNum = num || 0;
                            setGrandHandQtyVaild(nowNum >= CHEMICAL_WEIGHT_MIN && nowNum <= CHEMICAL_WEIGHT_MAX)
                            setGrandHandQtyTemp(nowNum);
                          }} />
                        {grandHandQtyVaild === false && <div className="text-danger">{t("message.number_limit_invalid", { minValue: CHEMICAL_WEIGHT_MIN.toFixed(10), maxValue: CHEMICAL_WEIGHT_MAX })}</div>}
                      </>
                    } */}
                  </div>}
                </div>
                <hr />
                <div className="row align-items-start mt-4">
                  {!isArrayEmpty(selectedGhsImg) &&
                    <div className="col-xl-4 px-5 mb-3">
                      <label className="mb-2 h5">{t('text.chemical.ghs_img')}</label> <br />
                      {selectedGhsImg.map(filteredItem => {
                        return (
                          <React.Fragment key={filteredItem.configId}>
                            <GhsImage src={filteredItem.configValue} alt={filteredItem.configName} title={filteredItem.configName} />
                          </React.Fragment>
                        )
                      })}
                    </div>
                  }
                  {hasSelectedPubHazClass &&
                    <div className="col-xl-4 px-5 mb-3">
                      <label className="mb-2 h5">{t('text.chemical.public_hazard_classify')}</label> <br />
                      {selectedPubHazClass.map(filteredItem => (
                        <React.Fragment key={filteredItem.configId}>
                          <label className="mb-2">{filteredItem.configName}</label>
                          <br />
                        </React.Fragment>
                      ))}
                    </div>
                  }
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.chemical.formula')}</label>
                    <input type="text" className="form-control" value={chemicalInfo.chemFormula || ''}
                      onChange={(e) => { changeChemInfo({ chemFormula: e.target.value }) }} />
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.chemical.formula_structural')}</label>
                    <input type="text" className="form-control" value={chemicalInfo.chemStructFormula || ''}
                      onChange={(e) => { changeChemInfo({ chemStructFormula: e.target.value }) }} />
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.chemical.boil_point')}{hasLiquid && <span className="text-danger ms-1">*</span>}</label> <br />
                    <ChemicalTemperatureObj tempratureTypeOptions={tempratureTypeOptions} temperatureType={CHEMICAL_TEMPERATURE_BOILING} checked={boilType} checkedRadioOnChange={e => {
                      const tempType = getChemicalTemperatureType(parseInt(e.currentTarget.value))!!;
                      setBoilType(tempType);

                      if (tempType === ChemicalTemperatureType.Single) {
                        changeChemInfo({ chemBoilPointMax: null })
                      }
                    }} minValue={chemicalInfo.chemBoilPoint || undefined} maxValue={chemicalInfo.chemBoilPointMax || undefined} minOnChange={num => {
                      changeChemInfo({ chemBoilPoint: num })
                    }} maxOnChange={num => {
                      changeChemInfo({ chemBoilPointMax: num })
                    }} />
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.chemical.melt_point')}</label> <br />
                    <ChemicalTemperatureObj tempratureTypeOptions={tempratureTypeOptions} temperatureType={CHEMICAL_TEMPERATURE_MELTING} checked={meltType} checkedRadioOnChange={e => {
                      const tempType = getChemicalTemperatureType(parseInt(e.currentTarget.value))!!;
                      setMeltType(tempType);

                      if (tempType === ChemicalTemperatureType.Single) {
                        changeChemInfo({ chemMeltPointMax: null })
                      }
                    }} minValue={chemicalInfo.chemMeltPoint || undefined} maxValue={chemicalInfo.chemMeltPointMax || undefined} minOnChange={num => {
                      changeChemInfo({ chemMeltPoint: num })
                    }} maxOnChange={num => {
                      changeChemInfo({ chemMeltPointMax: num })
                    }} />
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.chemical.allow_concen')}</label> <br />
                    <InputNumFloat className="form-control d-inline item-width-80" value={chemicalInfo.chemPermConcen || undefined} maxValue={CHEMICAL_CONCEN_MAX}
                      maxLength={3 + CHEMICAL_CONCEN_DECIMAL_PLACES}
                      decimalPlaces={CHEMICAL_CONCEN_DECIMAL_PLACES}
                      onBlur={(num) => {
                        changeChemInfo({ chemPermConcen: num })
                      }} />
                    <span className="fs-4">&ensp;ppm</span>
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.chemical.allow_concen')} 2</label> <br />
                    <InputNumFloat className="form-control d-inline item-width-80" value={chemicalInfo.chemPermConcenSecond || undefined} maxValue={CHEMICAL_CONCEN_MAX}
                      maxLength={3 + CHEMICAL_CONCEN_DECIMAL_PLACES}
                      decimalPlaces={CHEMICAL_CONCEN_DECIMAL_PLACES}
                      onBlur={(num) => {
                        changeChemInfo({ chemPermConcenSecond: num })
                      }} />
                    <span className="fs-4">&ensp;mg / m3</span>
                  </div>
                  <div className="col-xl-4 px-5 mb-3">
                    <label className="mb-2 h5">{t('text.note')}</label>
                    <input type="text" className="form-control" value={chemicalInfo.chemNote || ''}
                      onChange={(e) => { changeChemInfo({ chemNote: e.target.value }) }} />
                  </div>
                </div>
                <hr />
                <a role="button" className="btn btn-blue mx-4" data-bs-toggle="collapse" href="#other-info">
                  <i className="fas fa-plus me-1" />{t('button.chemical.ghs_classification')}</a>
                <div className="row justify-content-around align-items-start mt-4 collapse" id="other-info">
                  {mainGhsClass.map((ghs, index) => {
                    return (
                      <React.Fragment key={ghs.configId}>
                        <div className="col-xl-4 px-5 mb-3" >
                          <label className="mb-2 h5">{ghs.configName}</label>
                          <br />
                          {subGhsClassObject[ghs.configId].map(subGhs => (
                            <div className="form-check form-check-inline mt-3" key={'check' + subGhs.configId}>
                              <input className="form-check-input" type="checkbox" value={subGhs.configId}
                                data-parsley-mincheck="1" id={'check' + subGhs.configId} checked={isCheckedOptionById(selectedOptions.ghsClassify, subGhs.configId)}
                                onChange={(e) => {
                                  handleCheckboxOptionChange(e, subGhs, configTypeMap.ghsClassify)
                                  handleGhsCheckboxOptionChange(e, subGhs);
                                }} />
                              <label className="form-check-label" htmlFor={'check' + subGhs.configId}>{subGhs.configName}</label>
                            </div>
                          ))}
                        </div>
                        {(index + 1) % 3 === 0 && index !== mainGhsClass.length - 1 && <hr />}
                      </React.Fragment>
                    );
                  })
                  }
                </div>
                <div className="text-center">{saveBtn}</div>
              </div>
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </BlockUi>
    </StlyedChemicalInfoModify >
  );
}

const StlyedChemicalInfoModify = styled.div`
  padding-bottom:150px;
  
  .react-datepicker-wrapper {
    width: 100%;
  }

  .multi-inputs{
    user-select: none;
  }
  .fa-trash {
      cursor: pointer; /* 将鼠标光标形状改为手指指示器 */
  }
  .badge, .form-check-label {
    user-select: none;
  }

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    font-size: 14px;
  }
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }
  table {
    position:relative;
    th {
      text-align: center;
      white-space:nowrap;
    }
    tr:nth-of-type(2n+1){
      background:#e9ecef;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
  .card-body{
    overflow-x:auto;
  }
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 100px;
        text-align:left;
        min-height:39.5px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          white-space: nowrap;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
`;

export default ChemicalInfoModify;
