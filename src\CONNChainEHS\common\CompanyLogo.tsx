import React from 'react';
import styled from 'styled-components';
import { ComponentSize } from '../enums/ComponentSize';

interface CompanyLogoProps {
  size?: ComponentSize;
}

const CompanyLogo: React.FC<CompanyLogoProps> = ({ size = ComponentSize.MEDIUM }) => {
  return (
    <StyledCompanyLogo $size={size}>
      <span className="company-name">Cloud</span>
      <span className="company-center-name">t</span>
      <span className="company-name">hink</span>
    </StyledCompanyLogo>
  );
};

const StyledCompanyLogo = styled.div<{ $size: ComponentSize }>`
  display: inline-flex;
  align-items: baseline;
  
  .company-name {
    font-family: 'Chiller';
    font-size: ${props =>
    props.$size === ComponentSize.SMALL ? '120%' :
      props.$size === ComponentSize.LARGE ? '200%' : '160%'
  };
    color: black;
    line-height: 1;
  }

  .company-center-name {
    font-family: 'Chiller';
    font-size: ${props =>
    props.$size === ComponentSize.SMALL ? '190%' :
      props.$size === ComponentSize.LARGE ? '300%' : '250%'
  };
    color: #235601;
    line-height: 0.8;
  }
`;

export default CompanyLogo; 