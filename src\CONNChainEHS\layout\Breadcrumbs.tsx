import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { AppPaths } from '../../config/app-paths';

export interface BreadcrumbItem {
    label: string;
    path?: string;
}

interface BreadcrumbsProps {
    items: BreadcrumbItem[];
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ items }) => {
    const { t } = useTranslation();
    return (
        <ol className="breadcrumb float-xl-end">
            <li className="breadcrumb-item"><a href={`/${AppPaths.home}`}>{t("func.home")}</a></li>
                {items.map((item, index) => (
                    <li key={index} className={`breadcrumb-item ${!item.path ? 'active' : ''}`}>
                        {item.path ? (
                            <Link to={'/'+item.path}>{(item.label)}</Link>
                        ) : (
                            (item.label)
                        )}
                    </li>
                ))}
            </ol>
    )
}

    export default Breadcrumbs;