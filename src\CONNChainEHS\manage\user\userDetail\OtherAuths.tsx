import BlockUi from "@availity/block-ui";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { RouAPI } from "../../../../api/rouAPI";
import BlockuiMsg from "../../../common/BlockuiMsg";
import NamesSplitFragment from "../../../common/NamesSplitFragment";
import SelectOrg from "../../../common/SelectOrg";
import { confirmMsg, errorMsg } from "../../../common/SwalMsg";
import { showSuccessToast } from "../../../common/Toast";
import { ORG_SPLIT_FLAG, ROLE_CAN_MODIFY_MIN_LEVEL, ROLE_LAB_MAX_LEVEL, ROU_ALL, ROU_TYPE_ORG } from "../../../constant/constants";
import useLoginUser from "../../../hooks/useLoginUser";
import { EhsOrg } from "../../../models/EhsOrg";
import { EhsOrgLevel } from "../../../models/EhsOrgLevel";
import { OrgSelect } from "../../../models/EhsOrgSelect";
import { EhsRole } from "../../../models/EhsRole";
import { EhsRou } from "../../../models/EhsRou";
import { isArrayEmpty } from "../../../utils/arrayUtil";
import { isApiCallSuccess } from "../../../utils/resultUtil";
import { EhsArea } from "../../../models/EhsArea";
import { compareStrings } from "../../../utils/stringUtil";
import { getBasicLoginUserInfo, getLoginUserRoleLevel } from "../../../utils/authUtil";

export default function OtherAuths(props: {
    isEditMode: boolean, userId: string, otherAuthList: EhsRou[], areaList: EhsArea[], roleList: EhsRole[], fetchUserRole: () => void,
    orgInfo: OrgSelect
}) {
    const { isEditMode, userId, otherAuthList, areaList, roleList, fetchUserRole, orgInfo } = props;
    const { t } = useTranslation();
    const { loginUser } = useLoginUser();
    const roleLevel = getLoginUserRoleLevel(loginUser);
    const [loadingBlock, setLoadingBlock] = useState(false);
    const [orgLevelList, setOrgLevelList] = useState<EhsOrgLevel[]>([]);
    const [orgList, setOrgList] = useState<EhsOrg[]>([]);
    const [selectOrgIds, setSelectOrgIds] = useState<Record<string, string>>({});
    const [selectedAreaId, setSelectedAreaId] = useState<string>("")
    const [selectRoleId, setSelectRoleId] = useState<string>("");
    const roleOtherAuthList = roleList.filter((role: EhsRole) => role.roleLevel < ROLE_LAB_MAX_LEVEL && role.roleLevel > ROLE_CAN_MODIFY_MIN_LEVEL &&
        role.roleLevel > roleLevel);
    const isCanModifyLevel = roleLevel < ROLE_LAB_MAX_LEVEL;
    const isCurrentUser = compareStrings(loginUser?.loginUserId ?? "", userId);
    const showModifyObj = isEditMode && !isCurrentUser && isCanModifyLevel;

    useEffect(() => {
        setOrgLevelList(orgInfo.orgLevelList)
        setOrgList(orgInfo.orgList)
    }, [orgInfo])

    useEffect(() => {
        if (!selectRoleId && !isArrayEmpty(roleOtherAuthList)) {
            setSelectRoleId(roleOtherAuthList[0].roleId);
        }
    }, [roleOtherAuthList])

    const changeSelectRole = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectRoleId(e.target.value);
    }

    const addOtherAuth = () => {
        setLoadingBlock(true);
        const maxKey = Object.keys(selectOrgIds)
            .filter(key => selectOrgIds[key])
            .reduce((maxKey, key) => key > maxKey ? key : maxKey, "-1");
        const orgid = selectOrgIds[maxKey] || ROU_ALL;

        RouAPI.addSingleRou({
            ...getBasicLoginUserInfo(loginUser)!,
            labId: ROU_ALL,
            orgId: orgid,
            userId: userId!,
            roleId: selectRoleId,
            rouType: ROU_TYPE_ORG
        }).then(result => {
            if (isApiCallSuccess(result)) {
                showSuccessToast(t('message.success'));
                fetchUserRole();
            } else {
                errorMsg(result.message);
            }
            setLoadingBlock(false);
        }).catch(err => {
            errorMsg(err);
            setLoadingBlock(false);
        })
    }

    return (
        <div className="tab-pane fade" id="user-tab-2">
            <StyledOtherAuths>
                <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
                    {showModifyObj && <div className="card mb-3">
                        <div className="card-body">
                            <div className="row align-items-end">
                                <div className="col-md-3">
                                    <h5>{t('table.title.role.item')}</h5>
                                    <select className="form-select" value={selectRoleId} onChange={(e) => changeSelectRole(e)}>
                                        {roleOtherAuthList.map((data: EhsRole) => <option key={'otherauth_role' + data.roleId} value={data.roleId}>{data.roleName}</option>)}
                                    </select>
                                </div>
                                <div className="col-md-3">
                                    <h5>{t('text.area.item')}</h5>
                                    <select className="form-select" data-parsley-required="true" value={selectedAreaId} onChange={(e) => { setSelectedAreaId(e.target.value) }}>
                                        <option value="">{t('text.all')}</option>
                                        {areaList.map((data: EhsArea) => <option key={'area_opt' + data.areaId} value={data.areaId}>{data.areaName}</option>)}
                                    </select>
                                </div>
                                {orgLevelList.map((orglv =>
                                    <SelectOrg key={'orglv' + orglv.orglvId} className="col-md-3" labelClassName="h5"
                                        orglv={orglv} orgList={orgList} selectOrgIds={selectOrgIds} setSelectOrgIds={setSelectOrgIds} defaultOptionType={1}
                                        filterCondition={{
                                            areaId: selectedAreaId
                                        }} />
                                ))}
                                <div className="col-md-3">
                                    <button type="button" className="btn btn-purple mt-3" title={t('button.add')} onClick={addOtherAuth}>
                                        <i className="fa-solid fa-user-plus me-1"></i> {t('button.add')}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>}
                    {/* 其他權限表格 */}
                    <table className="table table-hover text-center align-middle">
                        <thead className="fs-4 fw-bold table-bordered">
                            <tr>
                                <th className="text-start">{t('table.title.role.item')}</th>
                                <th className="text-start">{t('table.title.org.item')}</th>
                                {showModifyObj && <th>{t('table.title.action')}</th>}
                            </tr>
                        </thead>
                        <tbody className="text-center fs-5">
                            {otherAuthList.map(data => <Row key={'ownerauth' + data.rouId} otherAuth={{ ...data }} showModifyObj={showModifyObj} fetchUserRole={fetchUserRole} setLoadingBlock={setLoadingBlock} />)}
                        </tbody>
                    </table>
                </BlockUi>
            </StyledOtherAuths>
        </div>
    );

}


const Row = (props: {
    otherAuth: EhsRou, showModifyObj: boolean, fetchUserRole: () => void,
    setLoadingBlock: (block: boolean) => void,
}) => {
    const { t } = useTranslation();
    const { loginUser } = useLoginUser()
    const { otherAuth, showModifyObj, fetchUserRole, setLoadingBlock } = props;
    const clickDelete = () => {
        confirmMsg(t('message.confirm.delete'), t).then((value) => {
            if (value) {
                setLoadingBlock(true);
                RouAPI.delRou({
                    ...getBasicLoginUserInfo(loginUser)!,
                    rouId: otherAuth.rouId,
                    deleteBasicRou: true
                }).then(result => {
                    if (isApiCallSuccess(result)) {
                        fetchUserRole();
                        showSuccessToast(t('message.success'));
                    } else {
                        errorMsg(result.message);
                    }
                    setLoadingBlock(false);
                }).catch(err => {
                    errorMsg(err);
                    setLoadingBlock(false);
                })
            }
        })
    }
    return (<tr>
        <td data-title={t('table.title.role.item')} className="text-start">{otherAuth.roleName}</td>
        <td data-title={t('table.title.org.item')} className="text-start"><NamesSplitFragment names={otherAuth.orgNames || t('text.all')} separator={ORG_SPLIT_FLAG} /></td>
        {showModifyObj && <td data-title={t('table.title.action')}>
            <button type="button" className="btn btn-danger fs-5" title={t('button.delete')} onClick={clickDelete}><i className="fas fa-trash-can me-1" />{t('button.delete')}</button></td>}
    </tr>)
}

const StyledOtherAuths = styled.div`
    .fas.fa-trash-can{
        cursor:pointer;
    }
  thead {
    background:rgb(251, 205, 165);
  }
    @media (max-width: 600px){
        label {
            width:200px;
        }
        .buttonPanel {
            margin-top:10px;
            display:flex;
            justify-content: flex-end;
        }
        thead {
            display:none;
        }
        tbody, td, tr {
            display:block;
            background: #fff !important;
            box-shadow: inset 0 0 0 9999px white;

        }
        tr {
            border: 1px solid #ccc;
            margin-bottom: 10px;
            background: #fff !important;
        }
        td {
            background: #fff!important;
            position:relative;
            padding-left: 100px;
            text-align:left;
        }
        td::before {
            content: attr(data-title);
            position: absolute;
            top: 6px;
            left: 6px;
            width: 30%;
            padding-right: 10px;
            white-space: nowrap;
            text-align: left;
            font-weight: bold;
            color: #1a1a1a;
        }
    
    }

`