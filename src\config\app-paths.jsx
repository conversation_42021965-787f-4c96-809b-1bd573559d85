// app-paths.js
export const AppPaths = {
    login: '',
    resetPwd: 'reset-pwd/*',
    home: 'home',
    system: {
        actionRecordList: 'system/action/record/list',
        barcodeFormatSetting: 'system/barcode/format/setting',
        customOptionSetting: 'system/option/setting',
        funcList: 'system/func/list',
        funcDetail: 'system/func/detail',
        funcGroupList: 'system/func/group/list',
        funcGroupDetail: 'system/func/group/detail',
        funcGroupEdit: 'system/func/group/edit',
        orgLevelList: 'system/org/level/list', 
        roleList: 'system/role/list',
        roleEdit: 'system/role/edit',
        roleDetail: 'system/role/detail',
    },
    manage: {
        areaList: 'manage/area/list',
        buildingList: 'manage/building/list',
        buildingFloorList: 'manage/building/floor/list',
        userList: 'manage/user/list',
        userAdd: 'manage/user/add',
        userDetail: 'manage/user/detail',
        userEdit: 'manage/user/edit',
        orgList: 'manage/org/list',
        labList: 'manage/lab/list',
        labAdd: 'manage/lab/add',
        labEdit: 'manage/lab/edit',
        labDetail: 'manage/lab/detail',
    },
    signoff: {
        purchaseList: 'signoff/purchase/list',
        chemicalModifyList: 'signoff/chemical/modify/list',
        labUserModifyList: 'signoff/lab/user/modify/list',
        signoffDetail: 'signoff/detail',
    },
    chemical: {
        informationList: 'chemical/information/list',
        informationAdd: 'chemical/information/add',
        informationEdit: 'chemical/information/edit',
        informationDetail: 'chemical/information/detail',
        purchaseRequest: 'chemical/purchase/request',
        purchaseList: 'chemical/purchase/list',
        purchaseDetail: 'chemical/purchase/detail',
        purchaseSignOff: 'chemical/purchase/signoff',
        purchaseArrival: 'chemical/purchase/arrival',
        purchaseInspection: 'chemical/purchase/inspection',
        purchaseReturn: 'chemical/purchase/return',
        purchaseCancel: 'chemical/purchase/cancel',
        itemAdd: 'chemical/item/add',
        operationList: 'chemical/operation/list',
        operationItemList: 'chemical/operation/item/list',
        operationRecordList: 'chemical/operation/record/list',
        operationDetail: 'chemical/operation/detail',
        operationStopList: 'chemical/operation/stop/list',
        barcodeList: 'chemical/barcode/list',
    },
    waste: {
        wasteInformationList: 'waste/information/list',
    },
    information: {
        newsList: 'information/news/list',
        newsDetail: 'information/news/detail',
        newsManageList: 'information/news/manage/list',
        newsModify: 'information/news/manage/modify',
    },
    toshms: {
        oshmsList: 'toshms/oshms/list',
        oshmsManageList: 'toshms/oshms/manage/list',
        oshmsEdit: 'toshms/oshms/manage/edit',
        riskList: 'toshms/risk/list',
    }
};

