import { useMemo } from 'react';
import { getUserPreferences } from '../utils/storageUtil';
import { useTranslation } from 'react-i18next';
import { getMenuByUserInfo } from 'ehs/utils/funcUtil';
import useLoginUser from './useLoginUser';
 
export function useMenu() {
    const { t, i18n } = useTranslation();
    const { loginUser } = useLoginUser();
    const userPreferences = getUserPreferences();
    const menuData = useMemo(() => {
        const { menuList, userMenuMap } = getMenuByUserInfo(loginUser, userPreferences?.userMenuList || [], t);
        return {
            menuMap: Array.from(userMenuMap.keys()),
            menuList: menuList
        };
    }, [i18n.language, loginUser]);

    return menuData;
}