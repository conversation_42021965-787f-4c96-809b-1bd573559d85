import { SEARCH_METHOD_EXACT, SEARCH_METHOD_FUZZY } from 'ehs/constant/constants';
import React from 'react';
import { useTranslation } from 'react-i18next';

interface SearchMethodProps {
    className?: string;
    searchMethod: string;
    handleSearchMethodChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const SearchMethod = ({ className = "", searchMethod = SEARCH_METHOD_FUZZY, handleSearchMethodChange = () => { } }: SearchMethodProps) => {
    const { t } = useTranslation();

    const searchOptions = [
        { id: 'search_fuzzy', value: SEARCH_METHOD_FUZZY, label: t('text.search.fuzzy') },
        { id: 'search_exact_match', value: SEARCH_METHOD_EXACT, label: t('text.search.exact_match') }
    ];

    return (
        <div className={className}>
            <label>{t('text.search.method')}</label><br />
            {searchOptions.map(option => (
                <div key={option.id} className="form-check form-check-inline mt-1">
                    <input
                        type="radio"
                        className="form-check-input"
                        id={option.id}
                        name="searchChemicals"
                        value={option.value}
                        checked={searchMethod === option.value}
                        onChange={handleSearchMethodChange}
                    />
                    <label htmlFor={option.id}>{option.label}</label>
                </div>
            ))}
        </div>
    );
};

export default SearchMethod;
