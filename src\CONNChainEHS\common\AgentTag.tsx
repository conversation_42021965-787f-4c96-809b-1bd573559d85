import { useTranslation } from 'react-i18next';
import useLoginUser from 'CONNChainEHS/hooks/useLoginUser';

interface AgentTagProps {
    className?: string;
}

export const AgentTag: React.FC<AgentTagProps> = ({
    className,
}) => {
    const { t } = useTranslation();
    const { isAgent } = useLoginUser();

    if (!isAgent) return null;

    return (
        <span className={className}>{t('text.agent')}</span>
    );
};