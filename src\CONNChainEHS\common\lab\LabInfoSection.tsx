import React from 'react';
import { useTranslation } from 'react-i18next';
import { findItemByLangType, getLabTextObj } from 'ehs/utils/langUtil';
import { EhsLab } from 'ehs/models/EhsLab';
import useLoginUser from 'ehs/hooks/useLoginUser';
import { LAB_STATUS_CAN_OPERATION } from 'ehs/constant/constants';

interface LabInfoSectionProps {
    labData: EhsLab;
}

export const LabInfoSection: React.FC<LabInfoSectionProps> = ({
    labData,
}) => {
    const { loginUser } = useLoginUser();
    const { t, i18n } = useTranslation();
    const { labInformation, labLocation, labPhoneText, labPhoneExtText, msgLabDisabledOper } = getLabTextObj(loginUser, t);
    const {
        labId,
        labNameList,
        buildName,
        labFloor,
        labHousenum,
        labPhone,
        labPhoneExt,
        labStatus
    } = labData;
    const isLabCanOperation = LAB_STATUS_CAN_OPERATION.includes(labStatus);

    return (
        <div className="row justify-content-around mb-1">
            <h4>{labInformation}</h4>
            <div className="col-xl-10 fs-4 offeset-xl-2 mt-1">
                <label className="h2 d-flex align-items-center gap-2">
                    {findItemByLangType(labNameList, i18n.language)?.langValue}
                    {labId && !isLabCanOperation && (
                        <span className="badge bg-warning text-dark fs-6">
                            {msgLabDisabledOper}
                        </span>
                    )}
                </label>
                <div className="row">
                    <div className="col-12 col-md-4">
                        <ul>
                            <li>{labLocation}：{buildName} {labFloor} {labHousenum}</li>
                        </ul>
                    </div>
                    <div className="col-12 col-md-4">
                        <ul>
                            <li>{labPhoneText}：{labPhone}</li>
                        </ul>
                    </div>
                    <div className="col-12 col-md-4">
                        <ul>
                            <li>{labPhoneExtText}：{labPhoneExt}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    );
}; 