import { ChemicalAPI } from "api/chemicalAPI";
import { FileAPI } from "api/fileAPI";
import { CONFIG_SUB_TYPE_CCB_GROUP_S, CONFIG_SUB_TYPE_CCB_SECURITY_ENV, FILE_TYPE_CCB_FORM } from "ehs/constant/constants";
import { ChemicalCcbLinkType } from "ehs/enums/ChemicalCcbLinkType";
import useLoginUser from "ehs/hooks/useLoginUser";
import { EhsChemicalCcbDetail, initEhsChemicalCcbDetail } from "ehs/models/EhsChemicalCcbDetail";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { getBasicLoginUserInfo } from "ehs/utils/authUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import DownloadButton from "../button/DownloadButton";

interface CcbResponse {
  ccbDetail: EhsChemicalCcbDetail; // 根據實際 ccbDetail 的結構定義適當的介面
  ccbFormList: EhsConfigParam[]; // 根據實際 ccbFormList 的結構定義適當的介面
}

function CcbViewDialog(props: {
  onClose: () => void;
  setLoadingBlock: (block: boolean) => void;
  mode: boolean | null;
  linkType: ChemicalCcbLinkType;
  linkId: string;
  forceDataRefresh?: number;
}) {
  const { onClose, setLoadingBlock, mode, linkType, linkId, forceDataRefresh = 0 } = props;
  const { loginUser } = useLoginUser();
  const { t, i18n } = useTranslation();
  const [ccbDetail, setCcbDetail] = useState<EhsChemicalCcbDetail>(initEhsChemicalCcbDetail);
  const [ccbFormList, setCcbFormList] = useState<EhsConfigParam[]>([]);
  const isGroupS = ccbDetail?.isGroupS === 1;

  useEffect(() => {
    if (linkType && linkId && loginUser) {
      fetchCcbData();
    }
  }, [loginUser, linkType, linkId, forceDataRefresh]);

  const fetchCcbData = async () => {
    try {
      setLoadingBlock(true);
      const result = await ChemicalAPI.getCcbDetail({
        ...getBasicLoginUserInfo(loginUser)!,
        linkType: linkType,
        linkId: linkId,
      });

      if (isApiCallSuccess(result)) {
        const responseData = result.results as CcbResponse;
        setCcbDetail(responseData.ccbDetail);
        setCcbFormList(responseData.ccbFormList);
      }
    } catch (error) {
      console.error('獲取 CCB 資料失敗:', error);
    } finally {
      setLoadingBlock(false);
    }
  }

  const handleDownloadExecutionRecord = async () => {
    try {
      await ChemicalAPI.downloadCcbExecutionRecord({
        ...getBasicLoginUserInfo(loginUser),
        ccbDetailId: ccbDetail.ccbDetailId
      });
    } catch (error) {
      console.error('下載執行紀錄失敗:', error);
    }
  };

  const handleDownloadCheckList = async () => {
    try {
      await ChemicalAPI.downloadCcbCheckList({
        ...getBasicLoginUserInfo(loginUser),
      });
    } catch (error) {
      console.error('下載檢查表失敗:', error);
    }
  };

  return (
    <StyledCcbViewDialog>
      <div className="ccb-view">
        <div className="ccb-view-header">
          <h4 className="modal-title">{t('text.chemical.ccb.item')}</h4>
          <button
            type="button"
            className="btn-close"
            aria-hidden="true"
            onClick={onClose}
          ></button>
        </div>
        <div className="ccb-view-body">
          {!ccbDetail?.ccbDetailId ? (
            <div className="no-data-container">
              <div className="no-data-message">
                <i className="fas fa-exclamation-circle me-2"></i>
                {t('text.chemical.ccb.no_ccb_data_available')}
              </div>
            </div>
          ) : (
            <>
              <h3 className="ms-3 mb-3">
                {t('text.chemical.ccb.risk_level_display_title', {
                  riskLevel: ccbDetail?.ccbRiskLevelId
                    ? ccbDetail.ccbRiskLevelId.slice(-1)
                    : ''
                })}
              </h3>
              <TableSection className="table-responsive">
                <h3>{t('text.chemical.ccb.inhalation_hazard_form')}</h3>
                <TableContainer>
                  <thead className="nxp-bg-beige">
                    <tr>
                      <th className="text-start">{t('text.chemical.ccb.work_type')}</th>
                      <th className="text-start">{t('text.chemical.ccb.exposure_control_form')}</th>
                    </tr>
                  </thead>
                  <TableContent
                    configSubType={ccbDetail?.ccbRiskLevelId}
                    ccbFormList={ccbFormList}
                    loginUser={loginUser}
                  />
                </TableContainer>
              </TableSection>
              {isGroupS && (
                <>
                  <TableSection className="table-responsive">
                    <h3>{t('text.chemical.ccb.skin_contact_form')}</h3>
                    <TableContainer>
                      <thead className="nxp-bg-beige">
                        <tr>
                          <th className="text-start">{t('text.chemical.ccb.work_type')}</th>
                          <th className="text-start">{t('text.chemical.ccb.exposure_control_form')}</th>
                        </tr>
                      </thead>
                      <TableContent
                        configSubType={CONFIG_SUB_TYPE_CCB_GROUP_S}
                        ccbFormList={ccbFormList}
                        loginUser={loginUser}
                      />
                    </TableContainer>
                  </TableSection>
                  <TableSection className="table-responsive">
                    <h3>{t('text.chemical.ccb.security_env_form')}</h3>
                    <TableContainer>
                      <thead className="nxp-bg-beige">
                        <tr>
                          <th className="text-start">{t('text.chemical.ccb.work_type')}</th>
                          <th className="text-start">{t('text.chemical.ccb.exposure_control_form')}</th>
                        </tr>
                      </thead>
                      <TableContent
                        configSubType={CONFIG_SUB_TYPE_CCB_SECURITY_ENV}
                        ccbFormList={ccbFormList}
                        loginUser={loginUser}
                      />
                    </TableContainer>
                  </TableSection>
                </>
              )}
              <div className="d-flex justify-content-center gap-2 my-4">
                <button
                  className="btn btn-primary"
                  type="button"
                  title={t("button.download_ccb_execution_record")}
                  onClick={handleDownloadExecutionRecord}
                ><i className="fa fa-lg fa-file-word me-1" />
                  {t("button.download_ccb_execution_record")}
                </button>
                <button
                  className="btn btn-primary"
                  type="button"
                  title={t("button.download_ccb_check_list")}
                  onClick={handleDownloadCheckList}
                ><i className="fa fa-lg fa-file-word me-1" />
                  {t("button.download_ccb_check_list")}
                </button>
              </div>
            </>
          )}
        </div>
        {<div className="ccb-view-footer">
          <div className="d-flex justify-content-end gap-2">
            <button className="btn btn-white" onClick={onClose}>
              <i className="fas fa-times me-1" />
              {t("button.close")}
            </button>
          </div>
        </div>}
      </div>
    </StyledCcbViewDialog >
  );
}

interface TableContentProps {
  configSubType: string;
  ccbFormList: EhsConfigParam[];
  loginUser: any;
}

const fetchCcbFile = async (configId: string, loginUser: any) => {
  const result = await FileAPI.getFileList({
    ...getBasicLoginUserInfo(loginUser)!,
    fileTypeList: [FILE_TYPE_CCB_FORM],
    fileMappingIdList: [configId]
  });

  if (isApiCallSuccess(result) && !isArrayEmpty(result.results)) {
    const fileInfo = result.results[0];
    return {
      content: fileInfo.fileContentBase64,
      fileName: fileInfo.fileName || ''
    };
  }
  return {
    content: '',
    fileName: ''
  };
};

const TableContent = ({ configSubType, ccbFormList, loginUser }: TableContentProps) => {
  const filteredItems = ccbFormList.filter(item => item.configSubType === configSubType);
  const { t } = useTranslation();

  return (
    <TableBody>
      {filteredItems.map((item, index) => (
        <tr key={index}>
          <td data-label={t('text.chemical.ccb.work_type')}>{item.configName}</td>
          <td data-label={t('text.chemical.ccb.exposure_control_form')}>
            <DownloadButton
              fileName={item.configValue || ''}
              onDownload={() => fetchCcbFile(item.configId || '', loginUser)}
            />
          </td>
        </tr>
      ))}
    </TableBody>
  );
};

const StyledCcbViewDialog = styled.div`
  background: white;
  width: 100%;
  max-width: 1400px;
  height: 90vh;
  display: flex;
  flex-direction: column;

  .ccb-view {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .ccb-view-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  .ccb-view-body {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    max-height: calc(90vh - 120px);
  }
  .ccb-view-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-purple {
      margin-left: 10px;
    }
  }

  @media (max-width: 1024px) {
    max-width: 95%;
    
    .ccb-view-body {
      padding: 10px;
    }
  }

  @media (max-width: 768px) {
    max-width: 100%;
    height: 95vh;
    
    .ccb-view-body {
      padding: 8px;
      max-height: calc(95vh - 110px);
    }
    
    h3 {
      font-size: 1rem;
      margin-bottom: 0.5rem;
    }
    
    button {
      font-size: 0.9rem;
      padding: 0.375rem 0.5rem;
    }
  }

  @media (max-width: 480px) {
    height: 100vh;
    
    .ccb-view-header {
      padding: 10px;
    }
    
    .ccb-view-body {
      padding: 5px;
      max-height: calc(100vh - 100px);
    }
    
    .ccb-view-footer {
      padding: 10px;
    }
    
    button {
      font-size: 0.8rem;
      padding: 0.25rem 0.4rem;
      
      i {
        margin-right: 0.2rem !important;
      }
    }
  }

  .no-data-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }
  
  .no-data-message {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    font-size: 1.1rem;
    color: #6c757d;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    
    @media (max-width: 768px) {
      padding: 15px;
      font-size: 1rem;
    }
    
    @media (max-width: 480px) {
      padding: 10px;
      font-size: 0.9rem;
    }
  }
`;

const TableSection = styled.div`
  margin-bottom: 2rem;
  padding: 0 15px; 
  position: relative;
  max-height: 550px;
  overflow-y: auto;
  
  h3 {
    position: sticky;
    top: 0;
    background: white;
    padding: 10px 0;
    margin: 0;
    color: #333;
    font-size: 1.2rem;
    z-index: 2;
  }

  @media (max-width: 480px) {
    padding: 0 5px;
    margin-bottom: 1rem;
    
    h3 {
      font-size: 1rem;
      padding: 8px 0;
    }
  }
`;

const TableContainer = styled.table`
  width: 100%;
  border-collapse: collapse; 

  thead {
    th {
      padding: 16px 12px;
      vertical-align: middle;
      border: 1px solid #ddd;
      background-color: #f5f5f5;
      font-weight: 600;
      
      &:first-child {
        width: 60%;
      }
      
      &:last-child {
        width: 40%;
      }
      
      @media (max-width: 768px) {
        padding: 12px 8px;
        font-size: 0.9rem;
      }
      
      @media (max-width: 480px) {
        padding: 8px 6px;
        font-size: 0.85rem;
        width: 60% !important;
        
        &:last-child {
          width: 40% !important;
        }
      }
    }
  }
`;

const TableBody = styled.tbody`
  tr { 
    
    td {
      padding: 12px;
      border: 1px solid #ddd;
      
      @media (max-width: 768px) {
        padding: 10px 8px;
        font-size: 0.9rem;
      }
      
      @media (max-width: 480px) {
      
        &:first-child {
          width: 100%;
        }
        
        &:last-child {
          text-align: center;
        }
        
        padding: 8px 6px;
        font-size: 0.85rem;
        display: table-cell;
        vertical-align: middle;
        
        &:last-child {
          width: 40% !important;
          text-align: center;
        }
      }
    }
  } 
`;

export default CcbViewDialog;