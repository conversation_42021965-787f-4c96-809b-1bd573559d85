import { EhsNews } from "ehs/models/EhsNews";
import { EhsFile } from "../CONNChainEHS/models/EhsFile";
import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { apiRequest, createLoginPostFormDataConfig, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const NewsAPI = {
  getNewsList: async (
      parms: BaseParams & {
      newsStatus: number;
      maxResults: number;
    }
  ) => {
    return apiRequest(getApiAddress + "news/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getNewsById: async (
    parms: BaseParams & {
      newsId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "news/detail", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addNews: async (
    parms: BaseParams & {
      ehsNews: EhsNews;
      fileList?: EhsFile[];
    }
  ) => {
    const formData = new FormData();
    const jsonParams = Object.keys(parms).reduce((acc, key) => {
      acc[key] = (parms as any)[key]; // 使用類型斷言
      return acc;
    }, {} as any);
    jsonParams.fileList = parms.fileList?.map((file: EhsFile) => {
      return {
        ...file,
        fileContent: [], // 將 fileContent 設定為空陣列
      };
    });
    formData.append("jsonString", JSON.stringify(jsonParams));

    // 將檔案列表添加到FormData中
    if (parms.fileList) {
      parms.fileList.forEach((file, index) => {
        if (file.fileObj) {
          // 確認 file.fileObj 存在
          formData.append(`multiFileList[${index}]`, file.fileObj);
        }
      });
    }
    return apiRequest(getApiAddress + "news/add", createLoginPostFormDataConfig(parms, formData)).then((res) => handleFetchResponse(res));
  },
  editNews: async (
    parms: BaseParams & {
      ehsNews: EhsNews;
      fileList?: EhsFile[];
    }
  ) => {
    const formData = new FormData();
    const jsonParams = Object.keys(parms).reduce((acc, key) => {
      acc[key] = (parms as any)[key]; // 使用類型斷言
      return acc;
    }, {} as any);
    jsonParams.fileList = parms.fileList?.map((file: EhsFile) => {
      return {
        ...file,
        fileContent: [], // 將 fileContent 設定為空陣列
      };
    });

    // 將檔案列表添加到FormData中
    if (parms.fileList) {
      parms.fileList.forEach((file, index) => {
        if (file.fileObj) {
          // 確認 file.fileObj 存在
          formData.append(`multiFileList[${index}]`, file.fileObj);
        }
      });
    }
    formData.append("jsonString", JSON.stringify(jsonParams));
    return apiRequest(getApiAddress + "news/edit", createLoginPostFormDataConfig(parms, formData)).then((res) => handleFetchResponse(res));
  },
  increaseViews: async (
    parms: BaseParams & {
    newsId: string;
  }
  ) => {
    return apiRequest(getApiAddress + "news/views/increase", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};
