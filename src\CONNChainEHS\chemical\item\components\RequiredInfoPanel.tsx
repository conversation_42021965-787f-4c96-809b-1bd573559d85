import InputDate from 'ehs/common/input/InputDate';
import { EhsConfigParam } from 'ehs/models/EhsConfigParam';
import { SelectItem } from 'ehs/models/SelectItem';
import { isArrayEmpty } from 'ehs/utils/arrayUtil';
import { getFormatDateSlash } from 'ehs/utils/stringUtil';
import React from 'react';
import { Controller, UseFormRegister, Control, FieldErrors, UseFormSetValue } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import ItemWeightField from './ItemWeightField';
import InputNumFloat from 'ehs/common/input/InputNumFloat';
import { CHEMICAL_WEIGHT_MIN, CHEMICAL_WEIGHT_MAX, CHEMICAL_WEIGHT_DECIMAL_PLACES, CHEMICAL_TEMPERATURE_DECIMAL_PLACES, CHEMICAL_TEMPERATURE_MAX, CHEMICAL_TEMPERATURE_MIN } from 'ehs/constant/constants';

interface RequiredInfoPanelProps {
    register: UseFormRegister<any>;
    control: Control<any>;
    setValue: UseFormSetValue<any>;
    errors: FieldErrors;
    registerObj: any;
    isOperPurch: boolean;
    operTypeName: string;
    vendorOption: SelectItem[];
    storageLocationOptions: EhsConfigParam[];
    hasSelectedPhaseStateOptions: EhsConfigParam[];
    powderLevelOptions: EhsConfigParam[];
    ccbProcessTempOptions: EhsConfigParam[];
    isChooseSolid: boolean;
    isChooseLiquid: boolean;
    addItemChemicalInfo: any;
    changeAddItemChemInfo: (info: object) => void;
    hasSubmit: boolean;
    setShowEditWeights: (isShow: boolean) => void;
}

const RequiredInfoPanel: React.FC<RequiredInfoPanelProps> = ({
    register,
    control,
    setValue,
    errors,
    registerObj,
    isOperPurch,
    operTypeName,
    vendorOption,
    storageLocationOptions,
    hasSelectedPhaseStateOptions,
    powderLevelOptions,
    ccbProcessTempOptions,
    isChooseSolid,
    isChooseLiquid,
    addItemChemicalInfo,
    changeAddItemChemInfo,
    hasSubmit,
    setShowEditWeights
}) => {
    const { t } = useTranslation();

    return (
        <div className="row border border-2 border-info py-3">
            <h3 className="mb-3">{t('text.chemical.item_add_req_info')}<span className="text-danger ms-1">*</span></h3>
            <div className="col-sm-12 col-md-6 col-xl-3 mb-4">
                <label className="fw-bold">{t('table.title.product.item_number')}</label>
                <input type="text" className={`form-control ${errors[registerObj.productNo] && 'is-invalid error-field'}`}
                    {...register(registerObj.productNo, {
                        required: t("message.enter"),
                    })} />
                {errors.productNo && <div className="mt-2"><span className="text-danger">{errors.productNo.message?.toString()}</span></div>}
            </div>
            <div className="col-sm-12 col-md-6 col-xl-3 mb-4">
                <label className="fw-bold">{isOperPurch ? t('text.arrival_date') : t('table.title.chemical.item_add_date', {
                    operType: operTypeName
                })}</label>
                <Controller
                    control={control}
                    name={registerObj.addDate}
                    rules={{ required: t("message.select") }}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                        <InputDate className={`form-control ${errors[registerObj.addDate] && 'is-invalid error-field'}`}
                            defaultValue={getFormatDateSlash(value)}
                            onChange={date => {
                                setValue(registerObj.addDate, date, { shouldValidate: true });
                            }}
                        />
                    )}
                />
                {errors[registerObj.addDate] && <div className="mt-1"><span className="text-danger">{errors[registerObj.addDate]?.message?.toString()}</span></div>}
            </div>
            <div className="col-sm-12 col-md-6 col-xl-3 mb-4">
                <label className="fw-bold">{t('table.title.vendor')}</label>
                <select className={`form-select ${errors.manufacturerId && 'is-invalid error-field'}`}
                    {...register(registerObj.manufacturerId, {
                        required: t("message.select"),
                    })}
                    onChange={(e) => {
                        const { value } = e.target;
                        setValue(registerObj.manufacturerId, value, { shouldValidate: true });
                    }}>
                    <option key="defaultOption" value="" title={t("message.select")}>{t("message.select")}</option>
                    {vendorOption.map(item => {
                        return <option value={item.value} key={'vendor-option-' + item.value} title={item.label}>{item.label}</option>;
                    })}
                </select>
                {errors.manufacturerId && <div className="mt-2"><span className="text-danger">{errors.manufacturerId.message?.toString()}</span></div>}
            </div>
            <div className="col-sm-12 col-md-6 col-xl-3 mb-4">
                <label className="fw-bold">{t('table.title.bottle_qty')}</label>
                <select className="form-select" onChange={e => changeAddItemChemInfo({
                    quantity: parseInt(e.target.value)
                })}>
                    {Array.from({
                        length: 20
                    }, (_, index) => <option key={`single_bottle_qty${index + 1}`} value={index + 1}>
                        {index + 1}
                    </option>)}
                </select>
            </div>
            <div className="col-sm-12 col-md-6 col-xl-3 mb-4">
                <label className="fw-bold">{t('table.title.single_bottle_weight')} (kg)</label>
                <ItemWeightField bottleCount={addItemChemicalInfo.quantity} weights={addItemChemicalInfo.weightList}
                    setWeights={weights => changeAddItemChemInfo({
                        weightList: weights
                    })}
                    setShowDialog={(isShow: boolean) => setShowEditWeights(isShow)} hasSubmit={hasSubmit} />
            </div>
            <div className="col-sm-12 col-md-6 col-xl-3 mb-4">
                <label className="fw-bold">{t('table.title.storage_location')}</label>
                <select className={`form-select ${errors.inventoryLocation && 'is-invalid error-field'}`} data-parsley-required="true"
                    {...register(registerObj.inventoryLocation, {
                        required: t("message.select"),
                    })}>
                    <option key="defaultOption" value="" title={t("message.select")}>{t("message.select")}</option>
                    {storageLocationOptions.map(item => {
                        return <option value={item.configId} key={'storage-location-option-' + item.configId} title={item.configName}>{item.configName}</option>;
                    })}
                </select>
                {errors.inventoryLocation && <div className="mt-2"><span className="text-danger">{errors.inventoryLocation.message?.toString()}</span></div>}
            </div>
            <div className="col-sm-12 col-md-6 col-xl-3 mb-4">
                <label className="fw-bold">{t('text.chemical.est_max_use_qty')}</label>
                <Controller
                    name={registerObj.estMaxUseQty}
                    control={control}
                    defaultValue=""
                    rules={{ required: t("message.enter") }}
                    render={({ field: { onChange, onBlur, value } }) => (
                        <InputNumFloat
                            className={`form-control ${errors[registerObj.estMaxUseQty] && 'is-invalid error-field'}`}
                            minValue={CHEMICAL_WEIGHT_MIN}
                            maxValue={CHEMICAL_WEIGHT_MAX}
                            maxLength={7 + CHEMICAL_WEIGHT_DECIMAL_PLACES}
                            decimalPlaces={CHEMICAL_WEIGHT_DECIMAL_PLACES}
                            value={value}
                            onChange={onChange}
                            onBlur={onBlur}
                            allowEmptyUndefine
                            placeholder={t('text.chemical.est_max_use_qty_placeholder')}
                        />
                    )}
                />
                {errors.estMaxUseQty && <div className="mt-2"><span className="text-danger">{errors.estMaxUseQty.message?.toString()}</span></div>}
            </div>
            {!isArrayEmpty(hasSelectedPhaseStateOptions) && (
                <div className="col-sm-12 col-md-6 col-xl-3 mb-4">
                    <label className="d-flex align-items-center fw-bold">
                        {t('table.title.chemical.product_phase_state')}
                    </label>

                    {hasSelectedPhaseStateOptions.length === 1 ? (
                        // 如果只有一個選項，僅顯示 label
                        <span>{hasSelectedPhaseStateOptions[0].configName}</span>
                    ) : (
                        // 否則渲染 radio 按鈕
                        hasSelectedPhaseStateOptions.map((item, index) => {
                            const { configId, configName } = item;
                            const id = 'phasestate-radio_' + configId;
                            return (
                                <div key={'phasestate-radio-' + configId} className="form-check form-check-inline">
                                    <input
                                        className="form-check-input"
                                        type="radio"
                                        name="phasestate"
                                        id={id}
                                        value={configId}
                                        data-parsley-mincheck="1"
                                        defaultChecked={index === 0} // 預設勾選第一個選項
                                        onChange={e => {
                                            changeAddItemChemInfo({
                                                phaseState: e.target.value
                                            });
                                        }}
                                    />
                                    <label className="form-check-label" htmlFor={id}>
                                        {configName}
                                    </label>
                                </div>
                            );
                        })
                    )}
                </div>
            )}
            {isChooseSolid && (
                <div className="col-sm-12 col-md-6 col-xl-3 mb-4">
                    <label className="fw-bold">{t('text.chemical.solid_powder_level')}</label>
                    {powderLevelOptions.map((item, index) => {
                        const { configId, configName } = item;
                        const id = 'powderlevel-radio_' + configId;
                        return (
                            <div key={configId} className="form-check form-check-inline">
                                <input
                                    className="form-check-input"
                                    type="radio"
                                    name="powderlevel"
                                    id={id}
                                    value={configId}
                                    data-parsley-mincheck="1"
                                    defaultChecked={index === 0} // 預設勾選第一個選項
                                    onChange={e => {
                                        changeAddItemChemInfo({
                                            powderLevel: e.target.value
                                        });
                                    }}
                                />
                                <label className="form-check-label" htmlFor={id}>
                                    {configName}
                                </label>
                            </div>
                        );
                    })}
                </div>
            )}
            {isChooseLiquid && (
                <div className="col-sm-12 col-md-6 col-xl-3 mb-4">
                    <label className="fw-bold">{t('text.chemical.ccb_process_temperature')}</label>
                    {ccbProcessTempOptions.map((item, index) => {
                        const { configId, configName, configIvalue } = item;
                        const id = 'ccbprocess-radio_' + configId;
                        return (
                            <div key={configId}>
                                <div className="form-check form-check-inline">
                                    <input
                                        className="form-check-input"
                                        type="radio"
                                        name="ccbprocess"
                                        id={id}
                                        value={configIvalue}
                                        data-parsley-mincheck="1"
                                        defaultChecked={index === 0}
                                        onChange={e => {
                                            changeAddItemChemInfo({
                                                processTemperatureType: e.target.value,
                                                processTemperature: null // 清空自訂溫度
                                            });
                                            setValue(registerObj.processTemperature, null, {
                                                shouldValidate: true  // 可選：是否要觸發驗證
                                            });
                                        }}
                                    />
                                    <label className="form-check-label" htmlFor={id}>
                                        {configName}
                                    </label>
                                </div>
                                {/* 當選中且 configIvalue === "1" 時顯示輸入欄位 */}
                                {addItemChemicalInfo.processTemperatureType === "1" && configIvalue === 1 && (
                                    <div className="mt-2 ms-4">
                                        <Controller
                                            name={registerObj.processTemperature}
                                            control={control}
                                            rules={{
                                                required: t("message.enter"),
                                            }}
                                            render={({ field: { onChange, value } }) => (
                                                <div className="d-flex align-items-center">
                                                    <InputNumFloat
                                                        className={`form-control ${errors[registerObj.processTemperature] && 'is-invalid error-field'}`}
                                                        value={addItemChemicalInfo.processTemperature ?? value}
                                                        onBlur={(newValue) => {
                                                            onChange(newValue);
                                                            changeAddItemChemInfo({
                                                                processTemperature: newValue
                                                            });
                                                        }}
                                                        decimalPlaces={CHEMICAL_TEMPERATURE_DECIMAL_PLACES}
                                                        maxLength={8}
                                                        minValue={CHEMICAL_TEMPERATURE_MIN}
                                                        maxValue={CHEMICAL_TEMPERATURE_MAX}
                                                        allowNegative
                                                        allowEmptyUndefine
                                                    />
                                                    <span className="ms-2">°C</span>
                                                </div>
                                            )}
                                        />
                                        {errors[registerObj.processTemperature] && (
                                            <div className="mt-1">
                                                <span className="text-danger">
                                                    {errors[registerObj.processTemperature]?.message?.toString()}
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        );
                    })}
                </div>
            )}
            <div className="col-sm-12 col-md-6 col-xl-3 mb-4">
                <label className="fw-bold">{t('text.note')}</label>
                <textarea className={`form-control ${errors[registerObj.inventoryNote] && 'is-invalid error-field'}`}
                    {...register(registerObj.inventoryNote, {
                        required: t("message.enter"),
                    })} />
                {errors.inventoryNote && <div className="mt-2"><span className="text-danger">{errors.inventoryNote.message?.toString()}</span></div>}
            </div>
        </div>
    );
};

export default RequiredInfoPanel; 