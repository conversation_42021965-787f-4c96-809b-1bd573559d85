import InputDate from 'ehs/common/input/InputDate';
import InputNumFloat from 'ehs/common/input/InputNumFloat';
import { showWarnToast } from 'ehs/common/Toast';
import { CHEMICAL_OPER_ITEM_NO_DATE, CHEMICAL_OPER_ITEM_NO_WEIGHT, CHEMICAL_PHASE_LIQUID, CHEMICAL_TEMPERATURE_MAX, CHEMICAL_TEMPERATURE_MIN, CHEMICAL_WEIGHT_DECIMAL_PLACES, CHEMICAL_WEIGHT_MAX, CHEMICAL_WEIGHT_MIN, CONFIG_TYPE_CCB_PROCESS_TEMP } from 'ehs/constant/constants';
import { OperateType } from 'ehs/enums/OperateType';
import { EhsChemicalInventory } from 'ehs/models/EhsChemicalInventory';
import { EhsConfigParam } from 'ehs/models/EhsConfigParam';
import { OperateInfo } from 'ehs/models/OperateInfo';
import { isArrayEmpty } from 'ehs/utils/arrayUtil';
import { getFormatDateDash, getFormatDateSlash } from 'ehs/utils/stringUtil';
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface OperActionRowProps {
    inventory: EhsChemicalInventory;
    phaseState: string;
    nowDate: Date;// 現在日期
    defStopOperDate: Date; //  預設停止操作日期
    operItemConfigs: EhsConfigParam[];
    updateOperation: (formState: OperateInfo) => void;
    //實際停止日
    setChemStopDate?: React.Dispatch<React.SetStateAction<{ [inventoryId: string]: Date | null }>>;
    configMap: { [configId: string]: EhsConfigParam };
}

const OperActionRow: React.FC<OperActionRowProps> = ({
    inventory, phaseState, nowDate, defStopOperDate, operItemConfigs,
    updateOperation, setChemStopDate, configMap
}) => {
    const { t } = useTranslation();
    const { inventoryId, inspDate } = inventory;
    const [formState, setFormState] = useState<OperateInfo>({
        operDate: '',
        operItem: 0,
        weight: undefined,
        note: '',
        processTemperature: null
    });
    const [useStopOperDate, setUseStopOperDate] = useState<Date | null>(null);
    const [processTemperatureType, setProcessTemperatureType] = useState<string>("0");
    const [temperatureError, setTemperatureError] = useState<string>("");
    const noNeedWeightOperItem = CHEMICAL_OPER_ITEM_NO_WEIGHT.includes(Number(formState.operItem));
    const isNeedDateOperItem = !CHEMICAL_OPER_ITEM_NO_DATE.includes(Number(formState.operItem));
    const isNeedProcessTemperature = processTemperatureType === "1";
    const isLiquid = useMemo(() =>
        configMap[phaseState]?.configValue === CHEMICAL_PHASE_LIQUID,
        [configMap, phaseState]
    );
    const isUseType = useMemo(() => {
        const operItemValue = Number(formState.operItem);
        return operItemValue === OperateType.USE;
    }, [formState.operItem]);
    const ccbProcessTempOptions = useMemo(() =>
        Object.values(configMap).filter(
            (config): config is EhsConfigParam =>
                config.configType === CONFIG_TYPE_CCB_PROCESS_TEMP
        ),
        [configMap]);

    //預設第一筆
    useEffect(() => {
        if (!isArrayEmpty(operItemConfigs) && formState.operItem === 0) {
            setFormState(prevState => ({
                ...prevState,
                operItem: operItemConfigs[0].configIvalue
            }));
        }
    }, [operItemConfigs])

    useEffect(() => {
        if (formState.operItem !== 0) {
            setFormState(prevState => ({
                ...prevState,
                weight: undefined
            }));
        }
    }, [formState.operItem])

    useEffect(() => {
        let rsDate: Date | null = null;
        // 檢查 inspDate 是否為有效日期
        if (inspDate) {
            const inspDateObj = new Date(inspDate);
            const stopOperDateObj = new Date(defStopOperDate);

            // 根據比較結果更新 useStopOperDate
            rsDate = inspDateObj.getTime() > stopOperDateObj.getTime() ? inspDateObj : stopOperDateObj;
        } else {
            rsDate = new Date(defStopOperDate);
        }
        setUseStopOperDate(rsDate);
        if (setChemStopDate) {
            setChemStopDate(prevState => ({
                ...prevState,
                [inventoryId]: rsDate,
            }));
        }
    }, [inspDate, defStopOperDate]);

    useEffect(() => {
        handleDateChange(isNeedDateOperItem ? null : nowDate);
    }, [isNeedDateOperItem])

    useEffect(() => {
        if (!isLiquid || !isUseType) {
            setFormState(prevState => ({
                ...prevState,
                processTemperature: null
            }));
            setProcessTemperatureType("");
        }
    }, [isLiquid, isUseType]);

    const handleInputChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormState(prevState => ({
            ...prevState,
            [name]: value
        }));
    };

    const handleBlur = (value: number) => {
        setFormState(prevState => ({
            ...prevState,
            weight: value
        }));
    };

    const handleDateChange = (date: Date | null) => {
        setFormState(prevState => ({
            ...prevState,
            operDate: date ? getFormatDateDash(date) : ''
        }));
    };

    const handleOperAction = () => {
        const weight = formState.weight;
        if (!formState.operDate) {
            showWarnToast(t('message.chemical.no_operation_date'));
            return;
        }
        if (!noNeedWeightOperItem) {
            if (weight === undefined) {
                showWarnToast(t('message.chemical.no_operation_weight'));
                return;
            } else if (weight === 0) {
                showWarnToast(t('message.chemical.zero_operation_weight'));
                return;
            }
        }
        if (isNeedProcessTemperature) {
            if (formState.processTemperature === undefined || formState.processTemperature === null) {
                showWarnToast(t('message.chemical.no_enter_process_temperature'));
                return;
            }
        }
        updateOperation(formState);
    };

    return (
        <div className="row justify-content-center border py-3 px-0 mx-0 shadow rounded-3 d-flex align-items-start bg-white">
            <div className="col-xl-2">
                <label htmlFor="datepicker-autoClose">{t('text.chemical.operation_date')}</label><br />
                <InputDate className="form-control mt-1" minDate={getFormatDateSlash(useStopOperDate)} maxDate={getFormatDateSlash(nowDate)}
                    onChange={handleDateChange} />
            </div>
            <div className="col-xl-2">
                <label>{t('text.chemical.operation_item')}</label>
                <select className="form-select form-select-lg mt-1"
                    name="operItem"
                    value={formState.operItem}
                    onChange={handleInputChange}>
                    {operItemConfigs.map((item) => {
                        return <option key={'operItem' + item.configId} value={item.configIvalue} title={item.configName}>{item.configName}</option>
                    })}
                </select>
            </div>
            <div className="col-xl-2">
                <label>{t('text.weight')} <span className="text-danger fw-bold">(Kg)</span></label>
                <InputNumFloat className="form-control  mt-1" minValue={CHEMICAL_WEIGHT_MIN} maxValue={CHEMICAL_WEIGHT_MAX} maxLength={7 + CHEMICAL_WEIGHT_DECIMAL_PLACES}
                    decimalPlaces={CHEMICAL_WEIGHT_DECIMAL_PLACES} onBlur={(num) => handleBlur(num || 0)}
                    name="weight"
                    value={noNeedWeightOperItem ? undefined : formState.weight}
                    disabled={noNeedWeightOperItem}
                />
            </div>
            <div className="col-xl-2">
                <label>{t('text.note')}</label>
                <textarea className="form-control mt-1" rows={3} onChange={handleInputChange} name="note" value={formState.note} ></textarea>
            </div>
            {isLiquid && isUseType &&
                <div className="col-xl-2">
                    <label className="fw-bold">{t('text.chemical.ccb_process_temperature')}</label>
                    {ccbProcessTempOptions?.map((item, index) => {
                        const { configId, configName, configIvalue } = item;
                        const id = 'ccbprocess-radio_' + configId;
                        return (
                            <div key={configId}>
                                <div className="form-check form-check-inline">
                                    <input
                                        className="form-check-input"
                                        type="radio"
                                        name="ccbprocess"
                                        id={id}
                                        value={configIvalue}
                                        defaultChecked={index === 0}
                                        onChange={e => {
                                            setProcessTemperatureType(e.target.value);
                                            setTemperatureError("");
                                            setFormState(prevState => ({
                                                ...prevState,
                                                processTemperature: null
                                            }));
                                        }}
                                    />
                                    <label className="form-check-label" htmlFor={id}>
                                        {configName}
                                    </label>
                                </div>
                                {isNeedProcessTemperature && configIvalue === 1 && (
                                    <div className="mt-2 ms-4">
                                        <div className="d-flex align-items-center">
                                            <InputNumFloat
                                                className={`form-control ${temperatureError ? 'is-invalid error-field' : ''}`}
                                                value={formState.processTemperature || undefined}
                                                onBlur={(newValue) => {
                                                    if (!newValue && isNeedProcessTemperature) {
                                                        setTemperatureError(t("message.enter"));
                                                    } else {
                                                        setTemperatureError("");
                                                        setFormState(prevState => ({
                                                            ...prevState,
                                                            processTemperature: newValue || null
                                                        }));
                                                    }
                                                }}
                                                decimalPlaces={CHEMICAL_WEIGHT_DECIMAL_PLACES}
                                                maxLength={8}
                                                minValue={CHEMICAL_TEMPERATURE_MIN}
                                                maxValue={CHEMICAL_TEMPERATURE_MAX}
                                                allowNegative
                                                allowEmptyUndefine
                                            />
                                            <span className="ms-2">°C</span>
                                        </div>
                                        {temperatureError && (
                                            <div className="mt-1">
                                                <span className="text-danger">
                                                    {temperatureError}
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        );
                    })}
                </div>
            }
            <div className="col-xl-2 align-self-center fs-5 d-grid">
                <button type="button" className="btn btn-success fs-5" onClick={() => handleOperAction()}><i className='fas fa-flask text-white fa-lg me-2' />{t('button.chemical.operation_action')}</button>
            </div>
        </div>
    );
};

export default OperActionRow;