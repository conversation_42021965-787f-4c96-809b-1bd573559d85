import { CONFIG_VAL_CONCERNED, CONFIG_VAL_PRIORITY, CONFIG_VAL_PUBLIC_HAZARD, CONFIG_VAL_TOXIC, } from 'ehs/constant/constants';
import { isArrayEmpty } from 'ehs/utils/arrayUtil';
import React from 'react';

// 定義欄位類型
export interface ColumnConfig {
    key: string;
    label: string;
    visible: boolean;
    width?: string;
    align?: 'start' | 'center' | 'end';
    renderComponent?: React.FC<any>;
    condition?: (data: any) => boolean;
    chemicalTypes?: string[];
}

// 預設欄位配置
export const getDefaultColumnConfig = (t: any, options: { itemText?: string } = {}): ColumnConfig[] => [
    { key: 'index', label: t('table.title.item'), visible: true, width: 'w-4', align: 'center' },
    { key: 'chemCtrlNo', label: t('table.title.ctrl_no'), visible: true, width: 'w-10', align: 'start', chemicalTypes: [CONFIG_VAL_TOXIC, CONFIG_VAL_CONCERNED] },
    { key: 'casNo', label: t('table.title.casno'), visible: true, width: 'w-10', align: 'start' },
    { key: 'name', label: t('table.title.name'), visible: true, align: 'start' },
    // { key: 'ghsImg', label: t('table.title.ghs_img'), visible: true, align: 'center' },
    // { key: 'publicHazard', label: t('table.title.chemical.public_hazard_classify'), visible: true, width: 'w-15', align: 'start' },
    { key: 'toxicClass', label: t('table.title.toxic_class'), visible: true, width: 'w-7', align: 'start', chemicalTypes: [CONFIG_VAL_TOXIC] },
    { key: 'concernedClass', label: t('table.title.chemical.concerned_class'), visible: true, width: 'w-7', align: 'start', chemicalTypes: [CONFIG_VAL_CONCERNED] },
    { key: 'priorityClass', label: t('table.title.chemical.prority_class_title'), visible: true, width: 'w-7', align: 'start', chemicalTypes: [CONFIG_VAL_PRIORITY] },
    { key: 'pubhazClass', label: t('table.title.chemical.public_hazard_classify'), visible: true, width: 'w-7', align: 'start', chemicalTypes: [CONFIG_VAL_PUBLIC_HAZARD] },
    { key: 'chemClass', label: t('table.title.chem_class'), visible: true, align: 'start' },
    { key: 'concentration', label: t('table.title.chemical.declared_concentration') + '/' + t('table.title.chemical.phase_state'), visible: true, width: 'w-10', align: 'center' },
    { key: 'chemQty', label: t('table.title.chemical.unit_ctrl_qty_total', { unit: options.itemText || '' }), visible: true, width: 'w-12', align: 'end' },
    { key: 'inventoryQty', label: t('table.title.inventory_quantity'), visible: true, width: 'w-10', align: 'end' },
    { key: 'action', label: t('table.title.action'), visible: true, width: 'w-10', align: 'center' }
];

// 根據化學品類型過濾欄位
export const filterColumnsByType = (columns: ColumnConfig[], chemicalType: string): ColumnConfig[] => {
    return columns.filter(column => {
        // 若沒有指定化學品類型限制，則顯示
        if (!column.chemicalTypes) return column.visible;

        // 若有指定化學品類型限制，則只在該類型下顯示
        return column.chemicalTypes.includes(chemicalType) && column.visible;
    });
};

// 根據選擇的化學品分類獲取應顯示的欄位
export const getColumnsByChemClasses = (
    columns: ColumnConfig[],
    selectedChemClassesConfig: any[] = []
): ColumnConfig[] => {
    if (isArrayEmpty(selectedChemClassesConfig)) {
        // 如果沒有選擇任何分類，顯示通用欄位（不包含特定化學品類型的欄位）
        return columns.filter(column => !column.chemicalTypes);
    }

    // 獲取所有已選分類的類型值
    const selectedTypes = selectedChemClassesConfig
        .map(config => config?.configValue)
        .filter(Boolean);

    // 顯示通用欄位和選中類型的特定欄位
    return columns.filter(column => {
        if (!column.chemicalTypes) return column.visible;
        return column.chemicalTypes.some(type => selectedTypes.includes(type)) && column.visible;
    });
};

export default function ChemicalColumnConfig() {
    return null; // 這是一個功能性組件，不需要渲染UI
} 