import LoadingSpinner from "ehs/common/LoadingSpinner";
import { errorMsg } from "ehs/common/SwalMsg";
import { NEWS_STATUS_SHOW } from "ehs/constant/constants";
import { useEffect, useState } from "react";
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import styled from "styled-components";
import { NewsAPI } from "../../../api/newsAPI";
import { AppPaths } from '../../../config/app-paths';
import BackButton from '../../common/button/BackButton';
import useLoginUser from '../../hooks/useLoginUser';
import Footer from '../../layout/Footer';
import { EhsNews } from "../../models/EhsNews";
import { checkTimeoutAction, getBasicLoginUserInfo, navigateToHome } from '../../utils/authUtil';
import { isApiCallSuccess } from '../../utils/resultUtil';
import { getFormatDateSlash } from '../../utils/stringUtil';

function NewsList() {
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const [newsList, setNewsList] = useState<EhsNews[]>([]);

  useEffect(() => {
    if (!loginUser) {
      navigateToHome(navigate);
      return;
    }
    fetchData();
  }, [loginUser, i18n.language]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const result = await NewsAPI.getNewsList({
        ...getBasicLoginUserInfo(loginUser)!,
        newsStatus: NEWS_STATUS_SHOW,
      })
      if (isApiCallSuccess(result)) {
        setLoading(false);
        setNewsList(result.results);
      } else {
        errorMsg(result.message);
      }
    } catch (err) {
      checkTimeoutAction(err, navigate, t);
      errorMsg(t('message.error'));
    }
  }

  return (
    <StyledNewsList $loading={loading}>
      <div className="d-flex flex-column p-0" id="content">
        {/* BEGIN scrollbar */}
        <div className="app-content-padding flex-grow-1">
          {/* BEGIN breadcrumb */}
          <div className="row">
            <div className="col-xl-12">
              <div className="card py-3">
                <div className="card-body">
                  <BackButton />
                </div>
                <div className="card-body">
                  <div className="table-container">
                    <table
                      id="data-table-default"
                      className={
                        "table table-hover align-middle dt-responsive"
                      }
                    >
                      <caption className='table-caption'>
                        <i className="fas fa-grid-2"></i>{t("table.title.news.latest_news")}
                      </caption>
                      <thead className="text-center fs-4 fw-bold">
                        <tr>
                          <th>{t("table.title.item")} </th>
                          <th>{t("table.title.news.announcement_title")}</th>
                          <th>{t("table.title.news.published_date")}</th>
                        </tr>
                      </thead>
                      <tbody className="text-center fs-5">
                        {loading ? (
                          <tr>
                            <td colSpan={3} className="text-center">
                              <LoadingSpinner />
                            </td>
                          </tr>
                        ) : (
                          newsList.map((data, idx) => {
                            return <Row
                              key={idx}
                              index={idx + 1}
                              news={data}
                            />;
                          })
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* BEGIN #footer */}
        <Footer />
        {/* END #footer */}
      </div>
    </StyledNewsList>
  );
}

const Row = (props: {
  index: number;
  news: EhsNews;
}) => {
  const { t } = useTranslation();
  const { index, news } = props;
  const { newsId, newsTitle, createDate } = news;
  const showCreateDate = createDate ? getFormatDateSlash(createDate) : "";
  const navigate = useNavigate();
  const clickDetail = () => navigate('/' + AppPaths.information.newsDetail, { state: { initNewsId: newsId } });

  return (
    <tr>
      <td data-title={t("table.title.item")}>{index}</td>
      <td data-title={t("table.title.news.announcement_title")}>
        <span onClick={clickDetail}>{newsTitle}</span>
      </td>
      <td data-title={t("table.title.news.published_date")}>{showCreateDate}</td>
    </tr>
  );
};

const StyledNewsList = styled.div<{ $loading?: boolean }>`
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }

  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }

  .table-caption {
    caption-side: top;
    text-align: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    padding: 10px;
    width: 100%;
  }

  table {
    position:relative;
    min-height:${props => props.$loading ? "200px" : "auto"};
    thead {
      background:rgb(174, 219, 240);
    }
    tbody {
      tr {
        line-height: 3;
        border-bottom: 1px solid #ddd;
        span:hover { 
          color: #007bff;
          text-decoration: underline;
          cursor: pointer;
        }
      }
    }
    th {
      text-align: start;
    } 
    td {
      padding: 12px 8px;
      .form-check {
        justify-content:center;  
      }
      text-align: start;
    }
  }

  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }  
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        display: flex;
        flex-direction: row;
        padding: 6px;
        min-height: auto;
        align-items: flex-start;
        gap: 10px;
        word-break: break-word;
      }

      td::before {
        content: attr(data-title);
        flex: 0 0 40%; /* 左側 label 寬度：可視需要微調 */
        font-weight: bold;
        color: #1a1a1a;
        white-space: normal;
        word-break: break-word;
      }
    }
  }
`;

export default NewsList;