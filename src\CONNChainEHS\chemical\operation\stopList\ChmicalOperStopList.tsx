import BlockUi from "@availity/block-ui";
import { StopOperOption } from "ehs/models/StopOperOption";
import { getStopOperDate } from "ehs/utils/chemicalUtil";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import { ChemicalAPI } from "../../../../api/chemicalAPI";
import { ConfigAPI } from "../../../../api/configAPI";
import { OptionAPI } from "../../../../api/optionAPI";
import BlockuiMsg from "../../../common/BlockuiMsg";
import Dialog from "../../../common/Dialog";
import Loader from "../../../common/Loader";
import NoDataRow from "../../../common/NoDataRow";
import { showSuccessToast } from "../../../common/Toast";
import { CONFIG_TYPE_CHEM_CLASS_LV, OPTION_CHEM_STOP_OPER_MONTHS_GENER, OPTION_SUBTYPE_STOP_OPER_MONTHS } from "../../../constant/constants";
import useLoginUser from "../../../hooks/useLoginUser";
import useServerNowDate from "../../../hooks/useServerNowDate";
import Breadcrumbs from "../../../layout/Breadcrumbs";
import Footer from "../../../layout/Footer";
import { EhsChemical } from "../../../models/EhsChemical";
import { EhsChemicalCon } from "../../../models/EhsChemicalCon";
import { EhsConfigParam } from "../../../models/EhsConfigParam";
import { EhsOptions, initEhsOptions } from "../../../models/EhsOptions";
import { isArrayEmpty } from "../../../utils/arrayUtil";
import { checkTimeoutAction, getBasicLoginUserInfo } from "../../../utils/authUtil";
import { notEnglishLang, splitChemNameListByLang } from "../../../utils/langUtil";
import { isApiCallSuccess } from "../../../utils/resultUtil";
import { decodeHTMLEntities, getFormatDateSlash } from "../../../utils/stringUtil";
import StopDateModify from "./StopDateModify";
import { initPageInfo, PageInfo } from "ehs/models/PageInfo";
import Pagination from "ehs/layout/Pagination";

function ChemicalOperStopList() {
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const navigate = useNavigate();
  const [nowDate] = useServerNowDate(loginUser);
  const [chemicalMap, setChemicalMap] = useState<{ [configId: string]: EhsChemical[] }>({});
  const [ctrlChemClassObj, setCtrlChemClassObj] = useState<{ [key: string]: EhsConfigParam[] }>({});
  const [filterChemClassObj, setFilterChemClassObj] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(false);
  const [loadingBlock, setLoadingBlock] = useState(false);
  const [popupMode, setPopupMode] = useState<boolean | null>(null);
  const [defaultStopOperDays, setDefaultStopOperDays] = useState<{ [key: string]: EhsOptions }>({});
  const [configMap, setConfigMap] = useState<{ [key: string]: EhsConfigParam[] }>({});
  const [modifyStopMonth, setModifyStopMonth] = useState<number>(0);
  const [modifyStopDay, setModifyStopDay] = useState<number>(0);
  const [modifyConList, setModifyConList] = useState<EhsChemicalCon[]>([]);
  const chemClassLvList = configMap[CONFIG_TYPE_CHEM_CLASS_LV] || [];
  const [activeTab, setActiveTab] = useState<string>("");
  const [fetchedTabs, setFetchedTabs] = useState<Set<string>>(new Set());
  // 為每個分頁籤維護獨立的分頁信息
  const [pageInfoMap, setPageInfoMap] = useState<{ [tabId: string]: PageInfo }>({});

  // 為每個分頁籤維護獨立的分頁條件
  const [conditionMap, setConditionMap] = useState<{
    [tabId: string]: {
      currentPage: number;
      pageSize: number;
      // 其他可能的篩選條件
    }
  }>({});

  useEffect(() => {
    if (loginUser) {
      fetchOption();
    }
  }, [loginUser]);

  useEffect(() => {
    if (loginUser) {
      fetchConfig();
      fetchCtrlChemClass();
    }
  }, [loginUser, i18n.language]);

  // 當配置加載完成後，設置初始分頁籤並加載數據
  useEffect(() => {
    if (loginUser && !isArrayEmpty(chemClassLvList) && !activeTab) {
      const initialTab = chemClassLvList[0].configId;
      setActiveTab(initialTab);
      fetchDataByTab(initialTab);
    }
  }, [loginUser, chemClassLvList]);

  // 當分頁籤變更時，檢查是否需要加載數據
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    if (!fetchedTabs.has(tabId)) {
      // 首次加載分頁籤數據
      fetchDataByTab(tabId);
    }
  };

  const fetchDataByTab = (tabId: string, page: number = 1, pageSize: number = 50) => {
    setLoading(true);

    // 更新當前分頁籤的條件
    setConditionMap(prev => ({
      ...prev,
      [tabId]: {
        ...prev[tabId],
        currentPage: page,
        pageSize: pageSize
      }
    }));

    ChemicalAPI.getChemicalStopOperList({
      ...getBasicLoginUserInfo(loginUser),
      chemClassLv: tabId,
      currentPage: page,
      pageSize: pageSize
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const { chemicalList, concenList } = result.results;
        const pageinfo = result.pageinfo;

        // 處理化學品數據...
        const chemicalObj = chemicalList.reduce((acc: { [chemId: string]: EhsChemical }, chemical: EhsChemical) => {
          acc[chemical.chemId] = chemical;
          return acc;
        }, {} as { [key: string]: EhsChemical });

        // 將 concenList 加入到對應的 chemicalObj 中...
        concenList.forEach((concenItem: EhsChemicalCon) => {
          const chemId = concenItem.chemId;
          const chemical = chemicalObj[chemId];

          if (chemical) {
            if (!chemical.conList) {
              chemical.conList = [];
            }
            chemical.conList.push(concenItem);
          }
        });

        // 處理好的化學品列表
        const processedChemicals = Object.values(chemicalObj) as EhsChemical[];

        // 更新化學品映射
        setChemicalMap(prevMap => ({
          ...prevMap,
          [tabId]: processedChemicals
        }));

        // 更新分頁信息
        setPageInfoMap(prevMap => ({
          ...prevMap,
          [tabId]: pageinfo
        }));

        // 記錄已查詢過的分頁籤
        setFetchedTabs(prev => new Set(prev).add(tabId));
        setLoading(false);
      }
    }).catch((error) => {
      checkTimeoutAction(error, navigate, t);
      setLoading(false);
    });
  };

  const fetchConfig = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser),
      configTypeList: [CONFIG_TYPE_CHEM_CLASS_LV],
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const newConfigMap = result.results.reduce((acc: { [key: string]: EhsConfigParam[] }, config: EhsConfigParam) => {
          if (!acc[config.configType]) {
            acc[config.configType] = [];
          }
          acc[config.configType].push(config);
          return acc;
        }, {});
        setConfigMap(newConfigMap);
      }
    });
  };

  const fetchCtrlChemClass = () => {
    ConfigAPI.getConfigChemClassCtrl({
      ...getBasicLoginUserInfo(loginUser),
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const rs: EhsConfigParam[] = result.results;
        const groupedByChemClassLv = rs.reduce<{ [key: string]: EhsConfigParam[] }>((acc, current) => {
          const { chemClassLv } = current;

          if (!acc[chemClassLv]) {
            acc[chemClassLv] = [];
          }

          acc[chemClassLv].push(current);

          return acc;
        }, {});

        setCtrlChemClassObj(groupedByChemClassLv);
      }
    });
  };

  const fetchOption = () => {
    OptionAPI.getOptionListBySubTypes({
      ...getBasicLoginUserInfo(loginUser),
      subtypeList: [OPTION_SUBTYPE_STOP_OPER_MONTHS],
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const options = result.results;
        const optionsMap: {
          [key: string]: EhsOptions;
        } = {};
        options.forEach((item: any) => {
          optionsMap[item.optionMapId] = item;
        });
        setDefaultStopOperDays(optionsMap);
      }
    });
  }

  /**
   * 篩選化學品分類
   * @param chemicalList 
   * @param configId 
   * @returns 
   */
  const filterChemicals = (chemicalList: EhsChemical[], configId: string): EhsChemical[] => {
    if (!chemicalList || isArrayEmpty(chemicalList)) return [];
    if (!configId) return chemicalList;

    return chemicalList.filter(chemical => {
      // 檢查所有濃度項目中是否有任何一個匹配指定的分類
      if (!chemical.conList || isArrayEmpty(chemical.conList)) return false;

      // 遍歷所有濃度項目，檢查是否有任何一個匹配指定的分類
      return chemical.conList.some(con => {
        if (!con.categoryList || isArrayEmpty(con.categoryList)) return false;
        // 檢查是否有任何分類匹配指定的 configId
        return con.categoryList.some(category => category.configId === configId);
      });
    });
  };

  const modifyStopDate = (newConList: EhsChemicalCon[]) => {
    setPopupMode(null);
    const allChemicals = Object.values(chemicalMap).flat();
    const updatedChemicalMap = { ...chemicalMap };
    newConList.forEach(newCon => {
      // 在 chemicalMap 中找到對應的 chemical
      allChemicals.forEach(chemical => {
        const matchingCon = chemical.conList.find(con => con.chemConId === newCon.chemConId);
        if (matchingCon) {
          // 更新 matchingCon 的 stopOperMonths
          matchingCon.stopOperMonths = newCon.stopOperMonths;
          const chemicalId = chemical.chemId;

          // 更新 chemicalMap
          if (updatedChemicalMap[chemicalId]) {
            // 更新 chemicalMap 中的 chemicals
            const updatedChemicals = updatedChemicalMap[chemicalId].map(c =>
              c.chemId === chemicalId ? chemical : c
            );
            updatedChemicalMap[chemicalId] = updatedChemicals;
          }
        }
      });
    });
    // 設置更新後的 chemicalMap
    setChemicalMap(updatedChemicalMap);
    showSuccessToast(t('message.success'));
  }

  return (
    <StlyedChemicalOperStopList $loading={loading}>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {
            <Dialog
              content={
                <StopDateModify
                  onClose={() => {
                    setPopupMode(null);
                  }}
                  onActionSuccess={modifyStopDate}
                  setLoadingBlock={setLoadingBlock}
                  serverDate={nowDate}
                  monthNum={modifyStopMonth}
                  dayOfMonth={modifyStopDay}
                  setMonthNum={(num: number) => setModifyStopMonth(num)}
                  conList={modifyConList}
                  mode={popupMode}
                />
              }
              show={popupMode !== null}
            />
          }
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.chemical.manage") },
                { label: t("func.chemical.operation_stop_list") },
              ]}
            />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{t("func.chemical.operation_stop_list")} </h1>
            {/* END page-header */}
            <ul className="nav nav-tabs">
              {chemClassLvList.map((item, idx) =>
                <li className="nav-item" key={'ctrlChemClassList-nav-' + item.configId}>
                  <a
                    href={`#tab-${item.configId}`}
                    data-bs-toggle="tab"
                    className={`nav-link ${item.configId === activeTab && 'active'}`}
                    onClick={() => handleTabChange(item.configId)}
                  >
                    {item.configName}
                  </a>
                </li>
              )}
            </ul>
            <div className="tab-content panel p-3 rounded-0 rounded-bottom">
              {loading && <Loader />}
              {chemClassLvList.map((item, index) => {
                const { configId } = item;
                const chemicalList = filterChemicals(chemicalMap[configId] || [], filterChemClassObj[configId]);
                const defaultStopOperOption = defaultStopOperDays[configId] || defaultStopOperDays[OPTION_CHEM_STOP_OPER_MONTHS_GENER];
                const stopOperOption = defaultStopOperOption ? JSON.parse(defaultStopOperOption.optionValue) : initEhsOptions;
                const currentPageInfo = pageInfoMap[configId] || initPageInfo;
                return (
                  <div
                    className={`tab-pane fade ${configId === activeTab && 'active show'}`}
                    id={`tab-${configId}`}
                    key={'stopoperlist-tab-' + configId}
                  >
                    <div className="row justify-content-start mb-3">
                      <div className="col-md-3 d-md-flex align-items-center">
                        <label className="col-md-3 h5">{t("text.chemical.class")}</label>
                        <select className="form-select form-select-lg" onChange={(e) => {
                          setFilterChemClassObj((pre) => ({
                            ...pre,
                            [configId]: e.target.value,
                          }));
                        }}>
                          <option key="all" value="">{t('text.all')}</option>
                          {ctrlChemClassObj[configId] && ctrlChemClassObj[configId].map((item) => {
                            return (<option key={`stopclassoption${item.configId}`} value={item.configId}>{item.configName}</option>)
                          })}
                        </select>
                      </div>
                    </div>
                    <div className="table-responsive">
                      <table
                        id="data-table-default"
                        className={
                          "table table-hover align-middle dt-responsive nowrap"
                        }
                      >
                        <thead className="text-center fs-4 fw-bold bg-lime-200">
                          <tr>
                            <th className="item-width-3">{t("table.title.item")} </th>
                            <th>
                              {t("table.title.ctrl_no")}
                            </th>
                            <th className='text-start'>
                              {t("table.title.casno")}
                            </th>
                            <th className='text-start item-width-30'>
                              {t("table.title.name")}
                            </th>
                            <th className="">{t("table.title.chemical.declared_concentration")}</th>
                            <th className="item-width-20">
                              {t("table.title.chemical.operation_stop_date")}
                            </th>
                            <th data-orderable="false">{t("table.title.action")}</th>
                          </tr>
                        </thead>
                        <tbody className="text-center fs-5">{!loading &&
                          (!isArrayEmpty(chemicalList) ? (
                            chemicalList.map((data, idx) => (
                              <Row key={data.chemId} index={idx + 1} chemical={data} defaultStopOperOptions={stopOperOption}
                                serverDate={nowDate} setPopupMode={(mode) => setPopupMode(mode)}
                                setModifyStopMonth={(month) => setModifyStopMonth(month)}
                                setModifyStopDay={(day) => setModifyStopDay(day)}
                                setModifyConList={(conList) => setModifyConList(conList)} />
                            ))
                          ) : (
                            <NoDataRow />
                          ))}

                        </tbody>
                      </table>
                    </div>
                    <Pagination
                      pageInfo={currentPageInfo}
                      setCondition={setConditionMap}
                      condition={conditionMap[configId] || { currentPage: 1, pageSize: 50 }}
                      specialCondition={{
                        tabPagination: {
                          tabId: configId,
                          updateTabPage: (tabId, page) => {
                            fetchDataByTab(tabId, page, conditionMap[tabId]?.pageSize || 50);
                          }
                        }
                      }}
                    />
                  </div>
                );
              })}
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </BlockUi >
    </StlyedChemicalOperStopList>
  );
};

const Row = (props: {
  index: number; chemical: EhsChemical; defaultStopOperOptions: StopOperOption;
  serverDate: Date;
  setPopupMode: (mode: boolean) => void;
  setModifyStopMonth: (modifyDate: number) => void
  setModifyStopDay: (modifyDay: number) => void
  setModifyConList: (conList: EhsChemicalCon[]) => void
}) => {
  const { t, i18n } = useTranslation();
  const { index, chemical, defaultStopOperOptions, serverDate, setPopupMode, setModifyStopMonth, setModifyStopDay, setModifyConList } = props;
  const { chemCtrlNo, casno, nameList, conList } = chemical
  const firstItemStopOperMonths = !isArrayEmpty(conList) ? conList[0].stopOperMonths : null;
  const useStopOperMonths = firstItemStopOperMonths || defaultStopOperOptions.month;
  const stopOperDay = defaultStopOperOptions.day;
  const stopMonthDate = useStopOperMonths && stopOperDay ? getStopOperDate(serverDate, useStopOperMonths, stopOperDay) : null;
  const notEnglish = notEnglishLang(i18n.language);
  const showStopOperDate = stopMonthDate ? getFormatDateSlash(stopMonthDate) : "";
  // 用於追蹤已出現過的 chemNameId
  const seenChemNameIds = new Set();
  // 過濾掉重複的項目
  const uniqueNameList = nameList.filter(({ chemNameId }) => {
    return !seenChemNameIds.has(chemNameId) && seenChemNameIds.add(chemNameId);
  });
  const { currentLangNames, enNames } = splitChemNameListByLang(uniqueNameList, i18n.language);

  const clickEdit = () => {
    setPopupMode(true);
    setModifyStopMonth(useStopOperMonths);
    setModifyStopDay(stopOperDay);
    setModifyConList(conList);
  };

  return (
    <tr>
      <td data-title={t("table.title.item")}>{index}</td>
      <td data-title={t("table.title.ctrl_no")}>{chemCtrlNo}</td>
      <td data-title={t("table.title.casno")} className='text-start'>{casno}</td>
      <td data-title={t("table.title.name")} className='text-start'>{notEnglish && currentLangNames.map((name) => <label key={'curname' + name.chemNameId}>{name.chemName}</label>)}
        {notEnglish && <br />}
        {enNames.map((name) => <label key={'enname' + name.chemNameId}>{name.chemName}</label>)} </td>
      <td data-title={t("table.title.chemical.declared_concentration")}>
        {conList.filter(con => con.concentrationShow).map((con, index) => (
          <React.Fragment key={'concen-' + con.chemConId}>
            <label>{con.concentrationShow}</label>
            {index < conList.filter(c => c.concentrationShow).length - 1 && <br />}
          </React.Fragment>
        ))}
      </td>
      <td data-title={t("table.title.chemical.operation_stop_date")}>{decodeHTMLEntities(t('text.chemical.stop_oper_date_show', { stopOperDate: showStopOperDate }))}</td>
      <td data-title={t("table.title.action")}>
        <button
          type="button"
          className="btn btn-warning me-3 fs-5"
          title={t("button.edit")}
          onClick={clickEdit}
        ><i className="fas fa-pen"></i> {t("button.edit")}
        </button>
      </td>
    </tr>
  );
};

const StlyedChemicalOperStopList = styled.div<{ $loading?: boolean }>`
  padding-bottom:150px;

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }
  table {
    position:relative;
    min-height:${props => props.$loading ? "300px" : "auto"};
    th {
      text-align: center;
      white-space:nowrap;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 120px;
        text-align:left;
        min-height:39.5px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          white-space: nowrap;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
`;

export default ChemicalOperStopList;