import { ChemicalAPI } from "api/chemicalAPI";
import { ConfigAPI } from "api/configAPI";
import { AppPaths } from "config/app-paths";
import ChemicalClassificationBadge from "ehs/common/chemical/ChemicalClassificationBadge";
import Loader from "ehs/common/Loader";
import NoDataRow from "ehs/common/NoDataRow";
import ShowMoreInfo from "ehs/common/ShowMoreInfo";
import SortIcon from "ehs/common/SortIcon";
import { confirmMsg, errorMsg } from "ehs/common/SwalMsg";
import { showSuccessToast } from "ehs/common/Toast";
import { CONFIG_TYPE_CHEM } from "ehs/constant/constants";
import { Language } from "ehs/enums/Language";
import useLoginUser from "ehs/hooks/useLoginUser";
import Breadcrumbs from "ehs/layout/Breadcrumbs";
import Footer from "ehs/layout/Footer";
import PageSizeSelector from "ehs/layout/PageSizeSelector";
import Pagination from "ehs/layout/Pagination";
import { EhsChemical } from "ehs/models/EhsChemical";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { PageInfo, initPageInfo } from "ehs/models/PageInfo";
import { ReactSelectOption } from "ehs/models/ReactSelectOption";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { checkTimeoutAction, getBasicLoginUserInfo } from "ehs/utils/authUtil";
import { notEnglishLang, splitChemNameListByLang } from "ehs/utils/langUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import Select from "react-select";
import styled from "styled-components";

function ChemicalInfoList() {
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const navigate = useNavigate();
  const [chemicalList, setChemicalList] = useState<EhsChemical[]>([]);
  const [localSearchResult, setLocalSearchResult] = useState<EhsChemical[]>([]);
  const [localSearchKey, setLocalSearchKey] = useState("");
  const [loading, setLoading] = useState(false);
  const chemCtrlNoRef = useRef<HTMLInputElement>(null);
  const casNoRef = useRef<HTMLInputElement>(null);
  const keywordRef = useRef<HTMLInputElement>(null);
  const [condition, setCondition] = useState<{
    keyword: string;
    currentPage: number;
    pageSize: number;
    chemCtrlNo: string;
    casNo: string;
    chemClassIdList?: string[];
  }>({
    keyword: "",
    currentPage: 1,
    pageSize: 50,
    chemCtrlNo: "",
    casNo: "",
    chemClassIdList: [],
  });
  const [pageInfo, setPageInfo] = useState<PageInfo>(initPageInfo);
  const [chemClassOptions, setChemClassOptions] = useState<any[]>([]);
  const [selectedChemClasses, setSelectedChemClasses] = useState<ReactSelectOption[]>([]);

  useEffect(() => {
    if (loginUser) {
      fetchData();
    }
  }, [loginUser, condition, i18n.language]);

  useEffect(() => {
    if (loginUser) {
      fetchConfig();
    }
  }, [loginUser, i18n.language]);


  useEffect(() => {
    if (localSearchKey) {
      const showChemList = handleShowChemicalData(chemicalList);
      const filteredList = showChemList.filter((data) =>
        [data.chemCtrlNo, data.firstCasno, data.currentLangName, data.enName].some((property) =>
          property && property.toLowerCase().includes(localSearchKey.toLowerCase())
        )
      );
      setLocalSearchResult(filteredList);
    } else {
      setLocalSearchResult([]);
    }
  }, [localSearchKey, chemicalList, i18n.language]);

  const fetchData = () => {
    setLoading(true)
    ChemicalAPI.getChemicalList({
      ...getBasicLoginUserInfo(loginUser),
      ...condition,
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        setLoading(false)
        setChemicalList(result.results);
        setPageInfo(result.pageinfo);
      }
    }).catch(error => {
      errorMsg(t('message.system_error'));
    });;
  };
  const fetchConfig = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser),
      configTypeList: [CONFIG_TYPE_CHEM],
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const options = result.results
          .filter((config: EhsConfigParam) => config.configType === CONFIG_TYPE_CHEM)
          .map((config: EhsConfigParam) => ({
            value: config.configId,
            label: config.configName
          }));
        setChemClassOptions(options);
      }
    });
  }


  const getShowList = () => {
    const targetList = localSearchKey ? localSearchResult : chemicalList;
    return handleShowChemicalData(targetList);
  };

  const handleShowChemicalData = (list: EhsChemical[]) => {
    return list.map((chemical) => ({
      ...chemical,
      firstCasno: chemical.casnoList[0]?.casno || "",
      currentLangName: chemical.nameList.find((name) => name.langType === i18n.language)?.chemName || "",
      enName: chemical.nameList.find((name) => name.langType === Language.ENUS)?.chemName || "",
    }));
  };

  const clickQueryChemical = () => {
    setCondition({
      ...condition,
      currentPage: 1,
      chemCtrlNo: chemCtrlNoRef.current?.value.trim() ?? "",
      casNo: casNoRef.current?.value.trim() ?? "",
      keyword: keywordRef.current?.value.trim() ?? "",
      chemClassIdList: selectedChemClasses.map(option => option.value)
    });
  };

  const editChemicalStatus = (chemId: string, activated: boolean) => {
    const chemStatus = activated ? 1 : 0;
    ChemicalAPI.editChemicalStatus({
      ...getBasicLoginUserInfo(loginUser),
      chemId: chemId,
      chemStatus: chemStatus
    }).then(result => {
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
        let cloneList = [...chemicalList];
        let updateData = cloneList.filter(updateData => updateData.chemId === chemId);
        if (updateData.length) {
          updateData[0].chemStatus = chemStatus;
          setChemicalList(cloneList);
        }
      } else {
        errorMsg(result.message);
      }
    }).catch(error => {
      errorMsg(t('message.system_error'));
    })

  }

  const deleteSuccess = () => {
    fetchData();
  }

  return (
    <StlyedChemicalInfoList $loading={loading}>
      <div className="d-flex flex-column p-0" id="content">
        {/* BEGIN scrollbar */}
        <div className="app-content-padding flex-grow-1">
          {/* BEGIN breadcrumb */}
          <Breadcrumbs
            items={[
              { label: t("func.chemical.manage") },
              { label: t("func.chemical.info") },
            ]}
          />
          {/* END breadcrumb */}
          {/* BEGIN page-header */}
          <h1 className="page-header">{t("func.chemical.info")} </h1>
          {/* END page-header */}

          <div className="card">
            <div className="card-body">
              {/* 新增化學品 button */}
              <button
                type="button"
                className="btn btn-purple fs-5"
                onClick={() => {
                  navigate('/' + AppPaths.chemical.informationAdd, { state: { isEdit: false, initChemId: "" } });
                }}
              >
                <i className="fas fa-plus"></i> {t("button.add")}
              </button></div>
            <div className="card-body p-4">
              <div className="row">
                <div className="col-xl-3 d-flex align-items-center">
                  <label className="col-3">{t('text.ctrl_no')}</label>
                  <input type="text" className="form-control" ref={chemCtrlNoRef} />
                </div>
                <div className="col-xl-3 d-flex align-items-center">
                  <label className="col-3">{t('text.chemical.casno')}</label>
                  <input type="text" className="form-control" ref={casNoRef} placeholder={t('text.chemical.casno_format_msg')} title={t('text.chemical.casno_format_msg')} />
                </div>                <div className="col-xl-3 d-flex align-items-center">
                  <label className="col-3">{t("text.chemical.class")}</label>
                  <Select
                    className="w-100"
                    isMulti
                    isSearchable
                    menuPosition="fixed"
                    options={chemClassOptions}
                    placeholder={t('text.all')}
                    value={selectedChemClasses}
                    onChange={(selectedOptions: any) => {
                      setSelectedChemClasses(selectedOptions || []);
                    }}
                    noOptionsMessage={() => t('text.select_no_option')}
                  />
                </div>
                <div className="col-xl-3 d-flex align-items-center">
                  <label className="col-3">{t('text.search.keyword')}</label>
                  <input type="text" className="form-control" ref={keywordRef} placeholder={t('text.chemical.search_info_kw_title')} />
                </div>
              </div>
              <div className="row text-end mt-3">
                <div className="">
                  <button type="button" className="btn btn-primary" onClick={clickQueryChemical}><i className="fas fa-magnifying-glass"></i> {t('button.search.item')}</button>
                </div>
              </div>
            </div>
          </div>
          <div className="card pt-3">
            <div className="row topFunctionRow">
              <div className="col-sm-12 col-md-6 left">
                <PageSizeSelector pageSize={condition.pageSize} setCondition={setCondition} />
              </div>
              <div className="col-sm-12 col-md-6 right">
                <div className="dataTables_filter d-flex">
                  search:
                  <input
                    value={localSearchKey}
                    onChange={(e) => {
                      setLocalSearchKey(e.target.value);
                    }}
                    type="search"
                    className="form-control form-control-sm"
                    placeholder=""
                    aria-controls="data-table-default"
                  />
                </div>
              </div>
            </div>
            <div className="card-body">
              {loading && <Loader />}
              <table
                id="data-table-default"
                className={
                  "table table-hover align-middle dt-responsive nowrap"
                }
              >
                <thead className="text-center fs-4 fw-bold bg-lime-200">
                  <tr>
                    <th>{t("table.title.item")}</th>
                    <th className="text-start">
                      {t("table.title.ctrl_no")}{" "}
                      <SortIcon
                        dataList={getShowList()}
                        dataField={"chemCtrlNo"}
                        setFunction={setChemicalList}
                      />
                    </th>
                    <th className="text-start">
                      {t("table.title.casno")}{" "}
                      <SortIcon
                        dataList={getShowList()}
                        dataField={"firstCasno"}
                        setFunction={setChemicalList}
                      />
                    </th>
                    <th className="text-start">
                      {t("table.title.name")}
                      <SortIcon
                        dataList={getShowList()}
                        dataField={"currentLangName"}
                        setFunction={setChemicalList}
                      />
                    </th>
                    {notEnglishLang(i18n.language) &&
                      <th className="text-start">
                        {t("table.title.en_name")}
                        <SortIcon
                          dataList={getShowList()}
                          dataField={"enName"}
                          setFunction={setChemicalList}
                        />
                      </th>}
                    <th className="text-start">
                      {t("table.title.chem_class")}
                    </th>
                    {/* <th data-orderable="false">{t("table.title.enable")}</th> */}
                    <th data-orderable="false" className="text-start">{t("table.title.action")}</th>
                  </tr>
                </thead>
                <tbody className="text-center fs-5">
                  {!loading && getShowList() && !isArrayEmpty(getShowList()) ?
                    getShowList().map((data, idx) => {
                      return <Row key={idx} index={idx + 1} chemical={data} onEditStatus={editChemicalStatus} onDeleteSuccess={deleteSuccess} />;
                    }) : (!loading && <NoDataRow />)}
                </tbody>
              </table>
              <Pagination pageInfo={pageInfo} setCondition={setCondition} />
            </div>
          </div>
        </div>
        {/* BEGIN #footer */}
        <Footer />
        {/* END #footer */}
      </div>
    </StlyedChemicalInfoList>
  );
}

const Row = (props: {
  index: number; chemical: EhsChemical;
  onEditStatus: (chemicalId: string, activated: boolean) => void; onDeleteSuccess: () => void;
}) => {
  const { loginUser } = useLoginUser();
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { index, chemical, onEditStatus, onDeleteSuccess } = props;
  const { chemId, chemCtrlNo, casnoList, nameList, categoryList } = chemical;
  const { currentLangNames, enNames } = splitChemNameListByLang(nameList, i18n.language);

  const clickDetail = () => {
    navigate('/' + AppPaths.chemical.informationDetail, { state: { initChemId: chemId } });
  };

  const clickEdit = () => {
    navigate('/' + AppPaths.chemical.informationEdit, { state: { isEdit: true, initChemId: chemId } });
  };

  const clickDelete = () => {
    if (loginUser) {
      confirmMsg(t('message.confirm.delete'), t).then((value) => {
        if (value) {
        }
      });
    }
  };

  return (
    <tr>
      <td data-title={t("table.title.item")}>{index}</td>
      <td data-title={t("table.title.ctrl_no")} className="text-start">{chemCtrlNo}</td>
      <td data-title={t("table.title.casno")} className="text-start">
        {<ShowMoreInfo dataList={casnoList} id="chemCasnoId" fieldName="casno" />}
      </td>
      <td data-title={t("table.title.name")} className="text-start">
        {notEnglishLang(i18n.language) ?
          <ShowMoreInfo dataList={currentLangNames} id="chemNameId" fieldName="chemName" /> :
          <ShowMoreInfo dataList={enNames} id="chemNameId" fieldName="chemName" />}
      </td>
      {notEnglishLang(i18n.language) && <td data-title={t("table.title.en_name")} className="text-start">
        {<ShowMoreInfo dataList={enNames} id="chemNameId" fieldName="chemName" />}
      </td>}
      <td data-title={t("table.title.chem_class")} className="text-start">
        {categoryList.map((item) => (
          <React.Fragment key={'category_' + item.configId}>
            <span className="d-inline-block mb-1">
              <ChemicalClassificationBadge item={item} />
            </span>
          </React.Fragment>
        ))}
      </td>
      <td data-title={t("table.title.action")} className="text-start">
        <button type="button" className="btn btn-secondary me-3 fs-5 mb-2" title={t("button.detail")} onClick={clickDetail}>
          <i className="fas fa-file-alt"></i> {t("button.detail")}
        </button>
        <button
          type="button"
          className="btn btn-warning me-3 fs-5 mb-2"
          title={t("button.edit")}
          onClick={clickEdit}
        >
          <i className="fas fa-pen"></i> {t("button.edit")}
        </button>
        <button
          type="button"
          className="btn btn-danger me-3 fs-5 mb-2"
          title={t("button.delete")}
          onClick={clickDelete}
        >
          <i className="fas fa-trash-can"></i> {t("button.delete")}
        </button>
      </td>
    </tr>
  );
};

const StlyedChemicalInfoList = styled.div<{ $loading?: boolean }>`
  padding-bottom:150px;

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }
  table {
    position:relative;
    min-height:${props => props.$loading ? "200px" : "auto"};
    th {
      text-align: center;
      white-space:nowrap;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  .more-link {
    color: blue; /* 设置链接颜色 */
    cursor: pointer; /* 设置鼠标悬停时的样式为手指 */
    user-select: none;
  }


  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 110px;
        text-align:left;
        min-height:39.5px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 15px;
          white-space: nowrap;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
`;

export default ChemicalInfoList;
