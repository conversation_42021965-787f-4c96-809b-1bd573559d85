/**
 * API 端點常數配置
 * 集中管理所有 API 端點，避免重複定義
 */

// API 端點常數
export const API_ENDPOINTS = {
  // 認證相關
  LOGIN: 'login',
  LOGOUT: 'logout',
  RECAPTCHA_VERIFY: 'recaptcha/verify',
  
  // 密碼重設相關
  PWD_RESET_CHECK: 'pwd/reset/check',
  PWD_RESET_NOTIFY: 'pwd/reset/notify', 
  PWD_RESET_AUTH_CHECK: 'pwd/reset/auth/check',
  PWD_RESET_UPDATE: 'pwd/reset/update',
  
  // Token 相關
  TOKEN_REFRESH: 'token/refresh',
  TOKEN_REVOKE: 'token/revoke',
  
  // 用戶相關
  USER_CURRENT: 'user/current',
  PERMISSION_SWITCH: 'permission/switch',
  PWD_CHANGE: 'pwd/change',
  TIME_NOW: 'time/now',
} as const;

// 不需要 Bearer token 的 API 端點
export const NO_TOKEN_ENDPOINTS = [
  API_ENDPOINTS.LOGIN,
  API_ENDPOINTS.RECAPTCHA_VERIFY,
  API_ENDPOINTS.PWD_RESET_CHECK,
  API_ENDPOINTS.PWD_RESET_NOTIFY,
  API_ENDPOINTS.PWD_RESET_AUTH_CHECK,
  API_ENDPOINTS.PWD_RESET_UPDATE,
  API_ENDPOINTS.TOKEN_REFRESH,
  API_ENDPOINTS.TOKEN_REVOKE,
] as const;

/**
 * 檢查 API 端點是否需要 Bearer token
 * @param url 請求 URL 或端點
 * @returns 是否需要 Bearer token
 */
export const shouldIncludeBearerToken = (url?: string): boolean => {
  if (!url) return true; // 默認需要 token
  
  // 檢查 URL 是否包含不需要 token 的端點
  return !NO_TOKEN_ENDPOINTS.some(endpoint => {
    return url.includes(`/${endpoint}`) || url.endsWith(endpoint);
  });
};