import InputNumFloat from 'ehs/common/input/InputNumFloat';
import { CHEMICAL_WEIGHT_DECIMAL_PLACES, CHEMICAL_WEIGHT_MAX, CHEMICAL_WEIGHT_MIN } from 'ehs/constant/constants';
import { t } from 'i18next';
import { useEffect, useState } from 'react';
import styled from 'styled-components';

interface ItemWeightFieldProps {
    bottleCount: number;
    weights: (number | null)[];
    setWeights: (weights: (number | null)[]) => void;
    setShowDialog: (isShow: boolean) => void;
    hasSubmit: boolean;
}

function ItemWeightField(props: ItemWeightFieldProps) {
    const { bottleCount, weights, setWeights, setShowDialog, hasSubmit } = props;
    const [hasEdit, setHasEdit] = useState(false);
    const firstWeight = weights[0];
    const showError = hasEdit && !firstWeight;

    useEffect(() => {
        if (hasSubmit) {
            setHasEdit(true);
        }
    }, [hasSubmit])

    const handleBlur = (value: number | undefined) => {
        const newWeights = [...weights];
        newWeights[0] = !value || value === 0 ? null : value;
        setWeights(newWeights);
        setHasEdit(true);
    };

    const handleEditClick = () => {
        setShowDialog(true);
    };

    return (
        <StlyedItemWeightField>
            {bottleCount === 1 ? (<div>
                <InputNumFloat className={`form-control ${showError && 'is-invalid'}`} minValue={CHEMICAL_WEIGHT_MIN} maxValue={CHEMICAL_WEIGHT_MAX} maxLength={7 + CHEMICAL_WEIGHT_DECIMAL_PLACES}
                    decimalPlaces={CHEMICAL_WEIGHT_DECIMAL_PLACES} onBlur={(num) => handleBlur(num)}
                    allowEmptyUndefine={true}
                    value={firstWeight !== null ? firstWeight : undefined}
                />
                {showError && <div className='mt-1'><span className='text-danger'>{t('message.chemical.no_product_weight')}</span></div>}
            </div>
            ) : (
                <div>
                    <button className="btn btn-warning" title={t('button.enter_weight')} onClick={handleEditClick}> <i className="fas fa-pen"></i> {t('button.enter_weight')}</button>
                    {<div>
                        {weights.map((weight, index) => (
                            <p key={'bottle-weight-' + index} className={`my-0 ${index === 0 ? 'pt-1' : ''}`}>
                                {t('text.bottle_num', { num: index + 1 })}：<span className={index < 9 ? 'space' : ''}></span> <span className={`${!weight && 'text-danger'}`}>{weight ? weight + ' kg' : t('message.chemical.no_product_weight')}</span>
                            </p>
                        ))}
                    </div>}
                </div>
            )}
        </StlyedItemWeightField>
    );
}

const StlyedItemWeightField = styled.div`
    .space {
    padding-left: 8px; /* 根據需要調整間距 */
    }
`

export default ItemWeightField;