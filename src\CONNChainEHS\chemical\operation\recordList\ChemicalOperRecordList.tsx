import BlockUi from "@availity/block-ui";
import { ChemicalAPI } from "api/chemicalAPI";
import { ConfigAPI } from "api/configAPI";
import { OptionAPI } from "api/optionAPI";
import { OrgAPI } from "api/orgAPI";
import { SelectListAPI } from "api/selectListAPI";
import BlockuiMsg from "ehs/common/BlockuiMsg";
import ChemicalClassificationBadge from "ehs/common/chemical/ChemicalClassificationBadge";
import { filterColumnsByType, getColumnsByChemClasses, getDefaultColumnConfig } from "ehs/common/chemical/ChemicalColumnConfig";
import ChemicalTableHeader from "ehs/common/chemical/ChemicalTableHeader";
import Dialog from "ehs/common/Dialog";
import GhsImage from "ehs/common/GhsImage";
import Loader from "ehs/common/Loader";
import NoDataRow from "ehs/common/NoDataRow";
import SearchConditionButton from "ehs/common/search/SearchConditionButton";
import SearchDivBuilding from "ehs/common/search/SearchDivBuilding";
import SearchDivLab from "ehs/common/search/SearchDivLab";
import SearchDivUnit from "ehs/common/search/SearchDivUnit";
import SearchDropdownSwitch from "ehs/common/search/SearchDropdownSwitch";
import ShowMoreInfo from "ehs/common/ShowMoreInfo";
import { errorMsg } from "ehs/common/SwalMsg";
import { CONFIG_TYPE_CHEM, CONFIG_TYPE_CONCEN_TYPE, CONFIG_TYPE_CONCERNED, CONFIG_TYPE_CTRL_CONCEN_TYPE, CONFIG_TYPE_GHS_IMG, CONFIG_TYPE_MIX_CONCEN_TYPE, CONFIG_TYPE_OPER_TYPE, CONFIG_TYPE_ORGANIC_SOLVENT, CONFIG_TYPE_PRIORITY, CONFIG_TYPE_PUBLIC_HAZARD, CONFIG_TYPE_SPECIFIC_CHEM, CONFIG_TYPE_STATE, CONFIG_TYPE_TOXIC, CONFIG_TYPE_WORK_ENV_MONITOR, CONFIG_TYPE_WORK_ENV_MONITOR_FREQ, CONFIG_VAL_CONCERNED, CONFIG_VAL_PRIORITY, CONFIG_VAL_TOXIC, OPTION_CHEM_REPORT_MONTH_EARLIEST, OPTION_CHEM_REPORT_MONTH_LONG, OPTION_CHEM_REPORT_MONTH_START_DF, OPTION_SHOW_SEARCH_ADVANCE, OPTION_SHOW_SEARCH_BUILDING, OPTION_SHOW_SEARCH_UNIT, ORG_SPLIT_FLAG } from "ehs/constant/constants";
import { SearchCondition } from "ehs/enums/SearchCondition";
import useLoginUser from "ehs/hooks/useLoginUser";
import useServerNowDate from "ehs/hooks/useServerNowDate";
import Breadcrumbs from "ehs/layout/Breadcrumbs";
import Footer from "ehs/layout/Footer";
import PageSizeSelector from "ehs/layout/PageSizeSelector";
import Pagination from "ehs/layout/Pagination";
import { ChemicalInventoryReport } from "ehs/models/ChemicalInventoryReport";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsOptions } from "ehs/models/EhsOptions";
import { EhsOrgLevel } from "ehs/models/EhsOrgLevel";
import { PageInfo, initPageInfo } from "ehs/models/PageInfo";
import { ReactSelectOption } from "ehs/models/ReactSelectOption";
import { SelectSearch } from "ehs/models/SearchLabInfo";
import { SelectItem } from "ehs/models/SelectItem";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { checkTimeoutAction, getBasicLoginUserInfo } from "ehs/utils/authUtil";
import { getShowChemCon } from "ehs/utils/chemicalUtil";
import { getConfigIdByValue } from "ehs/utils/configUtil";
import { ExcelColumnConfig, generateExcel } from "ehs/utils/excelUtil";
import { getUserUnitTextObj, notEnglishLang, splitChemNameListByLang } from "ehs/utils/langUtil";
import { addInventoryQty } from "ehs/utils/numUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { getFormatDateDash, getSplitUnitByIndex, isContainEnglish } from "ehs/utils/stringUtil";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import Select from "react-select";
import styled from "styled-components";
import OperRecordDialog, { OperRecordDialogData } from "./OperRecordDialog";

interface ConditionType {
  keyword: string;
  currentPage: number;
  pageSize: number;
  orgLevel: number | null;
  areaId: string;
  orgType: string;
  unit: { [key: number]: string };
  queryOrgId: string;
  buildId: string;
  floor: string;
  houseNum: string;
  queryLabIds: string[];
  chemConId: string;
  phaseState: string;
  toxicClass: string;
  svhcClass: string;
  priorityClass: string;
  organicClass: string;
  specificChemClass: string;
  envMonitorClass: string;
  envMonitorFreq: string;
  pubhazClass: string;
  casNo: string;
  operStartDate: string | undefined;
  operEndDate: string | undefined;
}

function ChemicalOperRecordList() {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const allOptionValue = "all"
  const allSelectOption = useMemo(() => ({ value: allOptionValue, label: t('text.all') }), [t]);
  const { loginUser } = useLoginUser();
  const [nowDate] = useServerNowDate(loginUser);
  const { itemText } = getUserUnitTextObj(loginUser, t);
  const [isLinkSelect, setIsLinkSelect] = useState<boolean>(true);
  const [showUnitCondition, setShowUnitCondition] = useState<boolean>(false);
  const [showBuildCondition, setShowBuildCondition] = useState<boolean>(false);
  const [showAdvCondition, setShowAdvCondition] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingBlock, setLoadingBlock] = useState<boolean>(false);
  const [popupMode, setPopupMode] = useState<boolean | null>(null);
  const [activeTab, setActiveTab] = useState<string>("");
  const [selectedChemData, setSelectedChemData] = useState<OperRecordDialogData>({
    casNo: "",
    chemCtrlNo: "",
    chemName: "",
    chemId: "",
    chemConId: "",
    phaseState: ""
  });
  const [inventoryList, setInventoryList] = useState<ChemicalInventoryReport[]>([]);
  const [cachedInventoryData, setCachedInventoryData] = useState<{
    [key: string]: {
      data: ChemicalInventoryReport[],
      pageInfo: PageInfo,
      timestamp: number,
      queryParams: any
    }
  }>({});
  const [searchSelectList, setSearchSelectList] = useState<SelectSearch[]>([]);
  const [chemClassOptions, setChemClassOptions] = useState<any[]>([]);
  const [chemClassTabs, setChemClassTabs] = useState<any[]>([]);
  const [orgLevelList, setOrgLevelList] = useState<EhsOrgLevel[]>([]);
  const [queryOrgIdList, setQueryOrgIdList] = useState<string[]>([]);
  const [configSelectOptions, setConfigSelectOptions] = useState<{ [key: string]: EhsConfigParam[] }>({});
  const [selectedChemClasses, setSelectedChemClasses] = useState<ReactSelectOption[]>([]);
  const [configMap, setConfigMap] = useState<{ [configId: string]: EhsConfigParam }>({});// config對應
  const [ctrlNoOptionList, setCtrlNoOptionList] = useState<SelectItem[]>([]); // 列管編號選項onfig對應
  const [svhcCtrlNoOptionList, setSvhcCtrlNoOptionList] = useState<SelectItem[]>([]); // 關注性編號選項onfig對應
  const [selectedCtrlNoOption, setSelectedCtrlNoOption] = useState<ReactSelectOption>(allSelectOption);
  const [selectedSvhcCtrlNoOption, setSelectedSvhcCtrlNoOption] = useState<ReactSelectOption>(allSelectOption);
  const [extendMap, setExtendMap] = useState<{ [index: number]: boolean }>({});// 下層展開
  const [queryOrgNameMap, setQueryOrgNameMap] = useState<{ [orgId: string]: string }>({});// 單位名稱對應
  const [optionsMap, setOptionsMap] = useState<{ [key: string]: EhsOptions }>({});
  const [selectedColumns, setSelectedColumns] = useState<any[]>([]);
  const initCondition = {
    keyword: "",
    currentPage: 1,
    pageSize: 50,
    orgLevel: null,
    areaId: "",
    orgType: "",
    unit: {},
    queryOrgId: "",
    buildId: "",
    floor: "",
    houseNum: "",
    queryLabIds: [],
    chemConId: "",
    phaseState: "",
    toxicClass: "",
    svhcClass: "",
    priorityClass: "",
    organicClass: "",
    specificChemClass: "",
    envMonitorClass: "",
    envMonitorFreq: "",
    pubhazClass: "",
    casNo: "",
    operStartDate: nowDate ? getFormatDateDash(new Date(nowDate.getFullYear(), nowDate.getMonth() - 6, nowDate.getDate())) : undefined,
    operEndDate: nowDate ? getFormatDateDash(nowDate) : undefined,
  };
  const [condition, setCondition] = useState<{ [key: string]: ConditionType }>({ [CONFIG_VAL_TOXIC]: initCondition });
  const [pageInfo, setPageInfo] = useState<PageInfo>(initPageInfo);
  const currentCondition = activeTab && condition[activeTab] ? condition[activeTab] : initCondition;
  const { orgLevel, operStartDate, operEndDate, currentPage, pageSize } = currentCondition;
  const otherTabVal = "others";
  const isActiveTabOther = activeTab === otherTabVal;
  const showAdvConditionMarginTop = showAdvCondition && isActiveTabOther ? 'mt-3' : '';

  // 使用標籤ID保存每個標籤的搜尋條件
  const [tabSearchConditions, setTabSearchConditions] = useState<{ [tabId: string]: any }>({});

  // 確保當前標籤有搜尋條件
  useEffect(() => {
    if (activeTab && !tabSearchConditions[activeTab]) {
      setTabSearchConditions(prev => ({
        ...prev,
        [activeTab]: {
          casNo: "",
          phaseState: "",
          toxicClass: "",
          concernedClass: "",
          priorityClass: "",
          organicClass: "",
          specificChemClass: "",
          envMonitorClass: "",
          envMonitorFreq: "",
          pubhazClass: "",
          operStartDate: nowDate ? getFormatDateDash(new Date(nowDate.getFullYear(), nowDate.getMonth() - 6, nowDate.getDate())) : undefined,
          operEndDate: nowDate ? getFormatDateDash(nowDate) : undefined,
          inventoryStatus: 1
        }
      }));
    }
  }, [activeTab, nowDate]);

  // 獲取當前標籤的搜尋條件
  const currentTabSearch = useMemo(() => {
    return activeTab && tabSearchConditions[activeTab] ? tabSearchConditions[activeTab] : {
      casNo: "",
      phaseState: "",
      toxicClass: "",
      concernedClass: "",
      priorityClass: "",
      organicClass: "",
      specificChemClass: "",
      envMonitorClass: "",
      envMonitorFreq: "",
      pubhazClass: "",
      operStartDate: operStartDate,
      operEndDate: operEndDate
    };
  }, [activeTab, tabSearchConditions, operStartDate, operEndDate]);

  // 更新當前標籤的搜尋條件
  const updateCurrentTabSearch = useCallback((updates: Partial<typeof currentTabSearch>) => {
    if (!activeTab) return;

    setTabSearchConditions(prev => ({
      ...prev,
      [activeTab]: {
        ...prev[activeTab],
        ...updates
      }
    }));
  }, [activeTab]);

  // 同樣使用 useMemo 包裹 selectedChemClassesConfig
  const selectedChemClassesConfig = useMemo(() => {
    return selectedChemClasses.map((option) => configMap[option.value]);
  }, [selectedChemClasses, configMap]);

  const phaseStateOptions = configSelectOptions[CONFIG_TYPE_STATE] || [];
  const toxicClassOptions = configSelectOptions[CONFIG_TYPE_TOXIC] || [];
  const concernedClassOptions = configSelectOptions[CONFIG_TYPE_CONCERNED] || [];
  const priorityClassOptions = configSelectOptions[CONFIG_TYPE_PRIORITY] || [];
  const organicClassOptions = configSelectOptions[CONFIG_TYPE_ORGANIC_SOLVENT] || [];
  const specificChemClassOptions = configSelectOptions[CONFIG_TYPE_SPECIFIC_CHEM] || [];
  const envMonitorClassOptions = configSelectOptions[CONFIG_TYPE_WORK_ENV_MONITOR] || [];
  const envMonitorFreqOptions = configSelectOptions[CONFIG_TYPE_WORK_ENV_MONITOR_FREQ] || [];
  const pubhazClassOptions = configSelectOptions[CONFIG_TYPE_PUBLIC_HAZARD] || [];
  const chemicalClassTypes = useMemo(() => [
    { type: CONFIG_TYPE_TOXIC, key: 'hasSelectedToxicClass' },
    { type: CONFIG_TYPE_CONCERNED, key: 'hasSelectedConcernedClass' },
    { type: CONFIG_TYPE_PRIORITY, key: 'hasSelectedPriorityClass' },
    { type: CONFIG_TYPE_ORGANIC_SOLVENT, key: 'hasSelectedOrganicClass' },
    { type: CONFIG_TYPE_SPECIFIC_CHEM, key: 'hasSelectedSpecificChemClass' },
    { type: CONFIG_TYPE_WORK_ENV_MONITOR, key: 'hasSelectedEnvMonitorClass' },
    { type: CONFIG_TYPE_PUBLIC_HAZARD, key: 'hasSelectedPubhazClass' },
  ], []);
  const selectedClassFlags = useMemo(() =>
    chemicalClassTypes.reduce<Record<string, boolean>>((acc, { type, key }) => {
      acc[key] = selectedChemClassesConfig.some((config) => config && config.configValue === type);
      return acc;
    }, {}),
    [chemicalClassTypes, selectedChemClassesConfig]);
  const keyToTypeMap = useMemo(() =>
    chemicalClassTypes.reduce<Record<string, string>>((acc, { type, key }) => {
      acc[key] = type;
      return acc;
    }, {}),
    [chemicalClassTypes]);


  const { hasSelectedToxicClass, hasSelectedConcernedClass, hasSelectedPriorityClass, hasSelectedOrganicClass, hasSelectedSpecificChemClass,
    hasSelectedEnvMonitorClass, hasSelectedPubhazClass } = selectedClassFlags;

  const createDeselectClearClassHandler = useCallback((refKey: string) => ({
    onSelect: () => { /* 可以在這裡添加選擇時的邏輯 */ },
    onDeselect: () => {
      updateCurrentTabSearch({ [refKey]: "" });
    }
  }), [updateCurrentTabSearch]);

  const classHandlers = useMemo(() => ({
    [CONFIG_TYPE_TOXIC]: {
      onSelect: () => {
        if (loginUser && isArrayEmpty(ctrlNoOptionList)) {
          fetchCtrlNoList();
        }
      },
      onDeselect: () => {
        updateCurrentTabSearch({ toxicClass: "" });
        setSelectedCtrlNoOption(allSelectOption);
      }
    },
    [CONFIG_TYPE_CONCERNED]: {
      onSelect: () => {
        if (loginUser && isArrayEmpty(svhcCtrlNoOptionList)) {
          fetchCtrlNoList();
        }
      },
      onDeselect: () => {
        updateCurrentTabSearch({ concernedClass: "" });
        setSelectedSvhcCtrlNoOption(allSelectOption);
      }
    },
    [CONFIG_TYPE_PRIORITY]: createDeselectClearClassHandler('priorityClass'),
    [CONFIG_TYPE_ORGANIC_SOLVENT]: createDeselectClearClassHandler('organicClass'),
    [CONFIG_TYPE_SPECIFIC_CHEM]: createDeselectClearClassHandler('specificChemClass'),
    [CONFIG_TYPE_WORK_ENV_MONITOR]: createDeselectClearClassHandler('envMonitorClass'),
    [CONFIG_TYPE_PUBLIC_HAZARD]: createDeselectClearClassHandler('pubhazClass'),
  }), [loginUser, ctrlNoOptionList, svhcCtrlNoOptionList, updateCurrentTabSearch]);

  useEffect(() => {
    Object.entries(selectedClassFlags).forEach(([key, isSelected]) => {
      const type = keyToTypeMap[key];
      const handler = classHandlers[type as keyof typeof classHandlers];

      if (handler) {
        if (isSelected) {
          handler.onSelect();
        } else {
          handler.onDeselect();
        }
      }
    });
  }, [selectedClassFlags, classHandlers]);
  const showOrgLevelName = useMemo(() =>
    orgLevel === null ? "" : orgLevelList.find((data) => data.orglvLevel === (orgLevel + 1))?.orglvName,
    [orgLevel, orgLevelList]);
  const nowLastQueryOrgId = queryOrgIdList[queryOrgIdList.length - 1];
  const nowOrgName = queryOrgNameMap[nowLastQueryOrgId];
  // 創建自定義標籤選項
  const tabChemClassConfig = useMemo(() => [
    CONFIG_VAL_TOXIC,
    CONFIG_VAL_CONCERNED,
    CONFIG_VAL_PRIORITY
  ], []);

  useEffect(() => {
    const loadData = async () => {
      if (loginUser && activeTab) {
        const cachedData = cachedInventoryData[activeTab];
        const currentTime = Date.now();
        const cacheExpireTime = 5 * 60 * 1000; // 5分鐘暫存時間

        // 檢查是否有暫存資料，是否未過期，且查詢參數相同
        if (cachedData &&
          (currentTime - cachedData.timestamp < cacheExpireTime) &&
          JSON.stringify(cachedData.queryParams) === JSON.stringify({
            currentPage,
            pageSize,
            orgLevel,
            language: i18n.language
          })) {
          // 使用暫存資料
          setInventoryList(cachedData.data);
          setPageInfo(cachedData.pageInfo);
          setLoading(false);
        } else {
          // 重新抓取資料
          await fetchData();
        }
      }
    };

    loadData();
  }, [loginUser, currentPage, pageSize, orgLevel, i18n.language, activeTab]);

  useEffect(() => {
    if (loginUser) {
      const loadOptions = async () => {
        await fetchOption();
      };
      loadOptions();
    }
  }, [loginUser]);

  useEffect(() => {
    if (loginUser) {
      const loadData = async () => {
        await Promise.all([
          fetchSelectListData(),
          fetchConfig(),
          fetchOrgLevelData()
        ]);
      };
      loadData();
    }
  }, [loginUser, i18n.language]);

  useEffect(() => {
    // 設置初始活動標籤
    if (!isArrayEmpty(chemClassTabs) && !activeTab) {
      setActiveTab(chemClassTabs[0].value);
    }
  }, [chemClassTabs, activeTab]);

  // 根據選項設定是否顯示搜尋條件
  useEffect(() => {
    if (!isArrayEmpty(Object.keys(optionsMap))) {
      const unitOption = optionsMap[OPTION_SHOW_SEARCH_UNIT];
      const buildOption = optionsMap[OPTION_SHOW_SEARCH_BUILDING];
      const advOption = optionsMap[OPTION_SHOW_SEARCH_ADVANCE];

      setShowUnitCondition(unitOption?.optionEnabled);
      setShowBuildCondition(buildOption?.optionEnabled);
      setShowAdvCondition(advOption?.optionEnabled);
    }
  }, [optionsMap]);

  // 修改 fetchData 使用函數式更新，避免直接依賴狀態
  const fetchData = useCallback(async (searchCondition = currentCondition) => {
    setLoading(true);
    const { queryLabIds } = searchCondition;
    const processedSearchCondition = {
      ...searchCondition,
      queryLabIds: queryLabIds ? (Array.isArray(queryLabIds) ? queryLabIds : [queryLabIds]) : []
    };

    await fetchChemicalInventoryList(processedSearchCondition);
  }, [currentCondition]); // 只在 currentCondition 改變時更新函數

  const fetchChemicalInventoryList = async (searchCondition = currentCondition) => {
    // 如果 activeTab 未設定，使用預設值
    const currentTab = activeTab || CONFIG_VAL_TOXIC;
    const isCurrentTabOther = currentTab === otherTabVal;

    const configId = getConfigIdByValue(configMap, currentTab);
    const excludeCategoryIds = isCurrentTabOther
      ? tabChemClassConfig.map(val => getConfigIdByValue(configMap, val)).filter(Boolean)
      : [];

    try {
      const result = await ChemicalAPI.getChemicalInventoryReportList({
        ...getBasicLoginUserInfo(loginUser),
        ...searchCondition,
        categoryId: configId,
        excludeCategoryIds,
      });
      handleFetchDataSuccess(result);
    } catch (err) {
      checkTimeoutAction(err, navigate, t);
      console.error(err);
    }
  }

  const handleFetchDataSuccess = (result: any) => {
    if (isApiCallSuccess(result)) {
      setPageInfo(result.pageinfo);
      setInventoryList(result.results);

      // 暫存資料
      setCachedInventoryData(prev => ({
        ...prev,
        [activeTab]: {
          data: result.results,
          pageInfo: result.pageinfo,
          timestamp: Date.now(),
          queryParams: {
            currentPage,
            pageSize,
            orgLevel,
            language: i18n.language
          }
        }
      }));

      setLoading(false)
    } else {
      if (result && result.message) {
        errorMsg(result.message)
      } else {
        errorMsg(t('text.error'))
      }
    }
  }

  const fetchSelectListData = async () => {
    try {
      const result = await SelectListAPI.getSelectLabSearchView({
        ...getBasicLoginUserInfo(loginUser),
      });
      if (result) {
        setSearchSelectList(result.results);
      }
    } catch (err) {
      console.error('獲取實驗室搜尋資料失敗:', err);
    }
  };

  const fetchConfig = async () => {
    try {
      const result = await ConfigAPI.getConfigByType({
        ...getBasicLoginUserInfo(loginUser),
        configTypeList: [CONFIG_TYPE_CHEM, CONFIG_TYPE_STATE, CONFIG_TYPE_CTRL_CONCEN_TYPE, CONFIG_TYPE_CONCEN_TYPE, CONFIG_TYPE_GHS_IMG,
          CONFIG_TYPE_MIX_CONCEN_TYPE, CONFIG_TYPE_TOXIC, CONFIG_TYPE_CONCERNED, CONFIG_TYPE_PRIORITY, CONFIG_TYPE_ORGANIC_SOLVENT,
          CONFIG_TYPE_SPECIFIC_CHEM, CONFIG_TYPE_WORK_ENV_MONITOR, CONFIG_TYPE_WORK_ENV_MONITOR_FREQ, CONFIG_TYPE_PUBLIC_HAZARD,
          CONFIG_TYPE_OPER_TYPE]
      });

      if (result) {
        const configMap = new Map<string, EhsConfigParam>();
        const configSelectOptions: { [key: string]: EhsConfigParam[] } = {};

        result.results.forEach((config: EhsConfigParam) => {
          const { configType, configId, configIvalue } = config;
          if (configType === CONFIG_TYPE_CTRL_CONCEN_TYPE) {
            configMap.set(String(configIvalue), config);
          } else {
            configMap.set(configId, config);
            if (!configSelectOptions[configType]) {
              configSelectOptions[configType] = [];
            }
            configSelectOptions[configType].push(config);
          }
        });

        const chemClassConfigs = configSelectOptions[CONFIG_TYPE_CHEM] || [];

        // 使用 some 方法過濾出不屬於三個特定分類的選項
        const filteredChemClassOptions = chemClassConfigs.filter(config =>
          !tabChemClassConfig.some(tabConfigVal => config.configValue === tabConfigVal)
        ).map(config => ({
          value: config.configId,
          label: config.configName
        }));

        // 使用 filter 和 map 組合創建標籤項
        const classTabItems = chemClassConfigs
          .filter(config => tabChemClassConfig.includes(config.configValue))
          .map(config => ({
            value: config.configValue,
            label: config.configName
          }));

        // 添加「其他」標籤
        classTabItems.push({ value: otherTabVal, label: t('table.title.other') });

        setConfigMap(Object.fromEntries(configMap)); // 將 Map 轉換為物件
        setChemClassOptions(filteredChemClassOptions); // 設置過濾後的選項
        setChemClassTabs(classTabItems);
        setConfigSelectOptions(configSelectOptions);
      }
    } catch (err) {
      console.error('獲取配置資料失敗:', err);
    }
  };

  const fetchCtrlNoList = async () => {
    try {
      const result = await SelectListAPI.getSelectChemicalCtrlnoList({
        ...getBasicLoginUserInfo(loginUser),
      });

      if (isApiCallSuccess(result)) {
        // 將 "全部" 選項添加到結果中
        const { updatedSvhcResults, updatedCtrlNoResults } = result.results.reduce((acc: any, item: SelectItem) => {
          if (isContainEnglish(item.label)) {
            acc.updatedSvhcResults.push(item);
          } else {
            acc.updatedCtrlNoResults.push(item);
          }
          return acc;
        }, { updatedSvhcResults: [], updatedCtrlNoResults: [] });

        setCtrlNoOptionList([allSelectOption, ...updatedCtrlNoResults]);
        setSvhcCtrlNoOptionList([allSelectOption, ...updatedSvhcResults]);
      }
    } catch (err) {
      console.error('獲取控制編號列表失敗:', err);
    }
  }

  const fetchOption = async () => {
    try {
      const optionIds = [OPTION_CHEM_REPORT_MONTH_START_DF, OPTION_CHEM_REPORT_MONTH_LONG, OPTION_CHEM_REPORT_MONTH_EARLIEST,
        OPTION_SHOW_SEARCH_UNIT, OPTION_SHOW_SEARCH_BUILDING, OPTION_SHOW_SEARCH_ADVANCE
      ];
      const result = await OptionAPI.getOptionListByIds({
        ...getBasicLoginUserInfo(loginUser),
        optionIdList: optionIds
      });

      if (isApiCallSuccess(result)) {
        const options = result.results;
        const newOptionsMap: {
          [key: string]: EhsOptions;
        } = {};
        options.forEach((item: any) => {
          if (optionIds.includes(item.optionId)) {
            newOptionsMap[item.optionId] = item;
          }
        });
        setOptionsMap(newOptionsMap);
      }
    } catch (error) {
      console.error('獲取選項設定失敗:', error);
    }
  };

  const fetchOrgLevelData = async () => {
    try {
      const result = await OrgAPI.getOrgLevelList({
        ...getBasicLoginUserInfo(loginUser)!,
      });

      if (isApiCallSuccess(result)) {
        setOrgLevelList(result.results);
      }
    } catch (error) {
      console.error('獲取組織層級資料失敗:', error);
    }
  }

  const getShowList = useCallback(() => {
    return inventoryList || [];
  }, [inventoryList]);

  const backPrevQueryUnit = () => {
    const prevOrgLevel = (orgLevel === null || orgLevel === 0) ? null : orgLevel - 1;
    setExtendMap({});
    const newQueryOrgIdList = queryOrgIdList.slice(0, -1);
    const newLastQueryOrgId = newQueryOrgIdList[newQueryOrgIdList.length - 1];
    setQueryOrgIdList(newQueryOrgIdList);
    setCondition({
      ...condition,
      [activeTab]: {
        ...currentCondition,
        orgLevel: prevOrgLevel,
        queryOrgId: newLastQueryOrgId,
        chemConId: prevOrgLevel === null ? '' : currentCondition.chemConId,
        phaseState: prevOrgLevel === null ? '' : currentCondition.phaseState,
      }
    });
  }

  const clickSearch = async () => {
    setExtendMap({});
    setQueryOrgIdList([]);
    setQueryOrgNameMap({});

    // 清除暫存資料 
    clearTabCache(activeTab);

    // 使用當前標籤的搜尋條件
    const {
      casNo, phaseState, operStartDate, operEndDate, inventoryStatus,
      toxicClass, concernedClass, priorityClass, organicClass,
      specificChemClass, envMonitorClass, envMonitorFreq, pubhazClass
    } = currentTabSearch;

    const chemCtrlNo = selectedCtrlNoOption.value === allOptionValue ? "" : selectedCtrlNoOption.value;
    const svhcCtrlNo = selectedSvhcCtrlNoOption.value === allOptionValue ? "" : selectedSvhcCtrlNoOption.value;

    const newCondition = {
      ...currentCondition,
      casNo,
      chemCtrlNo,
      svhcCtrlNo,
      phaseState,
      currentPage: 1,
      chemConId: "",
      orgLevel: null,
      operStartDate,
      operEndDate,
      inventoryStatus,
      chemClassIdList: selectedChemClasses.map((option) => option.value),
      chemCtrlNoList: [chemCtrlNo, svhcCtrlNo].filter(Boolean),
      chemSubCategoryIdList: [
        ...(toxicClass ? [toxicClass] : []),
        ...(concernedClass ? [concernedClass] : []),
        ...(priorityClass ? [priorityClass] : []),
        ...(organicClass ? [organicClass] : []),
        ...(specificChemClass ? [specificChemClass] : []),
        ...(envMonitorClass ? [envMonitorClass] : []),
        ...(envMonitorFreq ? [envMonitorFreq] : []),
      ],
      purchaseDetailConfigIdList: [...(pubhazClass ? [pubhazClass] : [])]
    };

    setCondition(prev => ({
      ...prev,
      [activeTab]: newCondition
    }));
    await fetchData(newCondition);
  }

  const clearTabCache = useCallback((tabId: string) => {
    setCachedInventoryData(prev => {
      const newCache = { ...prev };
      delete newCache[tabId];
      return newCache;
    });
  }, []);

  const handleTabClick = useCallback((tabId: string) => {
    // 如果是相同標籤，提早返回
    if (tabId === activeTab) {
      return;
    }

    // 清除"其他"標籤頁的化學品分類選擇
    if (tabId === otherTabVal) {
      setSelectedChemClasses([]);
    }

    // 重置控制元件
    setSelectedCtrlNoOption(allSelectOption);
    setSelectedSvhcCtrlNoOption(allSelectOption);

    // 設置活動標籤
    setActiveTab(tabId);
  }, [activeTab, otherTabVal, allSelectOption]);

  // 合併相同 chemConId 和 phaseState 的資料 (僅限毒化物和關注性標籤，且只計算主物質)
  const mergeInventoryData = (inventoryList: any[]) => {
    const mergedMap = new Map();

    inventoryList.forEach(inventory => {
      // 只處理主物質 (isPrimarySubst 為 true)
      if (!inventory.primarySubst) {
        return;
      }

      const key = `${inventory.chemConId}_${inventory.phaseState}`;

      if (mergedMap.has(key)) {
        const existing = mergedMap.get(key);

        // 累加庫存量 (使用工具方法處理decimal精度問題)
        existing.inventoryQty = addInventoryQty(existing.inventoryQty, inventory.inventoryQty);

        // 合併去重複的陣列欄位
        existing.casnoList = mergeUniqueArray(existing.casnoList, inventory.casnoList, 'casno');
        existing.chemNameList = mergeUniqueArray(existing.chemNameList, inventory.chemNameList, 'chemName');
        existing.chemCategoryList = mergeUniqueArray(existing.chemCategoryList, inventory.chemCategoryList, 'configId');
        existing.detailCategoryList = mergeUniqueArray(existing.detailCategoryList, inventory.detailCategoryList, 'configId');
        existing.substCategoryList = mergeUniqueArray(existing.substCategoryList, inventory.substCategoryList, 'configId');

      } else {
        mergedMap.set(key, { ...inventory });
      }
    });

    return Array.from(mergedMap.values());
  };

  // 合併陣列並去重複
  const mergeUniqueArray = (arr1: any[], arr2: any[], uniqueKey: string) => {
    if (!arr1) return arr2 || [];
    if (!arr2) return arr1;

    const merged = [...arr1];
    const existingKeys = new Set(arr1.map(item => item[uniqueKey]));

    arr2.forEach(item => {
      if (!existingKeys.has(item[uniqueKey])) {
        merged.push(item);
        existingKeys.add(item[uniqueKey]);
      }
    });

    return merged;
  };

  // 根據當前選擇的標籤過濾數據
  const getTabFilteredList = () => {
    if (!activeTab || isArrayEmpty(getShowList())) {
      return [];
    }

    // 處理清單以支援排序功能
    const processedList = getShowList().map(inventory => {
      const { casnoList, chemNameList } = inventory;
      return {
        ...inventory,
        firstCasno: casnoList && casnoList.length > 0 ? casnoList[0].casno : "",
        currentLangName: chemNameList.find((name) => name.langType === i18n.language)?.chemName ||
          (chemNameList.length > 0 ? chemNameList[0].chemName : "")
      };
    });

    // 只有毒化物和關注性標籤需要合併
    const shouldMerge = [CONFIG_VAL_TOXIC, CONFIG_VAL_CONCERNED].includes(activeTab);

    if (shouldMerge) {
      return mergeInventoryData(processedList);
    }

    return processedList;
  };

  const updateTabCondition = (updateFn: (prevTabCondition: ConditionType) => ConditionType) => {
    setCondition(prev => ({
      ...prev,
      [activeTab]: updateFn(prev[activeTab] || currentCondition)
    }));
  };
  const handleSetCondition = (prevCondition: any) => updateTabCondition(tabCondition => ({
    ...tabCondition,
    ...(typeof prevCondition === 'function' ? prevCondition(tabCondition) : prevCondition)
  }));

  // 排序欄位映射
  const sortFieldsMap = useMemo(() => ({
    'chemCtrlNo': 'chemCtrlNo',
    'casNo': 'firstCasno',
    'name': 'currentLangName',
    'chemQty': 'chemGradeHandQty',
    'inventoryQty': 'inventoryQty'
  }), []);

  // 根據當前標籤更新顯示欄位
  useEffect(() => {
    if (activeTab) {
      // 獲取基本欄位配置
      const columns = getDefaultColumnConfig(t, { itemText });

      if (isActiveTabOther) {
        // 其他標籤，根據選擇的化學品分類顯示欄位
        setSelectedColumns(getColumnsByChemClasses(columns, selectedChemClassesConfig));
      } else {
        // 特定標籤（毒化物、關注性、優先管理物質）
        setSelectedColumns(filterColumnsByType(columns, activeTab));
      }
    }
  }, [activeTab, selectedChemClassesConfig, t, isActiveTabOther, itemText]);

  // 在組件頂部添加 state
  const [tabDefaultAreaSet, setTabDefaultAreaSet] = useState<{ [tabId: string]: boolean }>({});

  // 添加處理頁籤預設區域的 useEffect
  useEffect(() => {
    if (activeTab && loginUser?.userInfo?.areaId && !isArrayEmpty(searchSelectList)) {
      const currentTabCondition = condition[activeTab] || initCondition;

      // 當前頁籤尚未設置預設區域且區域值為空
      if (!tabDefaultAreaSet[activeTab] && !currentTabCondition.areaId) {
        const newCondition = {
          ...currentTabCondition,
          areaId: loginUser.userInfo.areaId
        };

        setCondition(prev => ({
          ...prev,
          [activeTab]: newCondition
        }));

        // 如果使用 tabSearchConditions 來存儲頁籤搜索條件，也需要更新
        if (tabSearchConditions[activeTab]) {
          setTabSearchConditions(prev => ({
            ...prev,
            [activeTab]: {
              ...prev[activeTab],
              areaId: loginUser.userInfo.areaId
            }
          }));
        }

        // 標記已設置過
        setTabDefaultAreaSet(prev => ({
          ...prev,
          [activeTab]: true
        }));
      }
    }
  }, [activeTab, loginUser, searchSelectList]);

  // 添加Excel匯出功能
  const exportToExcel = async () => {
    try {
      // 定義根據當前活動標籤的欄位配置
      let columns: ExcelColumnConfig[] = [
        {
          header: t('table.title.item'),
          key: 'itemNo',
          width: 8
        }
      ];

      // 只在有單位層級時添加單位欄位
      if (orgLevel !== null) {
        columns.push({
          header: t('table.title.org.item'),
          key: 'orgName',
          width: 15,
          formatter: (_, row) => {
            return getSplitUnitByIndex(row.orgNames, orgLevel || 0);
          }
        });
      }

      // 檢查是否有選擇單位查詢，如果有則添加單位名稱欄位
      const hasUnitQuery = currentCondition.queryOrgId;
      if (hasUnitQuery) {
        // 從現有資料中取得單位名稱
        const getUnitName = () => {
          // 優先使用queryOrgNameMap中的名稱
          if (queryOrgNameMap[currentCondition.queryOrgId]) {
            return queryOrgNameMap[currentCondition.queryOrgId];
          }

          // 從searchSelectList中尋找對應的單位名稱
          const findUnitInSearchList = (targetOrgId: string): string | null => {
            for (const item of searchSelectList) {
              // 檢查各層級的ID
              if (item.labId === targetOrgId) return item.labName;
              if (item.buildId === targetOrgId) return item.buildName;

              // 檢查orgIds（可能是用分隔符號分隔的字串）
              if (item.orgIds) {
                const orgIdArray = item.orgIds.split(ORG_SPLIT_FLAG).map(id => id.trim());
                const orgNameArray = item.orgNames ? item.orgNames.split(ORG_SPLIT_FLAG).map(name => name.trim()) : [];

                const targetIndex = orgIdArray.findIndex(id => id === targetOrgId);
                if (targetIndex !== -1 && orgNameArray[targetIndex]) {
                  return orgNameArray[targetIndex];
                }
              }
            }
            return null;
          };

          const unitName = findUnitInSearchList(currentCondition.queryOrgId);
          if (unitName) {
            return unitName;
          }

          // 最後從inventoryList中取得
          const firstItem = getTabFilteredList()[0];
          if (firstItem && firstItem.orgIds && firstItem.orgNames) {
            const targetIndex = firstItem.orgIds.findIndex((id: any) => id === currentCondition.queryOrgId);
            if (targetIndex !== -1 && firstItem.orgNames[targetIndex]) {
              return firstItem.orgNames[targetIndex];
            }
          }

          return ''; // 找不到時回傳空字串
        };

        columns.push({
          header: t('table.title.org.name'),
          key: 'unitName',
          width: 20,
          formatter: () => {
            return getUnitName();
          }
        });
      }

      // 添加通用欄位
      columns = [
        ...columns,
        {
          header: t('table.title.ctrl_no'),
          key: 'chemCtrlNo',
          width: 15
        },
        {
          header: t('table.title.casno'),
          key: 'casNo',
          width: 15,
          formatter: (_, row) => {
            const casnoList = row.casnoList || [];
            return !isArrayEmpty(casnoList)
              ? casnoList.map((item: any) => item.casno).join(', ')
              : '';
          }
        },
        {
          header: t('table.title.name'),
          key: 'name',
          width: 25,
          formatter: (_, row) => {
            const chemNameList = row.chemNameList || [];
            const { currentLangNames } = splitChemNameListByLang(chemNameList, i18n.language);
            return !isArrayEmpty(currentLangNames)
              ? currentLangNames.map((item: any) => item.chemName).join(', ')
              : '';
          }
        }
      ];

      // 根據當前標籤添加特定欄位
      if (activeTab === CONFIG_VAL_TOXIC) {
        columns.push(
          {
            header: t('table.title.toxic_class'),
            key: 'toxicClass',
            width: 15,
            formatter: (_, row) => {
              const chemCategoryList = row.chemCategoryList || [];
              const toxicClassify = chemCategoryList.filter((item: any) => item.configType === CONFIG_TYPE_TOXIC) || [];
              return toxicClassify.map((item: any) => configMap[item.configId]?.configName).join(', ');
            }
          }
        );
      } else if (activeTab === CONFIG_VAL_CONCERNED) {
        columns.push(
          {
            header: t('table.title.chemical.concerned_class'),
            key: 'concernedClass',
            width: 15,
            formatter: (_, row) => {
              const chemCategoryList = row.chemCategoryList || [];
              const concernedClassify = chemCategoryList.filter((item: any) => item.configType === CONFIG_TYPE_CONCERNED) || [];
              return concernedClassify.map((item: any) => configMap[item.configId]?.configName).join(', ');
            }
          }
        );
      } else if (activeTab === CONFIG_VAL_PRIORITY) {
        columns.push(
          {
            header: t('table.title.chemical.prority_class_title'),
            key: 'priorityClass',
            width: 15,
            formatter: (_, row) => {
              const chemCategoryList = row.chemCategoryList || [];
              const priorityClassify = chemCategoryList.filter((item: any) => item.configType === CONFIG_TYPE_PRIORITY) || [];
              return priorityClassify.map((item: any) => configMap[item.configId]?.configName).join(', ');
            }
          }
        );
      }

      // 添加通用的剩餘欄位
      columns = [
        ...columns,
        {
          header: t('table.title.chemical.phase_state'),
          key: 'phaseState',
          width: 15,
          formatter: (value, _, configMap) => configMap[value]?.configName
        },
        {
          header: t('table.title.chemical.unit_ctrl_qty_total', { unit: itemText }),
          key: 'ctrlTotalQty',
          width: 15,
          formatter: (_, row, configMap, t) => {
            const showCtrlTotalQty = row.ctrlTotalQtyType === 1 ? row.chemGradeHandQty : row.ctrlTotalQty;
            return showCtrlTotalQty === null ? t('text.chemical.no_ctrL_limit') : `${showCtrlTotalQty} kg`;
          }
        },
        {
          header: t('table.title.inventory_quantity'),
          key: 'inventoryQty',
          width: 15,
          formatter: (value) => `${value} kg`
        }
      ];

      // 調用通用工具函數
      await generateExcel(
        getTabFilteredList(),
        columns,
        configMap,
        t,
        `Chemical ${activeTab} Operation Records.xlsx`,
        `${activeTab} Records`
      );
    } catch (err) {
      console.error('匯出Excel失敗:', err);
      errorMsg(t('message.export.failed'));
    }
  };

  return (
    <StlyedChemicalOperRecordList $loading={loading}>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {
            <Dialog
              content={
                <OperRecordDialog
                  onClose={() => {
                    setPopupMode(null);
                  }}
                  setLoadingBlock={setLoadingBlock}
                  mode={popupMode}
                  nowDate={nowDate}
                  configMap={configMap}
                  optionsMap={optionsMap}
                  activeTab={activeTab}
                  areaId={currentCondition.areaId}
                  chemData={selectedChemData}
                />
              }
              show={popupMode !== null}
            />
          }
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.chemical.manage") },
                { label: t("func.chemical.operation_record") },
              ]}
            />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{t("func.chemical.operation_record")} </h1>
            {/* END page-header */}

            {/* 新增標籤欄 */}
            <ul className="nav nav-tabs">
              {chemClassTabs.map((option, idx) => (
                <li className="nav-item" key={`tab-${option.value}`}>
                  <a
                    href={`#tab-${option.value}`}
                    data-bs-toggle="tab"
                    className={`nav-link ${option.value === activeTab ? 'active' : ''}`}
                    onClick={() => handleTabClick(option.value)}
                  >
                    {option.label}
                  </a>
                </li>
              ))}
            </ul>
            <div className="tab-content panel p-0 rounded-0 rounded-bottom">
              {chemClassTabs.map((option) => (
                <div
                  className={`tab-pane fade ${option.value === activeTab ? 'active show' : ''}`}
                  id={`tab-${option.value}`}
                  key={`content-${option.value}`}
                >
                  <div className="card">
                    <div className="card-body p-4">
                      <SearchDropdownSwitch checked={isLinkSelect} onChangeChecked={setIsLinkSelect} onChangeCondition={() => setCondition(prev => ({
                        ...prev,
                        [activeTab]: initCondition
                      }))} />
                      <SearchConditionButton buttonType={SearchCondition.UNIT} onClick={() => { setShowUnitCondition((pre) => !pre) }} />
                      <SearchConditionButton buttonType={SearchCondition.BUILDING} onClick={() => { setShowBuildCondition((pre) => !pre) }} />
                      <SearchConditionButton buttonType={SearchCondition.ADVANCEd} onClick={() => { setShowAdvCondition((pre) => !pre) }} />
                      <button type="button" className="btn btn-secondary ms-1 my-2" title={t('button.reset_search')} onClick={() => setCondition(prev => ({
                        ...prev,
                        [activeTab]: initCondition
                      }))}><i className="fas fa-undo mx-1"></i>{t('button.reset_search')}</button>
                      <div className="row my-3">

                        <SearchDivUnit dataList={searchSelectList} show={showUnitCondition} isLinkSelect={isLinkSelect}
                          condition={currentCondition} setCondition={handleSetCondition} showAll loginUser={loginUser} autoSetDefaultArea={false} />
                        <SearchDivBuilding dataList={searchSelectList} show={showBuildCondition} isLinkSelect={isLinkSelect} condition={currentCondition} setCondition={handleSetCondition} showAll />
                        <div className="row my-2">
                          <SearchDivLab dataList={searchSelectList} isLinkSelect={isLinkSelect} condition={currentCondition} setCondition={handleSetCondition} />
                          {showAdvCondition &&
                            <>
                              <div className={`col-xl-3 d-flex align-items-center`}>
                                <label className="w-25">{t('text.chemical.casno')}</label>
                                <input
                                  type="text"
                                  className="form-control form-control-lg"
                                  defaultValue={currentTabSearch.casNo}
                                  onChange={(e) => updateCurrentTabSearch({ casNo: e.target.value.trim() })}
                                />
                              </div>
                              <div className={`col-xl-3 d-flex align-items-center`}>
                                <label className="w-25">{t('text.chemical.phase_state')}</label>
                                <select
                                  className="form-select form-select-lg"
                                  defaultValue={currentTabSearch.phaseState}
                                  onChange={(e) => updateCurrentTabSearch({ phaseState: e.target.value })}
                                >
                                  <option value="">{t('text.all')}</option>
                                  {phaseStateOptions.map((option) => (
                                    <option key={option.configId} value={option.configId}>{option.configName}</option>
                                  ))}
                                </select>
                              </div>
                              {isActiveTabOther && <div className={`col-xl-3 d-flex align-items-center`}>
                                <label className="w-25">{t("text.chemical.class")}</label>
                                <Select
                                  className="w-75"
                                  isMulti
                                  isSearchable
                                  menuPosition="fixed"
                                  options={chemClassOptions}
                                  placeholder={t('text.all')}
                                  value={selectedChemClasses}
                                  onChange={(selectedOptions: any) => {
                                    setSelectedChemClasses(selectedOptions || []);
                                  }}
                                  noOptionsMessage={() => t('text.select_no_option')}
                                />
                              </div>}
                              {hasSelectedToxicClass && (
                                <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                                  <label className="w-25">{t('text.chemical.toxic_class')}</label>
                                  <select
                                    className="form-select form-select-lg"
                                    defaultValue={currentTabSearch.toxicClass}
                                    onChange={(e) => updateCurrentTabSearch({ toxicClass: e.target.value })}
                                  >
                                    <option value="">{t('text.all')}</option>
                                    {toxicClassOptions.map((option) => (
                                      <option key={option.configId} value={option.configId}>{option.configName}</option>
                                    ))}
                                  </select>
                                </div>
                              )}
                              {hasSelectedToxicClass && (
                                <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                                  <label className="w-25">{t('text.ctrl_no')}</label>
                                  <Select options={ctrlNoOptionList || []} isSearchable menuPosition="fixed" placeholder={t('text.all')} className="w-100"
                                    menuPortalTarget={document.body}
                                    onChange={(selectedOption: any) => {
                                      setSelectedCtrlNoOption(selectedOption);
                                    }} value={selectedCtrlNoOption}
                                    noOptionsMessage={() => t('message.search.non_ctrl_no')}
                                  />
                                </div>
                              )}
                              {hasSelectedConcernedClass && (
                                <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                                  <label className="w-25">{t('text.chemical.concerned_class')}</label>
                                  <select
                                    className="form-select form-select-lg"
                                    defaultValue={currentTabSearch.concernedClass}
                                    onChange={(e) => updateCurrentTabSearch({ concernedClass: e.target.value })}
                                  >
                                    <option value="">{t('text.all')}</option>
                                    {concernedClassOptions.map((option) => (
                                      <option key={option.configId} value={option.configId}>{option.configName}</option>
                                    ))}
                                  </select>
                                </div>
                              )}
                              {hasSelectedConcernedClass && (
                                <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                                  <label className="w-25">{t('text.chemical.concerned_ctrl_no')}</label>
                                  <Select options={svhcCtrlNoOptionList || []} isSearchable menuPosition="fixed" placeholder={t('text.all')} className="w-75"
                                    menuPortalTarget={document.body}
                                    onChange={(selectedOption: any) => {
                                      setSelectedSvhcCtrlNoOption(selectedOption);
                                    }} value={selectedSvhcCtrlNoOption}
                                    noOptionsMessage={() => t('message.search.non_ctrl_no')}
                                  />
                                </div>
                              )}
                              {hasSelectedPriorityClass && (
                                <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                                  <label className="w-25">{t('text.chemical.prority_class_title')}</label>
                                  <select
                                    className="form-select form-select-lg"
                                    defaultValue={currentTabSearch.priorityClass}
                                    onChange={(e) => updateCurrentTabSearch({ priorityClass: e.target.value })}
                                  >
                                    <option value="">{t('text.all')}</option>
                                    {priorityClassOptions.map((option) => (
                                      <option key={option.configId} value={option.configId}>{option.configName}</option>
                                    ))}
                                  </select>
                                </div>
                              )}
                              {hasSelectedOrganicClass && (
                                <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                                  <label className="w-25">{t('text.chemical.organic_solvent_class')}</label>
                                  <select
                                    className="form-select form-select-lg"
                                    defaultValue={currentTabSearch.organicClass}
                                    onChange={(e) => updateCurrentTabSearch({ organicClass: e.target.value })}
                                  >
                                    <option value="">{t('text.all')}</option>
                                    {organicClassOptions.map((option) => (
                                      <option key={option.configId} value={option.configId}>{option.configName}</option>
                                    ))}
                                  </select>
                                </div>
                              )}
                              {hasSelectedSpecificChemClass && (
                                <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                                  <label className="w-25">{t('text.chemical.specific_chem_class')}</label>
                                  <select
                                    className="form-select form-select-lg"
                                    defaultValue={currentTabSearch.specificChemClass}
                                    onChange={(e) => updateCurrentTabSearch({ specificChemClass: e.target.value })}
                                  >
                                    <option value="">{t('text.all')}</option>
                                    {specificChemClassOptions.map((option) => (
                                      <option key={option.configId} value={option.configId}>{option.configName}</option>
                                    ))}
                                  </select>
                                </div>
                              )}
                              {hasSelectedEnvMonitorClass && (
                                <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                                  <label className="w-25 me-1">{t('text.chemical.work_env_monitor_class')}</label>
                                  <select
                                    className="form-select form-select-lg"
                                    defaultValue={currentTabSearch.envMonitorClass}
                                    onChange={(e) => updateCurrentTabSearch({ envMonitorClass: e.target.value })}
                                  >
                                    <option value="">{t('text.all')}</option>
                                    {envMonitorClassOptions.map((option) => (
                                      <option key={option.configId} value={option.configId} title={option.configName}>{option.configName}</option>
                                    ))}
                                  </select>
                                </div>
                              )}
                              {hasSelectedEnvMonitorClass && (
                                <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                                  <label className="w-25 me-1">{t('text.chemical.work_env_monitor_freq')}</label>
                                  <select
                                    className="form-select form-select-lg"
                                    defaultValue={currentTabSearch.envMonitorFreq}
                                    onChange={(e) => updateCurrentTabSearch({ envMonitorFreq: e.target.value })}
                                  >
                                    <option value="">{t('text.all')}</option>
                                    {envMonitorFreqOptions.map((option) => (
                                      <option key={option.configId} value={option.configId}>{option.configName}</option>
                                    ))}
                                  </select>
                                </div>
                              )}
                              {hasSelectedPubhazClass && (
                                <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                                  <label className="w-25">{t('text.chemical.public_hazard_classify')}</label>
                                  <select
                                    className="form-select form-select-lg"
                                    defaultValue={currentTabSearch.pubhazClass}
                                    onChange={(e) => updateCurrentTabSearch({ pubhazClass: e.target.value })}
                                  >
                                    <option value="">{t('text.all')}</option>
                                    {pubhazClassOptions.map((option) => (
                                      <option key={option.configId} value={option.configId}>{option.configName}</option>
                                    ))}
                                  </select>
                                </div>
                              )}
                              {/* <div className={`col-xl-6 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                                <label className="w-25">{t('text.chemical.operation_date')}</label>
                                <div className="d-flex align-items-center">
                                  <InputDate
                                    defaultValue={searchRef.current.operStartDate}
                                    isClearable={false}
                                    onChange={handleStartDateChange}
                                    maxDate={searchRef.current.operEndDate ? getFormatDateDash(new Date(searchRef.current.operEndDate)) : undefined}
                                  />
                                  <span className="mx-2">~</span>
                                  <InputDate
                                    defaultValue={searchRef.current.operEndDate}
                                    isClearable={false}
                                    onChange={handleEndDateChange}
                                    minDate={searchRef.current.operStartDate ? getFormatDateDash(new Date(searchRef.current.operStartDate)) : undefined}
                                  />
                                </div>
                              </div> */}
                            </>
                          }
                          <div className={`col-xl-3 ${showAdvConditionMarginTop}`}>
                            <button type="button" className="btn btn-primary mt-1" title={t('button.search.item')} onClick={clickSearch}><i className="fas fa-magnifying-glass"></i> {t('button.search.item')}</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="card pt-3">
                    <div className="row topFunctionRow">
                      <div className="col-sm-12 col-md-6 left">
                        <PageSizeSelector
                          pageSize={pageSize}
                          condition={condition}
                          specialCondition={{
                            tabPagination: {
                              tabId: activeTab,
                              updateTabPageSize: (tabId, newPageSize) => {
                                // 清除當前標籤的暫存資料
                                clearTabCache(tabId);
                              }
                            }
                          }}
                          setCondition={(newCondition) => {
                            setCondition(newCondition);
                          }}
                        />
                      </div>
                      <div className="col-sm-12 col-md-6 right">
                        <button
                          type="button"
                          className="btn btn-green float-end"
                          onClick={exportToExcel}
                        >
                          <i className="fas fa-file-excel me-1"></i>
                          {t('button.export.operation_list')}
                        </button>
                      </div>
                    </div>
                    <div className="card-body">
                      {loading && <Loader />}
                      {showOrgLevelName && <label className="form-label fs-4">
                        {nowOrgName} {showOrgLevelName} {t('text.chemical.item')}
                        {orgLevel !== null && <button type="button" className="btn btn-secondary ms-1" onClick={backPrevQueryUnit}><i className="fas fa-arrow-left me-1"></i>{t('button.back_prev_org_level')}</button>}
                      </label>}
                      <div className="table-responsive mt-1">
                        <table
                          id="data-table-default"
                          className={
                            "table table-hover align-middle dt-responsive nowrap"
                          }
                        >
                          <ChemicalTableHeader
                            columns={selectedColumns}
                            dataList={getTabFilteredList()}
                            setFunction={setInventoryList}
                            sortFields={sortFieldsMap}
                          />
                          <tbody className="text-center fs-5">
                            {!loading && getTabFilteredList() && !isArrayEmpty(getTabFilteredList()) ?
                              getTabFilteredList().map((data, idx) => {
                                return <Row key={'inventory_' + idx} index={idx + 1} data={data}
                                  columns={selectedColumns}
                                  configMap={configMap}
                                  setLoadingBlock={setLoadingBlock}
                                  condition={currentCondition} setCondition={() => setCondition(prev => ({
                                    ...prev,
                                    [activeTab]: currentCondition
                                  }))}
                                  extendMap={extendMap} setExtendMap={setExtendMap}
                                  queryOrgIdList={queryOrgIdList} setQueryOrgIdList={setQueryOrgIdList}
                                  queryOrgNameMap={queryOrgNameMap} setQueryOrgNameMap={setQueryOrgNameMap}
                                  setPopupMode={(mode) => setPopupMode(mode)}
                                  setSelectedChemData={setSelectedChemData}
                                />;
                              }) : (!loading && <NoDataRow />)}
                          </tbody>
                        </table>
                      </div>
                      <Pagination
                        pageInfo={pageInfo}
                        specialCondition={{
                          tabPagination: {
                            tabId: activeTab,
                            updateTabPage: (tabId, page) => {
                              // 清除當前標籤的暫存資料
                              clearTabCache(tabId);
                            }
                          }
                        }}
                        setCondition={(prevCondition) => {
                          setCondition(prevCondition);
                        }}
                        condition={condition}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </BlockUi>
    </StlyedChemicalOperRecordList >
  );
}

const Row = (props: {
  index: number;
  data: ChemicalInventoryReport;
  columns: any[];
  configMap: { [configId: string]: EhsConfigParam };
  setLoadingBlock: (block: boolean) => void;
  condition: ConditionType;
  setCondition: (newCondition: ConditionType) => void;
  extendMap: { [index: number]: boolean };
  setExtendMap: (map: { [index: number]: boolean }) => void;
  queryOrgIdList: string[];
  setQueryOrgIdList: (list: string[]) => void;
  queryOrgNameMap: { [orgId: string]: string };
  setQueryOrgNameMap: (map: { [orgId: string]: string }) => void;
  setPopupMode: (mode: boolean) => void;
  setSelectedChemData: (data: OperRecordDialogData) => void;
}) => {
  const { index, data: inventory, columns, configMap, setLoadingBlock, condition, setCondition, extendMap, setExtendMap, queryOrgIdList, setQueryOrgIdList,
    queryOrgNameMap: queryOrgNameList, setQueryOrgNameMap: setQueryOrgNameList, setPopupMode, setSelectedChemData } = props;
  const { chemical, chemicalCon, phaseState, inventoryQty, orgIds,
    orgNames, nextUnitInventoryList, labInventoryList, chemId, chemConId, substList, substCategoryList,
    chemCtrlNo, casnoList: casNoList, chemNameList, chemCategoryList, detailCategoryList,
    chemGradeHandQty, chemCtrlConcen, chemCtrlConcenType, conLower, conUpper, conType,
    ctrlTotalQty, ctrlTotalQtyType
  } = inventory;
  // const { chemGradeHandQty = null, chemCtrlConcen, chemCtrlConcenType } = chemical || {};
  // const { conLower, conUpper, conType, conTypeValue, ctrlTotalQty = null,
  //   ctrlTotalQtyType, categoryList: chemicalConCategoryList = [] } = chemicalCon || {};

  const { orgLevel } = condition;
  const toxicClassify = chemCategoryList.filter((item) => item.configType === CONFIG_TYPE_TOXIC) || [];
  const concernedClassify = chemCategoryList.filter((item) => item.configType === CONFIG_TYPE_CONCERNED) || [];
  const priorityClassify = chemCategoryList.filter((item) => item.configType === CONFIG_TYPE_PRIORITY) || [];
  const GhsImages = detailCategoryList.filter((item) => item.configType === CONFIG_TYPE_GHS_IMG) || [];
  const publicHazardClassify = detailCategoryList.filter((item) => item.configType === CONFIG_TYPE_PUBLIC_HAZARD) || [];

  // const { loginUser } = useLoginUser();
  const { t, i18n } = useTranslation();
  // const { labText, msgLabDisabledOper, tableTitleLabBelongOrg } = getLabTextObj(loginUser, t);
  const { currentLangNames, enNames } = splitChemNameListByLang(chemNameList, i18n.language);
  const hasSearchUnit = orgLevel !== null;
  const isExtend = extendMap[index];
  const nowOrgName = getSplitUnitByIndex(orgNames, orgLevel || 0);
  const nowOrgId = getSplitUnitByIndex(orgIds, orgLevel || 0);
  const expandIcon = <i className={`fas fa-lg fa-fw me-10px ${isExtend ? 'fa-angle-up' : 'fa-angle-down'}`} onMouseEnter={() => setExtendMap({ ...extendMap, [index]: !isExtend })} onClick={() => setExtendMap({ ...extendMap, [index]: !isExtend })} />;
  const showCtrlTotalQty = ctrlTotalQtyType === 1 ? chemGradeHandQty : ctrlTotalQty;
  const concenTypeConfig = configMap[conType];

  const queryUnitQty = () => {
    const nextOrgLevel = !hasSearchUnit ? 0 : orgLevel + 1;
    setQueryOrgIdList([...queryOrgIdList, nowOrgId]);
    setQueryOrgNameList({ ...queryOrgNameList, [nowOrgId]: nowOrgName });
    setCondition({
      ...condition,
      orgLevel: nextOrgLevel,
      queryOrgId: nowOrgId,
      chemConId: chemConId,
      phaseState: phaseState
    });
  }

  const clickOperReport = () => {

    // 獲取第一個CAS號和名稱
    const firstCasNo = casNoList && !isArrayEmpty(casNoList) ? casNoList[0].casno : "";
    const firstChemName = currentLangNames && !isArrayEmpty(currentLangNames)
      ? currentLangNames[0].chemName
      : "";

    // 設置選中的CAS號和名稱
    setSelectedChemData({
      casNo: firstCasNo,
      chemCtrlNo: chemCtrlNo,
      chemName: firstChemName,
      chemId,
      chemConId,
      phaseState
    });

    // 顯示對話框
    setPopupMode(true);
  }

  // 渲染單元格內容，根據列的key決定顯示內容
  const renderCellContent = (column: any) => {
    // 使用物件映射替代 switch 語句
    const cellContentMap: Record<string, () => React.ReactNode> = {
      index: () => (
        <>
          <label className="my-3 me-2">{index}</label><br />
          {hasSearchUnit && expandIcon}
        </>
      ),
      orgName: () => nowOrgName,
      chemCtrlNo: () => chemCtrlNo,
      casNo: () => <ShowMoreInfo dataList={casNoList} id="chemCasnoId" fieldName="casno" />,
      name: () => notEnglishLang(i18n.language)
        ? <ShowMoreInfo dataList={currentLangNames} id="chemNameId" fieldName="chemName" />
        : <ShowMoreInfo dataList={enNames} id="chemNameId" fieldName="chemName" />,
      ghsImg: () => GhsImages.map((item) => {
        const config = configMap[item.configId];
        return (
          <GhsImage
            key={'ghs_img_' + item.configId}
            src={config.configValue}
            alt={config.configName}
            title={config.configName}
            extraClassName="ghs-img"
          />
        )
      }),
      publicHazard: () => publicHazardClassify.map((item) => {
        const config = configMap[item.configId];
        return (
          <React.Fragment key={'public_hazard_' + item.configId}>
            <label>{config.configName}</label><br />
          </React.Fragment>
        )
      }),
      toxicClass: () => toxicClassify.map((item) => {
        const config = configMap[item.configId];
        return (
          <React.Fragment key={'toxic_' + item.configId}>
            <label>{config.configName}</label><br />
          </React.Fragment>
        )
      }),
      concernedClass: () => concernedClassify.map((item) => {
        const config = configMap[item.configId];
        return (
          <React.Fragment key={'concerned_' + item.configId}>
            <label>{config.configName}</label><br />
          </React.Fragment>
        )
      }),
      priorityClass: () => priorityClassify.map((item) => {
        const config = configMap[item.configId];
        return (
          <React.Fragment key={'priority_' + item.configId}>
            <label>{config.configName}</label><br />
          </React.Fragment>
        )
      }),
      chemClass: () => substCategoryList.map((item) => {
        const categoryConfig = configMap[item.configId];
        return (
          <React.Fragment key={'category_' + item.configId}>
            <span className="d-inline-block mb-1">
              <ChemicalClassificationBadge item={categoryConfig} />
            </span>
          </React.Fragment>
        );
      }),
      concentration: () => (
        <>
          {conType
            ? getShowChemCon(concenTypeConfig?.configIvalue || 0, conLower, conUpper, concenTypeConfig?.configName || "")
            : <span className="text-danger">{t('message.chemical.no_concen')}</span>}
          {configMap[phaseState]?.configName && (
            <>
              <br />
              {configMap[phaseState]?.configName}
            </>
          )}
        </>
      ),
      chemQty: () => showCtrlTotalQty === null
        ? t('text.chemical.no_ctrL_limit')
        : showCtrlTotalQty + ' kg',
      inventoryQty: () => <label>{inventoryQty} kg</label>,
      action: () => (
        <button
          type="button"
          className="btn btn-info mt-1"
          onClick={clickOperReport}
          title={t('text.oper_record')}
        >
          <i className="fa fa-lg fa-file-alt me-1"></i>{t('text.oper_record')}
        </button>
      )
    };

    // 返回對應的內容，如果沒有對應的渲染函數則返回 null
    return cellContentMap[column.key]?.() || null;
  };

  return (
    <tr>
      {columns.map((column, idx) => (
        <td
          key={`cell-${column.key}-${idx}`}
          data-title={column.label}
          className={column.align ? `text-${column.align}` : ''}
        >
          {renderCellContent(column)}
        </td>
      ))}
    </tr>
  );
};

const StlyedChemicalOperRecordList = styled.div<{ $loading?: boolean }>`
  padding-bottom:150px;

  /* 設置容器為 flex，並允許換行 */
  .blocks-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px; /* 區塊之間的間距 */
  }

  /* 每個區塊固定寬度，使得每行最多 4 個區塊 */
  .block {
    flex: 1 1 calc(25% - 10px); /* 每行 4 個區塊，減去間距 */
    box-sizing: border-box;
    border: 1.5px solid #ccc; /* 區塊邊框 */
    padding: 10px; /* 區塊內間距 */
    // background-color: #f9f9f9; /* 區塊背景色 後拿掉如需要可在加回 */
  }

  /* 區塊內部的內容垂直排列 */
  .block-content {
    margin-bottom: 10px; /* 每個內容項之間的間距 */
  }

  /* 移除最後一個內容項的 margin-bottom */
  .block-content:last-child {
    margin-bottom: 0;
  }

  .custom-link {
    color: blue;
    cursor: pointer;
  }

  .custom-link:hover {
      color: #551A8B; 
      // text-decoration: underline;
  }

  .w-10{
    width:10%;
  }

  .w-7{
    width:7%;
  }

  .w-4{
    width:4%;
  }

  .ghs-img{
    width:40px;
  }

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }
  table {
    position:relative;
    min-height:${props => props.$loading ? "300px" : "auto"};
    th {
      text-align: center;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
  
    .ghs-img{
      width:30%;
    }
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 125px;
        text-align:left;
        min-height:100px; // rwd後 td最小高度
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          
    padding-bottom: 10px;
    min-height: auto; /* 重置最小高度 */
    height: auto; /* 重置高度 */
          // white-space: nowrap; /* 因標題過長 目前不需要不換行 */
    white-space: normal; /* 讓長標題能夠換行 */
    word-wrap: break-word; /* 在需要時強制斷詞 */
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
`;

export default ChemicalOperRecordList;
