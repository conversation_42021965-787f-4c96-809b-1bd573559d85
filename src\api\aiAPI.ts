import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

// AI聊天相關的型別定義
export interface AiChatRequest {
    message: string;
    userId: string;
    sessionId: string;
    context?: Record<string, any>;
}

export interface AiChatResponse {
    response: string;
    sessionId: string;
    success: boolean;
    errorMessage?: string;
    metadata?: Record<string, any>;
}

// 使用次數資料的型別定義
export interface UsageData {
    usedCount: number;
    maxCount: number;
    isLimitReached: boolean;
}

// API 回應的型別定義
export interface UsageCheckResponse {
    success: boolean;
    results?: UsageData;
    resultFailMsg?: string;
}

export interface UsageIncrementResponse {
    success: boolean;
    results?: {
        usedCount: number;
        isLimitReached: boolean;
    };
    resultFailMsg?: string;
}

export interface UsageResetResponse {
    success: boolean;
    message: string;
    errorMessage?: string;
}

export const AiAPI = {
    /**
     * 發送AI聊天請求（同步）
     * @param params 包含BaseParams和聊天內容的參數
     * @returns Promise<AiChatResponse>
     */
    sendChatMessage: async (
        params: BaseParams & {
            message: string;
            sessionId: string;
            context?: Record<string, any>;
        }
    ) => {
        const requestData = {
            message: params.message,
            userId: params.loginUserId,
            sessionId: params.sessionId,
            context: params.context
        };

        return apiRequest(getApiAddress + "ai/chat", createLoginPostJsonConfig(requestData))
            .then((res) => handleFetchResponse(res));
    },

    /**
     * 發送AI聊天請求（異步）
     * @param params 包含BaseParams和聊天內容的參數
     * @returns Promise<AiChatResponse>
     */
    sendChatMessageAsync: async (
        params: BaseParams & {
            message: string;
            sessionId: string;
            context?: Record<string, any>;
        }
    ) => {
        const requestData = {
            message: params.message,
            userId: params.loginUserId,
            sessionId: params.sessionId,
            context: params.context
        };

        return apiRequest(getApiAddress + "ai/chat/async", createLoginPostJsonConfig(requestData))
            .then((res) => handleFetchResponse(res));
    },

    /**
     * 檢查AI聊天使用次數
     * @param params 包含BaseParams的參數
     * @returns Promise<UsageCheckResponse>
     */
    checkUsage: async (params: BaseParams): Promise<UsageCheckResponse> => {
        const requestData = {
            userId: params.loginUserId
        };

        return apiRequest(getApiAddress + "ai/chat/usage-check", createLoginPostJsonConfig(requestData))
            .then((res) => handleFetchResponse(res));
    },

    /**
     * 增加AI聊天使用次數
     * @param params 包含BaseParams的參數
     * @returns Promise<UsageIncrementResponse>
     */
    incrementUsage: async (params: BaseParams): Promise<UsageIncrementResponse> => {
        const requestData = {
            userId: params.loginUserId
        };

        return apiRequest(getApiAddress + "ai/chat/usage-increment", createLoginPostJsonConfig(requestData))
            .then((res) => handleFetchResponse(res));
    },

    /**
     * 重置AI聊天使用次數（管理員功能）
     * @param params 包含BaseParams的參數
     * @returns Promise<UsageResetResponse>
     */
    resetUsage: async (params: BaseParams): Promise<UsageResetResponse> => {
        const requestData = {
            userId: params.loginUserId
        };

        return apiRequest(getApiAddress + "ai/chat/usage-reset", createLoginPostJsonConfig(requestData))
            .then((res) => handleFetchResponse(res));
    }
};

/**
 * 生成會話ID
 * @returns string
 */
export const generateSessionId = (): string => {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};