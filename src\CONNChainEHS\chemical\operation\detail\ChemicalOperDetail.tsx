import BlockUi from "@availity/block-ui";
import { ChemicalAPI } from "api/chemicalAPI";
import { ConfigAPI } from "api/configAPI";
import { LabAPI } from "api/labAPI";
import { AppPaths } from "config/app-paths";
import BlockuiMsg from "ehs/common/BlockuiMsg";
import BackButton from "ehs/common/button/BackButton";
import ChemicalClassificationBadge from "ehs/common/chemical/ChemicalClassificationBadge";
import Dialog from "ehs/common/Dialog";
import ShowMoreInfo from "ehs/common/ShowMoreInfo";
import { errorMsg } from "ehs/common/SwalMsg";
import { showSuccessToast, showWarnToast } from "ehs/common/Toast";
import { CONFIG_TYPE_CCB_PROCESS_TEMP, CONFIG_TYPE_CHEM, CONFIG_TYPE_CHEM_STORAGE_LOCATION, CONFIG_TYPE_GHS_IMG, CONFIG_TYPE_MIX_CONCEN_TYPE, CONFIG_TYPE_OPER_ITEM, CONFIG_TYPE_OPER_TYPE, CONFIG_TYPE_PUBLIC_HAZARD, CONFIG_TYPE_STATE, CONFIG_TYPE_TOXIC, FILE_TYPE_PURCHASE_DETAIL_SDS, LAB_STATUS_CAN_OPERATION } from "ehs/constant/constants";
import { isOperItemDetailMode, OperDetailPageMode } from "ehs/enums/OperDetailPageMode";
import useLoginUser from "ehs/hooks/useLoginUser";
import useServerNowDate from "ehs/hooks/useServerNowDate";
import Breadcrumbs from "ehs/layout/Breadcrumbs";
import Footer from "ehs/layout/Footer";
import { EhsChemical, initEhsChemical } from "ehs/models/EhsChemical";
import { EhsChemicalCategory } from "ehs/models/EhsChemicalCategory";
import { initEhsChemicalCon } from "ehs/models/EhsChemicalCon";
import { EhsChemicalInventory } from "ehs/models/EhsChemicalInventory";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsLab, initEhsLab } from "ehs/models/EhsLab";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { checkTimeoutAction, getBasicLoginUserInfo, navigateToHome } from "ehs/utils/authUtil";
import { getStopOperDate } from "ehs/utils/chemicalUtil";
import { findItemByLangType, getLabTextObj, notEnglishLang, splitChemNameListByLang } from "ehs/utils/langUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import React, { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, } from "react-router-dom";
import styled from "styled-components";
import InventoryHistory from "./InventoryHistory";
import InventoryItem from "./InventoryItem";
import { InventoryStatus } from "ehs/enums/InventoryStatus";
import ChemLabPlaceModify from "./ChemLabPlaceModify";
import { EhsFile } from "ehs/models/EhsFile";
import { FileAPI } from "api/fileAPI";

interface OperDetailState {
  inventoryId: string;
  labId: string;
  chemId: string;
  chemConId: string;
  phaseState: string;
  operDetailPageMode: OperDetailPageMode;
}

function ChemicalOperDetail() {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const { labInformation, labLocation, labPhoneText, labPhoneExtText } = getLabTextObj(loginUser, t);
  const [nowDate] = useServerNowDate(loginUser);
  const { state } = useLocation();
  const { inventoryId, labId, chemId, chemConId, phaseState, operDetailPageMode }: OperDetailState = state || {};
  const isOperItemPage = isOperItemDetailMode(operDetailPageMode);
  const defaultOpenIndex = isOperItemPage ? 0 : null;
  const [loadingBlock, setLoadingBlock] = useState(false);
  const [showInventoryHistory, setShowInventoryHistory] = useState<boolean | null>(null);
  const [showChangeWorkPlace, setShowChangeWorkPlace] = useState<boolean | null>(null);
  const [totalQty, setTotalQty] = useState(0);
  const [totalWeight, setTotalWeight] = useState(0);
  const [stopMonthNum, setStopMonthNum] = useState(0);
  const [stopDay, setStopDay] = useState(0);
  const [stopOperDate, setStopOperDate] = useState<Date>(nowDate);
  const [openIndex, setOpenIndex] = useState<number | null>(defaultOpenIndex);
  const [labData, setLabData] = useState<EhsLab>(initEhsLab);
  const [chemicalData, setChemicalData] = useState<EhsChemical>(initEhsChemical);
  const [inventoryList, setInventoryList] = useState<EhsChemicalInventory[]>([]);
  const [historyList, setHistoryList] = useState<EhsChemicalInventory[]>([]);
  const [sdsFileMap, setSdsFileMap] = useState<{ [fileMappingId: string]: EhsFile }>({});
  const [operItemConfigs, setOperItemConfigs] = useState<EhsConfigParam[]>([]);
  const [storageLocationConfigs, setStorageLocationConfigs] = useState<EhsConfigParam[]>([]);
  const [configMap, setConfigMap] = useState<{ [configId: string]: EhsConfigParam }>({});// config對應
  const [batchCheckedMap, setBatchCheckedMap] = useState<{ [inventoryId: string]: EhsChemicalInventory }>({});// 批次編號對應
  const [chemStopDate, setChemStopDate] = useState<{ [inventoryId: string]: Date | null }>({});
  const { labNameList, buildName, labFloor, labHousenum, labPhone, labPhoneExt, labStatus } = labData || {};
  const { casnoList, nameList, categoryList, conList } = chemicalData;
  const hasEnabledInventory = useMemo(() =>
    inventoryList.some(item => item.inventoryStatus === InventoryStatus.ENABLE),
    [inventoryList]);
  const chemCategoryMap: Map<string, EhsChemicalCategory[]> = new Map([
    [CONFIG_TYPE_CHEM, []],
    [CONFIG_TYPE_TOXIC, []],
  ]);
  categoryList.forEach((category: EhsChemicalCategory) => {
    const chemCategoryArray = chemCategoryMap.get(category.configType);
    if (chemCategoryArray) {
      chemCategoryArray.push(category);
    }
  });
  const chemClassify = chemCategoryMap.get(CONFIG_TYPE_CHEM) || [];
  const toxicClassify = chemCategoryMap.get(CONFIG_TYPE_TOXIC) || [];
  const concentrationRange = conList?.find((item) => item.chemConId === chemConId) || initEhsChemicalCon;
  const { currentLangNames, enNames } = splitChemNameListByLang(nameList, i18n.language);
  const isNotEnglishLang = notEnglishLang(i18n.language);
  const noInfo = !(labId && chemId && chemConId && phaseState);
  const isLabCanOperation = LAB_STATUS_CAN_OPERATION.includes(labStatus);

  // 新增一個變數來存儲共用的 disabled 條件
  const isOperationDisabled = !hasEnabledInventory || !isLabCanOperation;

  useEffect(() => {
    if (loginUser) {
      if (noInfo) {
        navigateToHome(navigate);
        return;
      }
      fetchTotalInventoryData();
      fetchOperStopMonth();
      fetchLabData();
      fetchChemicalData();
      fetchConfigData();
      fetchInventoryData();
    }
  }, [loginUser]);

  useEffect(() => {
    if (loginUser && (!isArrayEmpty(inventoryList) || !isArrayEmpty(historyList))) {
      fetchFiles();
    }
  }, [loginUser, inventoryList, historyList])

  useEffect(() => {
    if (stopMonthNum && stopDay) {
      setStopOperDate(getStopOperDate(nowDate, stopMonthNum, stopDay));
    }
  }, [stopMonthNum, stopDay])

  const fetchLabData = () => {
    LabAPI.getLabDetail({
      ...getBasicLoginUserInfo(loginUser),
      labId: labId,
    }).then((rs) => {
      if (isApiCallSuccess(rs)) {
        setLabData(rs.results.labBean);
      } else {
        errorMsg(rs.message);
      }
    })
  };

  const fetchChemicalData = () => {
    ChemicalAPI.getChemicalDetail({
      ...getBasicLoginUserInfo(loginUser),
      chemId: chemId,
    }).then((rs) => {
      if (isApiCallSuccess(rs)) {
        setChemicalData(rs.results);
      } else {
        errorMsg(rs.message);
      }
    })
  };

  const fetchOperStopMonth = () => {
    ChemicalAPI.getChemicalStopOperByChemConId({
      ...getBasicLoginUserInfo(loginUser),
      chemConId,
    }).then((rs) => {
      if (isApiCallSuccess(rs)) {
        const { stopMonth, day } = rs.results;
        setStopMonthNum(stopMonth);
        setStopDay(day);
      } else {
        errorMsg(rs.message);
      }
    })
  }

  const fetchTotalInventoryData = () => {
    ChemicalAPI.getChemicalInventoryTotalByLab({
      ...getBasicLoginUserInfo(loginUser),
      labId,
      chemConId,
      phaseState,
      inventoryId,
    }).then((rs) => {
      if (isApiCallSuccess(rs)) {
        const { totalQty, totalWeight } = rs.results;
        setTotalQty(totalQty);
        setTotalWeight(totalWeight);
      } else {
        errorMsg(rs.message);
      }
    })
  };

  const fetchInventoryData = () => {
    ChemicalAPI.getChemicalInventoryListByLab({
      ...getBasicLoginUserInfo(loginUser),
      labId,
      chemConId,
      phaseState,
      inventoryId,
    }).then((rs) => {
      if (isApiCallSuccess(rs)) {
        const results: EhsChemicalInventory[] = rs.results;
        const newInventoryList: EhsChemicalInventory[] = [];
        const newHistoryList: EhsChemicalInventory[] = [];
        results.forEach(item => {
          if (item.inventoryQty === 0) {
            newHistoryList.push(item);
          } else {
            newInventoryList.push(item);
          }
        });

        setInventoryList(newInventoryList);
        setHistoryList(newHistoryList);
      } else {
        errorMsg(rs.message);
      }
    }).catch(error => {
      checkTimeoutAction(error, navigate, t);
    })
  };

  const fetchConfigData = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser),
      configTypeList: [CONFIG_TYPE_STATE, CONFIG_TYPE_OPER_ITEM, CONFIG_TYPE_OPER_TYPE, CONFIG_TYPE_CHEM_STORAGE_LOCATION, CONFIG_TYPE_GHS_IMG,
        CONFIG_TYPE_PUBLIC_HAZARD, CONFIG_TYPE_CHEM, CONFIG_TYPE_MIX_CONCEN_TYPE, CONFIG_TYPE_CCB_PROCESS_TEMP],
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const configMap: { [configId: string]: EhsConfigParam } = {};
        const operItemConfigs: EhsConfigParam[] = [];
        const storageLocationConfigs: EhsConfigParam[] = [];

        result.results.forEach((config: EhsConfigParam) => {
          // 設置 configMap，處理 OPER_TYPE 特殊情況
          const key = config.configType === CONFIG_TYPE_OPER_TYPE ? config.configIvalue : config.configId;
          configMap[key] = config;

          // 同時處理 operItemConfigs 和 storageLocationConfigs
          if (config.configType === CONFIG_TYPE_OPER_ITEM) {
            operItemConfigs.push(config);
          } else if (config.configType === CONFIG_TYPE_CHEM_STORAGE_LOCATION) {
            storageLocationConfigs.push(config);
          }
        });
        setConfigMap(configMap);
        setOperItemConfigs(operItemConfigs);
        setStorageLocationConfigs(storageLocationConfigs);
      } else {
        errorMsg(result.message);
      }
    })
  };

  const fetchFiles = () => {
    // 同時從 inventoryList 和 historyList 收集檔案 ID
    const fileIdList = Array.from(new Set([
      ...inventoryList.map(item => item.sdsFileId),
      ...historyList.map(item => item.sdsFileId)
    ].filter(Boolean)));

    if (isArrayEmpty(fileIdList)) {
      return;
    }

    FileAPI.getFileList({
      ...getBasicLoginUserInfo(loginUser),
      fileTypeList: [FILE_TYPE_PURCHASE_DETAIL_SDS],
      fileIdList: fileIdList,
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        // 直接構建 sdsFileMap
        const newSdsFileMap = result.results
          .filter((file: EhsFile) => file.fileType === FILE_TYPE_PURCHASE_DETAIL_SDS)
          .reduce((acc: { [key: string]: EhsFile }, file: EhsFile) => {
            acc[file.fileId] = file;
            return acc;
          }, {});

        setSdsFileMap(newSdsFileMap);
      } else {
        errorMsg(result.message);
      }
    });
  };

  // 全選/反選處理函數 
  const handleCheckAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      // 選中所有啟用狀態的項目
      const enabledInventories = inventoryList.filter(item => item.inventoryStatus === InventoryStatus.ENABLE);
      const newCheckedMap: { [inventoryId: string]: EhsChemicalInventory } = {};
      enabledInventories.forEach(item => {
        newCheckedMap[item.inventoryId] = item;
      });
      setBatchCheckedMap(newCheckedMap);
    } else {
      // 取消所有選中
      setBatchCheckedMap({});
    }
  };

  /**
   * 顯示勾選批次運作的化學品 訊息
   */
  const showNoBatchMsg = () => {
    showWarnToast(t("message.chemical.select_oper_item_batch"));
  }

  /**
   * 獲取已勾選的庫存ID列表
   */
  const getSelectedInventoryIds = (): string[] => {
    return Object.keys(batchCheckedMap);
  };

  /**
   * 檢查是否有勾選項目
   */
  const checkHasSelectedItems = () => {
    const inventoryIds = getSelectedInventoryIds();
    if (isArrayEmpty(inventoryIds)) {
      showNoBatchMsg();
      return false;
    }
    return true;
  }

  /**
   * 批次盤點
   */
  const handleBatchInventory = () => {
    if (!checkHasSelectedItems()) {
      return;
    }

    setLoadingBlock(true);
    const inventoryIds = getSelectedInventoryIds();
    ChemicalAPI.addChemicalOperRecordNoChange({
      ...getBasicLoginUserInfo(loginUser),
      inventoryIdList: inventoryIds,
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
        fetchInventoryData();
      } else {
        errorMsg(result.message);
      }
      setLoadingBlock(false);
    }).catch((error) => {
      setLoadingBlock(false);
      console.log("🚀 ~ handleBatchInventory ~ error:", error)
    })
  };

  /**
   * 批次廢棄
   */
  const handleBatchDisposal = () => {
    if (!checkHasSelectedItems()) {
      return;
    }

    setLoadingBlock(true);
    const inventoryIds = getSelectedInventoryIds();
    ChemicalAPI.addChemicalOperRecordDisposal({
      ...getBasicLoginUserInfo(loginUser),
      inventoryIdList: inventoryIds,
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
        fetchInventoryData();
        fetchTotalInventoryData();
      } else {
        errorMsg(result.message);
      }
      setLoadingBlock(false);
    }).catch((error) => {
      setLoadingBlock(false);
      console.log("🚀 ~ handleBatchDisposal ~ error:", error)
    })
  };

  /**
   * 批次調撥
   */
  const handleBatchTransfer = () => {
    if (!checkHasSelectedItems()) {
      return;
    }

    setShowChangeWorkPlace(true);
  };

  return (
    <StlyedChemicalOperDetail>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {
            <Dialog
              content={<InventoryHistory onClose={() => setShowInventoryHistory(null)}
                historyList={historyList} setHistoryList={setHistoryList}
                nowDate={nowDate} stopOperDate={stopOperDate}
                storageLocationConfigs={storageLocationConfigs} configMap={configMap} operItemConfigs={operItemConfigs}
                sdsFileMap={sdsFileMap} />}
              show={showInventoryHistory !== null}
            />
          }
          {
            <Dialog
              content={<ChemLabPlaceModify
                onClose={() => setShowChangeWorkPlace(null)}
                onActionSuccess={() => {
                  showSuccessToast(t('message.success'));
                  setShowChangeWorkPlace(null);
                  fetchInventoryData();
                }}
                setLoadingBlock={setLoadingBlock}
                inventoryIdList={!isArrayEmpty(getSelectedInventoryIds()) ? getSelectedInventoryIds() : [inventoryId]}
                currentLabId={labId}
                isShow={showChangeWorkPlace !== null}
              />}
              show={showChangeWorkPlace !== null}
            />
          }
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.chemical.manage") },
                { label: t("func.chemical.operation"), path: AppPaths.chemical.operationList },
                { label: t("func.chemical.operation_action") },
              ]}
            />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{t("func.chemical.operation_action")} </h1>
            {/* END page-header */}
            <BackButton />
            {/* <!-- 化學品資訊 --> */}
            <div className="card card-body mt-3">
              <div className="row justify-content-around mb-1">
                <h4>{labInformation}</h4>
                <div className="col-xl-10 fs-4 offeset-xl-2 mt-1">
                  <label className="h2">{findItemByLangType(labNameList, i18n.language)?.langValue}</label>
                  <div className="row">
                    <div className="col-12 col-md-4">
                      <ul>
                        <li>{labLocation}：{buildName}{labFloor}{labHousenum}</li>
                      </ul>
                    </div>
                    <div className="col-12 col-md-4">
                      <ul>
                        <li>{labPhoneText}：{labPhone}</li>
                      </ul>
                    </div>
                    <div className="col-12 col-md-4">
                      <ul>
                        <li>{labPhoneExtText}：{labPhoneExt}</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              <hr />
              <div className="row justify-content-around mt-1">
                <h4>{t('text.chemical.information')}</h4>
                <div className="col-xl-4 text-center align-self-center">
                  <h4 className="text-primary">{<ShowMoreInfo dataList={casnoList} id="chemCasnoId" fieldName="casno" />}</h4>
                  <h2>{isNotEnglishLang ?
                    <ShowMoreInfo dataList={currentLangNames} id="chemNameId" fieldName="chemName" /> :
                    <ShowMoreInfo dataList={enNames} id="chemNameId" fieldName="chemName" />}</h2>
                  {isNotEnglishLang && <div className="fs-4">{<ShowMoreInfo dataList={enNames} id="chemNameId" fieldName="chemName" />}</div>}
                </div>
                <div className="col-xl-3 text-center align-self-center">
                  <div className="border p-2">
                    <i className="fas fa-flask text-primary fa-lg"></i>
                    <div className="fs-5">{t('text.inventory_sum')}</div>
                    <h3 className="mt-1">{t('text.chemical.inventory_total_title', { qty: totalQty, weight: totalWeight })}</h3>
                  </div>
                </div>
                <div className="col-xl-4 fs-4">
                  <ul>
                    <li>{t("text.chemical.class")}：
                      {chemClassify.map((item) => (
                        <React.Fragment key={'category_' + item.configId}>
                          <span className="d-inline-block mb-1">
                            <ChemicalClassificationBadge item={item} />
                          </span>
                        </React.Fragment>
                      ))}</li>
                    {!isArrayEmpty(toxicClassify) && <li>{t("text.chemical.toxic_class")}：
                      {toxicClassify.map((item) => (<span className="d-inline-block mb-1 me-3" key={'toxic_' + item.configId}>{item.configName}</span>))}
                    </li>}
                    <li>{t('text.chemical.appr_oper_concen')}：{concentrationRange.concentrationShow}</li>
                    <li>{t('text.chemical.phase_state')}：{configMap[phaseState]?.configName}</li>
                  </ul>
                </div>
              </div>
            </div>
            {/* <!-- 運作化學品 -->  */}
            <div className="card card-body mt-3">
              <div className="row">
                <h4>{t("func.chemical.operation_action")}</h4><div className="row">
                  {!isArrayEmpty(historyList) && <div className="col-xl-2 text-start">
                    <button type="button" className="btn me-1 fs-5 btn-secondary" onClick={() => setShowInventoryHistory(true)}>
                      <i className="fas fa-flask fa-lg me-1" />{t('button.historical_inventory')}</button>
                  </div>}
                  <div className="col-xl-5 ms-auto text-end">
                    <div className="form-check form-check-inline">
                      <input className="form-check-input" type="checkbox" id="checkAllChemical"
                        checked={getSelectedInventoryIds().length === inventoryList.length}
                        disabled={isOperationDisabled}
                        onChange={handleCheckAll} />
                      <label className="form-check-label" htmlFor="checkAllChemical">{t('text.batch_check_all')}</label>
                    </div>
                    <button type="button" className="btn me-1 fs-5 btn-info" onClick={handleBatchInventory}
                      disabled={isOperationDisabled}>
                      <i className="fas fa-check me-1" />
                      {t('button.batch_inventory')}
                    </button>
                    <button type="button" className="btn me-1 fs-5 btn-primary"
                      onClick={handleBatchTransfer}
                      disabled={isOperationDisabled}>
                      <i className="fas fa-exchange-alt me-1" />
                      {t('button.batch_transfer')}
                    </button>
                  </div>
                </div>

                <div className="accordion mt-3" id="accordion">
                  {inventoryList.map((item, idx) => {
                    const { inventoryId, sdsFileId } = item;
                    return (<InventoryItem key={inventoryId + '_' + idx} inventory={item} phaseState={phaseState}
                      isLabCanOperation={isLabCanOperation}
                      nowDate={nowDate} defStopOperDate={stopOperDate} sdsFile={sdsFileMap[sdsFileId]}
                      setSdsFile={(sdsFile: EhsFile | undefined) => {
                        if (sdsFile) {  // 加入空值檢查
                          setSdsFileMap(prev => ({
                            ...prev,
                            [sdsFileId]: sdsFile
                          }));
                        }
                      }}
                      historyList={historyList} setHistoryList={setHistoryList}
                      inventoryList={inventoryList} setInventoryList={setInventoryList}
                      openIndex={openIndex} setOpenIndex={setOpenIndex} idx={idx} operItemConfigs={operItemConfigs} configMap={configMap}
                      batchCheckedMap={batchCheckedMap} setBatchCheckedMap={setBatchCheckedMap} storageLocationConfigs={storageLocationConfigs}
                      chemStopDate={chemStopDate}
                      setChemStopDate={setChemStopDate}
                      setLoadingBlock={setLoadingBlock} fetchTotalInventoryData={fetchTotalInventoryData} />);
                  })}
                </div>
              </div>
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </BlockUi>
    </StlyedChemicalOperDetail >
  );
}


const StlyedChemicalOperDetail = styled.div`
  padding-bottom:150px;

  .accordion-button:hover  {
    outline: none; /* 取消默認的焦點樣式 */
    box-shadow: 0 0 0 4px rgba(47, 142, 203, 0.25) !important; /* 添加陰影 */
  }
    
  .info-table,
  .info-table thead tr,
  .info-table thead tr th {
      // border-color: orange;
  }

  .form-check-label{
    user-select: none;
  }
  
  .ghs-img{
    width:40px;
  }

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }
  table {
    position:relative;
    min-height:auto;
    th {
      text-align: center;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
  
    .ghs-img{
      width:30%;
    }
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
      .rc-table tbody tr:hover {
        background-color: #87CEEB; /* 淡藍色 */
      }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 125px;
        text-align:left;
        min-height:100px; // rwd後 td最小高度
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
  white-space: pre-line;
          
    padding-bottom: 10px;
    min-height: auto; /* 重置最小高度 */
    height: auto; /* 重置高度 */
          // white-space: nowrap; /* 因標題過長 目前不需要不換行 */
    // white-space: normal; /* 讓長標題能夠換行 */
    word-wrap: break-word; /* 在需要時強制斷詞 */
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
`;

export default ChemicalOperDetail;
