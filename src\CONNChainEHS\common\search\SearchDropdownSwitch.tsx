import useLoginUser from "ehs/hooks/useLoginUser";
import { getLabTextObj } from "ehs/utils/langUtil";
import { useTranslation } from "react-i18next";

interface SearchDropdownSwitchProps {
    checked: boolean,
    onChangeChecked: (changeChecked: boolean) => void;
    onChangeCondition: () => void;
}

const SearchDropdownSwitch = ({ checked, onChangeChecked, onChangeCondition }: SearchDropdownSwitchProps) => {

    const { t } = useTranslation();
    const { loginUser } = useLoginUser();
    const { seacrchMultiLabTitle, seacrchMultiLabDesc } = getLabTextObj(loginUser, t);

    const handleSwitchChange = (e: any) => {
        onChangeChecked(e.target.checked);
        onChangeCondition();
    };

    const desc = checked ? t('text.search_link_desc') : seacrchMultiLabDesc;
    const labelText = checked ? t('text.search_link_title') : seacrchMultiLabTitle;

    return (
        <div className="form-check form-switch ps-0 mb-3 d-flex">
            <label className="mx-1 mb-1" htmlFor="searchdropdownswitch" title={desc}>{labelText}</label><br />
            <input
                id="searchdropdownswitch"
                className="form-check-input ms-1"
                type="checkbox"
                onChange={handleSwitchChange}
                checked={checked}
                title={desc}
            />
            <br />
        </div>
    );
};

export default SearchDropdownSwitch;
