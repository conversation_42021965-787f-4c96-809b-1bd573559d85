import { ChangeEvent, KeyboardEvent, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface Props {
    className?: string;
    value?: string;
    placeholder?: string;
    disabled?: boolean;
    onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
    onError?: (error: string) => void;
    onKeyDown?: (event: KeyboardEvent<HTMLInputElement>) => void;
    [key: string]: any; // 支援任意 data-* 屬性
}

function InputEmail({
    className = "form-control",
    value = "", 
    placeholder = "", 
    disabled = false, 
    onChange = (event: ChangeEvent<HTMLInputElement>) => { },
    onError = (error: string) => { },
    onKeyDown,
    ...restProps
}: Props) {
    const { t } = useTranslation();
    const [email, setEmail] = useState<string>('');
    const [errorMessage, setErrorMessage] = useState<string>('');
    const handleChange = (e: ChangeEvent<HTMLInputElement>): void => {
        const val = e.target.value;
        setEmail(val);
        setErrorMessage(val && !e.target.validity.valid ? t('message.format.email') : '');
        if (onChange) {
            onChange(e);
        }
    };

    useEffect(() => {
        setEmail(value || "");
        if (!value && errorMessage) {
            setErrorMessage('');
        }
    }, [value, errorMessage])

    useEffect(() => {
        onError(errorMessage);  // 回傳錯誤訊息給父組件
    }, [errorMessage, onError]);

    return (
        <div>
            <input
                type="email"
                className={`${className} ${errorMessage ? 'is-invalid' : ''}`}
                value={email}
                placeholder={placeholder}
                disabled={disabled}
                onChange={handleChange}
                onKeyDown={onKeyDown}
                {...restProps}
            />
            {errorMessage && <p className='text-danger my-1'>{errorMessage}</p>}
        </div>
    );
}

export default InputEmail;