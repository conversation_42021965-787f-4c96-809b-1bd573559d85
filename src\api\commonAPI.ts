import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const CommonAPI = {
  getMultiDetailList: async (
    parms: BaseParams & {
      linkIdList: string[];
      linkTypeList: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "common/multipledetail/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};
