import { useTranslation } from "react-i18next";
import React from 'react'; 
import { SearchCondition } from "ehs/enums/SearchCondition";

const SearchConditionButton = (props: { buttonType: SearchCondition, onClick?: () => void }) => {
    const { t } = useTranslation();
    const { buttonType, onClick } = props;

    let buttonText = '';
    let iconClass = '';
    switch (buttonType) {
        case SearchCondition.UNIT:
            buttonText = t('button.search.unit');
            iconClass = 'fas fa-th-large';
            break;
        case SearchCondition.BUILDING:
            buttonText = t('button.search.building');
            iconClass = 'fas fa-building';
            break;
        case SearchCondition.ADVANCEd:
            buttonText = t('button.search.advanced');
            iconClass = 'fas fa-star';
            break;
        default:
            buttonText = '';
    }

    return (
        <button type="button" className={`btn btn-gray me-3`} data-bs-toggle="collapse" title={buttonText} onClick={onClick}>
            <i className={`me-1 ${iconClass}`} />
            {buttonText}
        </button>
    );
};

export default SearchConditionButton;
