import BlockUi from '@availity/block-ui';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { OrgAPI } from '../../../../api/orgAPI';
import { SelectListAPI } from '../../../../api/selectListAPI';
import { UserAPI } from '../../../../api/userAPI';
import BlockuiMsg from '../../../common/BlockuiMsg';
import Dialog from '../../../common/Dialog';
import Loader from '../../../common/Loader';
import SortIcon from '../../../common/SortIcon';
import { showSuccessToast } from '../../../common/Toast';
import { CONFIG_TYPE_UNIT, ORG_SPLIT_FLAG } from '../../../constant/constants';
import { ActionMode } from '../../../enums/ActionMode';
import { BtnType } from '../../../enums/BtnType';
import useLoginUser from '../../../hooks/useLoginUser';
import Breadcrumbs from '../../../layout/Breadcrumbs';
import Footer from '../../../layout/Footer';
import PageSizeSelector from '../../../layout/PageSizeSelector';
import Pagination from '../../../layout/Pagination';
import { EhsOrg, initEhsOrg } from '../../../models/EhsOrg';
import { EhsOrgLevel } from '../../../models/EhsOrgLevel';
import { EhsUser } from '../../../models/EhsUser';
import { PageInfo, initPageInfo } from '../../../models/PageInfo';
import { SelectItem } from '../../../models/SelectItem';
import { isArrayEmpty } from '../../../utils/arrayUtil';
import { checkBtnAuth, checkTimeoutAction, getBasicLoginUserInfo } from '../../../utils/authUtil';
import { isApiCallSuccess } from '../../../utils/resultUtil';
import OrgDetail from '../OrgDetail';
import OrgModify from '../OrgModify';
import OrgRow from './OrgRow';
import { useNavigate } from 'react-router-dom';
import ExpandableRwdTable from 'ehs/common/ExpandableRwdTable';

interface Condition {
  keyword: string;
  currentPage: number;
  pageSize: number;
}

const initCondition = {
  keyword: "",
  currentPage: 1,
  pageSize: 50,
}

function OrgList() {
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const navigate = useNavigate();
  const [areaOptionList, setAreaOptionList] = useState<Array<SelectItem>>([]);
  const [orgTypeOptionList, setOrgTypeOptionList] = useState<Array<SelectItem>>([]);
  const [orgLevelList, setOrgLevelList] = useState<Array<EhsOrgLevel>>([]);
  const [orgList, setOrgList] = useState<Array<EhsOrg>>([]);
  const [selectedTab, setSelectedTab] = useState("");
  const [selectedTabIdx, setSelectedTabIdx] = useState(1);
  const [filteredOrgItems, setFilteredOrgItems] = useState<Record<string, EhsOrg[]>>({});
  const [selectOrgItems, setSelectOrgItems] = useState<Record<string, EhsOrg[]>>({});
  const [selectParentOrgIds, setSelectParentOrgIds] = useState<Record<string, string>>({});
  const [orgManager, setOrgManager] = useState<Record<string, EhsUser[]>>({});
  const [localSearchResult, setLocalSearchResult] = useState<Array<EhsOrg>>([]);
  const [localSearchKey, setLocalSearchKey] = useState("");
  const [loading, setLoading] = useState(false);
  const [loadingBlock, setLoadingBlock] = useState(false);
  const [popupMode, setPopupMode] = useState<ActionMode | null>(null);
  const [popupDetail, setPopupDetail] = useState<boolean>(false);
  const [modifyData, setModifyData] = useState<EhsOrg>(initEhsOrg);
  const [condition, setCondition] = useState<Record<string, Condition>>({})
  const [pageInfo, setPageInfo] = useState<Record<string, PageInfo>>({});
  const isAddOrgRole = checkBtnAuth(BtnType.ADD_ORG);

  useEffect(() => {
    if (loginUser) {
      Promise.all([
        fetchOrgLevelData(),
        fetchOrgManagerData(),
        fetchOrgData(),
        fetchAreaOption(),
        fetchOrgTypeOption()
      ]).then(() => {
        // Handle any post-fetch logic here
      });
    }
  }, [loginUser, i18n.language])

  useEffect(() => {
    if (!isArrayEmpty(Object.keys(condition))) {
      const newPageInfo = pageInfo[selectedTab];
      const newCondition = condition[selectedTab];
      updatePageInfo(selectedTab, newPageInfo, newCondition);
    }
  }, [condition]);

  useEffect(() => {
    if (!isArrayEmpty(Object.keys(pageInfo))) {
      setFilteredOrgItems(prevState => ({ ...prevState, [selectedTab]: pageOrgList }));
    }
  }, [pageInfo]);

  useEffect(() => {
    if (!isArrayEmpty(orgLevelList)) {
      initializePageInfoAndConditions();
    }
  }, [orgLevelList])

  //篩選查詢單位
  useEffect(() => {
    if (!isArrayEmpty(orgList)) {
      initSelectOption();
      if (filteredOrgItems[selectedTab]) {
        setFilteredOrgItems(prevState => ({ ...prevState, [selectedTab]: pageOrgList }));
        const paging = pageInfo[selectedTab];
        if (paging) {
          paging.totalPages = totalPages;
          paging.totalRows = filterOrg ? filterOrg.length : 0;
        }
      }
    }
  }, [orgList])

  //初始所有層級單位
  useEffect(() => {
    if (!isArrayEmpty(orgList) && selectedTab && !filteredOrgItems[selectedTab]) {
      setFilteredOrgItems(prevState => ({ ...prevState, [selectedTab]: pageOrgList }));
      const paging = pageInfo[selectedTab];
      if (paging) {
        paging.totalPages = totalPages;
        paging.totalRows = filterOrg ? filterOrg.length : 0;
      }
    }
  }, [orgList, selectedTab])

  useEffect(() => {
    const paging = pageInfo[selectedTab];
    if (paging) {
      paging.currentPage = 1;
      paging.totalPages = totalPages;
      paging.totalRows = filterOrg ? filterOrg.length : 0;
      setPageInfo({ ...pageInfo, [selectedTab]: paging });
    }
    const newCondition = condition[selectedTab];
    if (newCondition) {
      newCondition.currentPage = 1;
      setCondition({ ...condition, [selectedTab]: newCondition });
    }
  }, [selectedTab])

  const fetchOrgLevelData = () => {
    OrgAPI.getOrgLevelList({
      ...getBasicLoginUserInfo(loginUser)!,
    }).then(result => {
      if (isApiCallSuccess(result)) {
        setOrgLevelList(result.results)
      }
    }).catch(error => {
      checkTimeoutAction(error, navigate, t);
    })
  }

  const fetchOrgData = () => {
    setLoading(true);
    OrgAPI.getOrgList({
      ...getBasicLoginUserInfo(loginUser)!,
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const orgData = result.results;
        setOrgList(orgData)
      }
    }).finally(() => {
      setLoading(false);
    })
  }
  const fetchOrgManagerData = () => {
    UserAPI.getUserOrgManager({
      ...getBasicLoginUserInfo(loginUser)!,
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const updatedOrgManager: Record<string, EhsUser[]> = {};

        result.results.forEach((item: EhsUser) => {
          const { orgId } = item;
          if (!updatedOrgManager[orgId]) {
            updatedOrgManager[orgId] = []; // 如果该 orgId 不存在于 orgManagerList 中，则初始化为空数组
          }
          updatedOrgManager[orgId].push(item); // 将用户添加到相应的 orgId 下
        });
        setOrgManager(updatedOrgManager);
      }
    })
  }

  const fetchAreaOption = () => {
    SelectListAPI.getSelectArea({
      ...getBasicLoginUserInfo(loginUser)!,
    }).then(result => {
      if (isApiCallSuccess(result)) {
        setAreaOptionList(result.results)
      }
    })
  }

  const fetchOrgTypeOption = () => {
    SelectListAPI.getSelectConfigByType({
      ...getBasicLoginUserInfo(loginUser)!,
      configType: CONFIG_TYPE_UNIT,
    }).then(result => {
      if (isApiCallSuccess(result)) {
        setOrgTypeOptionList(result.results);
      }
    })
  }

  const initializePageInfoAndConditions = () => {
    if (!selectedTab) {
      setSelectedTab(orgLevelList[0].orglvId);
      setSelectedTabIdx(1);
    }

    if (isArrayEmpty(Object.keys(pageInfo))) {
      const newPageInfo: Record<string, PageInfo> = {};
      orgLevelList.forEach(item => {
        newPageInfo[item.orglvId] = initPageInfo;
      });
      setPageInfo(newPageInfo);
    }
    if (isArrayEmpty(Object.keys(condition))) {
      const newCondition: Record<string, Condition> = {};
      orgLevelList.forEach(item => {
        newCondition[item.orglvId] = initCondition;
      });
      setCondition(newCondition);
    }
  };

  const initSelectOption = () => {
    const orgItem = orgList.reduce((itemMap: Record<string, EhsOrg[]>, org: EhsOrg) => {
      const orglvId = org.orglvId;
      itemMap[orglvId] = itemMap[orglvId] || [];
      itemMap[orglvId].push(org);
      return itemMap;
    }, {});

    setSelectOrgItems(orgItem);
  };

  const getParentOrgIdKey = (idx: number) => {
    return selectedTab + '-' + idx;
  }


  const startIndex = pageInfo[selectedTab] ? (pageInfo[selectedTab].currentPage - 1) * pageInfo[selectedTab].pageSize : 0; // 计算起始索引
  const endIndex = pageInfo[selectedTab] ? startIndex + pageInfo[selectedTab].pageSize : 0; // 计算结束索引
  const findSearchOid = () => {
    const keys = Object.keys(selectParentOrgIds);

    const filteredKeys = keys.filter(key => selectParentOrgIds[key]);
    // 挑出以指定鍵開頭的那些鍵
    const relevantKeys = filteredKeys.filter(key => key.startsWith(selectedTab + "-"));

    // 如果有符合條件的鍵
    if (relevantKeys.length > 0) {
      // 找出最大的那個鍵
      const maxKey = relevantKeys.reduce((a, b) => {
        return parseInt(b.split("-")[1]) > parseInt(a.split("-")[1]) ? b : a;
      });

      // 取得對應的值
      return selectParentOrgIds[maxKey];
    }
  }
  const searchOid = findSearchOid();
  const index = orgLevelList.findIndex(data => data.orglvId === selectedTab);
  const filterOrg = useMemo(() => {
    return searchOid ? orgList.filter(data => {
      return data.orglvId === selectedTab && (data.orgId === searchOid || data.orgIds.includes(searchOid + ORG_SPLIT_FLAG));
    }) : orgList.filter(data => data.orglvId === selectedTab);
  }, [searchOid, orgList, selectedTab]);
  const totalPages = useMemo(() => {
    return filterOrg && pageInfo[selectedTab] ? Math.ceil(filterOrg.length / pageInfo[selectedTab].pageSize) : 0;
  }, [filterOrg, pageInfo, selectedTab]);
  const pageOrgList = useMemo(() => {
    return filterOrg.slice(startIndex, endIndex);
  }, [filterOrg, startIndex, endIndex]);
  const parentOrgSelect = useMemo(() => {
    return index !== 0 && orgLevelList.slice(0, index).map((data, idx) => {
      const orgOption = selectOrgItems[data.orglvId];
      const selectOrgPid = selectParentOrgIds[getParentOrgIdKey(idx - 1)];
      const selectOrgId = selectParentOrgIds[getParentOrgIdKey(idx)];
      const filteredOrgOptions = selectOrgPid ? orgOption?.filter(item => selectOrgPid === item.orgPid) : orgOption;
      const isFirstOrgLv = idx === 0;

      return ((isFirstOrgLv || (!isFirstOrgLv && selectOrgPid && filteredOrgOptions.length > 0)) &&
        <div key={idx} className="col-xl-3 d-flex align-items-center">
          <label className="pe-3">{data.orglvName}</label>
          <select className="form-select w-75" value={selectOrgId} onChange={(e) => onChangeParentOrgSelect(idx, e.target.value)}>
            <option value=''>{t('text.all')}</option>
            {filteredOrgOptions?.map((data, idx) => (
              <option key={data.orgId} value={data.orgId}>{data.orgName}</option>
            ))}
          </select>
        </div>
      );
    });
  }, [index, orgLevelList, selectOrgItems, selectParentOrgIds, t]);

  const sortFunc = (sortList: []) => {
    setFilteredOrgItems(prevState => ({ ...prevState, [selectedTab]: sortList }));
  }

  const getShowList = () => {
    if (!localSearchKey) {
      return filteredOrgItems[selectedTab] || [];
    }
    return localSearchResult;
  }
  const showList = useMemo(() => getShowList(), [localSearchKey, filteredOrgItems, selectedTab, localSearchResult]);

  const handleTabClick = (props: { orglvId: string, orglvLevel: number }) => {
    const { orglvId, orglvLevel } = props;
    setSelectedTab(orglvId);
    setSelectedTabIdx(orglvLevel);
  };
  /* 更新選中單位且清空所有下層單位 */
  const onChangeParentOrgSelect = (idx: number, orgId: string) => {
    const key = getParentOrgIdKey(idx);
    setSelectParentOrgIds(prevState => {
      const newState = { ...prevState, [key]: orgId };
      const [prefix, currentIndex] = key.split('-');
      for (const existingKey of Object.keys(newState)) {
        const [existingPrefix, existingIndex] = existingKey.split('-');
        if (existingPrefix === prefix && parseInt(existingIndex) > parseInt(currentIndex)) {
          newState[existingKey] = "";
        }
      }
      return newState;
    });
  }

  const deleteSuccess = () => {
    fetchOrgData();
  }

  const updatePageInfo = (selectedTab: string, newPageInfo: PageInfo, newCondition: Condition) => {
    if (newPageInfo && newCondition && (newPageInfo.currentPage !== newCondition.currentPage || newPageInfo.pageSize !== newCondition.pageSize)) {
      newPageInfo.currentPage = newCondition.currentPage;
      newPageInfo.pageSize = newCondition.pageSize;
      newPageInfo.totalPages = filterOrg && pageInfo[selectedTab] ? Math.ceil(filterOrg.length / newPageInfo.pageSize) : 0;
      setPageInfo(prev => ({ ...prev, [selectedTab]: newPageInfo }));
    }
  };

  const clickSearch = () => {
    setFilteredOrgItems(prevState => ({ ...prevState, [selectedTab]: pageOrgList }));
  }

  return (
    <StlyedOrgList $loading={loading}>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {
            <Dialog
              content={
                <OrgDetail onClose={() => setPopupDetail(false)} modifyData={modifyData} />
              }
              show={popupDetail}
            />
          }
          {
            <Dialog
              content={
                <OrgModify
                  onClose={() => {
                    setPopupMode(null);
                  }}
                  onActionSuccess={() => {
                    fetchOrgData();
                    setPopupMode(null);
                    showSuccessToast(t('message.success'));
                  }}
                  setLoadingBlock={setLoadingBlock}
                  mode={popupMode}
                  modifyData={modifyData}
                  areaOption={areaOptionList}
                  orgTypeOption={orgTypeOptionList}
                  selectOrgLevelId={selectedTab}
                  selectOrgLevel={selectedTabIdx}
                  orgList={orgList}
                  orgLevelList={orgLevelList}
                />
              }
              show={popupMode !== null}
            />
          }
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs items={[
              { label: t("func.system_manage_setting"), },
              { label: t("func.org_manage") },
            ]} />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{t("func.org_manage")} </h1>
            {/* END page-header */}
            <ul className="nav nav-tabs">
              {orgLevelList.map((data, idx) =>
                <li className="nav-item" key={data.orglvId}>
                  <a href={`#tab-${data.orglvId}`} data-bs-toggle="tab" className={`nav-link ${idx === 0 && 'active'}`} onClick={() => handleTabClick(data)}>{data.orglvName}</a>
                </li>
              )}
            </ul>
            <div className="tab-content panel p-3 rounded-0 rounded-bottom">
              {orgLevelList.map((data, idx) =>
                <div className={`tab-pane fade ${idx === 0 && 'active show'}`} id={`tab-${data.orglvId}`} key={data.orglvId}>
                  {isAddOrgRole && <button type="button" className="btn btn-purple fs-5 my-4" title={t("button.add")} onClick={() => {
                    setModifyData(initEhsOrg)
                    setPopupMode(ActionMode.ADD);
                  }}>
                    <i className="fas fa-plus"></i> {t('button.add')}
                  </button>}
                  {idx !== 0 &&
                    <div className="card mb-3">
                      <div className="card-body">
                        <div className="row">
                          {parentOrgSelect}
                          <div className="col-xl-3">
                            <button type="button" className="btn btn-primary mt-1" title={t('button.search.item')} onClick={clickSearch}><i className="fas fa-magnifying-glass"></i> {t('button.search.item')}</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  }

                  <div className="card py-3">
                    <div className="row topFunctionRow">
                      <div className="col-sm-12 col-md-6 left">
                        <PageSizeSelector pageSize={condition[data.orglvId] ? condition[data.orglvId].pageSize : 50} condition={condition} setCondition={setCondition}
                          specialCondition={{ orgList: { orglvId: data.orglvId } }} />
                      </div>
                      <div className="col-sm-12 col-md-6 right">
                        {/* <div className="dataTables_filter d-flex">
                        search:
                        <input value={localSearchKey} onChange={(e) => {
                          setLocalSearchKey(e.target.value)
                        }} type="search" className="form-control form-control-sm" placeholder="" aria-controls="data-table-default" />
                      </div> */}
                      </div>
                    </div>
                    <div className="card-body">
                      {loading && <Loader />}
                      <ExpandableRwdTable>
                        <div className="table-container">
                          <table id="data-table-default" className={'table table-hover align-middle dt-responsive'}>
                            <thead className="fs-4 fw-bold">
                              <tr>
                                <th>{t('table.title.item')} </th>
                                <th className='text-start'>{t('table.title.name')} <SortIcon dataList={showList} dataField={"orgName"} setFunction={sortFunc} /></th>
                                <th className='text-start responsive-header' data-orderable="false">{t('table.title.org.manager')}</th>
                                <th className='text-start'>{t('table.title.building.item')}<SortIcon dataList={showList} dataField={"buildName"} setFunction={sortFunc} /></th>
                                <th className='responsive-header'>{t('table.title.building.floor.item')} <SortIcon dataList={showList} dataField={"orgFloor"} setFunction={sortFunc} /></th>
                                <th className='responsive-header'>{t('table.title.org.house_number')} <SortIcon dataList={showList} dataField={"orgHousenum"} setFunction={sortFunc} /></th>
                                <th className='responsive-header'>{t('table.title.ext')} <SortIcon dataList={showList} dataField={"orgPhone"} setFunction={sortFunc} /></th>
                                <th data-orderable="false">{t('table.title.action')}</th>
                              </tr>
                            </thead>
                            <tbody className="text-center fs-5">
                              {!loading && showList.map((data, idx) => {
                                return <OrgRow
                                  index={idx + 1}
                                  org={data}
                                  orgManager={orgManager[data.orgId]}
                                  key={data.orgId}
                                  onDeleteSuccess={deleteSuccess}
                                  setPopupMode={setPopupMode}
                                  setPopupDetail={setPopupDetail}
                                  setModifyData={setModifyData}
                                  setLoadingBlock={setLoadingBlock}
                                />
                              })}
                            </tbody>
                          </table>
                        </div>
                      </ExpandableRwdTable>
                      {pageInfo[data.orglvId] && <Pagination pageInfo={pageInfo[data.orglvId]} condition={condition} setCondition={setCondition}
                        specialCondition={{ orgList: { orglvId: data.orglvId } }} />}
                    </div>
                  </div>
                </div>
              )}
            </div>
            {/* END scrollbar */}
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </BlockUi>
    </StlyedOrgList>
  );
}

const StlyedOrgList = styled.div<{ $loading: boolean }>`
  padding-bottom:150px;

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }
  table {
    position:relative;
    min-height:${props => props.$loading ? "300px" : "auto"};
    thead {
      background:rgb(251, 205, 165);
    }
    th {
      text-align: center;
      white-space:nowrap;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 125px;
        text-align:left;
        min-height:39.5px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          white-space: nowrap;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
`;

export default OrgList;