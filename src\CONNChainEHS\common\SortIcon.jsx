// @ts-nocheck
import { useState, useEffect } from "react";

export default function SortIcon({ dataList, dataField, setFunction }) {
    const [direction, setDirection] = useState("");

    useEffect(() => {
        if (!direction) {
            return;
        }
        let sortList = [...dataList].sort(sortComparer);
        setFunction(sortList);
    }, [direction]);

    const sortComparer = (a, b) => {
        let result;
        let valueA = getPropertyByString(a, dataField); 
        let valueB = getPropertyByString(b, dataField); 
        if (new Date(valueA).toString() === "Invalid Date" || new Date(valueB).toString() === "Invalid Date") {
            //照unicode排序
            result = valueA.localeCompare(valueB);
        }
        else {
            //按日期大小排序
            result =
                new Date(valueA).getTime() - new Date(valueB).getTime();
        }

        return result * (direction === "up" ? 1 : -1);
    };

    function getPropertyByString(obj, path) {
        const keys = path.split(".");
        let current = obj;
        for (let key of keys) {
            if (key.includes("[")) {
                const [arrayKey, index] = key.match(/(\w+)\[(\d+)\]/).slice(1);
                current = current[arrayKey]?.[parseInt(index)];
            } else {
                current = current[key];
            }
            if (typeof current === "undefined") {
                return undefined;
            }
        }
        return current;
    }

    const iconClass = () => {
        if (!direction) {
            return "fa-sort";
        } else if (direction === "up") {
            return "fa-sort-up";
        } else {
            return "fa-sort-down";
        }
    };

    return (
        <i
            className={`fas fa-lg fa-fw me-15px ${iconClass()}`}
            style={{ cursor: "pointer", color: !direction ? "#ccc" : "#348fe2", fontSize: "14px" }}
            onClick={() => {
                setDirection(direction === "up" ? "down" : "up");
            }}
        />
    );
}