import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { createAuthorPostJsonConfig, createLoginPostJsonConfig, getApiAddress, getApiAddressByUniformNum, handleFetchResponse, apiRequest } from "./config";
import { API_ENDPOINTS } from "./apiEndpoints";

export const AuthorAPI = {
  login: async (parms: {
    uniformNum: string;
    loginId: string;
    loginPwd: string;
    langType: string;
  }) => {
    return apiRequest(getApiAddressByUniformNum(parms.uniformNum) + API_ENDPOINTS.LOGIN, createAuthorPostJsonConfig(parms, API_ENDPOINTS.LOGIN)).then((res) => res.json());
  },
  logout: async (parms: {
    uniformNum: string;
    loginId: string;
    langType: string;
  }) => {
    return apiRequest(getApiAddressByUniformNum(parms.uniformNum) + API_ENDPOINTS.LOGOUT, createAuthorPostJsonConfig(parms, API_ENDPOINTS.LOGOUT)).then((res) => res.json());
  },
  getCurrentUser: async (parms: {
    uniformNum: string;
    langType: string;
  }) => {
    return apiRequest(getApiAddress + API_ENDPOINTS.USER_CURRENT, createLoginPostJsonConfig(parms, API_ENDPOINTS.USER_CURRENT)).then((res) => handleFetchResponse(res));
  },
  changePermission: async (parms: {
    userId: string;
    rouId: string;
    langType: string;
  }) => {
    return apiRequest(getApiAddress + API_ENDPOINTS.PERMISSION_SWITCH, createLoginPostJsonConfig(parms, API_ENDPOINTS.PERMISSION_SWITCH)).then((res) => handleFetchResponse(res));
  },
  changePwd: async (parms: {
    uniformNum: string;
    loginId: string;
    langType: string;
  }) => {
    return apiRequest(getApiAddress + API_ENDPOINTS.PWD_CHANGE, createLoginPostJsonConfig(parms, API_ENDPOINTS.PWD_CHANGE)).then((res) => handleFetchResponse(res));
  },
  verifyRecaptcha: async (parms: {
    uniformNum: string;
    token: string;
  }) => {
    return apiRequest(getApiAddressByUniformNum(parms.uniformNum) + API_ENDPOINTS.RECAPTCHA_VERIFY, createAuthorPostJsonConfig(parms, API_ENDPOINTS.RECAPTCHA_VERIFY)).then((res) => res.json());
  },
  checkResetPwd: async (parms: {
    uniformNum: string;
    account: string;
    email: string;
  }) => {
    return apiRequest(getApiAddressByUniformNum(parms.uniformNum) + API_ENDPOINTS.PWD_RESET_CHECK, createAuthorPostJsonConfig(parms, API_ENDPOINTS.PWD_RESET_CHECK)).then((res) => handleFetchResponse(res));
  },
  notifyResetPwd: async (parms: {
    uniformNum: string;
    account: string;
    email: string;
  }) => {
    return apiRequest(getApiAddressByUniformNum(parms.uniformNum) + API_ENDPOINTS.PWD_RESET_NOTIFY, createAuthorPostJsonConfig(parms, API_ENDPOINTS.PWD_RESET_NOTIFY)).then((res) => handleFetchResponse(res));
  },
  checkResetPwdAuth: async (parms: {
    uniformNum: string;
    checkPwdKey: string;
  }) => {
    return apiRequest(getApiAddressByUniformNum(parms.uniformNum) + API_ENDPOINTS.PWD_RESET_AUTH_CHECK, createAuthorPostJsonConfig(parms, API_ENDPOINTS.PWD_RESET_AUTH_CHECK)).then((res) => handleFetchResponse(res));
  },
  updateResetPwd: async (parms: {
    uniformNum: string;
    userId: string;
    pwd: string;
  }) => {
    return apiRequest(getApiAddressByUniformNum(parms.uniformNum) + API_ENDPOINTS.PWD_RESET_UPDATE, createAuthorPostJsonConfig(parms, API_ENDPOINTS.PWD_RESET_UPDATE)).then((res) => handleFetchResponse(res));
  },
  getNowTime: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + API_ENDPOINTS.TIME_NOW, createLoginPostJsonConfig(parms, API_ENDPOINTS.TIME_NOW)).then((res) => handleFetchResponse(res));
  },
  refreshToken: async () => {
    return apiRequest(getApiAddress + API_ENDPOINTS.TOKEN_REFRESH, createLoginPostJsonConfig({}, API_ENDPOINTS.TOKEN_REFRESH)).then((res) => handleFetchResponse(res));
  },
  revokeToken: async () => {
    return apiRequest(getApiAddress + API_ENDPOINTS.TOKEN_REVOKE, createAuthorPostJsonConfig({}, API_ENDPOINTS.TOKEN_REVOKE)).then((res) => handleFetchResponse(res));
  },
};
