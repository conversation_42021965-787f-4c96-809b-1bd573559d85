import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const RouAPI = {
  getUserRoleList: async (
    parms: BaseParams & {
      userId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "rou/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSwitchableRoles: async (
    parms: BaseParams & {
      userId: string;
      rouId?: string;
      includeAgent: boolean;
    }
  ) => {
    return apiRequest(getApiAddress + "rou/switchable/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getAgentList: async (
    parms: BaseParams & {
      userId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "rou/agent/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addSingleRou: async (
    parms: BaseParams & {
      userId: string;
      roleId: string;
      labId: string;
      orgId: string;
      rouType: string;
    }
  ) => {
    return apiRequest(getApiAddress + "rou/single/add", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addAgent: async (
    parms: BaseParams & { 
      userId: string;
      rouId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "rou/agent/add", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  updateDefaultLoginPermission: async (
    parms: BaseParams & {
      userId: string;
      rouId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "rou/default-login/update", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  delRou: async (
    parms: BaseParams & {
      rouId: string;
      deleteBasicRou: boolean;
    }
  ) => {
    return apiRequest(getApiAddress + "rou/del", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};
