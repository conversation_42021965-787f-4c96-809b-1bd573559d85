import { EhsOptions } from "ehs/models/EhsOptions";
import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const OptionAPI = {
  getAllOptions: async (
    parms: BaseParams & {
    }
  ) => {
    return apiRequest(getApiAddress + "options/list-all", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getOptionListByIds: async (
    parms: BaseParams & {
      optionIdList: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "options/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getOptionListBySubTypes: async (
    parms: BaseParams & {
      subtypeList: string[];
    }
  ) => {
    return apiRequest(getApiAddress + "options/list/subtype", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getOptionListByLikeIds: async (
    parms: BaseParams & {
      idPrefix: string;
    }
  ) => {
    return apiRequest(getApiAddress + "options/list/like", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  saveOptionsBatch: async (
    parms: BaseParams & {
      optionList: EhsOptions[];
    }
  ) => {
    return apiRequest(getApiAddress + "options/save/batch", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};
