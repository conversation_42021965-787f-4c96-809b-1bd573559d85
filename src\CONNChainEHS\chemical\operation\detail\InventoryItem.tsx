import { ChemicalAPI } from 'api/chemicalAPI';
import { FileAPI } from 'api/fileAPI';
import { AgentNameTag } from 'ehs/common/AgentNameTag';
import DownloadButton from 'ehs/common/button/DownloadButton';
import CcbViewDialog from 'ehs/common/chemical/CcbViewDialog';
import Dialog from 'ehs/common/Dialog';
import InputDate from 'ehs/common/input/InputDate';
import InputNumFloat from 'ehs/common/input/InputNumFloat';
import { confirmMsg, errorMsg } from 'ehs/common/SwalMsg';
import TextWithLineBreaks from 'ehs/common/TextWithLineBreaks';
import { showSuccessToast, showWarnToast } from 'ehs/common/Toast';
import { CHEMICAL_WEIGHT_DECIMAL_PLACES, CHEMICAL_WEIGHT_MAX, CHEMICAL_WEIGHT_MIN } from 'ehs/constant/constants';
import { BtnType } from 'ehs/enums/BtnType';
import { ChemicalCcbLinkType } from 'ehs/enums/ChemicalCcbLinkType';
import { InventoryStatus } from 'ehs/enums/InventoryStatus';
import { OperateType } from 'ehs/enums/OperateType';
import useLoginUser from 'ehs/hooks/useLoginUser';
import { EhsChemicalInventory } from 'ehs/models/EhsChemicalInventory';
import { EhsChemicalOperRecord, initEhsChemicalOperRecord } from 'ehs/models/EhsChemicalOperRecord';
import { EhsConfigParam } from 'ehs/models/EhsConfigParam';
import { EhsFile } from 'ehs/models/EhsFile';
import { OperateInfo } from 'ehs/models/OperateInfo';
import { isArrayEmpty } from 'ehs/utils/arrayUtil';
import { checkBtnAuth, getBasicLoginUserInfo } from 'ehs/utils/authUtil';
import { isApiCallSuccess } from 'ehs/utils/resultUtil';
import { getFormatDateDash, getFormatDateSlash } from 'ehs/utils/stringUtil';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import styled, { css } from 'styled-components';
import ChemicalDetailDialog from './ChemicalDetailDialog';
import EditableLocationCell from './EditableLocationCell';
import EditableNoteCell from './EditableNoteCell';
import OperActionRow from './OperActionRow';
import SdsModify from './SdsModify';

interface InventoryItemProps {
    inventory: EhsChemicalInventory; // 核心資料
    isHistory?: boolean; // 是否為歷史項目
    phaseState?: string; // 狀態
    isLabCanOperation?: boolean; // 是否為實驗室可操作
    idx: number; // 索引
    nowDate: Date;// 現在日期
    defStopOperDate: Date; // 預設停止操作日期

    // 檔案相關
    sdsFile: EhsFile | undefined;
    setSdsFile?: (sdsFile: EhsFile | undefined) => void;

    // 開啟狀態相關
    openIndex?: number | null;
    setOpenIndex?: React.Dispatch<React.SetStateAction<number | null>>;

    // 列表相關
    inventoryList?: EhsChemicalInventory[];
    historyList?: EhsChemicalInventory[];
    setInventoryList?: React.Dispatch<React.SetStateAction<EhsChemicalInventory[]>>;
    setHistoryList?: React.Dispatch<React.SetStateAction<EhsChemicalInventory[]>>;

    // 批次選擇相關
    batchCheckedMap?: { [inventoryId: string]: EhsChemicalInventory };
    setBatchCheckedMap?: React.Dispatch<React.SetStateAction<{ [inventoryId: string]: EhsChemicalInventory }>>;
    //實際停止日
    chemStopDate?: { [inventoryId: string]: Date | null };
    setChemStopDate?: React.Dispatch<React.SetStateAction<{ [inventoryId: string]: Date | null }>>;

    // 配置相關
    operItemConfigs: EhsConfigParam[];
    storageLocationConfigs: EhsConfigParam[];
    configMap: { [configId: string]: EhsConfigParam };

    // 載入狀態和數據獲取
    setLoadingBlock?: React.Dispatch<React.SetStateAction<boolean>>;
    fetchTotalInventoryData?: () => void;
}

interface EditChemicalOperRecord {
    operDate: Date | null;
    operQty: number | undefined;
    // 其他屬性...
}

const initEditChemicalOperRecord: EditChemicalOperRecord = {
    operDate: null,
    operQty: undefined
};

const InventoryItem: React.FC<InventoryItemProps> = ({
    inventory, isHistory, phaseState = "",
    isLabCanOperation = false, nowDate, defStopOperDate, openIndex,
    // 檔案相關
    sdsFile,
    setSdsFile = (sdsFile) => { },
    idx, setOpenIndex, operItemConfigs, inventoryList = [], historyList = [],
    setInventoryList = () => { }, setHistoryList = () => { }, batchCheckedMap, setBatchCheckedMap, chemStopDate, setChemStopDate, storageLocationConfigs, configMap, setLoadingBlock = () => { },
    fetchTotalInventoryData = () => { }
}) => {
    const { loginUser } = useLoginUser();
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [sdsEditDialog, setSdsEditDialog] = useState<boolean | null>(null);
    const [editRecordIndex, setEditRecordIndex] = useState<number | null>(null);
    const [showDetailDialog, setShowDetailDialog] = useState<boolean | null>(null);
    const [showCCBView, setShowCCBView] = useState<boolean | null>(null);
    const [ccbLinkId, setCcbLinkId] = useState<string>("");
    const [ccbDataRefreshCounter, setCcbDataRefreshCounter] = useState<number>(0);
    const [editRecordInfo, setEditRecordInfo] = useState<EditChemicalOperRecord>(initEditChemicalOperRecord);
    const { inventoryId, purchaseDetailId, barcode, concentration, inventoryQty, inventoryNote,
        inventoryLocation, inspDate, manufacturerName, sdsExpDate, createId,
        creatorName, operRecordList, inventoryStatus, productSdsId, productSdsName } = inventory;
    const isEnabled = inventoryStatus === InventoryStatus.ENABLE;
    const isSigning = inventoryStatus === InventoryStatus.SIGNING;
    const isPurchasing = inventoryStatus === InventoryStatus.PURCHASING;
    const isOperating = !isHistory && isEnabled;
    const stopOperDate = chemStopDate && chemStopDate[inventoryId];
    const { fileContentBase64, fileName = "" } = sdsFile || {};
    const isEditFirstChemOperRecordRole = checkBtnAuth(BtnType.EDIT_CHEM_OPER_RECORD_FIRST);

    const INVENTORY_FIELDS = {
        LOCATION: {
            key: 'LOCATION' as const,
            value: 'inventoryLocation' as const,
            requiresApi: true,
            apiMethod: ChemicalAPI.editChemicalInventoryLocation
        },
        NOTE: {
            key: 'NOTE' as const,
            value: 'inventoryNote' as const,
            requiresApi: true,
            apiMethod: ChemicalAPI.editChemicalInventoryNote
        },
        SDS_EXP_DATE: {
            key: 'SDS_EXP_DATE' as const,
            value: 'sdsExpDate' as const,
            requiresApi: false
        },
        INVENTORY_STATUS: {
            key: 'INVENTORY_STATUS' as const,
            value: 'inventoryStatus' as const,
            requiresApi: false
        }
    } as const;
    type InventoryFieldValue = typeof INVENTORY_FIELDS[keyof typeof INVENTORY_FIELDS]['value'];

    const onChangeEditRecordInfo = (obj: any) => {
        setEditRecordInfo({
            ...editRecordInfo,
            ...obj
        });
    }

    const fetchSdsFile = async (sdsId: string, loginUser: any) => {
        const result = await FileAPI.getProductSdsFile({
            ...getBasicLoginUserInfo(loginUser)!,
            sdsId: sdsId
        });

        if (isApiCallSuccess(result) && result.results) {
            const fileInfo = result.results;
            return {
                content: fileInfo.fileContentBase64,
                fileName: fileInfo.fileName || ''
            };
        }
        return {
            content: '',
            fileName: ''
        };
    };

    const updateInventoryField = async (
        inventoryId: string,
        fieldName: InventoryFieldValue,
        newValue: any,
        oldValue: any
    ) => {
        if (oldValue === newValue) return;

        const fieldConfig = Object.values(INVENTORY_FIELDS).find(field => field.value === fieldName);
        if (!fieldConfig) return;

        try {
            // 如果需要 API 調用
            if (fieldConfig.requiresApi && fieldConfig.apiMethod) {
                const params = {
                    ...getBasicLoginUserInfo(loginUser)!,
                    inventoryId,
                    [fieldName]: newValue
                };

                const result = await fieldConfig.apiMethod(params);
                if (!isApiCallSuccess(result)) {
                    return;
                }
                showSuccessToast(t('message.success'));
            }

            // 更新本地狀態
            const index = inventoryList.findIndex(item => item.inventoryId === inventoryId);
            if (index !== -1) {
                const updatedList = [...inventoryList];
                updatedList[index] = {
                    ...updatedList[index],
                    [fieldName]: newValue,
                };
                setInventoryList(updatedList);
            }
        } catch (error) {
            console.error(`Error updating ${fieldName}:`, error);
            errorMsg(t('message.error'));
        }
    };

    const updateLocation = async (inventoryId: string, inventoryLocation: string, oldInventoryLocation: string) => {
        setLoadingBlock(true);
        await updateInventoryField(inventoryId, INVENTORY_FIELDS.LOCATION.value, inventoryLocation, oldInventoryLocation);
        setLoadingBlock(false);
    };

    const updateNote = async (inventoryId: string, inventoryNote: string, oldInventoryNote: string) => {
        setLoadingBlock(true);
        await updateInventoryField(inventoryId, INVENTORY_FIELDS.NOTE.value, inventoryNote, oldInventoryNote);
        setLoadingBlock(false);
    };

    const updateSdsExpDate = async (inventoryId: string, newSdsExpDate: Date, oldSdsExpDate: Date | null) => {
        await updateInventoryField(inventoryId, INVENTORY_FIELDS.SDS_EXP_DATE.value, newSdsExpDate, oldSdsExpDate);
    };

    const updateInventoryStatus = async (inventoryId: string, newStatus: number, oldStatus: number) => {
        await updateInventoryField(inventoryId, INVENTORY_FIELDS.INVENTORY_STATUS.value, newStatus, oldStatus);
    };

    /**
     * 檢查處理運作
     * @param formState 運作資訊
     * @param inventory 存量資訊
     */
    const handleUpdateOperation = async (formState: OperateInfo, inventory: EhsChemicalInventory) => {
        try {
            setLoadingBlock(true);
            const { operItem, weight } = formState;
            const { inventoryQty } = inventory;
            const operItemNumber = typeof operItem === 'string' ? Number(operItem) : operItem;

            // 現在可以安全地使用 operItemNumber 作為數字
            const isDisable = operItemNumber === OperateType.DISABLE;


            if ((operItemNumber === OperateType.NO_CHANGE || isDisable) && !inventoryQty) {
                showWarnToast(t('message.chemical.depleted_cannot_operate'));
                return;
            }

            if (operItemNumber === OperateType.PURCHASE && weight) {
                await addRePurchase(formState);
                return;
            }
            // 如果不是減少類型的操作，直接執行
            const subtractTypes = [OperateType.USE, OperateType.INVENTORY_SUBTRACT];
            if ((!subtractTypes.includes(operItemNumber) || !weight) && !isDisable) {
                await addOperation(formState);
                return;
            }

            if (isDisable) {
                const confirmed = await confirmMsg(t('message.confirm.chemical_oper_disable'), t);
                if (!confirmed) return;
            }

            // 檢查數量
            if (weight && weight > inventoryQty) {
                showWarnToast(t('message.chemical.over_oper_qty'));
                return;
            }

            // 如果會用完，詢問確認
            if (weight === inventoryQty) {
                const confirmed = await confirmMsg(t('message.confirm.chemical_oper_depleted'), t);
                if (!confirmed) return;
            }

            await addOperation(formState);

        } catch (error) {
            console.error('Operation update failed:', error);
        } finally {
            setLoadingBlock(false);
        }
    };

    /**
     * 運作
     * @param formState 運作資訊
     */
    const addOperation = (formState: OperateInfo) => {
        const { operDate, operItem, weight, note } = formState;
        ChemicalAPI.addChemicalOperation({
            ...getBasicLoginUserInfo(loginUser)!,
            chemicalOperRecord: {
                ...initEhsChemicalOperRecord,
                inventoryId: inventoryId,
                operDate: operDate,
                operType: operItem,
                chemRecordNote: note?.trim(),
                inventoryPre: inventoryQty,
                operQty: weight
            }
        }).then((result) => {
            if (isApiCallSuccess(result)) {
                showSuccessToast(t('message.success'));
                updateOperNewInventoryList(result.results);

                // 如果操作類型是 USE，則刷新 CCB 數據
                if (operItem === OperateType.USE) {
                    handleRefreshCcbData();
                }
            } else {
                errorMsg(result.message);
            }
        }).catch((error) => {
            errorMsg(t('message.system_error'));    
        }).finally(() => {
            setLoadingBlock(false);
        })
    }

    /**
     * 運作
     * @param formState 運作資訊
     */
    const addRePurchase = (formState: OperateInfo) => {
        const { operDate, operItem, weight, note } = formState;
        ChemicalAPI.addChemicalRePurchase({
            ...getBasicLoginUserInfo(loginUser)!,
            chemicalOperRecord: {
                ...initEhsChemicalOperRecord,
                inventoryId: inventoryId,
                operDate: operDate,
                operType: operItem,
                chemRecordNote: note?.trim(),
                inventoryPre: inventoryQty,
                operQty: weight
            }
        }).then((result) => {
            if (isApiCallSuccess(result)) {
                showSuccessToast(t('message.success'));
                updateOperNewInventoryList(result.results);
            } else {
                errorMsg(result.message);
            }
        }).catch((error) => {
            errorMsg(t('message.system_error'));
        }).finally(() => {
            setLoadingBlock(false);
        })

    }

    const handleSingleCheck = (
        e: React.ChangeEvent<HTMLInputElement>,
        inventoryId: string,
        item: EhsChemicalInventory,
    ) => {
        e.stopPropagation();
        if (!setBatchCheckedMap) return;
        setBatchCheckedMap((prev) => {
            const next = { ...prev };
            if (e.target.checked) {
                next[inventoryId] = item;
            } else {
                delete next[inventoryId];
            }
            return next;
        });
    };

    const handleAccordionDivClick = (event: React.MouseEvent<HTMLDivElement>, index: number) => {
        const target = event.target as HTMLElement;
        // 避免在點擊 form 控制項、按鈕、連結時觸發開合
        const shouldIgnore = target.closest('input, button, a, textarea, select, label');
        if (shouldIgnore) return;
        if (setOpenIndex) {
            setOpenIndex(openIndex === index ? null : index);
        }
    };

    const handleDeleteOperRecord = (item: EhsChemicalOperRecord) => {
        const { operDate } = item;
        if (stopOperDate) {
            stopOperDate.setHours(0, 0, 0, 0);
        }
        if (!stopOperDate || (new Date(operDate!!).getTime() < new Date(stopOperDate).getTime())) {
            showWarnToast(t('message.chemical.error.over_stop_date_delete'));
            return;
        }
        confirmMsg(t('message.confirm.delete'), t).then((value) => {
            if (value) {
                setLoadingBlock(true);
                ChemicalAPI.deleteChemicalRecord({
                    ...getBasicLoginUserInfo(loginUser)!,
                    inventoryId: inventoryId,
                    chemRecordId: item.chemRecordId
                }).then((result) => {
                    if (isApiCallSuccess(result)) {
                        showSuccessToast(t('message.success'));
                        updateOperNewInventoryList(result.results);
                    } else {
                        errorMsg(result.message);
                    }
                    setLoadingBlock(false);
                }).catch(err => {
                    setLoadingBlock(false);
                })
            }
        });
    }

    const updateOperNewInventoryList = (newInventtory: EhsChemicalInventory) => {
        const { inventoryQty, operRecordList } = newInventtory;
        const index = inventoryList.findIndex(inv => inv.inventoryId === inventoryId);

        if (inventoryQty === 0) {
            // 找到並移除 inventoryList 中的該項目
            const itemToRemove = inventoryList.find(inv => inv.inventoryId === inventoryId);
            const updatedInventoryList = inventoryList.filter(inv => inv.inventoryId !== inventoryId);
            setInventoryList(updatedInventoryList);

            // 將該項添加至 historyList
            if (itemToRemove) {
                const historyItem = { ...itemToRemove, };
                const updatedHistoryList = [...historyList, historyItem];

                // 按照 editDate 排序 historyList
                updatedHistoryList.sort((a, b) => new Date(b.editDate).getTime() - new Date(a.editDate).getTime());
                setHistoryList(updatedHistoryList);
            }
        } else {
            if (index !== -1) {
                // 更新 inventoryList
                const updatedInventoryList = [...inventoryList];
                updatedInventoryList[index] = {
                    ...updatedInventoryList[index],
                    inventoryQty: inventoryQty,
                    operRecordList: operRecordList
                };
                setInventoryList(updatedInventoryList);
                fetchTotalInventoryData();
            }
        }
        setEditRecordIndex(null);
    }

    const saveEditRecord = (info: EhsChemicalOperRecord) => {
        const { chemRecordId, operType, operDate: originalOperDate, operQty: originalOperQty } = info;
        const { operDate: editOperDate, operQty: editOperQty } = editRecordInfo;

        // 檢查是否有實際修改內容
        const isDateUnchanged = new Date(originalOperDate!).getTime() === new Date(editOperDate!).getTime();
        const isQtyUnchanged = originalOperQty === editOperQty;

        // 如果沒有修改，直接關閉編輯模式
        if (isDateUnchanged && isQtyUnchanged) {
            setEditRecordInfo(initEditChemicalOperRecord);
            setEditRecordIndex(null);
            return;
        }

        const errorMessage = validateEditRecord(editOperDate, editOperQty, t);
        if (errorMessage) {
            showWarnToast(errorMessage);
            return;
        }

        ChemicalAPI.modifyChemicalOperation({
            ...loginUser!,
            chemicalOperRecord: {
                ...initEhsChemicalOperRecord,
                chemRecordId: chemRecordId,
                inventoryId: inventoryId,
                operType: operType,
                operDate: editOperDate,
                operQty: editOperQty,
            }
        }).then((result) => {
            if (isApiCallSuccess(result)) {
                showSuccessToast(t('message.success'));
                updateOperNewInventoryList(result.results);
            } else {
                errorMsg(result.message);
            }
        })
        setEditRecordInfo(initEditChemicalOperRecord);
        setEditRecordIndex(null);
    }

    const validateEditRecord = (editOperDate: Date | null, editOperQty: number | undefined, t: (key: string) => string): string | null => {
        if (editOperDate === null) {
            return t('message.chemical.no_operation_date');
        }
        if (editOperQty === undefined) {
            return t('message.chemical.no_operation_weight');
        }
        if (editOperQty === 0) {
            return t('message.chemical.zero_operation_weight');
        }
        return null;
    };

    const handleRefreshCcbData = () => {
        setCcbDataRefreshCounter(prev => prev + 1); // 每次遞增，觸發子組件重新獲取數據
    };

    const isOpen = Boolean(isHistory) || openIndex === idx;
    return (<StyledInventoryItem $collapsed={!isOpen} className={`inventory-item accordion-item border-0 mt-3`} key={'inventory_' + inventoryId} onClick={(event) => handleAccordionDivClick(event, idx)} >
        {
            <Dialog
                onClick={(e) => {
                    e.stopPropagation();
                }}
                content={
                    <SdsModify
                        inventoryId={inventoryId}
                        purchaseDetailId={purchaseDetailId}
                        nowDate={nowDate}
                        sdsExpDate={sdsExpDate}
                        onClose={() => {
                            setSdsEditDialog(null);
                        }}
                        onActionSuccess={(newSdsFile, newSdsExpDate, newInventoryStatus) => {
                            setSdsEditDialog(null);
                            showSuccessToast(t('message.success'));
                            if (newSdsFile) {
                                setSdsFile(newSdsFile);
                            }
                            if (newSdsExpDate) {
                                updateSdsExpDate(inventoryId, newSdsExpDate, sdsExpDate);
                            }
                            if (newInventoryStatus) {
                                updateInventoryStatus(inventoryId, newInventoryStatus, inventoryStatus);
                            }
                        }}
                        setLoadingBlock={setLoadingBlock}
                        sdsFile={sdsFile || null}
                    />}
                show={sdsEditDialog !== null}
            />
        }
        {
            <Dialog
                onClick={(e) => {
                    e.stopPropagation();
                }}
                content={
                    <ChemicalDetailDialog
                        inventory={inventory}
                        configMap={configMap}
                        sdsFile={sdsFile || null}
                        onClose={() => setShowDetailDialog(null)}
                    />
                }
                show={showDetailDialog !== null}
            />
        }
        {
            <Dialog
                content={
                    <CcbViewDialog
                        onClose={() => {
                            setShowCCBView(null);
                        }}
                        setLoadingBlock={setLoadingBlock}
                        mode={showCCBView}
                        linkType={ChemicalCcbLinkType.OPER}
                        linkId={ccbLinkId!}
                        forceDataRefresh={ccbDataRefreshCounter}
                    />
                }
                show={showCCBView !== null}
            />
        }
        {/* <!-- 顯示內容 --> */}
        <div className="accordion-header" id={"heading" + inventoryId}>
            <div
                className="accordion-button d-block px-3 py-10px pointer-cursor text-dark border border-lime rounded-3" data-bs-toggle="collapse">
                {isSigning && (
                    <div className="text-danger mt-1">
                        <small>{t('text.signoff.under_review')}</small>
                    </div>
                )}
                {isPurchasing && (
                    <div className="text-danger mt-1">
                        <small>{t('text.purchasing')}</small>
                    </div>
                )}
                {isOperating && <div className="form-check" onClick={(e) => e.stopPropagation()}>
                    <input
                        className="form-check-input mt-1"
                        type="checkbox"
                        id={"checkbox" + inventoryId}
                        checked={batchCheckedMap && batchCheckedMap[inventoryId] !== undefined}
                        onClick={(e) => e.stopPropagation()}
                        onChange={(e) => handleSingleCheck(e, inventoryId, inventory)}
                    />
                    <label
                        className="form-check-label fs-5"
                        htmlFor={"checkbox" + inventoryId}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {t('text.chemical.batch_operation')}
                    </label>
                </div>}
                <table className="table mt-2 text-center fs-5 align-middle info-table">
                    <thead className='bg-lime-200'>
                        <tr className="align-middle">
                            <th className="item-width-10">{t('table.title.barcode')}</th>
                            <th className="item-width-7">{t('table.title.concentration')}(%)</th>
                            <th className="item-width-7">{t('table.title.inventory_quantity')}(kg)</th>
                            <th className="item-width-15">{t('table.title.arrival_date_oper')} <br /> {t('table.title.vendor')}</th>
                            <th className="item-width-10">{t('table.title.buyer')}</th>
                            <th className="item-width-8">{t('table.title.vendor')}<br />{t('table.title.sds_file')}</th>
                            <th className="item-width-10">{t('table.title.other')}<br />{t('table.title.sds_file')}</th>
                            <th className="item-width-7">{t('table.title.storage_location')}</th>
                            <th className="item-width-15">{t('table.title.chemical.note')}</th>
                            <th className="item-width-7">{t('table.title.chemical.information')}</th>
                        </tr>
                    </thead>
                    <tbody className="bg-white">
                        <tr>
                            <td data-title={t('table.title.barcode')}>{barcode}</td>
                            <td data-title={t('table.title.concentration')}>{concentration}</td>
                            <td data-title={t('table.title.inventory_quantity')}>{inventoryQty}</td>
                            <td data-title={`${t('table.title.arrival_date_oper')}\n${t('table.title.vendor')}`}>{getFormatDateSlash(inspDate)}{inspDate && <br />} {manufacturerName}</td>
                            <td data-title={t('table.title.buyer')}>{createId}<br />{creatorName}<AgentNameTag agentId={createId} agentName={creatorName} withBreak /></td>
                            <td data-title={`${t('table.title.vendor')}\n${t('table.title.sds_file')}`}>{productSdsId && <DownloadButton fileName={productSdsName} onDownload={() => fetchSdsFile(productSdsId, loginUser)} />}</td>
                            <td data-title={`${t('table.title.other')}\n${t('table.title.sds_file')}`}>
                                {fileContentBase64 && <DownloadButton content={fileContentBase64} fileName={fileName} />}
                                {sdsExpDate && <br />}{sdsExpDate && <span>{getFormatDateSlash(sdsExpDate)}</span>}
                                {isOperating && <>
                                    <br />
                                    <button type="button" className="btn btn-warning"
                                        onClick={(event) => {
                                            event.stopPropagation();
                                            setSdsEditDialog(true);
                                        }}>
                                        <i className="fas fa-pencil fa-lg me-1" />{t('button.edit')}
                                    </button>
                                </>}
                            </td>
                            <td data-title={t('table.title.storage_location')}>
                                <EditableLocationCell canEdit={isOperating} initialValue={inventoryLocation} storageLocationConfigs={storageLocationConfigs}
                                    updateLocation={(location: string) => updateLocation(inventoryId, location, inventoryLocation)} />
                            </td>
                            <td data-title={t('table.title.chemical.note')}>
                                <EditableNoteCell canEdit={isOperating} initialValue={inventoryNote} updateNote={(note: string) => updateNote(inventoryId, note?.trim(), inventoryNote)} />
                            </td>
                            <td data-title={t('table.title.chemical.information')}>
                                <button type="button" className="btn btn-secondary m-1" onClick={(e) => {
                                    e.stopPropagation();
                                    setShowDetailDialog(true);
                                }}>
                                    <i className="fas fa-file-alt me-1" />{t('button.detail')}
                                </button>
                                <button className="btn btn-secondary m-1" title={t('text.chemical.ccb_view')}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        setCcbLinkId(inventoryId);
                                        setShowCCBView(true);
                                    }}>
                                    <i className="fas fa-file-alt me-1" />{t('text.chemical.ccb_view')}
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                {/* <!-- 隱藏內容 運作紀錄 --> */}
                {(isHistory || openIndex === idx) && !isArrayEmpty(operRecordList) &&
                    <div className="accordion-collapse"
                        data-bs-parent="#accordion" onClick={(event) => { event.stopPropagation(); }}>
                        <div className="accordion-body m-0 p-0 ">
                            <div className="oper-section">
                                {/* <!-- 運作 --> */}
                                {isOperating && isEnabled && (
                                    <OperActionRow
                                        inventory={inventory}
                                        nowDate={nowDate}
                                        defStopOperDate={defStopOperDate}
                                        operItemConfigs={operItemConfigs}
                                        updateOperation={(operateInfo) => { handleUpdateOperation(operateInfo, inventory) }}
                                        setChemStopDate={setChemStopDate}
                                        phaseState={phaseState}
                                        configMap={configMap}
                                    />
                                )}
                                {/* <!-- 運作紀錄 --> */}
                                <div className="table-responsive bg-white">
                                    <table
                                        className="table table-hover text-center align-middle fs-5 m-0 p-0 rc-table">
                                        <thead className='oper-thead'>
                                            <tr>
                                                <th className="text-center item-width-12">{t('table.title.chemical.operation_date')}</th>
                                                <th className="text-start item-width-10">{t('table.title.chemical.operation_item')}</th>
                                                <th className="text-end item-width-10">{t('table.title.chemical.weight_operation_before')}(kg)</th>
                                                <th className="text-end item-width-10">{t('table.title.chemical.weight_operation')}(kg)</th>
                                                <th className="text-end item-width-10">{t('table.title.chemical.weight_operation_after')}(kg)</th>
                                                <th className="text-start item-width-12">{t('table.title.chemical.operator')}</th>
                                                <th className="text-start item-width-16">{t('table.title.note')}</th>
                                                {isOperating && <th className="text-start item-width-10">{t('table.title.action')}</th>}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {operRecordList.map((item, idx) => {
                                                const { operDate, operType, inventoryPre, operQty, inventory,
                                                    chemRecordStatus, operatorId, operatorName, operatorAgentId, operatorAgentName, chemRecordNote } = item;
                                                const isEnabled = chemRecordStatus === 1;
                                                const isSignoff = chemRecordStatus === 2;
                                                const canModifyOperType = operType < 5 || operType > 8;
                                                const isDelOperType = operType !== OperateType.PURCHASE && canModifyOperType;
                                                const isFirstRecord = idx === 0;
                                                const isEditMode = editRecordIndex === idx;
                                                const showEditOperRecordButton = isEnabled && canModifyOperType &&
                                                    (!isFirstRecord || isEditFirstChemOperRecordRole) && isLabCanOperation;

                                                return (
                                                    <tr key={idx}>
                                                        <td data-title={t('table.title.chemical.operation_date')}>{!isFirstRecord && isEditMode ?
                                                            <InputDate className="form-control mt-1" minDate={stopOperDate ? getFormatDateSlash(stopOperDate) : undefined} maxDate={getFormatDateSlash(nowDate)}
                                                                defaultValue={getFormatDateSlash(operDate)}
                                                                onChange={(date) => onChangeEditRecordInfo({ operDate: date ? getFormatDateDash(date) : null })} />
                                                            : getFormatDateSlash(operDate)}</td>
                                                        <td data-title={t('table.title.chemical.operation_item')} className="text-start">{configMap[operType]?.configName}</td>
                                                        <td data-title={t('table.title.chemical.weight_operation_before')} className="text-end">{inventoryPre}</td>
                                                        <td data-title={t('table.title.chemical.weight_operation')} className="text-end">{operQty !== 0 && isEditMode ?
                                                            <InputNumFloat className="form-control  mt-1" minValue={CHEMICAL_WEIGHT_MIN} maxValue={CHEMICAL_WEIGHT_MAX} maxLength={7 + CHEMICAL_WEIGHT_DECIMAL_PLACES}
                                                                decimalPlaces={CHEMICAL_WEIGHT_DECIMAL_PLACES} onBlur={(num) => {
                                                                    onChangeEditRecordInfo({ operQty: num });
                                                                }}
                                                                value={operQty}
                                                            /> : operQty}</td>
                                                        <td data-title={t('table.title.chemical.weight_operation_after')} className="text-end">{inventory}</td>
                                                        {/* <td data-title={t('table.title.status')}>{chemRecordStatus}</td> */}
                                                        <td data-title={t('table.title.chemical.operator')} className="text-start">{operatorId}<br />{operatorName}
                                                            <AgentNameTag agentId={operatorAgentId} agentName={operatorAgentName} withBreak />
                                                        </td>
                                                        <td data-title={t('table.title.note')} className="text-start">
                                                            <label><TextWithLineBreaks text={chemRecordNote} /></label>
                                                            {isSignoff && <label className="text-danger mt-1">{t('text.signoff.under_review')}</label>}
                                                            {isPurchasing && <label className="text-danger mt-1">{t('text.purchasing')}</label>}
                                                        </td>
                                                        {isOperating && <td data-title={t('table.title.action')} className="text-start">
                                                            {showEditOperRecordButton && (isEditMode ?
                                                                <button type="button" className="btn btn-success mt-1" onClick={() => {
                                                                    saveEditRecord(item);
                                                                }}>
                                                                    <i className="fas fa-floppy-disk me-1" />{t('button.save')}
                                                                </button>
                                                                : <button type="button" className="btn btn-warning mt-1" onClick={() => {
                                                                    setEditRecordIndex(idx);
                                                                    onChangeEditRecordInfo({ operDate: getFormatDateDash(operDate), operQty: operQty })
                                                                }}>
                                                                    <i className="fas fa-pen me-1" />{t('button.edit')}
                                                                </button>)}
                                                            {isEnabled && isDelOperType && !isFirstRecord && isLabCanOperation && <button type="button" className="btn btn-danger mt-1 ms-1" onClick={() => { handleDeleteOperRecord(item) }}><i className="fas fa-trash-can me-1" />{t('button.delete')}</button>}
                                                        </td>}
                                                    </tr>
                                                );
                                            })}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>}
            </div>
        </div>
    </StyledInventoryItem>
    );
};


const StyledInventoryItem = styled.div<{ $collapsed: boolean }>`
  transition: filter 120ms ease, box-shadow 120ms ease;
  ${({ $collapsed }) => $collapsed ? css`
    /* 未展開：整卡微灰且暗，維持原本設計結構與色系，只做視覺淡化 */
    filter: grayscale(80%) brightness(0.96);
  ` : css`
    /* 展開：維持原設計，不做任何覆寫 */
    filter: none;
  `}

  /* 視覺分區：運作(表單) 與 運作紀錄(表格) */
  .oper-section {
    border: 1px solid #eaeaea;
    border-radius: 20px;
    background: #fff;
  }

  .oper-section__header {
    display: flex;
    align-items: center;
    margin: 6px 6px 5px;
  }

  .oper-section__title {
    display: inline-flex;
    align-items: center;
    font-weight: 600;
    font-size: 0.95rem;
    color: #3d5f2d; /* 深一點的綠灰 */
    background: #f0f6ec; /* 很淺的綠灰底 */
    border: 1px solid #e2efe0;
    padding: 4px 10px;
    border-radius: 8px;
  }

  /* 運作紀錄 thead 新色：低飽和綠灰，與上方 lime 做出區隔但不花俏 */
  .oper-thead {
    background: linear-gradient(180deg, #eaf4e6 0%, #e2efe0 100%);
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
`;

export default InventoryItem;