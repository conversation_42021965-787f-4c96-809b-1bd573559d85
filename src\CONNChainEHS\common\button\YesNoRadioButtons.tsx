import React from 'react';
import { useTranslation } from 'react-i18next';

// 定義 props 的型別
interface YesNoRadioButtonsProps {
    isChecked: boolean;
    onSelectionChange?: (value: number) => void; // 通用回調函數
    idPrefix: string; // 唯一的前綴，確保每次元件渲染時都有不同的 id 和 name
    disabled?: boolean;
}

const YesNoRadioButtons: React.FC<YesNoRadioButtonsProps> = ({
    isChecked,
    onSelectionChange,
    idPrefix,
    disabled,
}) => {
    const { t } = useTranslation();

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = parseInt(e.target.value);

        // 如果有提供通用的選項處理邏輯，則執行
        if (onSelectionChange) {
            onSelectionChange(newValue);
        }
    };

    return (
        <div>
            <div className="form-check form-check-inline mt-2">
                <input
                    className="form-check-input"
                    type="radio"
                    name={`${idPrefix}_yesNoOption`}  // 動態生成 name
                    id={`${idPrefix}_yesOption`}     // 動態生成 id
                    value="1"
                    data-parsley-mincheck="1"
                    checked={isChecked}
                    disabled={disabled}
                    onChange={handleChange}
                />
                <label className="form-check-label" htmlFor={`${idPrefix}_yesOption`}>{t('text.option.yes')}</label>
            </div>
            <div className="form-check form-check-inline">
                <input
                    className="form-check-input"
                    type="radio"
                    name={`${idPrefix}_yesNoOption`}  // 動態生成 name
                    id={`${idPrefix}_noOption`}      // 動態生成 id
                    value="0"
                    data-parsley-mincheck="1"
                    checked={!isChecked}
                    disabled={disabled}
                    onChange={handleChange}
                />
                <label className="form-check-label" htmlFor={`${idPrefix}_noOption`}>{t('text.option.no')}</label>
            </div>
        </div>
    );
};

export default YesNoRadioButtons;
