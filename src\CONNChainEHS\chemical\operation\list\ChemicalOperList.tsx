import BlockUi from "@availity/block-ui";
import { ChemicalAPI } from "api/chemicalAPI";
import { ConfigAPI } from "api/configAPI";
import { OptionAPI } from "api/optionAPI";
import { OrgAPI } from "api/orgAPI";
import { SelectListAPI } from "api/selectListAPI";
import BlockuiMsg from "ehs/common/BlockuiMsg";
import ChemicalClassificationBadge from "ehs/common/chemical/ChemicalClassificationBadge";
import GhsImage from "ehs/common/GhsImage";
import Loader from "ehs/common/Loader";
import NamesSplitFragment from "ehs/common/NamesSplitFragment";
import NoDataRow from "ehs/common/NoDataRow";
import SearchConditionButton from "ehs/common/search/SearchConditionButton";
import SearchDivBuilding from "ehs/common/search/SearchDivBuilding";
import SearchDivLab from "ehs/common/search/SearchDivLab";
import SearchDivUnit from "ehs/common/search/SearchDivUnit";
import SearchDropdownSwitch from "ehs/common/search/SearchDropdownSwitch";
import ShowMoreInfo from "ehs/common/ShowMoreInfo";
import SortIcon from "ehs/common/SortIcon";
import { errorMsg } from "ehs/common/SwalMsg";
import { CONFIG_TYPE_CHEM, CONFIG_TYPE_CONCEN_TYPE, CONFIG_TYPE_CONCERNED, CONFIG_TYPE_CTRL_CONCEN_TYPE, CONFIG_TYPE_GHS_IMG, CONFIG_TYPE_MIX_CONCEN_TYPE, CONFIG_TYPE_ORGANIC_SOLVENT, CONFIG_TYPE_PRIORITY, CONFIG_TYPE_PUBLIC_HAZARD, CONFIG_TYPE_SPECIFIC_CHEM, CONFIG_TYPE_STATE, CONFIG_TYPE_TOXIC, CONFIG_TYPE_WORK_ENV_MONITOR, CONFIG_TYPE_WORK_ENV_MONITOR_FREQ, OPTION_CHEM_REPORT_MONTH_EARLIEST, OPTION_CHEM_REPORT_MONTH_LONG, OPTION_CHEM_REPORT_MONTH_START_DF, OPTION_SHOW_SEARCH_ADVANCE, OPTION_SHOW_SEARCH_BUILDING, OPTION_SHOW_SEARCH_UNIT, ORG_SPLIT_FLAG } from "ehs/constant/constants";
import { InventoryStatus } from "ehs/enums/InventoryStatus";
import { OperDetailPageMode } from "ehs/enums/OperDetailPageMode";
import { SearchCondition } from "ehs/enums/SearchCondition";
import useLoginUser from "ehs/hooks/useLoginUser";
import useServerNowDate from "ehs/hooks/useServerNowDate";
import Breadcrumbs from "ehs/layout/Breadcrumbs";
import Footer from "ehs/layout/Footer";
import PageSizeSelector from "ehs/layout/PageSizeSelector";
import Pagination from "ehs/layout/Pagination";
import { EhsChemicalInventory } from "ehs/models/EhsChemicalInventory";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsOptions } from "ehs/models/EhsOptions";
import { EhsOrgLevel } from "ehs/models/EhsOrgLevel";
import { PageInfo, initPageInfo } from "ehs/models/PageInfo";
import { ReactSelectOption } from "ehs/models/ReactSelectOption";
import { SelectSearch } from "ehs/models/SearchLabInfo";
import { SelectItem } from "ehs/models/SelectItem";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { checkTimeoutAction, getBasicLoginUserInfo } from "ehs/utils/authUtil";
import { getShowChemCon, sortCategoryListByConfigSeq } from "ehs/utils/chemicalUtil";
import { ExcelColumnConfig, generateExcel } from "ehs/utils/excelUtil";
import { getLabTextObj, getUserUnitTextObj, notEnglishLang, splitChemNameListByLang } from "ehs/utils/langUtil";
import { isApiCallSuccess } from "ehs/utils/resultUtil";
import { getFormatDateDash, getSplitUnitByIndex, isContainEnglish } from "ehs/utils/stringUtil";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import Select from "react-select";
import styled from "styled-components";
import OperDetailButton from "./OperDetailButton";

interface ConditionType {
  keyword: string;
  currentPage: number;
  pageSize: number;
  orgLevel: number | null;
  areaId: string;
  orgType: string;
  unit: { [key: number]: string };
  queryOrgId: string;
  buildId: string;
  floor: string;
  houseNum: string;
  queryLabIds: string[];
  chemConId: string;
  phaseState: string;
  toxicClass: string;
  svhcClass: string;
  priorityClass: string;
  organicClass: string;
  specificChemClass: string;
  envMonitorClass: string;
  envMonitorFreq: string;
  pubhazClass: string;
  casNo: string;
  operStartDate: string | undefined;
  operEndDate: string | undefined;
  inventoryStatus: number;
}

function ChemicalOperList() {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const allOptionValue = "all"
  const allSelectOption = { value: allOptionValue, label: t('text.all') }
  const { loginUser } = useLoginUser();
  const [nowDate] = useServerNowDate(loginUser);
  const { itemText } = getUserUnitTextObj(loginUser, t);
  const [isLinkSelect, setIsLinkSelect] = useState<boolean>(true);
  const [optionsMap, setOptionsMap] = useState<{ [key: string]: EhsOptions }>({});
  const [showUnitCondition, setShowUnitCondition] = useState<boolean>(false);
  const [showBuildCondition, setShowBuildCondition] = useState<boolean>(false);
  const [showAdvCondition, setShowAdvCondition] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingBlock, setLoadingBlock] = useState<boolean>(false);
  const [inventoryList, setInventoryList] = useState<EhsChemicalInventory[]>([]);
  const [searchSelectList, setSearchSelectList] = useState<SelectSearch[]>([]);
  const [chemClassOptions, setChemClassOptions] = useState<any[]>([]);
  const [orgLevelList, setOrgLevelList] = useState<EhsOrgLevel[]>([]);
  const [queryOrgIdList, setQueryOrgIdList] = useState<string[]>([]);
  const [configSelectOptions, setConfigSelectOptions] = useState<{ [key: string]: EhsConfigParam[] }>({});
  const [selectedChemClasses, setSelectedChemClasses] = useState<ReactSelectOption[]>([]);
  const [configMap, setConfigMap] = useState<{ [configId: string]: EhsConfigParam }>({});// config對應
  const [ctrlNoOptionList, setCtrlNoOptionList] = useState<SelectItem[]>([]); // 列管編號選項onfig對應
  const [svhcCtrlNoOptionList, setSvhcCtrlNoOptionList] = useState<SelectItem[]>([]); // 關注性編號選項onfig對應
  const [selectedCtrlNoOption, setSelectedCtrlNoOption] = useState<ReactSelectOption>(allSelectOption);
  const [selectedSvhcCtrlNoOption, setSelectedSvhcCtrlNoOption] = useState<ReactSelectOption>(allSelectOption);
  const [extendMap, setExtendMap] = useState<{ [index: number]: boolean }>({});// 下層展開
  const [queryOrgNameMap, setQueryOrgNameMap] = useState<{ [orgId: string]: string }>({});// 單位名稱對應
  const initCondition = {
    keyword: "",
    currentPage: 1,
    pageSize: 50,
    orgLevel: null,
    areaId: "",
    orgType: "",
    unit: {},
    queryOrgId: "",
    buildId: "",
    floor: "",
    houseNum: "",
    queryLabIds: [],
    chemConId: "",
    phaseState: "",
    toxicClass: "",
    svhcClass: "",
    priorityClass: "",
    organicClass: "",
    specificChemClass: "",
    envMonitorClass: "",
    envMonitorFreq: "",
    pubhazClass: "",
    casNo: "",
    operStartDate: nowDate ? getFormatDateDash(new Date(nowDate.getFullYear(), nowDate.getMonth() - 6, nowDate.getDate())) : undefined,
    operEndDate: nowDate ? getFormatDateDash(nowDate) : undefined,
    inventoryStatus: 1,
  };
  const [condition, setCondition] = useState<ConditionType>(initCondition);
  const [pageInfo, setPageInfo] = useState<PageInfo>(initPageInfo);
  const { orgLevel } = condition;
  const showAdvConditionMarginTop = showAdvCondition ? 'mt-3' : '';

  // 使用 ref 來管理所有暫存的搜尋條件
  const searchRef = useRef({
    casNo: "",
    phaseState: "",
    toxicClass: "",
    concernedClass: "",
    priorityClass: "",
    organicClass: "",
    specificChemClass: "",
    envMonitorClass: "",
    envMonitorFreq: "",
    pubhazClass: "",
    operStartDate: condition.operStartDate,
    operEndDate: condition.operEndDate,
    inventoryStatus: condition.inventoryStatus
  });
  const selectedChemClassesConfig = selectedChemClasses.map((option) => configMap[option.value]);
  const phaseStateOptions = configSelectOptions[CONFIG_TYPE_STATE] || [];
  const toxicClassOptions = configSelectOptions[CONFIG_TYPE_TOXIC] || [];
  const concernedClassOptions = configSelectOptions[CONFIG_TYPE_CONCERNED] || [];
  const priorityClassOptions = configSelectOptions[CONFIG_TYPE_PRIORITY] || [];
  const organicClassOptions = configSelectOptions[CONFIG_TYPE_ORGANIC_SOLVENT] || [];
  const specificChemClassOptions = configSelectOptions[CONFIG_TYPE_SPECIFIC_CHEM] || [];
  const envMonitorClassOptions = configSelectOptions[CONFIG_TYPE_WORK_ENV_MONITOR] || [];
  const envMonitorFreqOptions = configSelectOptions[CONFIG_TYPE_WORK_ENV_MONITOR_FREQ] || [];
  const pubhazClassOptions = configSelectOptions[CONFIG_TYPE_PUBLIC_HAZARD] || [];
  const chemicalClassTypes = [
    { type: CONFIG_TYPE_TOXIC, key: 'hasSelectedToxicClass' },
    { type: CONFIG_TYPE_CONCERNED, key: 'hasSelectedConcernedClass' },
    { type: CONFIG_TYPE_PRIORITY, key: 'hasSelectedPriorityClass' },
    { type: CONFIG_TYPE_ORGANIC_SOLVENT, key: 'hasSelectedOrganicClass' },
    { type: CONFIG_TYPE_SPECIFIC_CHEM, key: 'hasSelectedSpecificChemClass' },
    { type: CONFIG_TYPE_WORK_ENV_MONITOR, key: 'hasSelectedEnvMonitorClass' },
    { type: CONFIG_TYPE_PUBLIC_HAZARD, key: 'hasSelectedPubhazClass' },
  ];
  const selectedClassFlags = chemicalClassTypes.reduce<Record<string, boolean>>((acc, { type, key }) => {
    acc[key] = selectedChemClassesConfig.some((config) => config && config.configValue === type);
    return acc;
  }, {});
  const keyToTypeMap = chemicalClassTypes.reduce<Record<string, string>>((acc, { type, key }) => {
    acc[key] = type;
    return acc;
  }, {});


  const { hasSelectedToxicClass, hasSelectedConcernedClass, hasSelectedPriorityClass, hasSelectedOrganicClass, hasSelectedSpecificChemClass,
    hasSelectedEnvMonitorClass, hasSelectedPubhazClass } = selectedClassFlags;

  const createDeselectClearClassHandler = (refKey: string) => ({
    onSelect: () => { /* 可以在這裡添加選擇時的邏輯 */ },
    onDeselect: () => {
      (searchRef.current as any)[refKey] = "";
    }
  });

  const classHandlers = useMemo(() => ({
    [CONFIG_TYPE_TOXIC]: {
      onSelect: () => {
        if (loginUser && isArrayEmpty(ctrlNoOptionList)) {
          fetchCtrlNoList();
        }
      },
      onDeselect: () => {
        searchRef.current.toxicClass = "";
        setSelectedCtrlNoOption(allSelectOption);
      }
    },
    [CONFIG_TYPE_CONCERNED]: {
      onSelect: () => {
        if (loginUser && isArrayEmpty(svhcCtrlNoOptionList)) {
          fetchCtrlNoList();
        }
      },
      onDeselect: () => {
        searchRef.current.concernedClass = "";
        setSelectedSvhcCtrlNoOption(allSelectOption);
      }
    },
    [CONFIG_TYPE_PRIORITY]: createDeselectClearClassHandler('priorityClass'),
    [CONFIG_TYPE_ORGANIC_SOLVENT]: createDeselectClearClassHandler('organicClass'),
    [CONFIG_TYPE_SPECIFIC_CHEM]: createDeselectClearClassHandler('specificChemClass'),
    [CONFIG_TYPE_WORK_ENV_MONITOR]: createDeselectClearClassHandler('envMonitorClass'),
    [CONFIG_TYPE_PUBLIC_HAZARD]: createDeselectClearClassHandler('pubhazClass'),
  }), [loginUser, ctrlNoOptionList, svhcCtrlNoOptionList]);

  useEffect(() => {
    Object.entries(selectedClassFlags).forEach(([key, isSelected]) => {
      const type = keyToTypeMap[key];
      const handler = classHandlers[type as keyof typeof classHandlers];

      if (handler) {
        if (isSelected) {
          handler.onSelect();
        } else {
          handler.onDeselect();
        }
      }
    });
  }, [selectedClassFlags, classHandlers]);
  const showOrgLevelName = orgLevel === null ? "" : orgLevelList.find((data) => data.orglvLevel === (orgLevel + 1))?.orglvName;
  const nowLastQueryOrgId = queryOrgIdList[queryOrgIdList.length - 1];
  const nowOrgName = queryOrgNameMap[nowLastQueryOrgId];

  useEffect(() => {
    if (loginUser) {
      fetchData();
    }
  }, [loginUser, condition.currentPage, condition.pageSize, condition.orgLevel, i18n.language]);

  useEffect(() => {
    if (loginUser) {
      fetchOption();
    }
  }, [loginUser])

  useEffect(() => {
    if (loginUser) {
      fetchSelectListData();
      fetchConfig();
      fetchOrgLevelData();
    }
  }, [loginUser, i18n.language]);

  // 根據選項設定是否顯示搜尋條件
  useEffect(() => {
    if (!isArrayEmpty(Object.keys(optionsMap))) {
      const unitOption = optionsMap[OPTION_SHOW_SEARCH_UNIT];
      const buildOption = optionsMap[OPTION_SHOW_SEARCH_BUILDING];
      const advOption = optionsMap[OPTION_SHOW_SEARCH_ADVANCE];

      setShowUnitCondition(unitOption?.optionEnabled);
      setShowBuildCondition(buildOption?.optionEnabled);
      setShowAdvCondition(advOption?.optionEnabled);
    }
  }, [optionsMap]);

  const fetchData = (searchCondition = condition) => {
    setLoading(true)
    const { queryLabIds } = searchCondition;
    if (queryLabIds) {
      //單選時 統一換成array做查詢
      searchCondition.queryLabIds = Array.isArray(queryLabIds) ? queryLabIds : [queryLabIds];
    }

    fetchChemicalInventorySum(searchCondition);
  };

  const fetchChemicalInventorySum = (searchCondition = condition) => {
    ChemicalAPI.getChemicalInventorySum({
      ...getBasicLoginUserInfo(loginUser),
      ...searchCondition,
    }).then((result) => {
      handleFetchDataSuccess(result);
    }).catch(err => {
      checkTimeoutAction(err, navigate, t);
      debugger;
      console.error(err)
    });
  }

  const handleFetchDataSuccess = (result: any) => {
    if (isApiCallSuccess(result)) {
      setPageInfo(result.pageinfo);
      setInventoryList(result.results);
      setLoading(false)
    } else {
      if (result && result.message) {
        errorMsg(result.message)
      } else {
        errorMsg(t('text.error'))
      }
    }
  }

  const fetchSelectListData = () => {
    SelectListAPI.getSelectLabSearchView({
      ...getBasicLoginUserInfo(loginUser),
    }).then((result) => {
      if (result) {
        setSearchSelectList(result.results);
      }
    });
  };

  const fetchConfig = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser),
      configTypeList: [CONFIG_TYPE_CHEM, CONFIG_TYPE_STATE, CONFIG_TYPE_CTRL_CONCEN_TYPE, CONFIG_TYPE_CONCEN_TYPE, CONFIG_TYPE_GHS_IMG,
        CONFIG_TYPE_MIX_CONCEN_TYPE, CONFIG_TYPE_TOXIC, CONFIG_TYPE_CONCERNED, CONFIG_TYPE_PRIORITY, CONFIG_TYPE_ORGANIC_SOLVENT,
        CONFIG_TYPE_SPECIFIC_CHEM, CONFIG_TYPE_WORK_ENV_MONITOR, CONFIG_TYPE_WORK_ENV_MONITOR_FREQ, CONFIG_TYPE_PUBLIC_HAZARD]
    }).then((result) => {
      if (result) {
        const configMap = new Map<string, EhsConfigParam>();
        const options: { value: string; label: string }[] = [];
        const configSelectOptions: { [key: string]: EhsConfigParam[] } = {};

        result.results.forEach((config: EhsConfigParam) => {
          if (config.configType === CONFIG_TYPE_CTRL_CONCEN_TYPE) {
            configMap.set(String(config.configIvalue), config);
          } else {
            configMap.set(config.configId, config);
            if (config.configType === CONFIG_TYPE_CHEM) {
              options.push({
                value: config.configId,
                label: config.configName
              });
            } else {
              if (!configSelectOptions[config.configType]) {
                configSelectOptions[config.configType] = [];
              }
              configSelectOptions[config.configType].push(config);
            }
          }
        });

        setConfigMap(Object.fromEntries(configMap)); // 將 Map 轉換為物件
        setChemClassOptions(options);
        setConfigSelectOptions(configSelectOptions);
      }
    });
  };

  const fetchCtrlNoList = () => {
    SelectListAPI.getSelectChemicalCtrlnoList({
      ...getBasicLoginUserInfo(loginUser),
    }).then(result => {
      if (isApiCallSuccess(result)) {
        // 將 "全部" 選項添加到結果中
        const { updatedSvhcResults, updatedCtrlNoResults } = result.results.reduce((acc: any, item: SelectItem) => {
          if (isContainEnglish(item.label)) {
            acc.updatedSvhcResults.push(item);
          } else {
            acc.updatedCtrlNoResults.push(item);
          }
          return acc;
        }, { updatedSvhcResults: [], updatedCtrlNoResults: [] });

        setCtrlNoOptionList([allSelectOption, ...updatedCtrlNoResults]);
        setSvhcCtrlNoOptionList([allSelectOption, ...updatedSvhcResults]);
      }
    })
  }

  const fetchOption = () => {
    // 獲取搜尋條件選項配置，用於決定是否預設顯示搜尋條件
    const optionIds = [OPTION_SHOW_SEARCH_UNIT, OPTION_SHOW_SEARCH_BUILDING, OPTION_SHOW_SEARCH_ADVANCE];
    OptionAPI.getOptionListByIds({
      ...getBasicLoginUserInfo(loginUser),
      optionIdList: optionIds
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const options = result.results;
        const newOptionsMap: {
          [key: string]: EhsOptions;
        } = {};
        options.forEach((item: any) => {
          if (optionIds.includes(item.optionId)) {
            newOptionsMap[item.optionId] = item;
          }
        });
        setOptionsMap(newOptionsMap);
      }
    });
  };

  const fetchOrgLevelData = () => {
    OrgAPI.getOrgLevelList({
      ...getBasicLoginUserInfo(loginUser)!,
    }).then(result => {
      if (isApiCallSuccess(result)) {
        setOrgLevelList(result.results)
      }
    })
  }

  const getShowList = () => {
    return inventoryList || [];
  };

  const backPrevQueryUnit = () => {
    const prevOrgLevel = (orgLevel === null || orgLevel === 0) ? null : orgLevel - 1;
    setExtendMap({});
    const newQueryOrgIdList = queryOrgIdList.slice(0, -1);
    const newLastQueryOrgId = newQueryOrgIdList[newQueryOrgIdList.length - 1];
    setQueryOrgIdList(newQueryOrgIdList);
    setCondition({
      ...condition,
      orgLevel: prevOrgLevel,
      queryOrgId: newLastQueryOrgId,
      chemConId: prevOrgLevel === null ? '' : condition.chemConId,
      phaseState: prevOrgLevel === null ? '' : condition.phaseState,
    });
  }

  const clickSearch = () => {
    setExtendMap({});
    setQueryOrgIdList([]);
    setQueryOrgNameMap({});
    // 從 searchRef.current 解構所需資料，避免重複引用
    const {
      casNo, phaseState, operStartDate, operEndDate, inventoryStatus,
      toxicClass, concernedClass, priorityClass, organicClass,
      specificChemClass, envMonitorClass, envMonitorFreq, pubhazClass
    } = searchRef.current;

    const chemCtrlNo = selectedCtrlNoOption.value === allOptionValue ? "" : selectedCtrlNoOption.value;
    const svhcCtrlNo = selectedSvhcCtrlNoOption.value === allOptionValue ? "" : selectedSvhcCtrlNoOption.value;

    const newCondition = {
      ...condition,
      casNo,
      chemCtrlNo,
      svhcCtrlNo,
      phaseState,
      currentPage: 1,
      chemConId: "",
      orgLevel: null,
      operStartDate,
      operEndDate,
      inventoryStatus,
      chemClassIdList: selectedChemClasses.map((option) => option.value),
      chemCtrlNoList: [chemCtrlNo, svhcCtrlNo].filter(Boolean),
      chemSubCategoryIdList: [
        ...(toxicClass ? [toxicClass] : []),
        ...(concernedClass ? [concernedClass] : []),
        ...(priorityClass ? [priorityClass] : []),
        ...(organicClass ? [organicClass] : []),
        ...(specificChemClass ? [specificChemClass] : []),
        ...(envMonitorClass ? [envMonitorClass] : []),
        ...(envMonitorFreq ? [envMonitorFreq] : []),
      ],
      purchaseDetailConfigIdList: [...(pubhazClass ? [pubhazClass] : [])]
    };

    setCondition(newCondition);
    fetchData(newCondition);
  }

  // 添加Excel匯出功能
  const exportToExcel = async () => {
    // 定義欄位配置
    let columns: ExcelColumnConfig[] = [
      {
        header: t('table.title.item'),
        key: 'itemNo',
        width: 8
      }
    ];

    // 只在有單位層級時添加單位欄位
    if (orgLevel !== null) {
      columns.push({
        header: t('table.title.org.item'),
        key: 'orgName',
        width: 15,
        formatter: (_, row) => {
          return getSplitUnitByIndex(row.orgNames, orgLevel || 0);
        }
      });
    }

    // 添加其他通用欄位
    columns = [
      ...columns,
      {
        header: t('table.title.ctrl_no'),
        key: 'chemCtrlNo',
        width: 15,
        formatter: (_, row) => {
          return row.chemical?.chemCtrlNo || '';
        }
      },
      {
        header: t('table.title.casno'),
        key: 'casNo',
        width: 15,
        formatter: (_, row) => {
          const casnoList = row.chemical?.casnoList || [];
          return !isArrayEmpty(casnoList)
            ? casnoList.map((item: any) => item.casno).join(', ')
            : '';
        }
      },
      {
        header: t('table.title.name'),
        key: 'name',
        width: 25,
        formatter: (_, row) => {
          const nameList = row.chemical?.nameList || [];
          const { currentLangNames } = splitChemNameListByLang(nameList, i18n.language);
          return !isArrayEmpty(currentLangNames)
            ? currentLangNames.map((item: any) => item.chemName).join(', ')
            : '';
        }
      },
      {
        header: t('table.title.chemical.phase_state'),
        key: 'phaseState',
        width: 15,
        formatter: (value, _, configMap) => configMap[value]?.configName
      },
      {
        header: t('table.title.chemical.unit_ctrl_qty_total', { unit: itemText }),
        key: 'ctrlTotalQty',
        width: 15,
        formatter: (_, row, configMap, t) => {
          const { chemical, chemicalCon } = row;
          const { chemGradeHandQty } = chemical || {};
          const { ctrlTotalQty, ctrlTotalQtyType } = chemicalCon || {};
          const showCtrlTotalQty = ctrlTotalQtyType === 1 ? chemGradeHandQty : ctrlTotalQty;
          return showCtrlTotalQty === null ? t('text.chemical.no_ctrL_limit') : `${showCtrlTotalQty} kg`;
        }
      },
      {
        header: t('table.title.inventory_quantity'),
        key: 'inventoryQty',
        width: 15,
        formatter: (value) => `${value} kg`
      }
    ];

    // 調用通用工具函數
    await generateExcel(
      getShowList(),
      columns,
      configMap,
      t,
      'Chemical Operation List.xlsx',
      'Operations'
    );
  };

  return (
    <StlyedChemicalOperList $loading={loading}>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.chemical.manage") },
                { label: t("func.chemical.operation") },
              ]}
            />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{t("func.chemical.operation")} </h1>
            {/* END page-header */}

            <div className="card">
              <div className="card-body p-4">
                <SearchDropdownSwitch checked={isLinkSelect} onChangeChecked={setIsLinkSelect} onChangeCondition={() => setCondition(initCondition)} />
                <SearchConditionButton buttonType={SearchCondition.UNIT} onClick={() => { setShowUnitCondition((pre) => !pre) }} />
                <SearchConditionButton buttonType={SearchCondition.BUILDING} onClick={() => { setShowBuildCondition((pre) => !pre) }} />
                <SearchConditionButton buttonType={SearchCondition.ADVANCEd} onClick={() => { setShowAdvCondition((pre) => !pre) }} />
                <button type="button" className="btn btn-secondary ms-1 my-2" title={t('button.reset_search')} onClick={() => setCondition(initCondition)}><i className="fas fa-undo mx-1"></i>{t('button.reset_search')}</button>
                <div className="row my-3">
                  <SearchDivUnit dataList={searchSelectList} show={showUnitCondition} isLinkSelect={isLinkSelect}
                    condition={condition} setCondition={setCondition} showAll loginUser={loginUser} />
                  <SearchDivBuilding dataList={searchSelectList} show={showBuildCondition} isLinkSelect={isLinkSelect} condition={condition} setCondition={setCondition} showAll />
                  <div className="row my-2">
                    <SearchDivLab dataList={searchSelectList} isLinkSelect={isLinkSelect} condition={condition} setCondition={setCondition} />
                    {showAdvCondition &&
                      <>
                        <div className={`col-xl-3 d-flex align-items-center`}>
                          <label className="w-25">{t('text.chemical.casno')}</label>
                          <input
                            type="text"
                            className="form-control form-control-lg"
                            defaultValue={searchRef.current.casNo}
                            onChange={(e) => searchRef.current.casNo = e.target.value.trim()}
                          />
                        </div>
                        <div className={`col-xl-3 d-flex align-items-center`}>
                          <label className="w-25">{t('text.chemical.phase_state')}</label>
                          <select
                            className="form-select form-select-lg"
                            defaultValue={searchRef.current.phaseState}
                            onChange={(e) => searchRef.current.phaseState = e.target.value}
                          >
                            <option value="">{t('text.all')}</option>
                            {phaseStateOptions.map((option) => (
                              <option key={option.configId} value={option.configId}>{option.configName}</option>
                            ))}
                          </select>
                        </div>
                        <div className={`col-xl-3 d-flex align-items-center`}>
                          <label className="w-25">{t('text.status')}</label>
                          <select
                            className="form-select form-select-lg"
                            defaultValue={searchRef.current.inventoryStatus}
                            onChange={(e) => searchRef.current.inventoryStatus = Number(e.target.value)}
                          >
                            <option value={InventoryStatus.ENABLE} title={t('text.enabled')}>{t('text.enabled')}</option>
                            <option value={InventoryStatus.SIGNING} title={t('text.signoff.under_review')}>{t('text.signoff.under_review')}</option>
                            <option value={InventoryStatus.PURCHASING} title={t('text.purchasing')}>{t('text.purchasing')}</option>
                            <option value={InventoryStatus.DISABLED} title={t('text.disabled.item')}>{t('text.disabled.item')}</option>
                            <option value={InventoryStatus.DISPOSAL} title={t('text.chemical.disposal')}>{t('text.chemical.disposal')}</option>
                          </select>
                        </div>
                        <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                          <label className="w-25">{t("text.chemical.class")}</label>
                          <Select
                            className="w-75"
                            isMulti
                            isSearchable
                            menuPosition="fixed"
                            options={chemClassOptions}
                            placeholder={t('text.all')}
                            value={selectedChemClasses}
                            onChange={(selectedOptions: any) => {
                              setSelectedChemClasses(selectedOptions || []);
                            }}
                            noOptionsMessage={() => t('text.select_no_option')}
                          />
                        </div>
                        {hasSelectedToxicClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.chemical.toxic_class')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.toxicClass}
                              onChange={(e) => searchRef.current.toxicClass = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {toxicClassOptions.map((option) => (
                                <option key={option.configId} value={option.configId}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {hasSelectedToxicClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.ctrl_no')}</label>
                            <Select options={ctrlNoOptionList || []} isSearchable menuPosition="fixed" placeholder={t('text.all')} className="w-100"
                              menuPortalTarget={document.body}
                              onChange={(selectedOption: any) => {
                                setSelectedCtrlNoOption(selectedOption);
                              }} value={selectedCtrlNoOption}
                              noOptionsMessage={() => t('message.search.non_ctrl_no')}
                            />
                          </div>
                        )}
                        {hasSelectedConcernedClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.chemical.concerned_class')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.concernedClass}
                              onChange={(e) => searchRef.current.concernedClass = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {concernedClassOptions.map((option) => (
                                <option key={option.configId} value={option.configId}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {hasSelectedConcernedClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.chemical.concerned_ctrl_no')}</label>
                            <Select options={svhcCtrlNoOptionList || []} isSearchable menuPosition="fixed" placeholder={t('text.all')} className="w-75"
                              menuPortalTarget={document.body}
                              onChange={(selectedOption: any) => {
                                setSelectedSvhcCtrlNoOption(selectedOption);
                              }} value={selectedSvhcCtrlNoOption}
                              noOptionsMessage={() => t('message.search.non_ctrl_no')}
                            />
                          </div>
                        )}
                        {hasSelectedPriorityClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.chemical.prority_class_title')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.priorityClass}
                              onChange={(e) => searchRef.current.priorityClass = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {priorityClassOptions.map((option) => (
                                <option key={option.configId} value={option.configId}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {hasSelectedOrganicClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.chemical.organic_solvent_class')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.organicClass}
                              onChange={(e) => searchRef.current.organicClass = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {organicClassOptions.map((option) => (
                                <option key={option.configId} value={option.configId}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {hasSelectedSpecificChemClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.chemical.specific_chem_class')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.specificChemClass}
                              onChange={(e) => searchRef.current.specificChemClass = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {specificChemClassOptions.map((option) => (
                                <option key={option.configId} value={option.configId}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {hasSelectedEnvMonitorClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25 me-1">{t('text.chemical.work_env_monitor_class')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.envMonitorClass}
                              onChange={(e) => searchRef.current.envMonitorClass = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {envMonitorClassOptions.map((option) => (
                                <option key={option.configId} value={option.configId} title={option.configName}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {hasSelectedEnvMonitorClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25 me-1">{t('text.chemical.work_env_monitor_freq')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.envMonitorFreq}
                              onChange={(e) => searchRef.current.envMonitorFreq = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {envMonitorFreqOptions.map((option) => (
                                <option key={option.configId} value={option.configId}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {hasSelectedPubhazClass && (
                          <div className={`col-xl-3 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                            <label className="w-25">{t('text.chemical.public_hazard_classify')}</label>
                            <select
                              className="form-select form-select-lg"
                              defaultValue={searchRef.current.pubhazClass}
                              onChange={(e) => searchRef.current.pubhazClass = e.target.value}
                            >
                              <option value="">{t('text.all')}</option>
                              {pubhazClassOptions.map((option) => (
                                <option key={option.configId} value={option.configId}>{option.configName}</option>
                              ))}
                            </select>
                          </div>
                        )}
                        {/* <div className={`col-xl-6 d-flex align-items-center ${showAdvConditionMarginTop}`}>
                          <label className="w-25">{t('text.chemical.operation_date')}</label>
                          <div className="d-flex align-items-center">
                            <InputDate
                              defaultValue={searchRef.current.operStartDate}
                              isClearable={false}
                              onChange={handleStartDateChange}
                              maxDate={searchRef.current.operEndDate ? getFormatDateDash(new Date(searchRef.current.operEndDate)) : undefined}
                            />
                            <span className="mx-2">~</span>
                            <InputDate
                              defaultValue={searchRef.current.operEndDate}
                              isClearable={false}
                              onChange={handleEndDateChange}
                              minDate={searchRef.current.operStartDate ? getFormatDateDash(new Date(searchRef.current.operStartDate)) : undefined}
                            />
                          </div>
                        </div> */}
                      </>
                    }
                    <div className={`col-xl-3 ${showAdvConditionMarginTop}`}>
                      <button type="button" className="btn btn-primary mt-1" title={t('button.search.item')} onClick={clickSearch}><i className="fas fa-magnifying-glass"></i> {t('button.search.item')}</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="card pt-3">
              <div className="row topFunctionRow">
                <div className="col-sm-12 col-md-6 left">
                  <PageSizeSelector pageSize={condition.pageSize} setCondition={setCondition} />
                </div>
                <div className="col-sm-12 col-md-6 right">
                  <button
                    type="button"
                    className="btn btn-green float-end"
                    onClick={exportToExcel}
                  >
                    <i className="fas fa-file-excel me-1"></i>
                    {t('button.export.operation_list')}
                  </button>
                </div>
              </div>
              <div className="card-body">
                {loading && <Loader />}
                {showOrgLevelName && <label className="form-label fs-4">
                  {nowOrgName} {showOrgLevelName} {t('text.chemical.item')}
                  {orgLevel !== null && <button type="button" className="btn btn-secondary ms-1" onClick={backPrevQueryUnit}><i className="fas fa-arrow-left me-1"></i>{t('button.back_prev_org_level')}</button>}
                </label>}
                <div className="table-responsive mt-1">
                  <table
                    id="data-table-default"
                    className={
                      "table table-hover align-middle dt-responsive nowrap"
                    }
                  >
                    <thead className="text-center fs-4 fw-bold bg-lime-200">
                      <tr>
                        <th className="w-4">{t("table.title.item")} </th>
                        {
                          orgLevel !== null &&
                          <th className='text-start w-10'>
                            {t("table.title.org.item")}
                          </th>
                        }
                        <th className='text-start w-10'>
                          {t("table.title.ctrl_no")}
                          <SortIcon
                            dataList={getShowList()}
                            dataField={"chemical.chemCtrlNo"}
                            setFunction={setInventoryList}
                          />
                        </th>
                        <th className='text-start w-10'>
                          {t("table.title.casno")}
                          <SortIcon
                            dataList={getShowList()}
                            dataField={"chemical.casno"}
                            setFunction={setInventoryList}
                          />
                        </th>
                        <th className='text-start'>
                          {t("table.title.name")}
                          <SortIcon
                            dataList={getShowList()}
                            dataField={"chemical.chemName"}
                            setFunction={setInventoryList}
                          />
                        </th>
                        {/* <th>{t("table.title.ghs_img")}</th>
                        <th className='text-start w-15'>{t("table.title.chemical.public_hazard_classify")}</th> */}
                        <th className='text-start w-7'>{t("table.title.toxic_class")}</th>
                        <th className='text-start'>{t("table.title.chem_class")}</th>
                        <th className='text-center w-10'> {t("table.title.chemical.declared_concentration")}<br />/{t("table.title.chemical.phase_state")}</th>
                        <th className='text-end w-12'> {t("table.title.chemical.unit_ctrl_qty_total", { unit: itemText })}
                          <SortIcon
                            dataList={getShowList()}
                            dataField={"chemical.chemGradeHandQty"}
                            setFunction={setInventoryList}
                          />
                        </th>
                        <th data-orderable="false" className='text-end w-10'>{t("table.title.inventory_quantity")}
                          <SortIcon
                            dataList={getShowList()}
                            dataField={"inventoryQty"}
                            setFunction={setInventoryList}
                          />
                        </th>
                      </tr>
                    </thead>
                    <tbody className="text-center fs-5">
                      {!loading && getShowList() && !isArrayEmpty(getShowList()) ?
                        getShowList().map((data, idx) => {
                          return <Row key={'inventory_' + idx} index={idx + 1} data={data}
                            configMap={configMap}
                            setLoadingBlock={setLoadingBlock}
                            condition={condition} setCondition={setCondition}
                            extendMap={extendMap} setExtendMap={setExtendMap}
                            queryOrgIdList={queryOrgIdList} setQueryOrgIdList={setQueryOrgIdList}
                            queryOrgNameMap={queryOrgNameMap} setQueryOrgNameMap={setQueryOrgNameMap}
                          />;
                        }) : (!loading && <NoDataRow />)}
                    </tbody>
                  </table>
                </div>
                <Pagination pageInfo={pageInfo} setCondition={setCondition} />
              </div>
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </BlockUi>
    </StlyedChemicalOperList >
  );
}

const Row = (props: {
  index: number; data: EhsChemicalInventory
  configMap: { [configId: string]: EhsConfigParam };
  setLoadingBlock: (block: boolean) => void;
  condition: ConditionType;
  setCondition: (newCondition: ConditionType) => void;
  extendMap: { [index: number]: boolean };
  setExtendMap: (map: { [index: number]: boolean }) => void;
  queryOrgIdList: string[];
  setQueryOrgIdList: (list: string[]) => void;
  queryOrgNameMap: { [orgId: string]: string };
  setQueryOrgNameMap: (map: { [orgId: string]: string }) => void;
}) => {
  const { index, data: inventory, configMap, setLoadingBlock, condition, setCondition, extendMap, setExtendMap, queryOrgIdList, setQueryOrgIdList,
    queryOrgNameMap: queryOrgNameList, setQueryOrgNameMap: setQueryOrgNameList } = props;
  const { chemical, chemicalCon, phaseState, inventoryQty, orgIds,
    orgNames, nextUnitInventoryList, labInventoryList, chemId, chemConId } = inventory;
  const { chemCtrlNo, casno, chemGradeHandQty, chemCtrlConcen, chemCtrlConcenType, nameList, categoryList, casnoList } = chemical;
  const { conLower, conUpper, conType, conTypeValue, ctrlTotalQty,
    ctrlTotalQtyType, categoryList: chemicalConCategoryList } = chemicalCon;

  // 修正為使用 configMap 中的 configSeq 進行排序
  const sortedChemicalConCategoryList = sortCategoryListByConfigSeq(chemicalConCategoryList, configMap);

  const { orgLevel } = condition;
  const toxicClassify = categoryList.filter((item) => item.configType === CONFIG_TYPE_TOXIC) || [];
  const GhsImages = categoryList.filter((item) => item.configType === CONFIG_TYPE_GHS_IMG) || [];
  const publicHazardClassify = categoryList.filter((item) => item.configType === CONFIG_TYPE_PUBLIC_HAZARD) || [];

  const { loginUser } = useLoginUser();
  const { t, i18n } = useTranslation();
  const { itemText } = getUserUnitTextObj(loginUser, t);
  const { labText, msgLabDisabledOper, tableTitleLabBelongOrg } = getLabTextObj(loginUser, t);
  const { currentLangNames, enNames } = splitChemNameListByLang(nameList, i18n.language);
  const hasSearchUnit = orgLevel !== null;
  const isExtend = extendMap[index];
  const nowOrgName = getSplitUnitByIndex(orgNames, orgLevel || 0);
  const nowOrgId = getSplitUnitByIndex(orgIds, orgLevel || 0);
  const expandIcon = <i className={`fas fa-lg fa-fw me-10px ${isExtend ? 'fa-angle-up' : 'fa-angle-down'}`} onMouseEnter={() => setExtendMap({ ...extendMap, [index]: !isExtend })} onClick={() => setExtendMap({ ...extendMap, [index]: !isExtend })} />;
  const showCtrlTotalQty = ctrlTotalQtyType === 1 ? chemGradeHandQty : ctrlTotalQty;
  const concenTypeConfig = configMap[conType];

  const queryUnitQty = () => {
    const nextOrgLevel = !hasSearchUnit ? 0 : orgLevel + 1;
    setQueryOrgIdList([...queryOrgIdList, nowOrgId]);
    setQueryOrgNameList({ ...queryOrgNameList, [nowOrgId]: nowOrgName });
    setCondition({
      ...condition,
      orgLevel: nextOrgLevel,
      queryOrgId: nowOrgId,
      chemConId: chemConId,
      phaseState: phaseState
    });
  }

  return (
    <>
      <tr>
        <td data-title={t("table.title.item")}><label className="my-3 me-2">{index}</label><br />{hasSearchUnit && expandIcon}</td>
        {hasSearchUnit && <td className='text-start'>{nowOrgName}</td>}
        <td data-title={t("table.title.ctrl_no")} className='text-start'>{chemCtrlNo}</td>
        <td data-title={t("table.title.casno")} className='text-start'>{<ShowMoreInfo dataList={casnoList} id="chemCasnoId" fieldName="casno" />}</td>
        <td data-title={t("table.title.name")} className='text-start'>
          {notEnglishLang(i18n.language) ?
            <ShowMoreInfo dataList={currentLangNames} id="chemNameId" fieldName="chemName" /> :
            <ShowMoreInfo dataList={enNames} id="chemNameId" fieldName="chemName" />}
          {notEnglishLang(i18n.language) && <ShowMoreInfo dataList={enNames} id="chemNameId" fieldName="chemName" />}
        </td>
        {/* <td data-title={t("table.title.ghs_img")}>
          {GhsImages.map((item) => {
            return (<GhsImage key={'ghs_img_' + item.configId} src={item.configValue} alt={item.configName} title={item.configName} extraClassName="ghs-img" />);
          })}
        </td>
        <td data-title={t("table.title.chemical.public_hazard_classify")} className='text-start'>
          {publicHazardClassify.map((item) => {
            return (<React.Fragment key={'toxic_' + item.configId}><label>{item.configName}</label><br /></React.Fragment>);
          })}
        </td> */}
        <td data-title={t("table.title.toxic_class")} className='text-start'>
          {toxicClassify.map((item) => {
            return (<React.Fragment key={'toxic_' + item.configId}><label>{item.configName}</label><br /></React.Fragment>);
          })}
        </td>
        <td data-title={t("table.title.chem_class")} className='text-start'>
          {sortedChemicalConCategoryList.map((item) => {
            const categoryConfig = configMap[item.configId];
            return (
              <React.Fragment key={'category_' + item.configId}>
                <span className="d-inline-block mb-1">
                  <ChemicalClassificationBadge item={categoryConfig} />
                </span>
              </React.Fragment>
            );
          })}</td>
        <td data-title={t("table.title.chemical.declared_concentration") + '/' + t("table.title.chemical.phase_state")} className='text-center'>{conType ? getShowChemCon(concenTypeConfig?.configIvalue || 0, conLower, conUpper, concenTypeConfig?.configName || "") : <span className="text-danger">{t('message.chemical.no_concen')}</span>}<br />{configMap[phaseState]?.configName}</td>
        <td data-title={t("table.title.chemical.unit_ctrl_qty_total", { unit: itemText })} className='text-end'>{showCtrlTotalQty === null ? t('text.chemical.no_ctrL_limit') : showCtrlTotalQty + ' kg'} </td>
        <td data-title={t("table.title.inventory_quantity")} className='text-end'>
          {hasSearchUnit ? <React.Fragment><label className="mb-3">{inventoryQty} kg</label><br />
            {expandIcon}</React.Fragment>
            : <span className="custom-link" onClick={() => queryUnitQty()}>{inventoryQty} kg</span>}
        </td>
      </tr>
      {isExtend &&
        <tr className="mx-0">
          <td className="text-start mx-0 px-0" colSpan={99}>
            {nextUnitInventoryList && !isArrayEmpty(nextUnitInventoryList) && <>
              <div className="alert alert-primary mt-2 mb-1">{nowOrgName} {t('table.title.org.sub')} {t("table.title.inventory_quantity")}</div>
              <div className="blocks-container">
                {nextUnitInventoryList?.map((item, index) => (
                  <div className="block" key={'nextorg' + index}>
                    <div className="block-content">{t('table.title.org.item')}：<NamesSplitFragment names={item.orgNames} separator={ORG_SPLIT_FLAG} lineBreak='&ensp;' /></div>
                    <div className="block-content">{t("table.title.inventory_quantity")}：{item.inventoryQty} kg</div>
                    <div className="action-area">
                      <button type="button" className="btn btn-primary" title={t('button.chemical.sub_units_inventory')} onClick={() => queryUnitQty()}>
                        <i className="fas fa-arrow-down me-1"></i>{t('button.chemical.sub_units_inventory')}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </>
            }
            {labInventoryList && !isArrayEmpty(labInventoryList) && <>
              <div className="alert alert-success mt-3 mb-1">{tableTitleLabBelongOrg} {t("table.title.inventory_quantity")}</div>
              <div className="blocks-container">
                {labInventoryList?.map((item, index) => (
                  <div className="block" key={'labinventory' + index}>
                    <div className="block-content">{t('table.title.org.item')}：<NamesSplitFragment names={item.orgNames} separator={ORG_SPLIT_FLAG} lineBreak='&ensp;' /></div>
                    <div className="block-content">{labText}：{item.labName}</div>
                    <div className="block-content">{t("table.title.inventory_quantity")}：{item.inventoryQty} kg</div>
                    <div className="action-area">
                      <OperDetailButton inventoryItem={item} operDetailPageMode={OperDetailPageMode.OPER} />
                    </div>
                  </div>
                ))}
              </div>
            </>
            }
          </td>
        </tr>
      }
    </>
  );
};

const StlyedChemicalOperList = styled.div<{ $loading?: boolean }>`
  padding-bottom:150px;

  /* 設置容器為 flex，並允許換行 */
  .blocks-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px; /* 區塊之間的間距 */
  }

  /* 每個區塊固定寬度，使得每行最多 4 個區塊 */
  .block {
    flex: 1 1 calc(25% - 10px); /* 每行 4 個區塊，減去間距 */
    box-sizing: border-box;
    border: 1.5px solid #ccc; /* 區塊邊框 */
    padding: 10px; /* 區塊內間距 */
    // background-color: #f9f9f9; /* 區塊背景色 後拿掉如需要可在加回 */
    display: flex;
    flex-direction: column;
  }

  /* 區塊內部的內容垂直排列 */
  .block-content {
    margin-bottom: 10px; /* 每個內容項之間的間距 */
  }

  /* 移除最後一個內容項的 margin-bottom */
  .block-content:last-child {
    margin-bottom: 0;
  }

  .custom-link {
    color: blue;
    cursor: pointer;
  }

  .custom-link:hover {
      color: #551A8B; 
      // text-decoration: underline;
  }
  
  /* 操作區域樣式 */
  .action-area {
    margin-top: auto;
    text-align: right;
    padding-top: 5px;
  }

  .w-10{
    width:10%;
  }

  .w-7{
    width:7%;
  }

  .w-4{
    width:4%;
  }

  .ghs-img{
    width:40px;
  }

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }
  table {
    position:relative;
    min-height:${props => props.$loading ? "300px" : "auto"};
    th {
      text-align: center;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
  
    .ghs-img{
      width:30%;
    }
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 125px;
        text-align:left;
        min-height:100px; // rwd後 td最小高度
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          
    padding-bottom: 10px;
    min-height: auto; /* 重置最小高度 */
    height: auto; /* 重置高度 */
          // white-space: nowrap; /* 因標題過長 目前不需要不換行 */
    white-space: normal; /* 讓長標題能夠換行 */
    word-wrap: break-word; /* 在需要時強制斷詞 */
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
`;

export default ChemicalOperList;
