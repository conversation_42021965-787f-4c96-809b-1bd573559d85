import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { EhsAreaChemicalApproveInfo } from "../CONNChainEHS/models/EhsAreaChemicalApproveInfo";
import { EhsLanguage } from "../CONNChainEHS/models/EhsLanguage";
import { apiRequest, createLoginPostFormDataConfig, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const AreaAPI = {
  getAreaList: async (parms: BaseParams) => {
    return apiRequest(getApiAddress + "area/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getAreaDetail: async (
    parms: BaseParams & {
      areaId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "area/detail", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getAreaChemicalApproveInfoList: async (
    parms: BaseParams & {
    }
  ) => {
    return apiRequest(getApiAddress + "area/chemical/approval/info/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getAreaChemicalApproveInfoByAreaId: async (
    parms: BaseParams & {
      areaId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "area/chemical/approval/info/areaid", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  checkAreaNoDuplicate: async (
    parms: BaseParams & {
      areaNo: string;
      areaId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "area/number/check/duplicate", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addArea: async (
    parms: BaseParams & {
      areaNo: string;
      areaCno: string;
      areaName: string;
      areaAddress: string;
      areaStatus: number;
      allBuildingImage: File;
      areaNameList: EhsLanguage[];
      approveTypeList: string[];
      approveInfoList: EhsAreaChemicalApproveInfo[];
      approveInfoFileList: File[];
    }
  ) => {
    const formData = new FormData();
    const customKey = ["approveInfoFileList", "allBuildingImage"];
    const jsonParams = Object.keys(parms).reduce((acc, key) => {
      if (!customKey.includes(key)) {
        acc[key] = (parms as any)[key]; // 使用類型斷言
      }
      return acc;
    }, {} as any);

    formData.append("jsonString", JSON.stringify(jsonParams));

    if (parms.allBuildingImage) {
      formData.append("allBuildingImage", parms.allBuildingImage);
    }
    // 將檔案列表添加到FormData中
    if (parms.approveInfoFileList) {
      parms.approveInfoFileList.forEach((file, index) => {
        formData.append(`approveInfoFileList[${index}]`, file);
      });
    }
    return apiRequest(getApiAddress + "area/add", createLoginPostFormDataConfig(parms, formData)).then((res) => handleFetchResponse(res));
  },
  editArea: async (
    parms: BaseParams & {
      areaNo: string;
      areaCno: string;
      areaName: string;
      areaAddress: string;
      areaStatus: number;
      allBuildingImage: File;
      areaNameList: EhsLanguage[];
      approveTypeList: string[];
      approveInfoList: EhsAreaChemicalApproveInfo[];
      approveFileTypeList: string[];
      approveInfoFileList: File[];
      delFileIdList: string[];
    }
  ) => {
    const formData = new FormData();
    const customKey = ["approveInfoFileList", "allBuildingImage"];
    const jsonParams = Object.keys(parms).reduce((acc, key) => {
      if (!customKey.includes(key)) {
        acc[key] = (parms as any)[key]; // 使用類型斷言
      }
      return acc;
    }, {} as any);

    formData.append("jsonString", JSON.stringify(jsonParams));
    if(parms.allBuildingImage){
      formData.append("allBuildingImage", parms.allBuildingImage);
    }
    // 將檔案列表添加到FormData中
    if (parms.approveInfoFileList) {
      parms.approveInfoFileList.forEach((file, index) => {
        formData.append(`approveInfoFileList[${index}]`, file);
      });
    }
    return apiRequest(getApiAddress + "area/edit", createLoginPostFormDataConfig(parms, formData)).then((res) => handleFetchResponse(res));
  },
  editAreaStatus: async (
    parms: BaseParams & {
      areaId: string;
      areaStatus: number;
    }
  ) => {
    return apiRequest(getApiAddress + "area/status/edit", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  deleteArea: async (
    parms: BaseParams & {
      areaId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "area/del", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};
