import ChemicalClassificationBadge from "ehs/common/chemical/ChemicalClassificationBadge";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsProductChemSubstance } from "ehs/models/EhsProductChemSubstance";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { getShowChemCon } from "ehs/utils/chemicalUtil";
import { splitChemNameListByLang } from "ehs/utils/langUtil";
import { useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

function SubStanceDetail(props: {
    subStance: EhsProductChemSubstance;
    configMap: { [configId: string]: EhsConfigParam };
}) {
    const { subStance, configMap } = props;
    const { isPrimary, casno,
        chemCtrlNo, chemNameList, conType, conLower, conUpper,
        substPhaseState, note, categoryList
    } = subStance;
    const { t, i18n } = useTranslation();
    const [isShow, setIsShow] = useState<boolean>(isPrimary);
    const firstNameList = chemNameList?.filter(chemName => chemName.chemNameSeq === 1);
    const { currentLangNames, enNames } = splitChemNameListByLang(firstNameList, i18n.language);
    const firstName = (!isArrayEmpty(currentLangNames) && currentLangNames[0].chemName) || "";
    const firstEnName = (!isArrayEmpty(enNames) && enNames[0].chemName) || "";
    const conTypeConfig = configMap[conType];
    const { configIvalue, configName } = conTypeConfig || {};
    const showCon = getShowChemCon(configIvalue, conLower, conUpper, configName);
    const subStanceItem = [
        { label: t("text.chemical.casno"), text: casno, status: true },
        { label: t("text.ctrl_no"), text: chemCtrlNo, status: isShow && chemCtrlNo },
        { label: t("text.chemical.name"), text: firstName ? firstName : firstEnName, status: isShow },
        { label: t("text.en_name"), text: firstEnName, status: isShow && firstName },
        { label: t("table.title.chemical.substance_phase_state"), text: configMap[substPhaseState]?.configName || "", status: isShow },
        { label: t('text.chemical.substance_concen'), text: showCon, status: isShow },
        {
            label: t("text.chemical.substance_class"),
            text: (
                <div className="d-flex flex-wrap align-items-center">
                    {categoryList.map((substCategory, idx) => (
                        <span key={idx} className="d-inline-block me-1 mb-1">
                            <ChemicalClassificationBadge item={configMap[substCategory.configId]} />
                        </span>
                    ))}
                </div>
            ),
            status: isShow
        },
        { label: t("text.note"), text: note, status: isShow && note },
    ];
    const contentRef = useRef<HTMLDivElement>(null);

    const handleToggle = () => {
        const newState = !isShow;
        setIsShow(newState);

        if (newState) {
            setTimeout(() => {
                contentRef.current?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }, 50);
        }
    };

    return (
        <StyledSubStanceDetail>
            <div className={`mix-box ${isShow ? 'expanded' : 'collapsed'}`}>
                <div
                    className="header-area d-flex justify-content-between align-items-center"
                    onClick={(e) => {
                        if (!(e.target as HTMLElement).closest('.expand-button')) {
                            handleToggle();
                        }
                    }}
                >
                    <div className="d-flex align-items-center">
                        {isPrimary && <span className="badge bg-danger me-2">{t("text.chemical.main_substance")}</span>}
                        {!isShow && (
                            <>
                                <span className="fw-bold">{casno}</span>
                                {firstName && <span className="ms-2">{firstName}</span>}
                            </>
                        )}
                    </div>
                    <button className="expand-button btn btn-icon btn-sm btn-primary" onClick={(e) => {
                        e.stopPropagation();
                        handleToggle();
                    }}>
                        <i className={`fas fa-${isShow ? "chevron-up" : "chevron-down"}`} />
                    </button>
                </div>

                {isShow && (
                    <div className="substance-content mt-3" ref={contentRef}>
                        {subStanceItem.filter(s => s.status).map((item, idx) => (
                            <label key={idx} className="d-flex align-items-center fw-bold">
                                {item.label}：{item.text}
                            </label>
                        ))}
                    </div>
                )}
            </div>
        </StyledSubStanceDetail>
    );
}

const StyledSubStanceDetail = styled.div`
    .mix-box {
        position: relative;
        border: 1px solid #ccc;
        border-radius: 10px;
        padding: 12px;
        background-color: #f9f9f9;
        transition: all 0.2s ease;
    }
    
    .mix-box.collapsed {
        border-left: 4px solid #ccc;
    }
    
    .mix-box.expanded {
        border-left: 4px solid #0d6efd;
    }
    
    .header-area {
        min-height: 38px;
        cursor: pointer;
    }
    
    .header-area:hover {
        background-color: #f0f0f0;
        border-radius: 6px;
    }
    
    .expand-button {
        min-width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        z-index: 1;
    }
    
    .substance-content {
        cursor: auto;
    }
`;

export default SubStanceDetail;