import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { OrgAPI } from "../../../api/orgAPI";
import { SelectListAPI } from "../../../api/selectListAPI";
import { confirmMsg, errorMsg } from "../../common/SwalMsg";
import { LANGUAGE_MAPPING_TYPE_ORG, ORG_SPLIT_FLAG } from "../../constant/constants";
import { ActionMode } from "../../enums/ActionMode";
import useLoginUser from "../../hooks/useLoginUser";
import { EhsLanguage, initEhsLanguage } from "../../models/EhsLanguage";
import { EhsOrg, initEhsOrg } from "../../models/EhsOrg";
import { EhsOrgLevel } from "../../models/EhsOrgLevel";
import { SelectItem } from "../../models/SelectItem";
import { isArrayEmpty } from "../../utils/arrayUtil";
import { sortLanguagesListByCurrent } from "../../utils/langUtil";
import { isApiCallSuccess } from "../../utils/resultUtil";
import { decodeHTMLEntities, getSplitValueByIndexReg } from "../../utils/stringUtil";
import { getBasicLoginUserInfo } from "../../utils/authUtil";
import InputEmail from "../../common/input/InputEmail";
import LoadingSpinner from "../../common/LoadingSpinner";

const initValidShowMsgValues = {
    orgNo: null as boolean | null,
    orgName: null as boolean | null,
    orgType: null as boolean | null,
    areaId: null as boolean | null,
    parentOrg: null as boolean | null,
    orgEmail: null as boolean | null
};

export default function OrgModify(props: {
    onClose: () => void;
    onActionSuccess: () => void;
    setLoadingBlock: (block: boolean) => void;
    mode: ActionMode | null;
    modifyData: EhsOrg;
    areaOption: Array<SelectItem>;
    orgTypeOption: Array<SelectItem>;
    selectOrgLevelId: string;
    selectOrgLevel: number;
    orgList: Array<EhsOrg>;
    orgLevelList: Array<EhsOrgLevel>;
}) {
    const { onClose, onActionSuccess, setLoadingBlock,
        mode, modifyData, areaOption, orgTypeOption, selectOrgLevelId, selectOrgLevel, orgList, orgLevelList } = props;
    const { loginUser } = useLoginUser();
    const { t, i18n } = useTranslation();
    const [editValues, setEditValues] = useState(initEhsOrg);
    const [valudShowMsgValues, setValidShowMsgValues] = useState(initValidShowMsgValues);
    const [buildOption, setBuildOption] = useState<Array<SelectItem>>([]);
    const [buildFloorOption, setBuildFloorOption] = useState<Array<SelectItem>>([]);
    const [filteredOrgItems, setFilteredOrgItems] = useState<Record<string, EhsOrg[]>>({});
    const [selectOrgItems, setSelectOrgItems] = useState<Record<string, EhsOrg[]>>({});
    const [selectParentOrgIds, setSelectParentOrgIds] = useState<Record<string, string>>({});
    const [showOtherLangName, setShowOtherLangName] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [orgNameList, setOrgNameList] = useState<EhsLanguage[]>([]);
    const isCloseMode = mode === null;
    const isAddMode = mode === ActionMode.ADD;
    const actBtnColor = isAddMode ? "purple" : "warning";
    const actTitle = isCloseMode ? "" : isAddMode ? t("text.add") : t("text.edit");
    const nameTitle = t("text.name");
    const sortedLangList = loginUser ? sortLanguagesListByCurrent(loginUser!.langType) : [];
    const hasMultiLang = sortedLangList.length > 1;
    const { areaId: editAreaId, buildId: editBuildId } = editValues;
    const isFirstLevel = selectOrgLevel === 1;
    const indexOrglv = orgLevelList.findIndex(data => data.orglvId === selectOrgLevelId);
    let firstLangValue = "";
    const isModifyLoading = isLoading && !isAddMode;

    /* 區域取建築 */
    useEffect(() => {
        if (editAreaId) {
            SelectListAPI.getSelectBuilding({
                ...getBasicLoginUserInfo(loginUser)!,
                areaId: editAreaId
            }).then(result => {
                if (result) {
                    setBuildOption(result.results);
                }
            })
        } else {
            setBuildOption([]);
        }
    }, [loginUser, editAreaId]);

    /* 建築取樓層 */
    useEffect(() => {
        if (editAreaId && editBuildId) {
            SelectListAPI.getSelectBuildingFloor({
                ...getBasicLoginUserInfo(loginUser)!,
                buildId: editBuildId
            }).then(result => {
                if (result) {
                    setBuildFloorOption(result.results);
                }
            })
        } else {
            setBuildFloorOption([]);
        }
    }, [loginUser, editAreaId, editBuildId]);

    /* modal 新增修改打開預設值 */
    useEffect(() => {
        if (!isCloseMode) {
            setShowOtherLangName(false);
            setOrgNameList([]);
            setEditValues(isAddMode ? initEhsOrg : modifyData);
            setValidShowMsgValues(initValidShowMsgValues);
            if (!isAddMode && modifyData.orgIds) {
                setIsLoading(true);
                const orgIds = modifyData.orgIds.split(ORG_SPLIT_FLAG).slice(0, indexOrglv);
                let updatedSelectParentOrgIds = { ...selectParentOrgIds };
                if (orgIds) {
                    orgIds.forEach((id, index) => {
                        updatedSelectParentOrgIds[getSelectParentOrgIdsKey(index)] = id;
                    });
                } else {
                    updatedSelectParentOrgIds = {};
                }
                setSelectParentOrgIds(updatedSelectParentOrgIds);

                OrgAPI.getOrgDetail({
                    ...getBasicLoginUserInfo(loginUser)!,
                    orgId: modifyData.orgId,
                }).then((result) => {
                    if (isApiCallSuccess(result)) {
                        setOrgNameList(result.results.orgNameList);
                    } else {
                        errorMsg(result.message)
                    }
                }).catch((err) => {
                    debugger;
                    alert(err);
                }).finally(() => {
                    setIsLoading(false);
                });
            } else {
                setSelectParentOrgIds({});
            }
        }
    }, [mode, modifyData]);

    useEffect(() => {
        if (!isArrayEmpty(orgList)) {
            initSelectOption();
            if (filteredOrgItems[selectOrgLevelId]) {
                setFilteredOrgItems(prevState => ({ ...prevState, [selectOrgLevelId]: filterOrg }));
            }
        }
    }, [orgList])

    /* 新增 修改 沒選擇單位時 要更新提示訊息 */
    useEffect(() => {
        if (selectLastParentOrgid) {
            setValidShowMsgValues({ ...valudShowMsgValues, parentOrg: !selectLastParentOrgid })
        }
    }, [selectParentOrgIds])

    const filterOrg = orgList.filter(data => data.orglvId === selectOrgLevelId);

    const initSelectOption = () => {
        const orgItem = orgList.reduce((itemMap: Record<string, EhsOrg[]>, org: EhsOrg) => {
            const orglvId = org.orglvId;
            itemMap[orglvId] = itemMap[orglvId] || [];
            itemMap[orglvId].push(org);
            return itemMap;
        }, {});

        setSelectOrgItems(orgItem);
    };


    const getSelectParentOrgIdsKey = (idx: number) => {
        return selectOrgLevelId + '-' + idx;
    }

    const parentOrgSelect = indexOrglv !== 0 && orgLevelList.slice(0, indexOrglv).map((data, idx) => {
        // 根據某條件篩選 selectOrgItems 的內容
        const orgOption = selectOrgItems[data.orglvId];
        const selectOrgPid = selectParentOrgIds[getSelectParentOrgIdsKey(idx - 1)] || "";
        const selectOrgId = selectParentOrgIds[getSelectParentOrgIdsKey(idx)] || "";
        // const filteredOrgItems = selectOrgPid ? orgOption?.filter(item => selectOrgPid === item.orgPid) : orgOption;//如需下拉上上層會用得到 目前為只能下拉上層
        const isFirstOrgLv = idx === 0;
        const notPrevOrgLv = idx !== indexOrglv - 1;
        return ((isFirstOrgLv || (!isFirstOrgLv && selectOrgPid && orgOption.length > 0)) &&
            <div key={idx} className={`col-md-12 mb-3 ${notPrevOrgLv && 'd-none'}`}>
                <label className="fw-bold mb-1">{data.orglvName}：<span className="text-danger">*</span></label>
                <select className={`form-select w-100 ${isFirstOrgLv && valudShowMsgValues.parentOrg && 'is-invalid'}`} value={selectOrgId} onChange={(e) => onChangeParentOrgSelect(idx, e.target.value)}>
                    <option value=''>{t('text.all')}</option>
                    {orgOption?.map((data, idx) => (
                        <option key={data.orgId} value={data.orgId}>{data.orgName}</option>
                    ))}
                </select>
                {isFirstOrgLv && valudShowMsgValues.parentOrg && <div className="invalid-feedback">{t("message.select")}</div>}
            </div>
        );
    });

    // const lastParentOrgId = selectParentOrgIds[getSelectParentOrgIdsKey(selectOrgLevel - 2)] || "";//目前最後一層選擇單位 
    const maxKey = Object.keys(selectParentOrgIds)
        .filter(key => selectParentOrgIds[key] && getSplitValueByIndexReg(key, 0, "-") === selectOrgLevelId)
        .reduce((maxKey, key) => key > maxKey ? key : maxKey, "-1");
    const hasMaxKey = maxKey !== "-1";
    const selectLastParentOrgid = selectParentOrgIds[maxKey] || "";//有選中單位裡面的最後一層流水號
    const selectLastOrglv = hasMaxKey ? parseInt(getSplitValueByIndexReg(maxKey, 1, "-")) + 1 : -1;
    const selectLastOrglvId = hasMaxKey ? orgLevelList.find(item => item.orglvLevel === selectLastOrglv + 1)?.orglvId || "" : "";
    const isChooseNowOrglv = selectLastOrglv === indexOrglv;
    const noChangeOrglv = isFirstLevel || isChooseNowOrglv;
    const modifyOrglvId = noChangeOrglv ? selectOrgLevelId : selectLastOrglvId;

    const onChangeParentOrgSelect = (idx: number, orgId: string) => {
        const key = getSelectParentOrgIdsKey(idx);

        setSelectParentOrgIds(prevState => {
            const newState = { ...prevState, [key]: orgId };
            const [prefix, currentIndex] = key.split('-');
            for (const existingKey of Object.keys(newState)) {
                const [existingPrefix, existingIndex] = existingKey.split('-');
                if (existingPrefix === prefix && parseInt(existingIndex) > parseInt(currentIndex)) {
                    newState[existingKey] = "";
                }
            }
            return newState;
        });
    }

    const onAction = () => {
        if (!loginUser) {
            return;
        }
        const { orgNo, orgName, orgType, areaId } = editValues;
        const showMsgValues = {
            ...valudShowMsgValues,
            orgNo: !orgNo,
            orgName: !orgName,
            areaId: !areaId,
            orgType: isFirstLevel ? !orgType : false,//非第一層的單位會帶上層 不需卡必填
            parentOrg: isFirstLevel ? false : !selectLastParentOrgid,
        };
        setValidShowMsgValues(showMsgValues);
        if (Object.values(showMsgValues).some(value => value)) {
            return;
        }
        if (noChangeOrglv) {
            doOrgModify();
        } else {
            confirmMsg(decodeHTMLEntities(t('message.confirm.org_modify_orglv', {
                nowLv: getOrglvName(selectOrgLevelId), selectLv: getOrglvName(selectLastOrglvId)
            })), t).then((value) => {
                if (value) {
                    doOrgModify();
                }
            });
        }
    };

    const doOrgModify = () => {

        if (!loginUser) {
            return;
        }
        setLoadingBlock(true);

        let newOrgNameList: EhsLanguage[] = [...orgNameList].map(org => ({
            ...org,
            langValue: org.langValue.trim()
        }));
        sortedLangList.forEach((item, idx) => {
            const isFirst = idx === 0;
            if (isFirst) {
                const firstLang = newOrgNameList.find(lang => lang.langType === item.code);
                firstLangValue = firstLang ? firstLang.langValue : ""; // 確保 firstLang 存在，避免錯誤
            } else {
                const exists = newOrgNameList.some((lang) => lang.langType === item.code);
                if (exists) {
                    // 如果存在，且 langValue 為空，則將其設置為 firstLangValue
                    const existingLangIndex = newOrgNameList.findIndex(lang => lang.langType === item.code);
                    if (existingLangIndex !== -1 && !newOrgNameList[existingLangIndex].langValue) {
                        newOrgNameList[existingLangIndex].langValue = firstLangValue;
                    }

                } else {
                    newOrgNameList = [
                        ...newOrgNameList,
                        {
                            ...initEhsLanguage,
                            langType: item.code,
                            langValue: firstLangValue,
                            mappingType: LANGUAGE_MAPPING_TYPE_ORG
                        }
                    ];
                }
            }
        });

        editValues.orgStatus = 1;
        editValues.orgPid = isFirstLevel ? '0' : selectLastParentOrgid;
        editValues.orglvId = modifyOrglvId;
        if (isAddMode) {
            OrgAPI.addOrg({
                ...editValues,
                ...getBasicLoginUserInfo(loginUser)!,
                orgNameList: newOrgNameList,
            }).then((result) => {
                if (isApiCallSuccess(result)) {
                    onActionSuccess();
                    setSelectParentOrgIds({})
                } else {
                    errorMsg(result.message)
                }
                setLoadingBlock(false);
            }).catch((err) => {
                debugger;
                errorMsg(err)
                setLoadingBlock(false);
            });
        } else {
            OrgAPI.editOrg({
                ...editValues,
                ...getBasicLoginUserInfo(loginUser)!,
                orgNameList: newOrgNameList,
            }).then((result) => {
                if (isApiCallSuccess(result)) {
                    onActionSuccess();
                } else {
                    errorMsg(result.message)
                }
                setLoadingBlock(false);
            }).catch((err) => {
                debugger;
                errorMsg(err)
                setLoadingBlock(false);
            });
        }
    }

    const getOrglvName = (orglvId: string) => {
        return orgLevelList.find(item => item.orglvId === orglvId)?.orglvName || "";
    }

    return (
        <StyledModifyOrg>
            <div className="modifyOrg">
                <div className="modifyOrg-header">
                    <h4 className="modal-title">{actTitle}</h4>
                    <button
                        type="button"
                        className="btn-close"
                        aria-hidden="true"
                        onClick={onClose}
                    ></button>
                </div>
                <div className="modifyOrg-body">
                    {isModifyLoading ? (
                        <div className="d-flex justify-content-center align-items-center h-100">
                            <LoadingSpinner />
                        </div>
                    ) : (
                        <div className="row mb-3 justify-content-center">
                            <div className="col-12">
                                <div className="col-md-12 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.no')}：<span className="text-danger">*</span>
                                    </label>
                                    <input
                                        value={editValues.orgNo}
                                        type="text"
                                        className={`form-control form-control-lg ${valudShowMsgValues.orgNo && 'is-invalid'}`}
                                        data-parsley-required="true"
                                        onChange={(e) => {
                                            setEditValues({
                                                ...editValues,
                                                orgNo: e.target.value,
                                            });
                                            setValidShowMsgValues({ ...valudShowMsgValues, orgNo: !e.target.value })
                                        }}
                                    />
                                    {valudShowMsgValues.orgNo &&
                                        <div className="invalid-feedback">{t("message.enter")}</div>
                                    }
                                </div>
                                {loginUser && sortedLangList.map((item, idx) => {
                                    const isFirst = idx === 0;
                                    const orgNameVal = orgNameList.find((lang) => lang.langType === item.code)?.langValue || "";
                                    return (
                                        <div className={`col-md-12 mb-3 ${!isFirst && !showOtherLangName && 'd-none'}`} key={'orgname' + item.code}>
                                            <label className="fw-bold mb-1">
                                                {nameTitle}：{isFirst && <span className="text-danger me-1">*</span>} {hasMultiLang && item.language}
                                            </label>
                                            <input
                                                value={orgNameVal}
                                                type="text"
                                                className={`form-control form-control-lg ${isFirst && valudShowMsgValues.orgName && 'is-invalid'}`}
                                                data-parsley-required="true"
                                                onChange={(e) => {
                                                    const newVal = e.target.value;
                                                    if (isFirst) {
                                                        setEditValues({
                                                            ...editValues,
                                                            orgName: newVal,
                                                        });
                                                        setValidShowMsgValues({ ...valudShowMsgValues, orgName: !newVal })
                                                    }
                                                    setOrgNameList((prevList) => {
                                                        const index = prevList.findIndex((lang) => lang.langType === item.code);
                                                        if (index !== -1) {
                                                            // 找到匹配的項目，更新其 langValue
                                                            return prevList.map((lang, idx) =>
                                                                idx === index ? { ...lang, langValue: newVal } : lang
                                                            );
                                                        } else {
                                                            // 未找到匹配的項目，新增一個新的項目
                                                            return [
                                                                ...prevList,
                                                                {
                                                                    ...initEhsLanguage,
                                                                    langType: item.code,
                                                                    langValue: newVal,
                                                                    mappingType: LANGUAGE_MAPPING_TYPE_ORG
                                                                }
                                                            ];
                                                        }
                                                    });
                                                }}
                                            />
                                            {isFirst && valudShowMsgValues.orgName &&
                                                <div className="invalid-feedback">{t("message.enter")}</div>
                                            }
                                            {isFirst && hasMultiLang && <button type="button" className="btn btn-primary mt-2" onClick={() => {
                                                setShowOtherLangName(!showOtherLangName);
                                            }}>{t('button.enter_other_lang')} {nameTitle}</button>}
                                        </div>
                                    )
                                })}
                                {isFirstLevel && <div className="col-md-12 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.org.classification')}：<span className="text-danger">*</span>
                                    </label>
                                    <select className={`form-select w-100 ${valudShowMsgValues.orgType && 'is-invalid'}`}
                                        value={editValues.orgType}
                                        onChange={(e) => {
                                            setEditValues({
                                                ...editValues,
                                                orgType: e.target.value,
                                            });
                                            setValidShowMsgValues({ ...valudShowMsgValues, orgType: !e.target.value })
                                        }}>
                                        <option value="">{t('text.all')}</option>
                                        {orgTypeOption.map((item) => {
                                            return <option key={item.value} value={item.value} title={item.description} >{item.label}</option>
                                        })}
                                    </select>
                                    {valudShowMsgValues.areaId &&
                                        <div className="invalid-feedback">{t("message.select")}</div>
                                    }
                                </div>}
                                {!isFirstLevel && parentOrgSelect}
                                <div className="col-md-12 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.area.item')}：<span className="text-danger">*</span>
                                    </label>
                                    <select className={`form-select w-100 ${valudShowMsgValues.areaId && 'is-invalid'}`} value={editValues.areaId}
                                        onChange={(e) => {
                                            setEditValues({
                                                ...editValues,
                                                areaId: e.target.value,
                                            });
                                            setValidShowMsgValues({ ...valudShowMsgValues, areaId: !e.target.value })
                                        }}>
                                        <option value="">{t('text.all')}</option>
                                        {areaOption.map((item) => {
                                            return <option key={item.value} value={item.value} title={item.description} >{item.label}</option>
                                        })}
                                    </select>
                                    {valudShowMsgValues.areaId &&
                                        <div className="invalid-feedback">{t("message.select")}</div>
                                    }
                                </div>
                                {editAreaId && <div className="col-md-12 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.building.item')}：
                                    </label>
                                    <select className="form-select w-100" value={editValues.buildId}
                                        onChange={(e) => {
                                            setEditValues({
                                                ...editValues,
                                                buildId: e.target.value,
                                            });
                                        }}>
                                        <option value="">{t('text.all')}</option>
                                        {buildOption.map((item) => {
                                            return <option key={item.value} value={item.value} title={item.description} >{item.label}</option>
                                        })}
                                    </select>
                                </div>}
                                {editAreaId && editBuildId && <div className="col-md-12 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.building.floor.item')}：
                                    </label>
                                    <select className="form-select w-100" value={editValues.orgFloor}
                                        onChange={(e) => {
                                            setEditValues({
                                                ...editValues,
                                                orgFloor: e.target.value,
                                            });
                                        }}>
                                        <option value="">{t('text.all')}</option>
                                        {buildFloorOption.map((item) => {
                                            return <option key={item.value} value={item.value} title={item.description} >{item.label}</option>
                                        })}
                                    </select>
                                </div>}
                            </div>
                            <div className="col-12">
                                <div className="col-md-12 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.org.house_number')}：
                                    </label>
                                    <input
                                        value={editValues.orgHousenum || ""}
                                        type="text"
                                        className={`form-control form-control-lg`}
                                        data-parsley-required="true"
                                        onChange={(e) => {
                                            setEditValues({
                                                ...editValues,
                                                orgHousenum: e.target.value,
                                            });
                                        }}
                                    />
                                </div>
                                <div className="col-md-12 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.phone_ext')}：
                                    </label>
                                    <input
                                        value={editValues.orgPhoneExt || ""}
                                        type="text"
                                        className={`form-control form-control-lg`}
                                        data-parsley-required="true"
                                        onChange={(e) => {
                                            setEditValues({
                                                ...editValues,
                                                orgPhoneExt: e.target.value,
                                            });
                                        }}
                                    />
                                </div>
                                <div className="col-md-12 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.dedicated_line')}：
                                    </label>
                                    <input
                                        value={editValues.orgPhone || ""}
                                        type="text"
                                        className={`form-control form-control-lg`}
                                        data-parsley-required="true"
                                        onChange={(e) => {
                                            setEditValues({
                                                ...editValues,
                                                orgPhone: e.target.value,
                                            });
                                        }}
                                    />
                                </div>
                                <div className="col-md-12 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.url')}：
                                    </label>
                                    <input
                                        value={editValues.orgUrl || ""}
                                        type="text"
                                        className={`form-control form-control-lg`}
                                        data-parsley-required="true"
                                        onChange={(e) => {
                                            setEditValues({
                                                ...editValues,
                                                orgUrl: e.target.value,
                                            });
                                        }}
                                    />
                                </div>
                                <div className="col-md-12 mb-3">
                                    <label className="fw-bold mb-1">
                                        {t('text.email')}：
                                    </label>
                                    <InputEmail className={`form-control form-control-lg`} value={editValues.orgEmail || ""}
                                        onChange={(e) => setEditValues({ ...editValues, orgEmail: e.target.value })}
                                        onError={(errMsg) => setValidShowMsgValues({ ...valudShowMsgValues, orgEmail: !!errMsg })}
                                    />
                                </div>
                            </div>
                        </div>
                    )}
                </div>
                {actTitle && <div className="modifyOrg-footer">
                    <div className="btn btn-white" aria-hidden="true" onClick={onClose}>
                        <i className="fas fa-times me-1" />
                        {t("button.close")}
                    </div>
                    {!isModifyLoading && <div className={`btn btn-${actBtnColor}  ms-2`} onClick={onAction}>
                        <i className="fas fa-cubes" /> {actTitle}
                    </div>}
                </div>}
            </div>
        </StyledModifyOrg>
    );
}

const StyledModifyOrg = styled.div`
  background: white; 
  width: 100%;
  max-width: 1000px;

  .modifyOrg-header {
    background: rgb(251, 205, 165);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  
  .modifyOrg-body {
    padding: 15px;
  }
  
  .modifyOrg-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-purple {
      margin-left: 10px;
    }
  }
    
    @media (max-width: 1024px) {
        width: 100%;  // 在小螢幕時使用 100% 寬度
        max-width: 100%;
        height: 100%;
        
        .modifyOrg-body {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 20px; // 添加底部間距，避免內容被 footer 遮擋
            max-height: 700px;
        }
        
        .modifyOrg-footer {
            position: sticky;
            bottom: 0;
            background: white;
            padding: 15px;
            border-top: 1px solid #ced4da;
            display: flex;
            justify-content: center;
        }
    }
`;
