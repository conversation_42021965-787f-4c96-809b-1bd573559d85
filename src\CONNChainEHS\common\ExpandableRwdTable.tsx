import React from 'react';
import styled from 'styled-components';

interface ExpandableRwdTableProps {
  children: React.ReactNode;
  breakpoint?: number; // 新增自定義斷點參數 (桌面版的寬度)
  expandOnMedium?: boolean; // 控制中等尺寸螢幕是否啟用展開功能
}

export default function ExpandableRwdTable({
  children,
  breakpoint = 1025,
  expandOnMedium = false
}: ExpandableRwdTableProps) {
  return (
    <StyledExpandableRwdTable $breakpoint={breakpoint} $expandOnMedium={expandOnMedium}>
      {children}
    </StyledExpandableRwdTable>
  );
}

const StyledExpandableRwdTable = styled.div<{
  $breakpoint: number;
  $expandOnMedium: boolean;
}>`
  

  /* 平板設備的響應式設計 */
  @media (min-width: 601px) and (max-width: ${props => props.$expandOnMedium ? '1400px' : '1024px'}) {
    .table-container {
      overflow-x: auto;
    }
    
    .table {
      width: 100%;
      table-layout: fixed;
    }
    
    /* 根據可用空間自動隱藏欄位和表頭 */
    @media (max-width: ${props => props.$expandOnMedium ? '1400px' : '1024px'}) {
      .responsive-cell,
      .responsive-header {
        display: none;
      }
      
      .expand-toggle {
        display: inline-flex !important;
      }
    }
    
    .action-cell {
      min-width: 180px;
    }
    
    .action-buttons {
      flex: 1;
    }
    
    .expand-toggle {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      color: #2196f3;
      background: rgba(33, 150, 243, 0.1);
      border-radius: 50%;
      border: none;
      cursor: pointer;
      margin-left: 8px;
      transition: all 0.2s ease;
      
      &:hover {
        background: rgba(33, 150, 243, 0.2);
        color: #1976d2;
      }
      
      i {
        font-size: 14px;
      }
    }
    
    .expanded-row {
      background-color: #ffffff;
      display: table-row;
    }
    
    .expanded-content {
      padding: 0;
      margin: 0;
      border-top: 1px solid #dee2e6;
    }
    
    .expanded-table {
      width: 100%;
      border-collapse: collapse;
      
      td {
        padding: 12px 24px;
        border-bottom: 1px solid #dee2e6;
        
        &:last-child {
          border-left: 1px solid #dee2e6;
        }
      }
      
      tr:last-child td {
        border-bottom: none;
      }
    }

    .expanded-label {
      width: 150px;
      color: #666;
      font-weight: 500;
      background-color: #f8f9fa;
      text-align: left;
    }

    .expanded-value {
      text-align: left;
    }
    
    /* 確保最後一欄有足夠空間顯示按鈕和展開圖示 */
    td:last-child {
      width: auto;
      min-width: 200px;
    }
  }

  /* 確保桌面版正常顯示 */
  @media (min-width: ${props => props.$breakpoint}px) {
    .expanded-row {
      display: none !important;
    }
    .expand-toggle {
      display: none !important;
    }
    
    .responsive-cell,
    .responsive-header {
      display: table-cell;
    }
  }
  
  /* 中等尺寸螢幕的特殊處理 */
  ${props => props.$expandOnMedium ? `
    @media (min-width: 1025px) and (max-width: 1400px) {
      .expanded-row {
        display: table-row;
      }
      .expand-toggle {
        display: inline-flex !important;
      }
      
      .responsive-cell,
      .responsive-header {
        display: none;
      }
    }
  ` : ''}
`;