import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import styled from "styled-components";
import { CheckListAPI } from "../../../api/checkListAPI";
import { LabAPI } from "../../../api/labAPI";
import { RoleAPI } from "../../../api/roleAPI";
import { AppPaths } from "../../../config/app-paths";
import BackButton from "../../common/button/BackButton";
import NamesSplitFragment from "../../common/NamesSplitFragment";
import { CONFIG_SUB_TYPE_TOP, FILE_SUB_TYPE_LAB_FLOORPLAN, FILE_TYPE_LAB, ORG_SPLIT_FLAG } from "../../constant/constants";
import useLoginUser from "../../hooks/useLoginUser";
import Breadcrumbs from "../../layout/Breadcrumbs";
import Footer from "../../layout/Footer";
import { EhsConfigParam } from "../../models/EhsConfigParam";
import { EhsLab, initEhsLab } from "../../models/EhsLab";
import { EhsLanguage } from "../../models/EhsLanguage";
import { EhsRole } from "../../models/EhsRole";
import { EhsRou, initEhsRou } from "../../models/EhsRou";
import { isArrayEmpty } from "../../utils/arrayUtil";
import { isApiCallSuccess } from "../../utils/resultUtil";
import { getBasicLoginUserInfo, navigateToHome } from "../../utils/authUtil";
import { EhsFile } from "../../models/EhsFile";
import { FileAPI } from "../../../api/fileAPI";
import { getLabTextObj, getUserUnitTextObj } from "../../utils/langUtil";
import { EhsMultipleDetail } from "../../models/EhsMultipleDetail";
import DownloadButton from "ehs/common/button/DownloadButton";

const multiValType = {
  combustible: "combustible",
  otherHazardSubst: "otherHazardSubst",
}

function LabDetail() {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const { userIdText } = getUserUnitTextObj(loginUser, t);
  const { funcLabManageText, labAttribute, labFile, labFloorplan, labInformation, labPersonnel } = getLabTextObj(loginUser, t);
  const [labInfo, setLabInfo] = useState<EhsLab>(initEhsLab);//實驗室資訊
  const [labAttributes, setLabAttributes] = useState<EhsConfigParam[]>([]);//實驗室屬性
  const [labUsers, setLabUsers] = useState<EhsRou[]>([]);//實驗室人員
  const [labUserRoles, setLabUserRoles] = useState<{ [key: string]: EhsRou[] }>({});//實驗室人員擁有的角色
  const [checkLabAttributesMap, setCheckLabAttributesMap] = useState<{ [key: string]: string }>({});//已勾選實驗室屬性
  const [labRoleList, setLabRoleList] = useState<EhsRole[]>([]);
  const [roleIdCountsMap, setRoleIdCountsMap] = useState<Map<string, number>>(new Map());
  const [roleUserIdsMap, setRoleUserIdsMap] = useState<Map<string, string[]>>(new Map());
  const [labFileMap, setLabFileMap] = useState<{ [key: string]: EhsFile[] }>({});
  const [multiInputs, setMultiInputs] = useState<{ [key: string]: string[] }>({
    [multiValType.combustible]: [],
    [multiValType.otherHazardSubst]: [],
  });
  const { state } = useLocation();
  const { initLabId } = state || {};
  const { areaId, areaName, buildId, buildName, labNo,
    labName, labPhone, labPhoneExt, labFloor, labHousenum,
    labNote, labStatusName, orgNames } = labInfo;
  const checkedTopLabAttr = labAttributes.filter((item: EhsConfigParam) => item.configSubType === CONFIG_SUB_TYPE_TOP && checkLabAttributesMap[item.configId]);

  useEffect(() => {
    if (!initLabId) {
      navigateToHome(navigate);
      return;
    }
    if (loginUser) {
      fetchLabFile();
    }
  }, [loginUser])

  useEffect(() => {
    if (!initLabId) {
      navigateToHome(navigate);
      return;
    }

    if (loginUser) {
      fetchData();
    }
  }, [loginUser, i18n.language]);

  useEffect(() => {
    const newRoleIdCountsMap = new Map();
    const newRoleUserIdsMap = new Map();

    Object.values(labUserRoles).forEach((roles) => {
      roles.forEach((role) => {
        // 更新 roleIdCountsMap
        newRoleIdCountsMap.set(role.roleId, (newRoleIdCountsMap.get(role.roleId) || 0) + 1);

        // 更新 roleUserIdsMap
        if (newRoleUserIdsMap.has(role.roleId)) {
          newRoleUserIdsMap.get(role.roleId).push(role.userId);
        } else {
          newRoleUserIdsMap.set(role.roleId, [role.userId]);
        }
      });
    });

    setRoleIdCountsMap(newRoleIdCountsMap);
    setRoleUserIdsMap(newRoleUserIdsMap);
  }, [labUserRoles]);

  const fetchData = () => {
    LabAPI.getLabDetail({
      ...getBasicLoginUserInfo(loginUser)!,
      labId: initLabId
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const labResults = result.results;
        const labDetail = labResults.labBean;
        const labNameObject = labDetail.labNameList.find((item: EhsLanguage) => item.langType === loginUser!.langType);
        const labName = labNameObject ? labNameObject.langValue : '';
        setLabInfo({
          ...labResults.labBean,
          labName: labName
        });
        const labDetailMultiList = labResults.labDetailMultiList;

        if (labDetailMultiList && !isArrayEmpty(labDetailMultiList)) {
          // 使用一個新的物件來更新 multiInputs
          const newMultiInputs = { ...multiInputs };

          labDetailMultiList.forEach((item: EhsMultipleDetail) => {
            const { detailType, detailContent } = item;
            // 檢查 detailType 是否存在於 multiInputs 中
            if (newMultiInputs[detailType]) {
              // 將 detailContent 加入對應的陣列中
              newMultiInputs[detailType].push(detailContent);
            }
          });

          // 更新 state
          setMultiInputs(newMultiInputs);
        }
        const attrList = labResults.attributesBeanList;
        if (attrList && !isArrayEmpty(attrList)) {
          attrList.forEach((attr: EhsConfigParam) => {
            checkLabAttributesMap[attr.configId] = attr.configSubType;
            setCheckLabAttributesMap(checkLabAttributesMap);
          });
        }

        // 創建一個新的 labUsers 和 labUserRoles 對象
        const newLabUsers: { [key: string]: EhsRou } = {};
        const newLabUserRoles: { [key: string]: EhsRou[] } = {};

        labResults.rouBeanList.forEach((rouBean: EhsRou) => {
          const { userId } = rouBean;
          // 如果 labUserRoles 中不存在該 userId，則將對應的 EhsRou 放入 labUsers 和 labUserRoles 中
          if (!newLabUserRoles[userId]) {
            newLabUsers[userId] = rouBean;
            newLabUserRoles[userId] = [rouBean];
          } else {
            // 如果 labUserRoles 中存在該 userId，則只將對應的 EhsRou 放入 labUserRoles 中
            newLabUserRoles[userId].push(rouBean);
          }
        });

        // 更新狀態，只調用一次 setLabUsers 和 setLabUserRoles
        setLabUsers(Object.values(newLabUsers));
        setLabUserRoles(newLabUserRoles);
      }
    });

    RoleAPI.getLabRoleList({
      ...getBasicLoginUserInfo(loginUser)!,
    }).then(result => {
      if (isApiCallSuccess(result)) {
        setLabRoleList(result.results);
      }
    })

    CheckListAPI.getLabAttributes({
      ...getBasicLoginUserInfo(loginUser)!,
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        setLabAttributes(result.results);
      }
    });
  };

  const fetchLabFile = () => {
    FileAPI.getFileList({
      ...getBasicLoginUserInfo(loginUser),
      fileTypeList: [FILE_TYPE_LAB],
      fileMappingIdList: [initLabId]
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const labFileMap = result.results.reduce((acc: { [key: string]: EhsFile[] }, file: EhsFile) => {
          const subtype = file.fileSubtype;

          // 如果 map 中已經存在這個 subtype，則將該 file 加入對應的陣列
          if (!acc[subtype]) {
            acc[subtype] = [];
          }
          acc[subtype].push(file);

          return acc;
        }, {});
        setLabFileMap(labFileMap);
      }
    })
  }

  labUsers.sort((a, b) => a.userId.localeCompare(b.userId));//實驗室人員照ID 排序

  return (
    <StlyedLabDetail >
      <div className="d-flex flex-column p-0" id="content">
        {/* BEGIN scrollbar */}
        <div className="app-content-padding flex-grow-1">
          {/* BEGIN breadcrumb */}
          <Breadcrumbs
            items={[
              { label: t("func.system_manage_setting") },
              { label: funcLabManageText, path: AppPaths.manage.labList },
              { label: t('func.detail') },
            ]}
          />
          {/* END breadcrumb */}
          {/* BEGIN page-header */}
          <h1 className="page-header">{t('func.detail')} </h1>
          {/* END page-header */}
          <BackButton />
          <div className="card px-3 py-4 mt-3">
            <div className="card-body">
              <div className="row justify-content-between">
                <div className="col-xxl-5 col-xl-12 ms-6">
                  {/* <!-- 實驗室場所基本資料 --> */}
                  <div className="row justify-content-center labBox pe-1 h-100">
                    <div className="col-md-12">
                      <h4 className="mb-3 text-orange-600">{labInformation}</h4>
                    </div>
                    <div className="col-md-6">
                      <div className="row text-start align-items-center fs-5 mt-3">
                        <div className="col-md-4 mb-1 bg-primary text-white py-1">{t('text.no')}</div>
                        <div className="col-md-8 mb-1">{labNo}</div>
                        <hr className="m-0" />
                        <div className="col-md-4 mb-1 bg-primary text-white py-1">{t('text.name')}</div>
                        <div className="col-md-8 mb-1">{labName}</div>
                        <hr className="m-0" />
                        <div className="col-md-4 mb-1 bg-primary text-white py-1">{t('text.dedicated_line')}</div>
                        <div className="col-md-8 mb-1">{labPhone}</div>
                        <hr className="m-0" />
                        <div className="col-md-4 mb-1 bg-primary text-white py-1">{t('text.phone_ext')}</div>
                        <div className="col-md-8 mb-1">{labPhoneExt}</div>
                        <hr className="m-0" />
                        <div className="col-md-4 mb-1 bg-primary text-white py-1">{t('text.lab.house_number')}</div>
                        <div className="col-md-8 mb-1">{labHousenum} </div>
                        <hr className="m-0" />
                        <div className="col-md-4 mb-1 bg-primary text-white py-1">{t('text.note')}</div>
                        <div className="col-md-8 mb-1">{labNote}</div>
                        <hr className="m-0" />
                        <div className="col-md-4 mb-1 bg-primary text-white py-1">{t('text.combustible_material')}</div>
                        <div className="col-md-8 mb-1">
                          {multiInputs.combustible.map((content, index) => (
                            <span key={'labdetailCombustible' + index}>
                              {content}
                              {index < multiInputs.combustible.length - 1 && '、'}
                            </span>
                          ))}
                        </div>
                        <hr className="m-0" />
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="row text-start align-items-center fs-5 mt-3">
                        <div className="col-md-4 mb-1 bg-primary text-white py-1">{t('text.status')}</div>
                        <div className="col-md-8 mb-1">{labStatusName}</div>
                        <hr className="m-0" />
                        <div className="col-md-4 mb-1 bg-primary text-white py-1">{t('text.area.item')}</div>
                        <div className="col-md-8 mb-1">{areaName}</div>
                        <hr className="m-0" />
                        {areaId && <>
                          <div className="col-md-4 mb-1 bg-primary text-white py-1">{t('text.building.item')}</div>
                          <div className="col-md-8 mb-1">{buildName}</div>
                          <hr className="m-0" />
                        </>}
                        {areaId && buildId && <>
                          <div className="col-md-4 mb-1 bg-primary text-white py-1">{t('text.building.floor.item')}</div>
                          <div className="col-md-8 mb-1">{labFloor}</div>
                          <hr className="m-0" />
                        </>}
                        <div className="col-md-4 mb-1 bg-primary text-white py-1">{t('text.org.item')}</div>
                        <div className="col-md-8 mb-1"><NamesSplitFragment names={orgNames} separator={ORG_SPLIT_FLAG} /></div>
                        <hr className="m-0" />
                        <div className="col-md-4 mb-1 bg-primary text-white py-1">{t('text.other_hazard_substance')}</div>
                        <div className="col-md-8 mb-1">
                          {multiInputs.otherHazardSubst.map((content, index) => (
                            <span key={'labdetailOtherHazardSubstance' + index}>
                              {content}
                              {index < multiInputs.otherHazardSubst.length - 1 && '、'}
                            </span>
                          ))}
                        </div>
                        <hr className="m-0" />
                      </div>
                    </div>
                    {/* <!-- icon --> */}
                    <i className="col-2 fas fa-building d-none d-xxl-block"></i>
                  </div>
                </div>
                <div className="col-xxl-5 col-xl-12 me-3 mb-2 pb-2">
                  {/* <!-- 實驗室場所屬性 --> */}
                  <div className="row labBox">
                    <div className="col-md-12">
                      <h4 className="text-orange-600 d-inline-block me-3">{labAttribute}</h4>
                      {checkLabAttributesMap && !isArrayEmpty(Object.keys(checkLabAttributesMap)) &&
                        <div className="table-responsive">
                          <table className="table border-none fs-5 mt-3">
                            <tbody>
                              {checkedTopLabAttr.map((topConfig) => (
                                <tr key={topConfig.configId}>
                                  <td>{topConfig.configName}</td>
                                  <td>
                                    {labAttributes
                                      .filter((item: EhsConfigParam) => item.configSubType === topConfig.configId && checkLabAttributesMap[item.configId])
                                      .map((subConfig, index) => (
                                        <React.Fragment key={subConfig.configId}>
                                          {/* 如果不是第一個子配置，則加上逗號 */}
                                          {index !== 0 && "、"}
                                          {/* 子配置的 configName */}
                                          {subConfig.configName}
                                        </React.Fragment>
                                      ))}
                                  </td>
                                </tr>
                              ))
                              }
                            </tbody>
                          </table>
                        </div>
                      }
                    </div>
                    {/* <!-- icon --> */}
                    <i className="col-2 fas fa-vial-virus d-none d-xxl-block"></i>
                  </div>
                  {/* <!-- 實驗室場所檔案 --> */}
                  <div className="row labBox">
                    <h4 className="text-orange-600">{labFile}</h4>
                    {/* <!-- icon --> */}
                    <i className="fas fa-file d-none d-xxl-block"></i>
                    <div className="col-12 mb-3">
                      <label className="h5">{labFloorplan}</label><br />
                      {labFileMap[FILE_SUB_TYPE_LAB_FLOORPLAN]?.map((file) => (
                        <DownloadButton key={'labfile-floorplan-' + file.fileId} content={file.fileContentBase64} fileName={file.fileName} />
                      ))}
                    </div>
                    {checkedTopLabAttr.map((topConfig) => (
                      labFileMap[topConfig.configValue] && <div className="col-12 mb-3" key={'labfile-div-' + topConfig.configId}>
                        <label className="h5">{t('text.lab.attribute_file', { labAttr: topConfig.configName })}</label><br />
                        {labFileMap[topConfig.configValue]?.map((file) => (
                          <DownloadButton key={'labfile-' + topConfig.configId + '-' + file.fileId} content={file.fileContentBase64} fileName={file.fileName} />
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* <!-- 實驗室場所人員清單 --> */}
          <div className="card px-3 py-4 mt-3">
            <div className="card-body">
              <div className="row mb-5">
                <div className="col-xl-12">
                  <h3 className="text-orange-600 d-inline-block me-3"><i className="fas fa-users-gear me-3"></i>
                    {labPersonnel}
                  </h3>
                </div>
              </div>
              <div className="lab-user-role-table">
                <div className="table-responsive">
                  <table id="data-table-default"
                    className="table align-middle dt-responsive nowrap">
                    <thead className="text-center fs-4 fw-bold">
                      <tr>
                        <th>{t('table.title.role.item')}</th>
                        <th>{userIdText}</th>
                        <th className="text-start">{t('table.title.lab.username_jobtitle')}</th>
                        <th className="text-start">{t('table.title.org.item')}</th>
                        <th className="text-start">{t('table.title.email')}</th>
                      </tr>
                    </thead>
                    <tbody className="text-center fs-5">
                      {labRoleList && labRoleList.map((role, index) => {
                        const isOddDataRowClass = index % 2 === 0 ? "role_title_odd_td" : ""
                        const userIds = roleUserIdsMap.get(role.roleId);
                        const [firstUserId, ...remainingUserIds] = userIds || []; // 使用数组的解构赋值
                        const firstUserInfo = firstUserId ? labUsers.find(rou => rou.userId === firstUserId) || initEhsRou : initEhsRou;

                        return (
                          <React.Fragment key={'labuser-' + role.roleId}>
                            <tr className={isOddDataRowClass} key={'labuser-' + role.roleId}>
                              <td data-title={t('table.title.role.item')} rowSpan={roleIdCountsMap.get(role.roleId)}>{role.roleName}</td>
                              <td data-title={userIdText}>{firstUserInfo.userId}</td>
                              <td data-title={t('table.title.lab.username_jobtitle')} className="text-start">{firstUserInfo.userName}<br />{firstUserInfo.jobTitle}</td>
                              <td data-title={t('table.title.org.item')} className="text-start"><NamesSplitFragment names={firstUserInfo.orgNames} separator={ORG_SPLIT_FLAG} /></td>
                              <td data-title={t('table.title.email')} className="text-start">{firstUserInfo.userEmail}</td>
                            </tr>
                            {remainingUserIds && remainingUserIds.map((userId, idx) => {
                              const userInfo = labUsers.find(rou => rou.userId === userId) || initEhsRou;
                              return (
                                <tr className={isOddDataRowClass} key={'labuser-' + role.roleId + '-' + userId}>
                                  <td data-title={userIdText}>{userInfo.userId}</td>
                                  <td data-title={t('table.title.lab.username_jobtitle')} className="text-start">{userInfo.userName}<br />{userInfo.jobTitle}</td>
                                  <td data-title={t('table.title.org.item')} className="text-start"><NamesSplitFragment names={userInfo.orgNames} separator={ORG_SPLIT_FLAG} /></td>
                                  <td data-title={t('table.title.email')} className="text-start">{userInfo.userEmail}</td>
                                </tr>
                              );
                            })}
                          </React.Fragment>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* BEGIN #footer */}
        <Footer />
        {/* END #footer */}
      </div>
    </StlyedLabDetail>
  );
}


const StlyedLabDetail = styled.div`
  /* icon放大 */
  td .fas {
      font-size: 1.2rem;
      padding: .5rem;
  }
  
  /* 實驗室屬性表格 */
  table.border-none tr td {
      border-bottom-width: 0;
  }

  /*奇數角色用灰色 做區隔*/
  .role_title_odd_td{
      background:#e9ecef;
  }
  
  table.border-none tr td:nth-child(1) {
      font-size: 1rem;
      width: 15%;
  }

  thead {
    background:rgb(251, 205, 165);
  }

  .labBox {
      border: solid 1px #ededed;
      border-left: solid 3px #f59c1a;
      border-radius: 10px;
      padding: 1rem .5rem;
      margin-bottom: 1rem;
      position: relative;
  }

  .labBox:hover{
    border: 2px solid #f59c1a;
  }
  
  .labBox .fas {
      position: absolute;
      font-size: 4rem;
      color: #f59c1a;
      opacity: .5;
      left: -6rem;
      z-index: 1;
  }

  .ms-6 {
      margin-left: 100px;
  }

  /* 人員表格的checkbox 置中 */
  #data-table-default input {
      float: none;
  }

  @media (max-width: 1400px) {
      .ms-6 {
          margin-left: 0;
      }
  }
  
  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    lab-user-role-table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 100px;
        text-align:left;
        min-height:50px;
        height:auto;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
        content: attr(data-title);
        position: absolute;
        top: 6px;
        left: 6px;
        width: 30%;
        min-height: 100%;
        height: auto;
        padding-right: 10px;
        text-align: left;
        font-weight: bold;
        color: #1a1a1a;
      }

    }
    
  }
`;

export default LabDetail;
