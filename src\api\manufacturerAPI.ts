import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const ManufacturerAPI = {
  getEnableManufacturerList: async (parms: BaseParams) => {
    return apiRequest(getApiAddress + "manufacturer/list/enable", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};
