import GhsImage from "ehs/common/GhsImage";
import TextWithLineBreaks from 'ehs/common/TextWithLineBreaks';
import DownloadButton from 'ehs/common/button/DownloadButton';
import ChemicalClassificationBadge from "ehs/common/chemical/ChemicalClassificationBadge";
import { CONFIG_TYPE_GHS_IMG, CONFIG_TYPE_PUBLIC_HAZARD } from "ehs/constant/constants";
import { EhsChemicalInventory } from "ehs/models/EhsChemicalInventory";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { EhsFile } from "ehs/models/EhsFile";
import { getChemicalClassifications, getDetailCategoryByType, getShowChemCon } from "ehs/utils/chemicalUtil";
import { getFormatDateSlash } from "ehs/utils/stringUtil";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { splitChemNameListByLang } from "ehs/utils/langUtil";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { EhsChemical, initEhsChemical } from "ehs/models/EhsChemical";

// 擴展EhsPurchaseSubst型別定義，添加實際使用的屬性
interface EnhancedSubstance {
    substId: string;
    isPrimary: string;
    conType: string;
    conLower: number | null;
    conUpper: number | null;
    substPhaseState: string;
    note: string;
    categoryList: any[];
    casNo?: string;
    chemCtrlNo?: string;
    chemNameList?: any[];
    chemName?: string;
}

interface ChemicalDetailDialogProps {
    inventory: EhsChemicalInventory;
    configMap: { [configId: string]: EhsConfigParam };
    sdsFile: EhsFile | null;
    onClose: () => void;
}

export default function ChemicalDetailDialog({
    inventory,
    configMap,
    sdsFile,
    onClose
}: ChemicalDetailDialogProps) {
    const { t, i18n } = useTranslation();
    const [currentSubstance, setCurrentSubstance] = useState(0);
    const {
        barcode,
        concentration,
        inventoryQty,
        inventoryNote,
        inventoryLocation,
        inspDate,
        manufacturerName,
        sdsExpDate,
        createId,
        creatorName,
        detailCategoryList,
        substList
    } = inventory;

    // 使用工具函數獲取化學品分類和 GHS 圖片
    const chemClasses = getChemicalClassifications(substList, configMap);
    const ghsImages = getDetailCategoryByType(detailCategoryList, configMap, CONFIG_TYPE_GHS_IMG);
    const publicHazardCategories = getDetailCategoryByType(detailCategoryList, configMap, CONFIG_TYPE_PUBLIC_HAZARD);

    // 將substList轉換為獲取到完整數據的格式
    const enhancedSubstList: EnhancedSubstance[] = substList.map(subst => {
        // 嘗試從 chemical 中獲取 casno 和 chemNameList
        const chemical: EhsChemical = (subst as any).chemical || inventory.chemical || initEhsChemical;
        return {
            ...subst,
            casno: (subst as any).casno || chemical.casno || '',
            chemNameList: (subst as any).chemNameList || chemical.nameList || [],
            chemCtrlNo: (subst as any).chemCtrlNo || chemical.chemCtrlNo || ''
        };
    });

    return (
        <StyledChemicalDetail>
            <div className="chemicalDetail">
                <div className="chemicalDetail-header">
                    <h4 className="modal-title text-primary"><i className="fas fa-flask me-2"></i>{t('text.detail')}</h4>
                    <button
                        type="button"
                        className="btn-close"
                        onClick={onClose}
                    ></button>
                </div>
                <div className="chemicalDetail-body">
                    <div className="row">
                        <div className="col-12 mb-4">
                            <div className="chemical-categories p-3 bg-light rounded mb-4">
                                <ul className="list-unstyled mb-0">
                                    <li className="mb-3">
                                        <strong className="me-2">• {t('text.chemical.class')}：</strong>
                                        <div className="d-inline-flex flex-wrap align-items-center">
                                            {chemClasses.map((category) => (
                                                <span key={category.configId} className="me-2 mb-1">
                                                    <ChemicalClassificationBadge item={category} />
                                                </span>
                                            ))}
                                        </div>
                                    </li>
                                    <li className="mb-3">
                                        <strong className="me-2">• {t('text.chemical.ghs_img')}：</strong>
                                        <div className="d-inline-flex flex-wrap align-items-center">
                                            {ghsImages.map((img, index) => {
                                                const { configId, configValue, configName } = img || {};
                                                return (
                                                    <GhsImage
                                                        key={'ghs_img_' + configId}
                                                        src={configValue}
                                                        alt={configName}
                                                        title={configName}
                                                        extraClassName="me-2 mb-1"
                                                        className="ghs-image"
                                                    />
                                                );
                                            })}
                                        </div>
                                    </li>
                                    <li className="mb-0">
                                        <strong className="me-2">• {t('text.chemical.public_hazard_classify')}：</strong>
                                        <div className="d-inline">
                                            {publicHazardCategories.map((item, index) =>
                                            (<React.Fragment key={'toxic_' + item.configId}>
                                                <label>{item.configName}</label><br />
                                            </React.Fragment>)
                                            )}
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div className="chemical-substances mb-4">
                            <h5 className="section-title">{t('text.chemical.substance')}</h5>
                            <div className="row g-3">
                                <div className="col-md-4">
                                    <div className="list-group substance-list">
                                        {enhancedSubstList && enhancedSubstList.map((substance, index) => {
                                            // 取得化學品名稱
                                            const { isPrimary, casNo, chemName } = substance;

                                            return (
                                                <button
                                                    key={substance.substId}
                                                    type="button"
                                                    className={`list-group-item list-group-item-action d-flex align-items-center ${index === currentSubstance ? 'active' : ''}`}
                                                    onClick={() => setCurrentSubstance(index)}
                                                >
                                                    {isPrimary && (
                                                        <span className="badge bg-danger me-2">{t('text.chemical.main_substance')}</span>
                                                    )}
                                                    <div>
                                                        <div className="fw-bold">{casNo}</div>
                                                        <small>{chemName}</small>
                                                    </div>
                                                </button>
                                            );
                                        })}
                                    </div>
                                </div>
                                <div className="col-md-8">
                                    {enhancedSubstList && enhancedSubstList.length > 0 && (
                                        <div className="card h-100 border-primary substance-detail-card">
                                            <div className="card-header bg-primary bg-opacity-10 d-flex align-items-center">
                                                {enhancedSubstList[currentSubstance].isPrimary && (
                                                    <span className="badge bg-danger me-2">{t('text.chemical.main_substance')}</span>
                                                )}
                                                <h6 className="card-title mb-0">
                                                    {t('text.chemical.substance_detail')}
                                                </h6>
                                            </div>
                                            <div className="card-body">
                                                <table className="table table-bordered mb-0">
                                                    <tbody>
                                                        {(() => {
                                                            // 獲取當前選擇的物質
                                                            const currentSubst = enhancedSubstList[currentSubstance];
                                                            const { casNo, chemName, substPhaseState, note, categoryList,
                                                                conType, conLower, conUpper
                                                            } = currentSubst;
                                                            // 處理濃度顯示
                                                            const conTypeConfig = configMap[conType];
                                                            const { configIvalue, configName } = conTypeConfig || {};
                                                            const showCon = getShowChemCon(configIvalue, conLower, conUpper, configName);

                                                            return (
                                                                <>
                                                                    <tr>
                                                                        <th className="bg-light" style={{ width: '30%' }}>{t('text.chemical.casno')}</th>
                                                                        <td>{casNo}</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th className="bg-light">{t('text.chemical.name')}</th>
                                                                        <td>{chemName}</td>
                                                                    </tr> 
                                                                    <tr>
                                                                        <th className="bg-light">{t('text.chemical.phase_state')}</th>
                                                                        <td>{configMap[substPhaseState]?.configName || ""}</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th className="bg-light">{t('text.concentration')}</th>
                                                                        <td>{showCon}</td>
                                                                    </tr>
                                                                    {categoryList && !isArrayEmpty(categoryList) && (
                                                                        <tr>
                                                                            <th className="bg-light">{t('text.chemical.substance_class')}</th>
                                                                            <td>
                                                                                <div className="d-flex flex-wrap align-items-center">
                                                                                    {categoryList.map((category, idx) => (
                                                                                        <span key={idx} className="d-inline-block me-1 mb-1">
                                                                                            <ChemicalClassificationBadge item={configMap[category.configId]} />
                                                                                        </span>
                                                                                    ))}
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                    )}
                                                                    {note && (
                                                                        <tr>
                                                                            <th className="bg-light">{t('text.note')}</th>
                                                                            <td>{note}</td>
                                                                        </tr>
                                                                    )}
                                                                </>
                                                            );
                                                        })()}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="col-12 mb-4">
                        <h5 className="section-title">{t('text.inventory_info')}</h5>
                        <div className="card border-info fixed-height-card">
                            <div className="card-header bg-info bg-opacity-10">
                                <h6 className="mb-0">{t('text.inventory_info')}</h6>
                            </div>
                            <div className="card-body">
                                <table className="table table-bordered mb-0">
                                    <tbody>
                                        <tr>
                                            <th className="bg-light">{t('table.title.barcode')}</th>
                                            <td>{barcode}</td>
                                            <th className="bg-light">{t('table.title.concentration')}(%)</th>
                                            <td>{concentration}</td>
                                        </tr>
                                        <tr>
                                            <th className="bg-light">{t('table.title.inventory_quantity')}(kg)</th>
                                            <td>{inventoryQty}</td>
                                            <th className="bg-light">{t('table.title.storage_location')}</th>
                                            <td>{configMap[inventoryLocation]?.configName || t('text.chemical.inventory_location_default')}</td>
                                        </tr>
                                        <tr>
                                            <th className="bg-light">{t('table.title.arrival_date_oper')}</th>
                                            <td>{getFormatDateSlash(inspDate)}</td>
                                            <th className="bg-light">{t('table.title.vendor')}</th>
                                            <td>{manufacturerName}</td>
                                        </tr>
                                        <tr>
                                            <th className="bg-light">{t('table.title.buyer')}</th>
                                            <td colSpan={3}>
                                                {createId}<br />
                                                {creatorName}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div className="col-12 mb-4">
                        <h5 className="section-title">{t('table.title.sds_file')}</h5>
                        <div className="card border-warning fixed-height-card-small">
                            <div className="card-header bg-warning bg-opacity-10">
                                <h6 className="mb-0">{t('table.title.sds_file')}</h6>
                            </div>
                            <div className="card-body">
                                <table className="table table-bordered mb-0">
                                    <tbody>
                                        <tr>
                                            <th className="bg-light">{t('table.title.sds_file')}</th>
                                            <td>
                                                {sdsFile?.fileContentBase64 &&
                                                    <DownloadButton
                                                        content={sdsFile.fileContentBase64}
                                                        fileName={sdsFile.fileName || ""}
                                                    />
                                                }
                                            </td>
                                            <th className="bg-light">{t('text.chemical.sds_file_exp_date')}</th>
                                            <td>{sdsExpDate && getFormatDateSlash(sdsExpDate)}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div className="col-12 mb-4">
                        <h5 className="section-title">{t('table.title.chemical.note')}</h5>
                        <div className="card border-secondary fixed-height-card-small">
                            <div className="card-header bg-secondary bg-opacity-10">
                                <h6 className="mb-0">{t('table.title.chemical.note')}</h6>
                            </div>
                            <div className="card-body">
                                <TextWithLineBreaks text={inventoryNote} />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="chemicalDetail-footer">
                <button type="button" className="btn btn-secondary" onClick={onClose}>
                    <i className="fas fa-times me-1"></i>{t('button.close')}
                </button>
            </div>
        </StyledChemicalDetail>
    );
}

const StyledChemicalDetail = styled.div`
    .chemicalDetail {
        width: 900px;
        max-width: 90vw;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        overflow: hidden;
        
        @media (max-width: 992px) {
            width: 90vw;
        }
        
        @media (max-width: 768px) {
            width: 95vw;
        }

        &-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #dee2e6;
            background-color: #f8f9fa;
        }

        &-body {
            padding: 1.5rem;
            max-height: 650px;
            overflow-y: auto;
            
            @media (max-width: 992px) {
                max-height: 70vh;
            }
        }

        &-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: flex-end;
            background-color: #f8f9fa;
        }

        .section-title {
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f8f9fa;
            color: #495057;
            font-weight: 600;
        }

        table {
            th {
                width: 20%;
                vertical-align: middle;
                font-weight: 600;
            }
            td {
                vertical-align: middle;
            }
        }

        .ghs-image {
            width: 64px;
            height: 64px;
        }
        
        .substance-list {
            height: 300px;
            overflow-y: auto;
            scrollbar-width: thin;
            
            &::-webkit-scrollbar {
                width: 6px;
            }
            
            &::-webkit-scrollbar-thumb {
                background-color: #ccc;
                border-radius: 3px;
            }
            
            @media (max-width: 768px) {
                height: 200px;
            }
        }
        
        .substance-detail-card {
            min-height: 300px;
            height: 300px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            
            .card-body {
                overflow-y: auto;
                flex: 1;
                scrollbar-width: thin;
                
                &::-webkit-scrollbar {
                    width: 6px;
                }
                
                &::-webkit-scrollbar-thumb {
                    background-color: #ccc;
                    border-radius: 3px;
                }
            }
            
            @media (max-width: 768px) {
                min-height: 200px;
                height: 200px;
            }
        }
        
        .card {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: box-shadow 0.3s ease;
            
            &:hover {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }
        }

        .fixed-height-card {
            height: 250px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            
            .card-body {
                overflow-y: auto;
                flex: 1;
                scrollbar-width: thin;
                
                &::-webkit-scrollbar {
                    width: 6px;
                }
                
                &::-webkit-scrollbar-thumb {
                    background-color: #ccc;
                    border-radius: 3px;
                }
            }
            
            @media (max-width: 768px) {
                height: auto;
                max-height: 250px;
            }
        }
        
        .fixed-height-card-small {
            height: 150px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            
            .card-body {
                overflow-y: auto;
                flex: 1;
                scrollbar-width: thin;
                
                &::-webkit-scrollbar {
                    width: 6px;
                }
                
                &::-webkit-scrollbar-thumb {
                    background-color: #ccc;
                    border-radius: 3px;
                }
            }
            
            @media (max-width: 768px) {
                height: auto;
                max-height: 150px;
            }
        }
    }
`;