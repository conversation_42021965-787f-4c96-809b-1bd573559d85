import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { EhsBarcodeLayoutField } from "../CONNChainEHS/models/EhsBarcodeLayoutField";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const BarcodeAPI = {
    getBarcodeList: async (
        parms: BaseParams & {
            barcodeTableName: string;
            barcode?: string;
            createDateStart?: string;
            createDateEnd?: string;
        }
    ) => {
        return apiRequest(getApiAddress + "barcode/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
    },
    getBarcodeLayoutFieldList: async (
        parms: BaseParams & {
        }
    ) => {
        return apiRequest(getApiAddress + "barcode/layout/field/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
    },
    saveBarcodeLayoutFields: async (
        parms: BaseParams & {
            layoutFields: EhsBarcodeLayoutField[];
        }
    ) => {
        return apiRequest(getApiAddress + "barcode/layout/field/save", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
    },
};