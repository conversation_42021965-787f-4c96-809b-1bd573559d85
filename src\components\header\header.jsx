// @ts-nocheck
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { AuthorAPI } from '../../api/authorAPI';
import logoImg from '../../assets/img/cloudthink-logo.png';
import { AppPaths } from "../../config/app-paths";
import Dialog from '../../CONNChainEHS/common/Dialog';
import { REACT_QUERY_CURRENT_USER, STORAGE_PAGE_LANGUAGE, WEB_IS_TEST_CUSTOMER } from '../../CONNChainEHS/constant/constants.ts';
import { ActionMode } from "../../CONNChainEHS/enums/ActionMode";
import { Language } from '../../CONNChainEHS/enums/Language';
import useLoginUser from '../../CONNChainEHS/hooks/useLoginUser';
import { hasChangeLanguage, langMap } from '../../CONNChainEHS/utils/langUtil.ts';
import { refreshLocalStorageForLogin } from '../../CONNChainEHS/utils/storageUtil';
import { AppSettings } from './../../config/app-settings.js';
import AgentSettingDialog from './AgentSettingDialog';
import ChangePermissionDialog from './ChangePermissionDialog';
import ChangePwdDialog from './ChangePwdDialog';
import DropdownMegaMenu from './dropdown/mega.jsx';
import WebTitleText from './WebTitleText';
import SystemTitleComponent from '../../CONNChainEHS/common/SystemTitleComponent';

function Header() {
	const navigate = useNavigate();
	const { t, i18n } = useTranslation();
	const { loginUser, isAgent } = useLoginUser();
	const queryClient = useQueryClient();
	const defaultLang = Language.ZHTW;
	const [flag, setFlag] = useState(defaultLang);
	const [popupDialogChangePwd, setPopupDialogChangePwd] = useState(false);
	const [popupDialogChangePermission, setPopupDialogChangePermission] = useState(false);
	const [popupDialogAgentSetting, setPopupDialogAgentSetting] = useState(false);

	useEffect(() => {
		setCurrentLang();
	}, [i18n.language])

	const setCurrentLang = () => {
		const nation = langMap[i18n.language] || langMap[defaultLang];
		setFlag(nation);
	};

	const goToProfile = () => {
		navigate(`/${AppPaths.manage.userEdit}`, { state: { userId: loginUser.loginUserId, roleId: loginUser.loginRoleId, mode: ActionMode.EDIT } });
	}

	const showChangePwdDialog = () => {
		setPopupDialogChangePwd(true);
	}

	const showChangePermissionDialog = () => {
		setPopupDialogChangePermission(true);
	}

	const showAgentSettingDialog = () => {
		setPopupDialogAgentSetting(true);
	}

	const doLogout = async () => {
		await AuthorAPI.logout({
			uniformNum: loginUser.uniformNum,
			loginId: loginUser.loginUserId,
			langType: Language.ZHTW,
		});

		// 清除 React Query 快取
		queryClient.clear();
		// 特別清除使用者資訊的快取
		queryClient.removeQueries({ queryKey: [REACT_QUERY_CURRENT_USER] });

		refreshLocalStorageForLogin();
		window.location.href = '/' + AppPaths.login;
	}

	return (
		<AppSettings.Consumer>
			{({ toggleAppSidebarMobile, toggleAppSidebarEnd, toggleAppSidebarEndMobile, toggleAppTopMenuMobile, appHeaderLanguageBar, appHeaderMegaMenu, appHeaderInverse, appSidebarTwo, appTopMenu, appSidebarNone }) => (
				<div id="header" className="app-header" data-bs-theme={appHeaderInverse ? 'dark' : ''}>
					<div className="navbar-header">
						{appSidebarTwo && (
							<button type="button" className="navbar-mobile-toggler" onClick={toggleAppSidebarEndMobile}>
								<span className="icon-bar"></span>
								<span className="icon-bar"></span>
								<span className="icon-bar"></span>
							</button>
						)}
						<Link to={`/${AppPaths.home}`} className="navbar-brand">{/* <span className="navbar-logo"></span> */}<img src={logoImg} alt="cloudthink" className='me-3' /> <WebTitleText /><SystemTitleComponent />
						</Link>

						{appHeaderMegaMenu && (
							<button type="button" className="navbar-mobile-toggler" data-bs-toggle="collapse" data-bs-target="#top-navbar">
								<span className="fa-stack fa-lg text-inverse">
									<i className="far fa-square fa-stack-2x"></i>
									<i className="fa fa-cog fa-stack-1x"></i>
								</span>
							</button>
						)}
						{appTopMenu && !appSidebarNone && (
							<button type="button" className="navbar-mobile-toggler" onClick={toggleAppTopMenuMobile}>
								<span className="fa-stack fa-lg text-inverse">
									<i className="far fa-square fa-stack-2x"></i>
									<i className="fa fa-cog fa-stack-1x"></i>
								</span>
							</button>
						)}
						{appSidebarNone && appTopMenu && (
							<button type="button" className="navbar-mobile-toggler" onClick={toggleAppTopMenuMobile}>
								<span className="icon-bar"></span>
								<span className="icon-bar"></span>
								<span className="icon-bar"></span>
							</button>
						)}
						{!appSidebarNone && (
							<button type="button" className="navbar-mobile-toggler" onClick={toggleAppSidebarMobile}>
								<span className="icon-bar"></span>
								<span className="icon-bar"></span>
								<span className="icon-bar"></span>
							</button>
						)}
					</div>

					{appHeaderMegaMenu && (
						<DropdownMegaMenu />
					)}
					{
						<Dialog
							content={<ChangePwdDialog onActionSuccess={() => setPopupDialogChangePwd(false)} onClose={() => setPopupDialogChangePwd(false)} />}
							show={popupDialogChangePwd}
						/>
					}
					{
						<Dialog
							content={
								<ChangePermissionDialog
									onClose={() => setPopupDialogChangePermission(false)}
									show={popupDialogChangePermission}
								/>
							}
							show={popupDialogChangePermission}
						/>
					}
					{
						<Dialog
							content={<AgentSettingDialog onActionSuccess={() => setPopupDialogAgentSetting(false)}
								onClose={() => setPopupDialogAgentSetting(false)}
								show={popupDialogAgentSetting} />}
							show={popupDialogAgentSetting}
						/>
					}
					<div className="navbar-nav">
						<RightCornerWrap>
							{!WEB_IS_TEST_CUSTOMER && <div className="navbar-item dropdown">
								<Link href="#" className="navbar-link dropdown-toggle px-0" data-bs-toggle="dropdown" aria-expanded="false">
									<span className="currentFlag mb-0 me-1 w-100" title={flag.title}>{flag.language}</span>
									<b className="caret"></b>
								</Link>
								<div className="dropdown-menu dropdown-menu-end me-1">
									{Object.entries(langMap).map(([key, value]) => (
										<button
											key={key}
											// to="#" // 这里可以是你希望的路径，或者为空字符串
											className="flag dropdown-item d-flex align-items-center mb-3"
											title={value.title}
											onClick={() => {
												if (hasChangeLanguage(key, i18n.language)) {
													i18n.changeLanguage(key);
													// 保存語言選擇到 localStorage
													localStorage.setItem(STORAGE_PAGE_LANGUAGE, key);
												}
												// navigate(currentPath, { state: state });
											}}
										>
											{/* 如果你想在這裡加入國旗圖片，可以使用 value.code 來得到代碼 */}
											{/* <img src={yourFlagImageSource} alt="" /> */}
											<div className="ps-2 fw-bold">{value.language}</div>
										</button>
									))}
								</div>
							</div>}
							<div className="navbar-item navbar-user dropdown">
								<Link href="#" className="navbar-link dropdown-toggle d-flex align-items-center" data-bs-toggle="dropdown">
									<i className="fas fa-circle-user fa-2x"></i>
									<span>
										{/* <span className="d-none d-md-inline">{userName }</span> */}
										<b className="caret"></b>
									</span>
								</Link>
								<div className="dropdown-menu dropdown-menu-end">
									<button className="dropdown-item" onClick={goToProfile}>{t('button.profile')}</button>
									<div className="dropdown-divider"></div>
									<button className="dropdown-item" onClick={showChangePermissionDialog}>{t('button.change_permission')}</button>
									<div className="dropdown-divider"></div>
									{!isAgent && (
										<>
											<button className="dropdown-item" onClick={showAgentSettingDialog}>{t('button.agent_setting')}</button>
											<div className="dropdown-divider"></div>
											<button className="dropdown-item" onClick={showChangePwdDialog}>{t('button.change_password')}</button>
											<div className="dropdown-divider"></div>
										</>
									)}
									<button className="dropdown-item" onClick={doLogout}>{t('button.logout')}</button>
								</div>
							</div>
						</RightCornerWrap>
					</div>
				</div>
			)}
		</AppSettings.Consumer>
	)
}


const RightCornerWrap = styled.div`
	display:flex;
	width:200px;
	align-items:center;
	justify-content:right; 
	.navbar-link{
		z-index:999;
	}
	.fas.fa-circle-user.fa-2x {
		font-size:2em !important;
		margin-right:10px;
	}
	.flag,.currentFlag {
		overflow:hidden;
		img {
			width:27.95px;
			height:20.96px;
			border-radius:4px;
			margin-right:5px;
		}
	}
	@media (max-width:767px) {
		width:auto;
	}
	
`;

export default Header;
