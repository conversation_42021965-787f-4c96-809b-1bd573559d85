import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { EhsFile } from "../CONNChainEHS/models/EhsFile";
import { EhsLab } from "../CONNChainEHS/models/EhsLab";
import { EhsLabAttributes } from "../CONNChainEHS/models/EhsLabAttributes";
import { EhsLanguage } from "../CONNChainEHS/models/EhsLanguage";
import { EhsMultipleDetail } from "../CONNChainEHS/models/EhsMultipleDetail";
import { EhsRou } from "../CONNChainEHS/models/EhsRou";
import { apiRequest, createLoginPostFormDataConfig, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const LabAPI = {
  getLabList: async (
    parms: BaseParams & {
      labFloor?: string;
      labHousenum?: string;
      areaId?: string;
      keyword?: string;
    }
  ) => {
    return apiRequest(getApiAddress + "lab/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getChemicalLabList: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "lab/list/chem", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getLabDetail: async (
    parms: BaseParams & {
      labId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "lab/detail", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addLab: async (
    parms: BaseParams & {
      ehsLab: EhsLab;
      labAttrList?: EhsLabAttributes[];
      rouList?: EhsRou[];
      labNameList: EhsLanguage[];
      fileList?: EhsFile[];
      labDetailMultiList?: EhsMultipleDetail[];
    }
  ) => {
    const formData = new FormData();
    const jsonParams = Object.keys(parms).reduce((acc, key) => {
      acc[key] = (parms as any)[key]; // 使用類型斷言
      return acc;
    }, {} as any);
    jsonParams.fileList = parms.fileList?.map((file: EhsFile) => {
      return {
        ...file,
        fileContent: [], // 將 fileContent 設定為空陣列
      };
    });
    formData.append("jsonString", JSON.stringify(jsonParams));

    // 將檔案列表添加到FormData中
    if (parms.fileList) {
      parms.fileList.forEach((file, index) => {
        if (file.fileObj) {
          // 確認 file.fileObj 存在
          formData.append(`multiFileList[${index}]`, file.fileObj);
        }
      });
    }
    return apiRequest(getApiAddress + "lab/add", createLoginPostFormDataConfig(parms, formData)).then((res) => handleFetchResponse(res));
  },
  editLab: async (
    parms: BaseParams & {
      ehsLab: EhsLab;
      labAttrList?: EhsLabAttributes[];
      rouList?: EhsRou[];
      labNameList: EhsLanguage[];
      fileList?: EhsFile[];
      labDetailMultiList?: EhsMultipleDetail[];
    }
  ) => {
    const formData = new FormData();
    const jsonParams = Object.keys(parms).reduce((acc, key) => {
      acc[key] = (parms as any)[key]; // 使用類型斷言
      return acc;
    }, {} as any);
    jsonParams.fileList = parms.fileList?.map((file: EhsFile) => {
      return {
        ...file,
        fileContent: [], // 將 fileContent 設定為空陣列
      };
    });
    formData.append("jsonString", JSON.stringify(jsonParams));

    // 將檔案列表添加到FormData中
    if (parms.fileList) {
      parms.fileList.forEach((file, index) => {
        if (file.fileObj) {
          // 確認 file.fileObj 存在
          formData.append(`multiFileList[${index}]`, file.fileObj);
        }
      });
    }
    return apiRequest(getApiAddress + "lab/edit", createLoginPostFormDataConfig(parms, formData)).then((res) => handleFetchResponse(res));
  },
  deleteLab: async (
    parms: BaseParams & {
      labId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "lab/del", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};
