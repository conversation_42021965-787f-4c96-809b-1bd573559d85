import React from 'react';
import styled from 'styled-components';
import { SYSTEM_TITLE } from '../constant/constants';
import { ComponentSize } from '../enums/ComponentSize';

interface SystemTitleComponentProps {
    size?: ComponentSize;
}

const SystemTitleComponent: React.FC<SystemTitleComponentProps> = ({ size = ComponentSize.MEDIUM }) => {
    return (
        <StyledSystemTitle $size={size}>
            {SYSTEM_TITLE}
        </StyledSystemTitle>
    );
};

const StyledSystemTitle = styled.div<{ $size: ComponentSize }>`
  color: #000000;
  font-weight: bold;
  opacity: 1;
  font-size: ${props =>
        props.$size === ComponentSize.SMALL ? '0.9rem' :
            props.$size === ComponentSize.LARGE ? '1.3rem' : '1.1rem'
    };
  display: inline-block;
`;

export default SystemTitleComponent; 