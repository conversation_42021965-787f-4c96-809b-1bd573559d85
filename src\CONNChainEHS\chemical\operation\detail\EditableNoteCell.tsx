import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import TextWithLineBreaks from '../../../common/TextWithLineBreaks';

interface EditableNoteCellProps {
    canEdit?: boolean;
    initialValue: string;
    updateNote: (note: string) => void;
}


const EditableNoteCell: React.FC<EditableNoteCellProps> = ({ canEdit, initialValue = "", updateNote }) => {
    const { t } = useTranslation();
    const [value, setValue] = useState(initialValue);
    const [isEditing, setIsEditing] = useState(false);

    const handleEdit = (event: React.MouseEvent<HTMLButtonElement>) => {
        setIsEditing(true);
        stopPropagation(event);
    };

    const handleSave = (event: React.MouseEvent<HTMLButtonElement>) => {
        setIsEditing(false);
        updateNote(value);
        setValue(value); // 更新狀態
        stopPropagation(event);
    };

    // 阻止事件冒泡 避免影響父元素的accordion收和
    const stopPropagation = (event: React.MouseEvent<HTMLButtonElement> | React.MouseEvent<HTMLTextAreaElement, MouseEvent>) => {
        event.stopPropagation();
    };

    return (
        <div>
            {isEditing ? (
                <textarea
                    className="form-control"
                    rows={5}
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                    onClick={(e) => stopPropagation(e)}
                />
            ) : (
                <label><TextWithLineBreaks text={value} /></label>
            )}
            {canEdit && <br />}
            {canEdit && <button
                type="button"
                className={`btn ${isEditing ? 'btn-success' : 'btn-warning'} mt-2`}
                onClick={isEditing ? (e) => handleSave(e) : (e) => handleEdit(e)}>
                {isEditing
                    ? <i className=" fas fa-floppy-disk me-1" />
                    : <i className="fas fa-pen me-1" />}
                {isEditing ? t('button.save') : t('button.edit')}
            </button>}
        </div>
    );
};

export default EditableNoteCell;