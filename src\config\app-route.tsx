import App from 'app';
import { GOOGLE_RECAPTCHA_KEY, PAGE_SOURCE } from 'ehs/constant/constants';
import { SignoffPageType } from 'ehs/enums/SignoffPageType';
import { lazy } from 'react';
import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';
import { AppPaths } from './app-paths';
import ProtectedRoute from 'routes/ProtectedRoute';
import Login from 'pages/login/login';	

const Home = lazy(() => import('pages/home/<USER>'));
const ResetPwd = lazy(() => import('pages/fotgotPwd/ResetPwd'));
const AreaList = lazy(() => import('ehs/manage/area/AreaList'));
const BuildingList = lazy(() => import('ehs/manage/building/BuildingList'));
const BuildingFloorList = lazy(() => import('ehs/manage/building/floor/BuildingFloorList'));
const LabDetail = lazy(() => import('ehs/manage/lab/LabDetail'));
const LabList = lazy(() => import('ehs/manage/lab/LabList'));
const LabModify = lazy(() => import('ehs/manage/lab/labModify/LabModify'));
const OrgList = lazy(() => import('ehs/manage/org/list/OrgList'));
const UserDetail = lazy(() => import('ehs/manage/user/userDetail/UserDetail'));
const UserList = lazy(() => import('ehs/manage/user/userList/UserList'));
const SignoffDetailPage = lazy(() => import('ehs/signoff/pages/SignoffDetailPage'));
const SignoffList = lazy(() => import('ehs/signoff/pages/SignoffList'));
const ActionRecordList = lazy(() => import('ehs/system/action/ActionRecordList'));
const BarcodeFormatSetting = lazy(() => import('ehs/system/barcode/BarcodeFormatSetting'));
const CustomOptionSetting = lazy(() => import('ehs/system/option/CustomOptionSetting'));
const FuncDetail = lazy(() => import('ehs/system/func/FuncDetail'));
const FuncList = lazy(() => import('ehs/system/func/FuncList'));
const FuncGroupDetail = lazy(() => import('ehs/system/group/detail/FuncGroupDetail'));
const FuncGroupList = lazy(() => import('ehs/system/group/list/FuncGroupList'));
const OrgLevelList = lazy(() => import('ehs/system/orgLevel/OrgLevelList'));
const RoleDetail = lazy(() => import('ehs/system/role/detail/RoleDetail'));
const RoleEdit = lazy(() => import('ehs/system/role/edit/RoleEdit'));
const RoleList = lazy(() => import('ehs/system/role/list/RoleList'));
const WasteInformationList = lazy(() => import('ehs/waste/WasteInformationList'));
const ChemicalInfoDetail = lazy(() => import('ehs/chemical/information/ChemicalInfoDetail'));
const ChemicalInfoList = lazy(() => import('ehs/chemical/information/ChemicalInfoList'));
const ChemicalInfoModify = lazy(() => import('ehs/chemical/information/modify/ChemicalInfoModify'));
const ChemicalItemAdd = lazy(() => import('ehs/chemical/item/ChemicalItemAdd'));
const ChemicalOperDetail = lazy(() => import('ehs/chemical/operation/detail/ChemicalOperDetail'));
const ChemicalBarcodeList = lazy(() => import('ehs/chemical/barcode/ChemicalBarcodeList'));
const ChemicalOperItemList = lazy(() => import('ehs/chemical/operation/list/ChemicalOperItemList'));
const ChemicalOperList = lazy(() => import('ehs/chemical/operation/list/ChemicalOperList'));
const ChemicalOperRecordList = lazy(() => import('ehs/chemical/operation/recordList/ChemicalOperRecordList'));
const ChemicalOperStopList = lazy(() => import('ehs/chemical/operation/stopList/ChmicalOperStopList'));
const PurchaseList = lazy(() => import('ehs/chemical/purchase/PurchaseList'));
const PurchaseOperation = lazy(() => import('ehs/chemical/purchase/PurchaseOperation'));
const PurchaseRequest = lazy(() => import('ehs/chemical/purchase/PurchaseRequest'));
const NewsManageList = lazy(() => import('ehs/Information/news/manage/NewsManageList'));
const NewsModify = lazy(() => import('ehs/Information/news/manage/NewsModify'));
const NewsDetail = lazy(() => import('ehs/Information/news/NewsDetail'));
const NewsList = lazy(() => import('ehs/Information/news/NewsList'));
const OshmsList = lazy(() => import('ehs/toshms/OshmsList'));
const OshmsManageList = lazy(() => import('ehs/toshms/OshmsManageList'));
const OshmsEdit = lazy(() => import('ehs/toshms/OshmsEdit'));

const loginPageComponent = (
	<GoogleReCaptchaProvider reCaptchaKey={GOOGLE_RECAPTCHA_KEY}>
		<Login />
	</GoogleReCaptchaProvider>
);

const AppRoute = [
	{
		path: AppPaths.login,
		element: loginPageComponent
	}, {
		path: AppPaths.resetPwd,
		element: <ResetPwd />
	}, {
		path: '*',
		element: (<ProtectedRoute>
			<App />  {/* 確保 App 組件在已登入時顯示 */}
		</ProtectedRoute>),
		children: [
			{
				path: AppPaths.home,
				element: <Home />
			},
			{
				path: AppPaths.manage.areaList,
				element: <AreaList />
			},
			{
				path: AppPaths.manage.buildingList,
				element: <BuildingList />
			},
			{
				path: AppPaths.manage.buildingFloorList,
				element: <BuildingFloorList />
			},
			{
				path: AppPaths.manage.userList,
				element: <UserList />
			},
			{
				path: AppPaths.manage.userAdd,
				element: <UserDetail />
			},
			{
				path: AppPaths.manage.userDetail,
				element: <UserDetail />
			},
			{
				path: AppPaths.manage.userEdit,
				element: <UserDetail />
			},
			{
				path: AppPaths.manage.orgList,
				element: <OrgList />
			},
			{
				path: AppPaths.manage.labList,
				element: <LabList />
			},
			{
				path: AppPaths.manage.labAdd,
				element: <LabModify />
			},
			{
				path: AppPaths.manage.labEdit,
				element: <LabModify />
			},
			{
				path: AppPaths.manage.labDetail,
				element: <LabDetail />
			},
			{
				path: AppPaths.system.actionRecordList,
				element: <ActionRecordList />
			},
			{
				path: AppPaths.system.barcodeFormatSetting,
				element: <BarcodeFormatSetting />
			},
			{
				path: AppPaths.system.customOptionSetting,
				element: <CustomOptionSetting />
			},
			{
				path: AppPaths.system.funcList,
				element: <FuncList />
			},
			{
				path: AppPaths.system.funcDetail,
				element: <FuncDetail />
			},
			{
				path: AppPaths.system.funcGroupList,
				element: <FuncGroupList />
			},
			{
				path: AppPaths.system.funcGroupDetail,
				element: <FuncGroupDetail />
			},
			{
				path: AppPaths.system.funcGroupEdit,
				element: <FuncGroupDetail />
			},
			{
				path: AppPaths.system.orgLevelList,
				element: <OrgLevelList />
			},
			{
				path: AppPaths.system.roleList,
				element: <RoleList />
			},
			{
				path: AppPaths.system.roleEdit,
				element: <RoleEdit />
			},
			{
				path: AppPaths.system.roleDetail,
				element: <RoleDetail />
			},
			{
				path: AppPaths.signoff.purchaseList,
				element: <PurchaseList source={PAGE_SOURCE.SIGN_OFF_PURCHASE_LIST} />
			},
			{
				path: AppPaths.signoff.chemicalModifyList,
				element: <SignoffList pageType={SignoffPageType.CHEM_MODIFY} />
			},
			{
				path: AppPaths.signoff.labUserModifyList,
				element: <SignoffList pageType={SignoffPageType.LAB_USER_MODIFY} />
			},
			{
				path: AppPaths.signoff.signoffDetail,
				element: <SignoffDetailPage />
			},
			{
				path: AppPaths.chemical.informationList,
				element: <ChemicalInfoList />
			},
			{
				path: AppPaths.chemical.informationDetail,
				element: <ChemicalInfoDetail />
			},
			{
				path: AppPaths.chemical.informationAdd,
				element: <ChemicalInfoModify />
			},
			{
				path: AppPaths.chemical.informationEdit,
				element: <ChemicalInfoModify />
			},
			{
				path: AppPaths.chemical.itemAdd,
				element: <ChemicalItemAdd />
			},
			{
				path: AppPaths.chemical.purchaseRequest,
				element: <PurchaseRequest />
			},
			{
				path: AppPaths.chemical.purchaseList,
				element: <PurchaseList />
			},
			{
				path: AppPaths.chemical.purchaseDetail,
				element: <PurchaseOperation />
			},
			{
				path: AppPaths.chemical.purchaseSignOff,
				element: <PurchaseOperation />
			},
			{
				path: AppPaths.chemical.purchaseArrival,
				element: <PurchaseOperation />
			},
			{
				path: AppPaths.chemical.purchaseInspection,
				element: <PurchaseOperation />
			},
			{
				path: AppPaths.chemical.purchaseReturn,
				element: <PurchaseOperation />
			},
			{
				path: AppPaths.chemical.purchaseCancel,
				element: <PurchaseOperation />
			},
			{
				path: AppPaths.chemical.operationList,
				element: <ChemicalOperList />
			},
			{
				path: AppPaths.chemical.operationItemList,
				element: <ChemicalOperItemList />
			},
			{
				path: AppPaths.chemical.operationDetail,
				element: <ChemicalOperDetail />
			},
			{
				path: AppPaths.chemical.operationRecordList,
				element: <ChemicalOperRecordList />
			},
			{
				path: AppPaths.chemical.operationStopList,
				element: <ChemicalOperStopList />
			},
			{
				path: AppPaths.chemical.barcodeList,
				element: <ChemicalBarcodeList />
			},
			{
				path: AppPaths.waste.wasteInformationList,
				element: <WasteInformationList />
			},
			{
				path: AppPaths.information.newsList,
				element: <NewsList />
			},
			{
				path: AppPaths.information.newsDetail,
				element: <NewsDetail />
			},
			{
				path: AppPaths.information.newsManageList,
				element: <NewsManageList />
			},
			{
				path: AppPaths.information.newsModify,
				element: <NewsModify />
			},
			{
				path: AppPaths.toshms.oshmsList,
				element: <OshmsList />
			},
			{
				path: AppPaths.toshms.oshmsManageList,
				element: <OshmsManageList />
			},
			{
				path: AppPaths.toshms.oshmsEdit,
				element: <OshmsEdit />
			}
		]
	}
];


export default AppRoute;