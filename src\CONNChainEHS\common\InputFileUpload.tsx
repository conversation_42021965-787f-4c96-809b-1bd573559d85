import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { UPLOAD_FILE_Size_IMAGE, UPLOAD_FILE_Size_PDF } from '../constant/constants';
import { EhsFile, initEhsFile } from '../models/EhsFile';
import { convertToSingleFiles } from '../utils/fileUtil';
import DownloadLink from './DownloadLink';

/**
 * 簡單上傳 component (單檔案使用)
 */

interface InputFileUploadProps {
    inputId?: string;
    acceptedFileTypes: string; // 允許的檔案類型
    defaultFile?: File | EhsFile;
    defaultFileInfo?: EhsFile;
    onFileChange: (file: File | null) => void;
}

const oneMb = 1024 * 1024;

const InputFileUpload: React.FC<InputFileUploadProps> = ({
    inputId,
    acceptedFileTypes,
    defaultFile,
    defaultFileInfo = initEhsFile,
    onFileChange,
}) => {
    const { t } = useTranslation();
    const [file, setFile] = useState<File | null>(null);
    const [error, setError] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement | null>(null); // 使用 ref 來引用 input
    const hasDefaultFileId = defaultFileInfo.fileId;
    const useInputId = inputId || "file-upload";

    useEffect(() => {
        if (defaultFile) {
            setFile(defaultFile instanceof File ? defaultFile : convertToSingleFiles(defaultFile));
        } else {
            setFile(null);
        }
    }, [defaultFile])

    const getMaxFileSize = (fileType: string): number => {
        switch (fileType) {
            case 'application/pdf':
                return UPLOAD_FILE_Size_PDF * oneMb; // PDF 最大 10 MB
            case 'image/png':
            case 'image/jpeg':
                return UPLOAD_FILE_Size_IMAGE * oneMb; // 圖片最大 5 MB
            default:
                return 0; // 不支援的類型
        }
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFile = event.target.files ? event.target.files[0] : null;
        if (selectedFile) {
            const maxFileSize = getMaxFileSize(selectedFile.type);

            // 檢查檔案類型
            const acceptedTypesArray = acceptedFileTypes.split(',').map(type => type.trim());
            if (!acceptedTypesArray.includes(`.${selectedFile.type.split('/')[1]}`)) {
                setError(`${t('message.upload_filetype_error')}，${t('message.upload')} ${acceptedFileTypes}`);
                setFile(null);
                onFileChange(null);
                return;
            }

            // 檢查檔案大小
            if (selectedFile.size > maxFileSize) {
                setError(t('message.upload_filesize_error', { size: maxFileSize / 1024 / 1024 }));
                setFile(null);
                onFileChange(null);
                return;
            }

            setFile(selectedFile);
            setError(null);
            onFileChange(selectedFile);
        }
    };

    const handleClearFile = () => {
        if (!defaultFile) {
            setFile(null);
        }
        onFileChange(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = ''; // 手動清除 input 的值
        }
    };

    return (
        <StlyedInputFileUpload>
            <div className="file-upload-container">
                {file ? (
                    <div className="mt-1">
                        <p>{hasDefaultFileId ? t('text.uploaded_file') : t('text.selected_file')}：<DownloadLink file={file} /></p>
                        <button className="btn btn-danger btn-sm" onClick={handleClearFile}>
                            <i className="fas fa-trash-can me-1" />
                            {hasDefaultFileId ? t('button.clear_uploaded_file') : t('button.clear_selected_file')}
                        </button>
                    </div>
                ) : <>
                    <input
                        type="file"
                        id={useInputId}
                        className='d-none'
                        onChange={handleFileChange}
                        accept={acceptedFileTypes}
                        ref={fileInputRef} // 設置 ref
                    />
                    <label htmlFor={useInputId} className="custom-file-upload">
                        <i className='fas fa-upload me-1' />
                        {t('text.choose_file') + (` ${acceptedFileTypes}`)} {/* 自訂按鈕文本 */}
                    </label>
                </>}
                {error && <p className="error-message text-danger">{error}</p>}
            </div>
        </StlyedInputFileUpload>
    );
};

const StlyedInputFileUpload = styled.div`
.custom-file-upload {
    display: inline-block;
    padding: 5px 10px; /* 調整內邊距 */
    cursor: pointer;
    background-color: #007bff; /* 按鈕背景顏色 */
    color: white; /* 按鈕文字顏色 */
    border: 1px solid #007bff; /* 按鈕邊框顏色 */
    border-radius: 4px; /* 圓角 */
    text-align: center; /* 文字居中 */
    transition: background-color 0.3s, border-color 0.3s; /* 過渡效果 */
}

.custom-file-upload:hover {
    background-color: #0056b3; /* 滑鼠懸停時的背景顏色 */
    border-color: #0056b3; /* 滑鼠懸停時的邊框顏色 */
}

`

export default InputFileUpload;
