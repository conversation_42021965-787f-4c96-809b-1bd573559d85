import ChemicalClassificationBadge from "ehs/common/chemical/ChemicalClassificationBadge";
import InputNumFloat from "ehs/common/input/InputNumFloat";
import { confirmMsg } from "ehs/common/SwalMsg";
import { showWarnToast } from "ehs/common/Toast";
import { CHEMICAL_CONCEN_CTRL_ALL, CHEMICAL_CONCEN_CTRL_NONE, CHEMICAL_CONCEN_DECIMAL_PLACES, CHEMICAL_CONCEN_MAX, CHEM<PERSON><PERSON>_CONCEN_MIN, CHEMICAL_CONCEN_TYPE_ALL, CHEMICAL_CONCEN_TYPE_RANGE, CHEMICAL_CTRL_TOTAL_QTY_TYPE_ANNC, CHEMICAL_CTRL_TOTAL_QTY_TYPE_NONE, CHEMICAL_CTRL_TOTAL_QTY_TYPE_REQ, CHEMIC<PERSON>_NEW_CHEM_CON_ID_PREFIX, CHEMICAL_WEIGHT_DECIMAL_PLACES, CHEMICAL_WEIGHT_MAX, CHEMIC<PERSON>_WEIGHT_MIN, CONFIG_VAL_CONCERNED, CONF<PERSON>_VAL_TOXIC } from "ehs/constant/constants";
import { EhsArea } from "ehs/models/EhsArea";
import { EhsChemical } from "ehs/models/EhsChemical";
import { EhsChemicalCon, initEhsChemicalCon } from "ehs/models/EhsChemicalCon";
import { initEhsChemicalConCategory } from "ehs/models/EhsChemicalConCategory";
import { EhsChemicalInfoArea, initEhsChemicalInfoArea } from "ehs/models/EhsChemicalInfoArea";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { isArrayEmpty } from "ehs/utils/arrayUtil";
import { getShowChemCon } from "ehs/utils/chemicalUtil";
import React, { ChangeEvent, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
interface ChemicalInfoConcenProps {
  param: {
    page: string; // 假設 param 對象中有一個名為 page 的屬性，類型為 string
    chemical: EhsChemical;
    setChemical: React.Dispatch<React.SetStateAction<EhsChemical>>;
    setShowGrandHandQty?: (show: boolean) => void
    ctrlTotalQtyTypeOption: EhsConfigParam[];
    concenTypeOption: EhsConfigParam[];
    checkedChemClass: {
      [key: string]: EhsConfigParam;
    };
    infoAreaFragment?: React.ReactNode; // 用於接收區域核可資訊
    areaList: EhsArea[];
    chemInfoArea: { [areaId: string]: EhsChemicalInfoArea };
    chemStatus: EhsConfigParam[];
    setChemInfoArea: React.Dispatch<React.SetStateAction<{ [areaId: string]: EhsChemicalInfoArea }>>;
    onConcentrationModified?: (chemConId: string) => void;
    onConcentrationDeleted?: (chemConId: string) => void;
  };
}

function ChemicalInfoConcen(props: ChemicalInfoConcenProps) {
  const {
    page,
    chemical,
    setChemical,
    setShowGrandHandQty = (show: boolean) => { },
    ctrlTotalQtyTypeOption,
    concenTypeOption,
    checkedChemClass,
    infoAreaFragment,
    areaList,
    chemInfoArea,
    chemStatus,
    setChemInfoArea,
    onConcentrationModified,
    onConcentrationDeleted
  } = props.param;
  const { t } = useTranslation();
  const isModifyPage = page === "modify";
  const concenTypeRange = CHEMICAL_CONCEN_TYPE_RANGE;
  const ctrlTotalQtyTypeNone = CHEMICAL_CTRL_TOTAL_QTY_TYPE_NONE;
  const ctrlTotalQtyTypeReq = CHEMICAL_CTRL_TOTAL_QTY_TYPE_REQ;
  const ctrlTotalQtyTypeAnnc = CHEMICAL_CTRL_TOTAL_QTY_TYPE_ANNC;
  const [concenTypeId, setConcenTypeId] = useState("");
  const [concenType, setConcenType] = useState(concenTypeRange);
  const [ctrlTotalQty, setCtrlTotalQty] = useState<(number | null)>(null); //暫存分級運作量
  const [isCtrl, setIsCtrl] = useState(false);
  const [ctrlTotalQtyType, setCtrlTotalQtyType] = useState<number>(ctrlTotalQtyTypeNone); //分級運作ㄌ類型
  const [editingConcentration, setEditingConcentration] = useState<EhsChemicalCon | null>(null);
  const [concenVal, setConcenVal] = useState({
    con1: 0,
    con2: 0
  });
  const {
    con1,
    con2
  } = concenVal;
  const [reverseCtrlTotalQtyTypeOption, setReverseCtrlTotalQtyTypeOption] = useState<EhsConfigParam[]>([]);

  const { chemGradeHandQty, chemCtrlConcen, chemCtrlConcenType, conList = [] } = chemical;
  const useGhQtyConList = conList.filter(item => item.ctrlTotalQtyType === ctrlTotalQtyTypeAnnc);
  const ctrlQtyTypeConListMap = conList.reduce((acc: { [key: string]: EhsChemicalCon[] }, item: EhsChemicalCon) => {
    const key = item.ctrlTotalQtyType;
    if (!acc[key]) {
      acc[key] = []; // 如果 key 不存在，則初始化為空陣列
    }

    acc[key].push(item); // 將元素加入對應的陣列
    return acc;
  }, {});
  const noCheckedChemClass = isArrayEmpty(Object.keys(checkedChemClass));
  const ctrlClassToSeparate = [CONFIG_VAL_TOXIC, CONFIG_VAL_CONCERNED];
  const noCtrlClassArray = Object.entries(checkedChemClass).reduce((acc, [key, value]) => {
    if (!ctrlClassToSeparate.includes(key)) {
      acc.push(value);
    }
    return acc;
  }, ([] as EhsConfigParam[]));
  const showClassArray = isCtrl ? Object.values(checkedChemClass) : noCtrlClassArray; //取得預設勾選Id
  const cannotShowAllConcenType = ![CHEMICAL_CONCEN_CTRL_NONE, CHEMICAL_CONCEN_CTRL_ALL].includes(chemCtrlConcenType);//非無管制跟全濃度的管制濃度 不能顯示全濃度

  useEffect(() => {
    // 只在首次渲染時進行反轉
    setReverseCtrlTotalQtyTypeOption([...ctrlTotalQtyTypeOption].reverse());
  }, [ctrlTotalQtyTypeOption]);

  /*有環境部公告之管制濃度 預設區間 否則為預設全濃度*/
  useEffect(() => {
    setConcenType(chemical.chemCtrlConcen ? concenTypeRange : CHEMICAL_CONCEN_TYPE_ALL);
  }, [chemical.chemCtrlConcen]);

  /*.有列管分類預設選擇使用公告之管制濃度 沒有則預設無管制上限 */
  useEffect(() => {
    setCtrlTotalQtyType(isCtrl ? ctrlTotalQtyTypeAnnc : ctrlTotalQtyTypeNone);
  }, [isCtrl])

  useEffect(() => {
    // 找出可用的選項（排除被過濾掉的全濃度選項）
    const availableOptions = concenTypeOption.filter(option =>
      !(option.configIvalue === CHEMICAL_CONCEN_CTRL_ALL && cannotShowAllConcenType)
    );

    // 如果有可用選項，選擇第一個可用選項或匹配當前濃度類型的選項
    if (availableOptions.length > 0) {
      // 優先選擇匹配當前濃度類型的選項
      const matchingOption = availableOptions.find(option => option.configIvalue === concenType);

      if (matchingOption) {
        setConcenTypeId(matchingOption.configId);
      } else {
        // 如果沒有匹配的選項，選擇第一個可用選項
        setConcenTypeId(availableOptions[0].configId);
        // 同時更新濃度類型
        setConcenType(availableOptions[0].configIvalue);
      }
    }
  }, [concenTypeOption, cannotShowAllConcenType, concenType]);

  useEffect(() => {
    setShowGrandHandQty(!isArrayEmpty(useGhQtyConList));
  }, [useGhQtyConList])

  const handleNeedGrandHandQtyChange = (event: ChangeEvent<HTMLInputElement>) => {
    const type = parseInt(event.target.value);
    setCtrlTotalQtyType(type);

    if (type !== 1) {
      setCtrlTotalQty(null);
    }
  };

  const handleConcenTypeChange = (event: ChangeEvent<HTMLInputElement>) => {
    const {
      id,
      value
    } = event.target;
    setConcenType(parseInt(value));
    setConcenTypeId(id);
  };

  interface ConcentrationParams {
    con1?: number;
    con2?: number;
  }

  const CONCENTRATION_VALIDATORS = {
    isZero: {
      1: ({ con1 }: ConcentrationParams) => con1 === 0,
      2: ({ con1, con2 }: ConcentrationParams) => con1 === 0 && con2 === 0,
      3: ({ con2 }: ConcentrationParams) => con2 === 0,
      4: ({ con1 }: ConcentrationParams) => con1 === 0,
      5: () => false
    },

    isValid: {
      1: ({ con1 }: ConcentrationParams) => con1 !== undefined && con1 !== 0,
      2: ({ con1, con2 }: ConcentrationParams) => con1 !== undefined && con2 !== undefined && con1 !== 0 && con2 !== 0 && con1 < con2,
      3: ({ con2 }: ConcentrationParams) => con2 !== undefined && con2 !== 0 && con2 !== CHEMICAL_CONCEN_MAX,
      4: ({ con1 }: ConcentrationParams) => con1 !== undefined && con1 !== 0 && con1 !== CHEMICAL_CONCEN_MAX,
      5: () => true
    }
  } as const;

  const isZeroConcentration = () => {
    const validator = CONCENTRATION_VALIDATORS.isZero[concenType as keyof typeof CONCENTRATION_VALIDATORS.isZero];
    return validator ? validator({ con1, con2 }) : false;
  };

  const isRangeConcentrationConflict = () => {
    const isRangeType = concenType === concenTypeRange;
    const isValidCtrlType = chemCtrlConcenType === 3 || chemCtrlConcenType === 4;
    const hasConflictingRange = chemCtrlConcen && con1 < chemCtrlConcen && con2 >= chemCtrlConcen;

    return isRangeType && isValidCtrlType && hasConflictingRange;
  };

  const isValidConcentration = () => {
    const validator = CONCENTRATION_VALIDATORS.isValid[concenType as keyof typeof CONCENTRATION_VALIDATORS.isValid];
    return validator ? validator({ con1, con2 }) : true;
  };

  const hasDuplicateConcentration = () => {
    let rs = false;

    if (!isArrayEmpty(conList)) {
      const filteredConList = editingConcentration
        ? conList.filter(item => item.chemConId !== editingConcentration.chemConId)
        : conList;

      filteredConList.forEach(item => {
        if (!rs) {
          if (concenType === 5) {
            rs = true;
          } else {
            const {
              conTypeValue,
              conLower,
              conUpper
            } = item;

            switch (conTypeValue) {
              case 1:
                if ((concenType === 1 && conLower === con1) || (concenType === 2 && conLower > con1 && conLower < con2) || (concenType === 3 && conLower < con2) || (concenType === 4 && conLower >= con1)) {
                  rs = true; //單一有重複
                }

                break;

              case 2:
                if ((concenType === 1 && conLower <= con1 && conUpper > con1) || (concenType === 2 && ((conLower <= con1 && conUpper > con1) || (conLower < con2 && conUpper > con2) || (conLower >= con1 && conUpper <= con2))) || (concenType === 3 && conLower < con2) || (concenType === 4 && conUpper > con1)) {
                  rs = true; //區間有重複
                }

                break;

              case 3:
                if ((concenType === 1 && conUpper > con2) || (concenType === 2 && (conUpper > con1 || conUpper > con2)) || concenType === 3 || (concenType === 4 && conUpper > con1)) {
                  rs = true; //以下有重複
                }

                break;

              case 4:
                if ((concenType === 1 && conLower <= con1) || (concenType === 2 && (conLower <= con1 || conLower <= con2)) || (concenType === 3 && conLower < con2) || concenType === 4) {
                  rs = true; //以上有重複
                }

                break;

              case 5:
                rs = true; //已有全濃度就無法新增

                break;

              default:
                break;
            }
          }
        }
      });
    }

    return rs;
  };

  const VALIDATION_RULES = {
    zeroConcentration: {
      isInvalid: () => isZeroConcentration(),
      message: t('message.chemical.error.enter_zero_concen')
    },
    validConcentration: {
      isInvalid: () => !isValidConcentration(),
      message: t('message.chemical.error.enter_concen')
    },
    rangeConflict: {
      isInvalid: () => isRangeConcentrationConflict(),
      message: t('message.chemical.error.enter_concen_range_conflict')
    },
    duplicateConcentration: {
      isInvalid: () => hasDuplicateConcentration(),
      message: t('message.chemical.error.duplicate_concen')
    },
    emptyClassArray: {
      isInvalid: () => isArrayEmpty(showClassArray),
      message: t('message.chemical.no_checked_classify')
    },
    missingCtrlQty: {
      isInvalid: () => ctrlTotalQtyType === ctrlTotalQtyTypeReq && !ctrlTotalQty,
      message: t('message.chemical.no_enter_ctrl_limit_total')
    }
  } as const;

  const handleAddClick = () => {
    const failedRule = Object.values(VALIDATION_RULES).find(rule => rule.isInvalid());

    if (failedRule) {
      showWarnToast(failedRule.message);
      return;
    }

    const checkedConcenTypeOption = concenTypeOption.find(item => item.configId === concenTypeId);
    const conTypeName = checkedConcenTypeOption?.configName || "";
    if (editingConcentration) {
      const chemConId = editingConcentration.chemConId;
      const newCategoryList = showClassArray.map(param => ({
        ...initEhsChemicalConCategory,
        chemConId: chemConId,
        configId: param.configId,
        configType: param.configType,
        configName: param.configName,
        configValue: param.configValue,
        configSeq: param.configSeq
      }));

      const updatedConcen: EhsChemicalCon = {
        ...editingConcentration,
        conLower: con1,
        conUpper: con2,
        conType: concenTypeId,
        conTypeName: conTypeName,
        conTypeValue: concenType,
        ctrlTotalQty: ctrlTotalQty,
        ctrlTotalQtyType: ctrlTotalQtyType,
        concentrationShow: getShowChemCon(concenType, con1, con2, conTypeName),
        categoryList: newCategoryList
      };

      const updatedConList = conList.map(item =>
        item.chemConId === chemConId ? updatedConcen : item
      );

      setChemical({
        ...chemical,
        conList: updatedConList
      });

      setEditingConcentration(null);

      resetForm();

      if (onConcentrationModified && !chemConId.includes(CHEMICAL_NEW_CHEM_CON_ID_PREFIX)) {
        onConcentrationModified(chemConId);
      }
    } else {
      const chemConId = CHEMICAL_NEW_CHEM_CON_ID_PREFIX + Date.now().toString();
      const newCategoryList = showClassArray.map(param => ({
        ...initEhsChemicalConCategory,
        chemConId: chemConId,
        configId: param.configId,
        configType: param.configType,
        configName: param.configName,
        configValue: param.configValue,
        configSeq: param.configSeq
      }));
      const newConcen: EhsChemicalCon = {
        ...initEhsChemicalCon,
        chemConId: chemConId,
        chemId: chemical.chemId,
        conLower: con1,
        conUpper: con2,
        conType: concenTypeId,
        conTypeName: conTypeName,
        conTypeValue: concenType,
        ctrlTotalQty: ctrlTotalQty,
        ctrlTotalQtyType: ctrlTotalQtyType,
        concentrationShow: getShowChemCon(concenType, con1, con2, conTypeName),
        categoryList: newCategoryList
      };
      // 初始化新區間的所有區域狀態
      setChemInfoArea(prevState => {
        const newState = { ...prevState };
        areaList.forEach(area => {
          // 找出同類型管制下的其他區間狀態
          const sameTypeStatus = Object.entries(prevState).find(([key, value]) => {
            // 找出這個 key 對應的濃度區間
            const conId = key.split('-')[0];
            const con = conList.find(c => c.chemConId === conId);
            // 確認是否為同一個區域且同一個管制類型
            return key.endsWith(`-${area.areaId}`) &&
              con?.ctrlTotalQtyType === ctrlTotalQtyType;
          })?.[1]?.chemStatus || 0;

          // 使用同類型管制的狀態初始化新區間
          newState[`${chemConId}-${area.areaId}`] = {
            ...initEhsChemicalInfoArea,
            chemConId: chemConId,
            areaId: area.areaId,
            chemStatus: sameTypeStatus  // 使用同類型管制的狀態
          };
        });
        return newState;
      });
      const updatedConList = [...conList, newConcen];
      updatedConList.sort((a, b) => {
        if (a.conLower < b.conLower) return -1;
        if (a.conLower > b.conLower) return 1;
        return 0;
      });
      setChemical({
        ...chemical,
        conList: updatedConList
      });
      resetForm();
    }
  };


  const handleEditConcentration = (concentration: EhsChemicalCon) => {
    setEditingConcentration(concentration);

    setConcenType(concentration.conTypeValue);
    setConcenVal({
      con1: concentration.conLower,
      con2: concentration.conUpper
    });
    setCtrlTotalQtyType(concentration.ctrlTotalQtyType);
    setCtrlTotalQty(concentration.ctrlTotalQty);

    const matchingOption = concenTypeOption.find(option => option.configIvalue === concentration.conTypeValue);
    if (matchingOption) {
      setConcenTypeId(matchingOption.configId);
    }

  };

  const handleDeleteConcentration = async (chemConId: string) => {
    try {
      // 使用 confirmMsg 替代 window.confirm
      const confirmed = await confirmMsg(t('message.confirm.delete'), t);
      if (!confirmed) return;

      // 以下是確認後的處理邏輯，保持不變
      const updatedConList = conList.filter(item => item.chemConId !== chemConId);

      setChemical({
        ...chemical,
        conList: updatedConList
      });

      setChemInfoArea(prevState => {
        const newState = { ...prevState };
        Object.keys(newState).forEach(key => {
          if (key.startsWith(`${chemConId}-`)) {
            delete newState[key];
          }
        });
        return newState;
      });

      if (editingConcentration && editingConcentration.chemConId === chemConId) {
        setEditingConcentration(null);
      }

      if (onConcentrationDeleted) {
        onConcentrationDeleted(chemConId);
      }
    } catch (error) {
      console.error('Delete concentration error:', error);
    }
  };

  const resetForm = () => {
    setConcenType(chemical.chemCtrlConcen ? concenTypeRange : CHEMICAL_CONCEN_TYPE_ALL);
    setConcenVal({
      con1: 0,
      con2: 0
    });
    setCtrlTotalQtyType(isCtrl ? ctrlTotalQtyTypeAnnc : ctrlTotalQtyTypeNone);
    setCtrlTotalQty(null);
  };

  return <StlyedChemicalInfoConcen className={isModifyPage ? 'isModifyPage' : ''}>
    <div className="px-4 ms-3 row">
      {isModifyPage && <div className="col-md-12 col-xl-6 ps-0 mb-md-0 mb-3">
        {<div className="d-flex align-items-center">
          <label className="col-3 h5"> {t('text.chemical.appr_oper_concen')}{isModifyPage && <span className="text-danger mx-1">*</span>}</label>
          {editingConcentration ? (
            <>
              <button className="btn btn-warning ms-3" id="saveConcenBtn" onClick={handleAddClick}>
                <i className="fas fa-save me-1" />{t('button.save')}
              </button>
              <button className="btn btn-secondary ms-2" onClick={() => {
                setEditingConcentration(null);
                resetForm();
              }}>
                <i className="fas fa-times me-1" />{t('button.cancel')}
              </button>
            </>
          ) : (
            <button className="btn btn-purple ms-3" onClick={handleAddClick}>
              <i className="fas fa-plus me-1" />{t('button.add')}
            </button>
          )}
        </div>}
        <div className="d-md--flex align-items-center my-2">
          <label className="me-5 col-2">1.{t('text.type')}</label>
          {concenTypeOption.map(item => {
            const {
              configId,
              configName,
              configIvalue
            } = item;

            // 如果條件成立，不顯示這個全濃度選項
            if (configIvalue === CHEMICAL_CONCEN_CTRL_ALL && cannotShowAllConcenType) {
              return null;
            }
            return <div className="form-check form-check-inline" key={"radioConcenTypediv" + configId}>
              <input className="form-check-input" type="radio" name="radioConcenType" id={configId} data-parsley-mincheck="1" checked={configIvalue === concenType} onChange={e => handleConcenTypeChange(e)} value={configIvalue} />
              <label className="form-check-label" htmlFor={configId}>{configName}</label>
            </div>;
          })}
        </div>
        <div className="d-md-flex align-items-center my-2"><label className="me-5 col-2">2.{t('text.concentration')}</label>
          <ConcenInputDiv chemical={chemical} concenType={concenType} concenTypeOption={concenTypeOption} concenVal={concenVal} setConcenVal={setConcenVal} setIsCtrl={setIsCtrl} />
        </div>
        <div className="d-md-flex align-items-center my-2"><label className="me-5 col-2">3.{t('text.chemical.class')}</label>
          <ChemClasssDiv chemical={chemical} noCheckedChemClass={noCheckedChemClass} checkedChemClass={checkedChemClass} concenVal={concenVal} showClassArray={showClassArray} />
        </div>
        <div className="d-md-flex align-items-start mt-2 mb-1"><label className="me-5 col-2">4.{t('text.chemical.ctrl_qty_total')}</label>
          {ctrlTotalQtyTypeOption.map((item, idx) => {
            const { configId, configName, configIvalue } = item
            const id = "ctrlTotalQuantity" + configId
            const isNone = configIvalue === ctrlTotalQtyTypeNone;
            const isAnnounce = configIvalue === ctrlTotalQtyTypeAnnc;
            const isReq = configIvalue === ctrlTotalQtyTypeReq;

            // 檢查是否已有其他濃度區間使用了特定管制類型
            const hasAnnounceType = Object.values(ctrlQtyTypeConListMap).some(list =>
              list?.some(item => item.ctrlTotalQtyType === ctrlTotalQtyTypeAnnc)
            );
            const hasReqType = Object.values(ctrlQtyTypeConListMap).some(list =>
              list?.some(item => item.ctrlTotalQtyType === ctrlTotalQtyTypeReq)
            );

            // 決定是否顯示該選項的邏輯
            const shouldDisplay =
              (isNone && !isCtrl) || // 非列管才能不管制
              (isAnnounce && isCtrl && !hasReqType) || // 列管且未有需管制時才能選環境部公告
              (isReq && !hasAnnounceType); // 未有環境部公告時才能選需管制

            return (shouldDisplay &&
              <div
                className={`form-check form-check-inline ${idx === ctrlTotalQtyTypeOption.length - 1 ? "col-md-3" : "col-md-2"
                  } col-12`}
                key={id}
              >
                <input
                  className="form-check-input"
                  type="radio"
                  name="ctrlTotalQuantity"
                  id={id}
                  data-parsley-mincheck="1"
                  onChange={e => handleNeedGrandHandQtyChange(e)}
                  value={configIvalue}
                  checked={ctrlTotalQtyType === configIvalue}
                />
                <label
                  className={`form-check-label`}
                  htmlFor={id}
                >
                  {configName}
                </label>
              </div>
            )
          })}
        </div>
        {ctrlTotalQtyType === ctrlTotalQtyTypeReq && <div className="d-flex align-items-start">
          <span className="col-md-2 me-md-5"></span>
          <div className="d-md-flex align-items-center col-md-5 col-12 mb-md-0 mb-3"><label className="col-md-3">{t('text.chemical.ctrl_qty_total')}：</label>
            <InputNumFloat className="form-control col-md-1 col-12 me-1" placeholder={t('text.chemical.ctrl_qty_placeholder')} maxValue={CHEMICAL_WEIGHT_MAX} minValue={CHEMICAL_WEIGHT_MIN} decimalPlaces={CHEMICAL_WEIGHT_DECIMAL_PLACES} onChange={(e, num) => {
              setCtrlTotalQty(num || null);
            }} onBlur={num => {
              setCtrlTotalQty(num || null);
            }} />Kg
          </div>
        </div>}
        {ctrlTotalQtyType === ctrlTotalQtyTypeAnnc && <div className="d-md-flex align-items-center">
          <span className="col-2 me-md-5 mb-md-0 mb-3"></span>
          <label className="h5 mt-md-2 mt-0">{t('text.chemical.ctrl_qty')}：{chemGradeHandQty !== null ? chemGradeHandQty + 'Kg' : t('text.chemical.no_ctrL_limit')}</label>
        </div>}
      </div>}
      {
        /* 顯示化學品所有區間 */
      }

      <div className="col-md-12 col-xl-6 px-0 mb-3">
        {!isArrayEmpty(conList) && (
          <div className="bg-primary bg-opacity-10 p-3 rounded mb-3">
            <h4 className="mb-0 text-primary">
              {t('text.chemical.have_concen_title')}
            </h4>
          </div>
        )}
        <div>
          {reverseCtrlTotalQtyTypeOption.map((item, idx) => {
            const { configId, configName, configIvalue } = item;
            const useTypeConList = ctrlQtyTypeConListMap[configIvalue];
            const useCtrlTotalQtyTypeNone = configIvalue === ctrlTotalQtyTypeNone;
            const useAnnounceType = configIvalue === ctrlTotalQtyTypeAnnc;

            if (!useTypeConList || useTypeConList.length === 0) {
              return null;
            }

            return (
              <div className="border border-info rounded my-3">
                {/* 標題區塊 */}
                <div className="bg-light border-bottom p-3">
                  <h5 className="mb-0 fw-bold d-flex justify-content-between align-items-center">
                    {configName}
                    {/* 環境部公告的管制總量只顯示一次 */}
                    {useAnnounceType && (
                      <div className="fs-6 fw-normal">
                        <strong>{t('text.chemical.ctrl_qty_total')}:</strong>
                        <span className="ms-2">
                          {chemGradeHandQty !== null
                            ? `${chemGradeHandQty}Kg`
                            : t('text.chemical.no_ctrL_limit')}
                        </span>
                      </div>
                    )}
                  </h5>
                </div>

                <div className="p-3">
                  {/* 濃度區間列表 */}
                  {useTypeConList?.map((conItem, conIdx) => {
                    const { concentrationShow, ctrlTotalQtyType, categoryList, chemConId } = conItem;
                    const isAnnounce = ctrlTotalQtyType === ctrlTotalQtyTypeAnnc;

                    return (
                      <div key={conIdx} className="mb-3 pb-3 border-bottom last:border-bottom-0 last:mb-0 last:pb-0">
                        <div className="d-flex align-items-center justify-content-between">
                          {/* 左側：濃度資訊 */}
                          <div className="d-flex flex-grow-1">
                            <div className="me-4">
                              <strong>{t('text.chemical.appr_oper_concen')}:</strong>
                              <span className="ms-2">{concentrationShow}</span>
                            </div>

                            {/* 需管制總量區塊才顯示個別管制量 */}
                            {!useAnnounceType && !useCtrlTotalQtyTypeNone && (
                              <div className="me-4">
                                <strong>{t('text.chemical.ctrl_qty_total')}:</strong>
                                <span className="ms-2">{conItem.ctrlTotalQty}Kg</span>
                              </div>
                            )}

                            {/* 右側：分類標籤 */}
                            <div className="d-flex align-items-center flex-wrap">
                              {categoryList.map(cate => {
                                const { configId, configValue } = cate;
                                const shouldDisplay = !isAnnounce || (isAnnounce && ctrlClassToSeparate.includes(configValue));
                                return shouldDisplay && (
                                  <div className="d-inline-block me-2 mb-1" key={"show-checked-class" + configId}>
                                    <ChemicalClassificationBadge item={cate} />
                                  </div>
                                );
                              })}
                            </div>
                          </div>

                          {/* 操作按鈕 */}
                          {isModifyPage && (
                            <div className="d-flex">
                              <button
                                className="btn btn-sm btn-warning me-2"
                                onClick={() => {
                                  handleEditConcentration(conItem)
                                }}
                                title={t('button.edit')}
                              >
                                <i className="fas fa-edit me-1"></i>{t('button.edit')}
                              </button>
                              <button
                                className="btn btn-sm btn-danger"
                                onClick={() => handleDeleteConcentration(chemConId)}
                                title={t('button.delete')}
                              >
                                <i className="fas fa-trash-alt me-1"></i>{t('button.delete')}
                              </button>
                            </div>
                          )}

                        </div>
                      </div>
                    );
                  })}

                  {/* 啟用狀態區塊 */}
                  <div className="mt-4">
                    <strong className="d-block mb-3">{t('text.chemical.enable_status')}</strong>
                    <div className="row g-3">
                      {areaList.map(area => {
                        const { areaId, areaName } = area;
                        const chemExtInfo = chemInfoArea[areaId];
                        const { expiryFlag } = chemExtInfo || {};
                        const isExpiry = expiryFlag === 1;
                        const firstConId = useTypeConList[0]?.chemConId;
                        const radioKey = `${firstConId}-${areaId}`;

                        return (
                          <div key={'area-status-' + areaId} className="col-12">
                            <div className="d-flex flex-column flex-md-row align-items-center">
                              <div className="mb-2 mb-md-0 text-center" style={{ width: '200px', minWidth: '200px' }}>
                                {areaName}
                              </div>
                              <div className="flex-grow-1">
                                {isExpiry ? (
                                  <small className="text-danger">
                                    {t('message.chemical.approvalno_exp_remove')}
                                  </small>
                                ) : isModifyPage ? (
                                  <div className="d-flex">
                                    <div className="btn-group btn-group-sm w-100">
                                      {chemStatus.map((status: EhsConfigParam) => {
                                        const radioId = `${status.configId}-${firstConId}-${areaId}`;
                                        return (
                                          <React.Fragment key={'radio-' + status.configId + '-' + areaId}>
                                            <input
                                              type="radio"
                                              className="btn-check"
                                              name={`chemStatus-${radioKey}`}
                                              id={radioId}
                                              value={status.configIvalue}
                                              checked={(chemInfoArea[radioKey]?.chemStatus || 0) === status.configIvalue}
                                              disabled={isExpiry}
                                              onChange={(e) => {
                                                const newStatus = parseInt(e.target.value);
                                                setChemInfoArea(prevState => {
                                                  const newState = { ...prevState };
                                                  // 更新該區域下所有濃度區間的狀態
                                                  useTypeConList.forEach(con => {
                                                    const key = `${con.chemConId}-${areaId}`;
                                                    newState[key] = {
                                                      ...prevState[key],
                                                      chemConId: con.chemConId,
                                                      areaId: areaId,
                                                      chemStatus: newStatus
                                                    };
                                                  });
                                                  return newState;
                                                });
                                              }}
                                            />
                                            <label
                                              className="btn btn-outline-secondary flex-grow-1 text-nowrap"
                                              htmlFor={radioId}
                                            >
                                              {status.configName}
                                            </label>
                                          </React.Fragment>
                                        )
                                      })}
                                    </div>
                                  </div>
                                ) : (
                                  // 非修改模式下的顯示方式
                                  <div className="d-flex">
                                    <div className="status-card">
                                      {(() => {
                                        const currentStatus = chemInfoArea[radioKey]?.chemStatus || 0;
                                        const statusObj = chemStatus.find(s => s.configIvalue === currentStatus);

                                        // 使用物件映射替代條件判斷
                                        const STATUS_CONFIGS: Record<string | number, { class: string; icon: string }> = {
                                          0: {
                                            class: "status-disabled",
                                            icon: "fa-times-circle"
                                          },
                                          1: {
                                            class: "status-enabled",
                                            icon: "fa-check-circle"
                                          },
                                          default: {
                                            class: "status-other",
                                            icon: "fa-info-circle"
                                          }
                                        };

                                        // 獲取對應狀態的配置，如果沒有則使用默認配置
                                        const statusConfig = STATUS_CONFIGS[currentStatus] || STATUS_CONFIGS.default;

                                        return (
                                          <div className={`status-container ${statusConfig.class}`}>
                                            <i className={`fas ${statusConfig.icon} status-icon`}></i>
                                            <span className="status-text">
                                              {statusObj?.configName || ""}
                                            </span>
                                          </div>
                                        );
                                      })()}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
      {infoAreaFragment}
    </div>
  </StlyedChemicalInfoConcen >;
}

const ConcenInputDiv = (props: {
  chemical: EhsChemical;
  concenType: number;
  concenTypeOption: EhsConfigParam[];
  concenVal: {
    con1: number;
    con2: number;
  };
  setConcenVal: React.Dispatch<React.SetStateAction<{
    con1: number;
    con2: number;
  }>>;
  setIsCtrl: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const {
    chemical,
    concenType,
    concenTypeOption,
    concenVal,
    setConcenVal,
    setIsCtrl
  } = props;
  const {
    chemCtrlConcen,
    chemCtrlConcenType
  } = chemical;
  const [uploadKey, setUploadKey] = useState(0);
  let content;
  const typeOption = concenTypeOption.find(item => item.configIvalue === concenType);
  useEffect(() => {
    changeIsCtrl();
  }, [concenVal]);
  useEffect(() => {
    handleUploadKey();

    if (concenType === 5) {
      setConcenVal({
        ...concenVal,
        con1: 0,
        con2: CHEMICAL_CONCEN_MAX
      });
    } else {
      setConcenVal({
        ...concenVal,
        con1: 0,
        con2: 0
      });
    }
  }, [concenType]);

  const changeIsCtrl = () => {
    if (chemCtrlConcenType !== 0) {
      let isCtrl = false; //如果需要列管

      switch (chemCtrlConcenType) {
        case 3:
          isCtrl = handleChemCtrlConcenBelow();
          break;

        case 4:
          isCtrl = handleChemCtrlConcenAbove();
          break;

        default:
          break;
      } //全濃度都要列管


      if (chemCtrlConcenType === 5 || concenType === 5) {
        isCtrl = true;
      }

      setIsCtrl(isCtrl);
    } //沒填寫內容不顯示列管


    if (concenVal.con1 === 0 && concenVal.con2 === 0) {
      setIsCtrl(false);
    }
  }; //以下不包含(等於) 管制濃度


  const handleChemCtrlConcenBelow = () => {
    switch (concenType) {
      case 1:
        return concenVal.con1 > 0 && concenVal.con1 < chemCtrlConcen && concenVal.con2 > 0 && concenVal.con2 < chemCtrlConcen;

      case 2:
        return concenVal.con1 < chemCtrlConcen || concenVal.con2 < chemCtrlConcen;

      case 3:
        return true;

      case 4:
        return concenVal.con1 < chemCtrlConcen && concenVal.con2 === CHEMICAL_CONCEN_MAX;

      default:
        return false;
    }
  }; //以上包含(等於) 管制濃度


  const handleChemCtrlConcenAbove = () => {
    switch (concenType) {
      case 1:
        return concenVal.con1 >= chemCtrlConcen && concenVal.con2 >= chemCtrlConcen;

      case 2:
        return concenVal.con1 >= chemCtrlConcen || concenVal.con2 >= chemCtrlConcen;

      case 3:
        return concenVal.con1 === 0 && concenVal.con2 >= chemCtrlConcen;

      case 4:
        return true;

      default:
        return false;
    }
  };

  const handleUploadKey = () => {
    setUploadKey(prevKey => prevKey + 1);
  };

  const CONCEN_TYPE_HANDLERS = {
    1: (concen: number) => ({ con1: concen, con2: concen }),
    2: (concen: number, conField?: number) => ({
      ...concenVal,
      [conField === 1 ? 'con1' : 'con2']: concen
    }),
    3: (concen: number) => ({ con1: 0, con2: concen }),
    4: (concen: number) => ({ con1: concen, con2: CHEMICAL_CONCEN_MAX }),
    default: () => ({ con1: 0, con2: 0 })
  } as const;

  const handleConcenInput = (concen: number, conField?: number) => {
    const handler = CONCEN_TYPE_HANDLERS[concenType as keyof typeof CONCEN_TYPE_HANDLERS]
      || CONCEN_TYPE_HANDLERS.default;

    setConcenVal({
      ...concenVal,
      ...handler(concen, conField)
    });
  };

  switch (concenType) {
    case 1:
      content = <div className="d-flex align-items-center"><InputNumFloat className="form-control me-1" onChange={(e, num) => handleConcenInput(num || 0)} maxLength={3 + CHEMICAL_CONCEN_DECIMAL_PLACES} minValue={CHEMICAL_CONCEN_MIN} maxValue={CHEMICAL_CONCEN_MAX} decimalPlaces={CHEMICAL_CONCEN_DECIMAL_PLACES} initKey={'1-' + uploadKey} /> %</div>;
      break;

    case 2:
      content = <div className="d-flex align-items-center">
        <InputNumFloat className="form-control me-1" onChange={(e, num) => handleConcenInput(num || 0, 1)} maxLength={3 + CHEMICAL_CONCEN_DECIMAL_PLACES} minValue={CHEMICAL_CONCEN_MIN} maxValue={CHEMICAL_CONCEN_MAX} initKey={'2-1' + uploadKey} /> <span className="me-1">~</span>
        <InputNumFloat className="form-control me-1" onChange={(e, num) => handleConcenInput(num || 0, 2)} maxLength={3 + CHEMICAL_CONCEN_DECIMAL_PLACES} minValue={CHEMICAL_CONCEN_MIN} maxValue={CHEMICAL_CONCEN_MAX} initKey={'2-2' + uploadKey} /> %</div>;
      break;

    case 3:
    case 4:
      content = <div className="d-flex align-items-center"><InputNumFloat className="form-control me-1" onChange={(e, num) => handleConcenInput(num || 0)} maxLength={3 + CHEMICAL_CONCEN_DECIMAL_PLACES} minValue={CHEMICAL_CONCEN_MIN} maxValue={CHEMICAL_CONCEN_MAX} decimalPlaces={CHEMICAL_CONCEN_DECIMAL_PLACES} initKey={'3-4-' + uploadKey} />
        <div className=" concen-type-show">% {typeOption?.configName}</div>
      </div>;
      break;

    case 5:
      content = <div className="d-flex align-items-center">
        {
          /* 0~100 % */
        }
        {typeOption?.configName}
      </div>;
      break;

    default:
      content = <div className=""></div>;
      break;
  }

  return content;
};

const ChemClasssDiv = (props: {
  chemical: EhsChemical;
  noCheckedChemClass: boolean;
  checkedChemClass: {
    [key: string]: EhsConfigParam;
  };
  concenVal: {
    con1: number;
    con2: number;
  }; // Explicitly specify the type here

  showClassArray: EhsConfigParam[];
}) => {
  const {
    t
  } = useTranslation();
  const {
    noCheckedChemClass,
    showClassArray
  } = props;
  let content;

  if (noCheckedChemClass) {
    content = <label>{t('message.chemical.no_checked_classify')}</label>;
  } else {
    content = <div>
      {showClassArray.map(item => <React.Fragment key={"show-checked-class" + item.configId}>
        <span className="d-inline-block mb-1">
          <ChemicalClassificationBadge item={item} />
        </span>
      </React.Fragment>)}
      <br />
      <label className="text-danger mt-1">
        ※ {t('message.chemical.checked_classify_concen_alert')}
      </label>
    </div>;
  }

  return content;
};

const StlyedChemicalInfoConcen = styled.div` 
  .concen-type-show {
    width: 100px;
  } 

  .grid-item {
    padding-top: 5px;
    padding-bottom: 5px;
  }

  /* 自定義按鈕樣式 */
  .btn-check + .btn-outline-secondary {
    color: #495057;
    border: 1px solid #6c757d;
    background-color: #ffffff;
    transition: all 0.2s ease-in-out;
  }
  
  .btn-check + .btn-outline-secondary:hover {
    color: #212529;
    background-color: #adb5bd;
    border-color: #6c757d;
  }

  .btn-check:checked + .btn-outline-secondary {
    color: #ffffff;
    background-color: #6c757d;
    border-color: #6c757d;
  }

  .btn-check:disabled + .btn-outline-secondary {
    background-color: #e9ecef;
    border-color: #6c757d;
    color: #6c757d;
    opacity: 0.65;
  }
  
  /* 按鈕組樣式 */
  .btn-group {
    display: inline-flex;
    flex-direction: row;
    width: auto;
  }
  
  .btn-group .btn {
    border-radius: 0;
  }
  
  .btn-group .btn:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  
  .btn-group .btn:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
  
  /* 狀態卡片樣式 */
  .status-card {
    width: 100%;
    padding: 0.25rem 0;
  }
  
  .status-container {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    font-size: 0.9rem;
  }
  
  .status-container:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
  
  .status-icon {
    font-size: 1rem;
    margin-right: 0.5rem;
  }
  
  .status-text {
    font-weight: 500;
    font-size: 0.9rem;
  }
  
  /* 狀態顏色 */
  .status-enabled {
    background-color: rgba(40, 167, 69, 0.05);
    border-left: 3px solid #28a745;
    color: #28a745;
  }
  
  .status-disabled {
    background-color: rgba(220, 53, 69, 0.05);
    border-left: 3px solid #dc3545;
    color: #dc3545;
  }
  
  .status-other {
    background-color: rgba(23, 162, 184, 0.05);
    border-left: 3px solid #17a2b8;
    color: #17a2b8;
  }
  
  /* 通用響應式設計 - 所有設備都使用垂直排列按鈕 */
  .btn-group {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  
  .btn-check + .btn-outline-secondary {
    width: 100%;
    margin: 2px 0;
    border-radius: 4px !important;
    border: 1px solid #6c757d !important;
    white-space: normal;
    height: auto;
    min-height: 38px;
    text-align: left;
    padding: 0.4rem 0.8rem;
    line-height: 1.2;
    display: flex;
    align-items: center;
  }
  
  .btn-group > .btn:not(:first-child) {
    margin-left: 0;
    border-top-left-radius: 4px !important;
    border-bottom-left-radius: 4px !important;
  }
  
  .btn-group > .btn:not(:last-child) {
    border-top-right-radius: 4px !important;
    border-bottom-right-radius: 4px !important;
  }
  
  /* 調整區域名稱和狀態按鈕的佈局 */
  .d-flex.flex-column.flex-md-row {
    flex-direction: column !important;
  }
  
  .d-flex.flex-column.flex-md-row > div:first-child {
    width: 100% !important;
    min-width: auto !important;
    margin-bottom: 0.5rem;
    text-align: left !important;
  }
  
  /* 確保內容不會超出容器 */
  .col-md-6 {
    padding-right: 10px;
    padding-left: 10px;
    max-width: 100%;
  }
  
  /* 調整整體容器寬度 */
  .px-4.ms-3.row {
    margin-right: 0;
    margin-left: 0;
    width: 100%;
    max-width: 100%;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
  
  /* 修復 Radio 按鈕文字顯示問題 */
  input[type="radio"] + label {
    display: inline-block;
    width: 100%;
    word-break: break-word;
    overflow-wrap: break-word;
  }
  
  /* 特別針對管制總量的 Radio 按鈕 */
  input[name^="chemStatus"] + label {
    min-height: 44px;
    padding: 0.5rem;
    display: flex;
    align-items: center;
  }
  
  /* 手機版特定調整 */
  @media (max-width: 767px) {
    .d-flex.flex-column.flex-md-row > div:first-child {
      font-size: 0.9rem;
    }
    
    .btn-check + .btn-outline-secondary {
      font-size: 0.85rem;
      padding: 0.35rem 0.7rem;
    }
  }
  
    /* iPad Air 和平板的特定調整 (768px ~ 1024px) */
    @media (min-width: 768px) and (max-width: 1024px) {
      .form-check.form-check-inline {
        display: block;
        margin-bottom: 0.5rem;
      }

      .col-md-6.col-12.px-0.mb-3 {
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
        
        /* 當父元素有 isModifyPage class 時，恢復原本的寬度 */
        .isModifyPage & {
          width: 50% !important;
          max-width: 50% !important;
          flex: 0 0 50% !important;
        }
      }

      /* 確保內容不會溢出 */
      .border.border-info.rounded {
        margin-left: 1rem;
        margin-right: 1rem;
      }
    }

    /* 確保其他螢幕尺寸維持原本的排版 */
    @media (max-width: 767px) {
      .col-md-6.col-12.px-0.mb-3 {
        width: 100%;
        max-width: 100%;
        flex: 0 0 100%;
      }
    }

    @media (min-width: 1025px) {
      .col-md-6.col-12.px-0.mb-3 {
        width: 50%;
        max-width: 50%;
        flex: 0 0 50%;
      }
    }
  /* iPad Air 和平板的特定調整 (768px ~ 1024px) */
  @media (min-width: 768px) and (max-width: 1024px) {
    .col-md-6.col-12.px-0.mb-3 {
      width: 100% !important;
      max-width: 100% !important;
      flex: 0 0 100% !important;
      
      /* 當父元素有 isModifyPage class 時，恢復原本的寬度 */
      .isModifyPage & {
        width: 50% !important;
        max-width: 50% !important;
        flex: 0 0 50% !important;
      }
    }

    /* 確保內容不會溢出 */
    .border.border-info.rounded {
      margin-left: 1rem;
      margin-right: 1rem;
    }
  }
  
  /* 確保所有容器內容不會溢出 */
  .row, .col-md-6, .col-12, .border, .p-3 {
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
  }
  
  /* 大螢幕特定調整 - 只在大螢幕上使用水平按鈕 */
  @media (min-width: 1400px) {
    .btn-group {
      flex-direction: row;
    }
    
    .btn-check + .btn-outline-secondary {
      width: auto;
      margin: 0;
      border-radius: 0 !important;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    /* 修復大螢幕上的按鈕邊框 */
    .btn-group > .btn {
      border: 1px solid #6c757d !important;
      margin-left: -1px;
    }
    
    .btn-group > .btn:first-child {
      border-top-left-radius: 4px !important;
      border-bottom-left-radius: 4px !important;
      margin-left: 0;
    }
    
    .btn-group > .btn:last-child {
      border-top-right-radius: 4px !important;
      border-bottom-right-radius: 4px !important;
    }
    
    .d-flex.flex-column.flex-md-row {
      flex-direction: row !important;
    }
    
    .d-flex.flex-column.flex-md-row > div:first-child {
      width: 200px !important;
      min-width: 200px !important;
      margin-bottom: 0;
      text-align: center !important;
    }
  }
  
  /* 修復按鈕組中的邊框問題 */
  .btn-group {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
  }
  
  /* 確保每個按鈕都有完整的邊框 */
  .btn-group > .btn {
    position: relative;
    flex: 1 1 auto;
  }
  
  /* 修復按鈕在選中狀態下的邊框 */
  .btn-check:checked + .btn-outline-secondary {
    border-color: #6c757d !important;
  }

  /* 確保其他螢幕尺寸維持原本的排版 */
  @media (max-width: 767px) {
    .col-md-6.col-12.px-0.mb-3 {
      width: 100%;
      max-width: 100%;
      flex: 0 0 100%;
    }
  }

  @media (min-width: 1025px) {
    .col-md-6.col-12.px-0.mb-3 {
      width: 50%;
      max-width: 50%;
      flex: 0 0 50%;
    }
  }
`;
export default ChemicalInfoConcen;