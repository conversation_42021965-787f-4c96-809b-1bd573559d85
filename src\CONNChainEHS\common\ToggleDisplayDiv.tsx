
import React, { ReactNode, useEffect, useState } from 'react';

interface ToggleDisplayDivProps {
    className?: string
    header: (isVisible: boolean) => ReactNode;  // 傳入一個函數來動態處理 header
    content: ReactNode; // 傳入一個函數來動態處理 content
    externalVisible?: boolean; // 外部狀態（可選） 從外面改變收合時可用
    onExternalVisibleChange?: (visible: boolean) => void;// 外部狀態改變的回調函數
    preventMouseExpand?: boolean; // 防止滑動展開 仍可透過點擊（可選）
}
const ToggleDisplayDiv: React.FC<ToggleDisplayDivProps> = ({
    className = "",
    header,
    content,
    externalVisible,
    onExternalVisibleChange,
    preventMouseExpand
}) => {
    const [isVisible, setIsVisible] = useState(false);

    // 監聽外部狀態變化，並同步到內部狀態
    useEffect(() => {
        if (externalVisible !== undefined) {
            setIsVisible(externalVisible);
        }
    }, [externalVisible]);

    // 監聽內部狀態變化，並通知外部
    useEffect(() => {
        if (onExternalVisibleChange) {
            onExternalVisibleChange(isVisible);
        }
    }, [isVisible]);

    // 處理鼠標進入事件
    const handleMouseEnter = () => {
        if (!preventMouseExpand) { // 只有當 preventExpand 為 false 時才顯示
            setIsVisible(true);
        }
    };

    // 處理鼠標離開事件
    const handleMouseLeave = () => {
        if (!preventMouseExpand) { // 只有當 preventExpand 為 false 時才隱藏
            // setIsVisible(false);
        }
    };

    // 處理點擊事件，切換顯示和隱藏
    const handleClick = (e: React.MouseEvent) => {
        // e.stopPropagation();
        // const target = e.target as HTMLElement;

        // // 如果點擊的是互動元素，則阻止事件冒泡
        // if (['INPUT', 'BUTTON', 'SELECT', 'TEXTAREA'].includes(target.tagName) ||
        //     (target.tagName === 'LABEL' && target.hasAttribute('for'))) {
        //     return;
        // }
        setIsVisible(!isVisible);
    };

    return (
        <div className={className}
            onMouseLeave={handleMouseLeave}
            onMouseEnter={handleMouseEnter}  >
            {/* 控制區域，當內容隱藏時顯示 */}
            <div onClick={handleClick}>
                {header(isVisible)}
            </div>
            {isVisible && content}
        </div>
    );
};

export default ToggleDisplayDiv;
