import { AppPaths } from "config/app-paths";
import LoadingSpinner from 'ehs/common/LoadingSpinner';
import { NEWS_PREVIEW_MAX_RESULT, NEWS_STATUS_SHOW } from "ehs/constant/constants";
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from "react-router-dom";
import styled from 'styled-components';
import { NewsAPI } from "../../../api/newsAPI";
import { errorMsg } from "../../common/SwalMsg";
import useLoginUser from '../../hooks/useLoginUser';
import { EhsNews } from "../../models/EhsNews";
import { checkTimeoutAction, getBasicLoginUserInfo } from '../../utils/authUtil';
import { isApiCallSuccess } from '../../utils/resultUtil';
import { getFormatDateSlash } from '../../utils/stringUtil';

function NewsSummary() {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { loginUser } = useLoginUser();
  const [loading, setLoading] = useState(false);
  const [newsList, setNewsList] = useState<EhsNews[]>([]);

  useEffect(() => {
    if (loginUser) {
      fetchData();
    }
  }, [loginUser, i18n.language])

  const fetchData = async () => {
    try {
      setLoading(true);
      const result = await NewsAPI.getNewsList({
        ...getBasicLoginUserInfo(loginUser)!,
        newsStatus: NEWS_STATUS_SHOW,
        maxResults: NEWS_PREVIEW_MAX_RESULT,
      });
      if (isApiCallSuccess(result)) {
        setLoading(false);
        setNewsList(result.results);
      } else {
        errorMsg(result.message);
      }
    } catch (err) {
      checkTimeoutAction(err, navigate, t);
      errorMsg(t('message.error'))
    }
  }

  const clickNewsList = () => navigate('/' + AppPaths.information.newsList);

  return (
    <StyledNewsSummary $loading={loading}>
      <div className="card py-3 table-container flex-column-container">
        <div className="table-header">
          <div className="table-caption">
            <i className="fas fa-grid-2"></i>{t("table.title.news.latest_news")}
          </div>
        </div>
        <div className="table-content-wrapper">
          {loading && <div className="loader-container"><LoadingSpinner /></div>}
          <table id="data-table-default" className="table align-middle dt-responsive fixed-table">
            <tbody className="fs-5">
              {!loading &&
                newsList.map((data, idx) => {
                  return (
                    <Row
                      key={idx}
                      news={data}
                    />
                  );
                })}
            </tbody>
          </table>
        </div>
        <div className="remark-container">
          <span className="remark-row cursor" onClick={clickNewsList} >{t("button.more")}</span>
        </div>
      </div>
    </StyledNewsSummary>
  );
}

const Row = (props: {
  news: EhsNews;
}) => {
  const { newsId, newsTitle, createDate } = props.news;
  const showCreateDate = createDate ? getFormatDateSlash(createDate) : "";
  const navigate = useNavigate();
  const clickDetail = () => navigate('/' + AppPaths.information.newsDetail, { state: { initNewsId: newsId } });

  return (
    <tr>
      <td className='text-start table-left-td' >
        <span className="detail-button cursor" onClick={clickDetail}>{newsTitle}</span>
      </td>
      <td className='table-right-td' >{showCreateDate}</td>
    </tr>
  );
};

const StyledNewsSummary = styled.div<{ $loading?: boolean }>`
  .card {
    width: 100%;
  }

  .cursor {
    cursor: pointer;
  }

  .table-container {
      border: 2px solid #ccc;  
      border-radius: 8px; 
      padding: 15px;
      background-color: #fff;
  }

  .table-header {
    position: relative;
    margin-bottom: 10px;
  }

  .table-content-wrapper {
    position: relative;
    min-height: 100px;
  }

  .loader-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 10;
  }

  .table {
      width: 100%;
      border-collapse: collapse;
      border: none;

      thead {
          position: sticky;
          top: 0;
          background-color: white;
          z-index: 2;
      }

      tbody {
          tr {
              padding: 15px 0;
              line-height: 2.5;
              border-bottom: 1px solid #ddd;

              &:hover {
                  background-color: #f5f5f5;
                  transition: background-color 0.2s ease-in-out;
              }

              span:hover { 
                  color: #007bff;
                  text-decoration: underline;
              }
          }

          td {
              padding: 12px 8px;

              .loading-cell {
                  height: 100px;
                  vertical-align: middle;
              }
          }
      }
  }

  .table-caption {
      caption-side: top;
      text-align: center;
      font-size: 1.5rem;
      font-weight: bold;
      color: #333;
      padding: 10px;
      width: 100%;
      border-bottom: 2px solid #ccc;
  }

  .detail-button  {
    color: black;
    text-decoration: none;
    outline: none;
  }

  .flex-column-container {
    display: 'flex';
    flexDirection: 'column';
    width: '100%';
  }

  .fixed-table {
   width: '100%';
   ableLayout: 'fixed';
  }

  .remark-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
    padding-right: 10px;
  }

  .remark-row {
    font-size: 1rem;
    font-weight: bold;
    text-decoration: none;
    color: #007bff;
  }

  @media (max-width: 600px){
    thead {
        display:none;
    }
    tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

    }
    tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
    }
    td {
        background: #fff!important;
        position:relative;
        padding-left: 100px;
        text-align:left;
        min-height: 44px;
    }
    td::before {
        content: attr(data-title);
        position: absolute;
        top: 6px;
        left: 6px;
        width: 30%;
        padding-right: 10px;
        white-space: nowrap;
        text-align: left;
        font-weight: bold;
        color: #1a1a1a;
    }
  }
`
export default NewsSummary;