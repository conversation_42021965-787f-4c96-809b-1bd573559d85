import BlockUi from "@availity/block-ui";
import ChemClassifyEditItem from "ehs/chemical/information/modify/ChemClassifyEditItem";
import { CONFIG_VAL_PUBLIC_HAZARD, CONFIG_VAL_GHS, CHEMICAL_CASNO_MAX_LENGTH, CHEMICAL_CONCEN_MIN, CHEMICAL_CONCEN_MAX, CHEMICAL_CONCEN_DECIMAL_PLACES } from "ehs/constant/constants";
import { Language } from "ehs/enums/Language";
import useLoginUser from "ehs/hooks/useLoginUser";
import { EhsChemicalName, initEhsChemicalName } from "ehs/models/EhsChemicalName";
import { EhsChemicalSubstance, initEhsChemicalSubstance } from "ehs/models/EhsChemicalSubstance";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { initEhsPurchaseSubstCategory, EhsPurchaseSubstCategory } from "ehs/models/EhsPurchaseSubstCategory";
import { ReactSelectOption } from "ehs/models/ReactSelectOption";
import { getFirstChemNames, getProtectChemClassReadonly } from "ehs/utils/chemicalUtil";
import { notEnglishLang } from "ehs/utils/langUtil";
import { checkFormatNumDesh } from "ehs/utils/stringUtil";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Select from "react-select";
import styled from "styled-components";
import BlockuiMsg from "./BlockuiMsg";
import InputNumFloat from "./input/InputNumFloat";
import { showWarnToast } from "./Toast";

export interface MixChemicalData {
    mixInfo: EhsChemicalSubstance;
}

interface MixChemicalProps {
    mixInfo: EhsChemicalSubstance;
    mixtureTypeOptions: EhsConfigParam[];
    phaseStateOptions: EhsConfigParam[];
    chemClassOptions: EhsConfigParam[];
    onChange: (info: EhsChemicalSubstance) => void;
    hasChemInfo?: boolean;
    hasPubHazClass?: boolean;
    ctrlNoOptionList?: ReactSelectOption[];
    casnoOptionList?: ReactSelectOption[];
    setMixtureCasnoSelectList?: (list: ReactSelectOption[]) => void;
}

const MixChemical: React.FC<MixChemicalProps> = ({
    mixInfo = initEhsChemicalSubstance,
    mixtureTypeOptions = [],
    phaseStateOptions = [],
    chemClassOptions = [],
    onChange = (info: EhsChemicalSubstance) => { },
    setMixtureCasnoSelectList = () => { },
    hasChemInfo = false,
    hasPubHazClass = false,
    ctrlNoOptionList = [],
    casnoOptionList = []
}) => {
    const {
        mixId,
        concentration,
        concentrationMax,
        casNo: casno,
        chemCtrlNo,
        nameList = [],
        phaseState,
        note,
        categoryList = [],
    } = mixInfo;

    const selectedChemClass = chemClassOptions.filter(option =>
        categoryList.some(category => category.configId === option.configId)
    );
    const {
        t,
        i18n
    } = useTranslation();
    const { loginUser } = useLoginUser();
    const [loadingBlock, setLoadingBlock] = useState(false);
    // const [casNoMappingChemInfoTemp, setCasNoMappingChemInfoTemp] = useState<{ [key: string]: EhsChemicalMixture; }>({});
    // const [ctrlNoMappingChemInfoTemp, setCtrlNoMappingChemInfoTemp] = useState<{ [key: string]: EhsChemicalMixture; }>({});
    // const [ctrlMappingCasnoListTemp, setCtrlMappingCasnoListTemp] = useState<{ [key: string]: ReactSelectOption[] }>({});
    // const isGeneral = chemCategory === ChemicalMixtureType.general;
    // const isControlled = chemCategory === ChemicalMixtureType.controlled;
    // const isSvhc = chemCategory === ChemicalMixtureType.svhc;
    // const isToxic = chemCategory === ChemicalMixtureType.toxic;
    const selectedCtrlNoOption = ctrlNoOptionList.find(option => option.value === chemCtrlNo);
    // const selectedCasnoOption = casnoOptionList.find(option => option.value === casno);
    // const title = mixtureTypeOptions.find(option => option.configValue === chemCategory)?.configName || "";
    const notEnglish = notEnglishLang(i18n.language);
    const {
        firstName,
        firstEnName
    } = getFirstChemNames(nameList, i18n.language);

    useEffect(() => {
        updateOnSelectedChange();
    }, [categoryList])

    useEffect(() => {
        // 創建新的 updatedCategoryList 並應用判斷邏輯
        let updatedCategoryList = [...mixInfo.categoryList];

        // 根據 hasPubHazClass 的值來決定是否加入或移除 CONFIG_VAL_PUBLIC_HAZARD
        if (hasPubHazClass) {
            const publicHazardOption = chemClassOptions.find(item => item.configValue === CONFIG_VAL_PUBLIC_HAZARD);
            if (publicHazardOption && !updatedCategoryList.some(item => item.configId === publicHazardOption.configId)) {
                updatedCategoryList.push({ ...initEhsPurchaseSubstCategory, ...publicHazardOption });
            }
        } else {
            updatedCategoryList = updatedCategoryList.filter(item => item.configValue !== CONFIG_VAL_PUBLIC_HAZARD);
        }

        // 更新 mixInfo 但僅當 updatedCategoryList 有變化時才更新，以避免無限循環
        if (JSON.stringify(updatedCategoryList) !== JSON.stringify(mixInfo.categoryList)) {
            onChange({ ...mixInfo, categoryList: updatedCategoryList });
        }
    }, [hasPubHazClass]);
    // const showCtrlLocalName = !isGeneral && firstName;
    // const showCtrlEnName = !isGeneral && firstEnName;
    // useEffect(() => {
    //     if (!hasChemInfo && loginUser && !chemCtrlNo && casno && isControlled) {
    //         searchChemicalByCasno();
    //     }
    // }, [casno]);

    // useEffect(() => {
    //     if (!hasChemInfo && loginUser && (isSvhc || isToxic)) {
    //         searchChemicalByCtrlNo();
    //     }
    // }, [chemCtrlNo]);

    // useEffect(() => {
    //     if (loginUser && (isSvhc || isToxic) && casnoOptionList.length === 1) {
    //         onChange({
    //             ...mixInfo,
    //             casno: casnoOptionList[0].value
    //         });
    //     }
    // }, [casnoOptionList]);

    // const searchChemicalByCasno = () => {
    //     const chemInfoTemp = casNoMappingChemInfoTemp[casno];

    //     if (chemInfoTemp) {
    //         onChange({
    //             ...mixInfo,
    //             chemId: chemInfoTemp.chemId,
    //             nameList: chemInfoTemp.nameList
    //         });
    //     } else if (casno) {
    //         ChemicalAPI.getChemicalByCasNo({
    //             ...getBasicLoginUserInfo(loginUser)!,
    //             casNo: casno
    //         }).then((result: any) => {
    //             if (isApiCallSuccess(result)) {
    //                 const rs = result.results;
    //                 const newMixInfo = {
    //                     ...mixInfo,
    //                     chemId: rs.chemId,
    //                     nameList: rs.nameList
    //                 };
    //                 onChange(newMixInfo);
    //                 setCasNoMappingChemInfoTemp({
    //                     ...casNoMappingChemInfoTemp,
    //                     [casno]: newMixInfo
    //                 });
    //             } else if (isGeneral) {
    //                 showWarnToast(result.message);
    //             }
    //         }).catch((err: any) => {
    //             showWarnToast(err);
    //         });
    //     }
    // };

    // const searchChemicalByCtrlNo = () => {
    //     const chemInfoTemp = ctrlNoMappingChemInfoTemp[chemCtrlNo];

    //     if (chemInfoTemp) {
    //         onChange({
    //             ...mixInfo,
    //             chemId: chemInfoTemp.chemId,
    //             nameList: chemInfoTemp.nameList
    //         });
    //         setMixtureCasnoSelectList(ctrlMappingCasnoListTemp[chemCtrlNo]);
    //     } else if (chemCtrlNo) {
    //         ChemicalAPI.getChemicalByCtrlNo({
    //             ...getBasicLoginUserInfo(loginUser)!,
    //             chemCtrlNo: chemCtrlNo
    //         }).then((result: any) => {
    //             if (isApiCallSuccess(result)) {
    //                 const rs = result.results;
    //                 const options: ReactSelectOption[] = rs.casnoList.map((item: {
    //                     casno: string;
    //                 }) => ({
    //                     value: item.casno,
    //                     label: item.casno
    //                 }));
    //                 setCtrlMappingCasnoListTemp({
    //                     ...ctrlMappingCasnoListTemp,
    //                     [chemCtrlNo]: options
    //                 });
    //                 setMixtureCasnoSelectList(options);
    //                 const newMixInfo = {
    //                     ...mixInfo,
    //                     chemId: rs.chemId,
    //                     nameList: rs.nameList
    //                 };
    //                 onChange(newMixInfo);
    //                 setCtrlNoMappingChemInfoTemp({
    //                     ...ctrlNoMappingChemInfoTemp,
    //                     [chemCtrlNo]: newMixInfo
    //                 });
    //             } else {
    //                 showWarnToast(result.message);
    //             }
    //         }).catch((err: any) => {
    //             showWarnToast(err);
    //         });
    //     }
    // };

    const onChangeName = (e: React.ChangeEvent<HTMLInputElement>, newLangType: string) => {
        const newName = e.target.value; // 檢查 nameList 是否包含該語系

        const existingItem = nameList?.find(item => item.langType === newLangType);
        let newNameList: EhsChemicalName[];

        if (existingItem) {
            // 如果存在，則更新該語系的名稱
            newNameList = nameList?.map(item => item.langType === newLangType ? {
                ...item,
                chemName: newName
            } : item);
        } else {
            // 如果不存在，則新增一個新的語系項目
            newNameList = [...nameList, {
                ...initEhsChemicalName,
                langType: newLangType,
                chemName: newName,
                chemNameSeq: 1
            }];
        } // 更新 mixInfo


        onChange({
            ...mixInfo,
            nameList: newNameList
        });
    };

    //變更化學品分類時的動作
    const handleChemClassChange = (event: React.ChangeEvent<HTMLInputElement>, chemClass: EhsConfigParam, checked?: boolean) => {
        const isChecked = checked !== undefined ? checked : event.target.checked;
        let updatedCategoryList = isChecked
            ? [...mixInfo.categoryList, { ...initEhsPurchaseSubstCategory, ...chemClass }]
            : mixInfo.categoryList.filter(item => item.configId !== chemClass.configId);

        updatedCategoryList = updateCategoryList(updatedCategoryList);
        // 通過 onChange 更新 mixInfo，僅包含更新的 categoryList
        onChange({ ...mixInfo, categoryList: updatedCategoryList });
    };

    const updateCategoryList = (updatedCategoryList: EhsPurchaseSubstCategory[]) => {
        const notGHSList = updatedCategoryList.filter(item => item.configValue !== CONFIG_VAL_GHS);

        if (notGHSList.length > 0) {
            // 如果數量大於 0，將 configValue 等於 CONFIG_VAL_GHS 的選項加入 updatedCategoryList
            const ghsOption = chemClassOptions.find(item => item.configValue === CONFIG_VAL_GHS);
            if (ghsOption && !updatedCategoryList.some(item => item.configId === ghsOption.configId)) {
                updatedCategoryList.push({ ...initEhsPurchaseSubstCategory, ...ghsOption });
            }
        } else {
            // 如果數量不大於 0，則移除 configValue 等於 CONFIG_VAL_GHS 的選項
            updatedCategoryList = updatedCategoryList.filter(item => item.configValue !== CONFIG_VAL_GHS);
        }

        return updatedCategoryList;
    };

    const updateOnSelectedChange = () => {
        const updatedCategoryList = updateCategoryList([...mixInfo.categoryList]);
        if (JSON.stringify(updatedCategoryList) !== JSON.stringify(mixInfo.categoryList)) {
            onChange({ ...mixInfo, categoryList: updatedCategoryList });
        }
    };

    return <StyledRow key={'mixChemical' + mixInfo.mixId}>
        <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
            <div className="row px-1 py-3">
                <div className="col-md-12">
                    {/* <h4 className="text-orange-600 d-inline-block me-3">{title}</h4> */}
                    {/* {!isGeneral && <> */}
                    {chemCtrlNo && <div className="mb-1">
                        <label className="d-flex align-items-center fw-bold"> {t('text.ctrl_no')}{hasChemInfo && '：' + chemCtrlNo}</label>
                        {!hasChemInfo && <Select options={ctrlNoOptionList} isSearchable menuPosition="fixed" placeholder={t("message.select")} className="w-100 ms-1" onChange={(e: any) => {
                            onChange({
                                ...mixInfo,
                                chemCtrlNo: e.value
                            });
                        }} value={selectedCtrlNoOption} noOptionsMessage={() => t('text.none_ctrl_no')} styles={{
                            menu: provided => ({
                                ...provided,
                                zIndex: 1000 // 確保選單在 label 之上

                            })
                        }} />}
                    </div>}
                    {/* {(isControlled || !isControlled && chemCtrlNo) && <div className="mb-1">
                            <label className="d-flex align-items-center fw-bold"> {t('text.chemical.casno')}&nbsp;<span className="text-danger">*</span> </label>
                            <Select options={casnoOptionList} isSearchable menuPosition="fixed" placeholder={t("message.select")} className="w-100 ms-1" onChange={(e: any) => {
                                onChange({
                                    ...mixInfo,
                                    casno: e.value
                                });
                            }} value={selectedCasnoOption} noOptionsMessage={() => t('message.chemical.none_casno')} styles={{
                                menu: provided => ({
                                    ...provided,
                                    zIndex: 1000 // 確保選單在 label 之上

                                })
                            }} />
                        </div>} */}
                    {/* </>} */}
                    {<>
                        <div className="mb-5px">
                            <label className="d-flex align-items-center fw-bold">
                                {t('text.chemical.casno')}&nbsp;{hasChemInfo ? '：' + casno : <span className="text-danger">*</span>}
                            </label>
                            {!hasChemInfo && <input type="text" value={casno} maxLength={CHEMICAL_CASNO_MAX_LENGTH} onChange={(e: any) => {
                                const inputEvent = (e.nativeEvent as InputEvent);
                                const char = inputEvent.data || ''; // 处理新输入的字符

                                if (checkFormatNumDesh(char)) {
                                    // 检查当前输入字符是否合法
                                    onChange({
                                        ...mixInfo,
                                        casNo: e.target.value
                                    });
                                }
                            }} className={`form-control`} />}
                        </div>
                        {/* {!hasChemInfo && <button type="button" className="btn btn-primary mb-1" onClick={searchChemicalByCasno}>
                            <i className="fas fa-magnifying-glass me-1"></i>
                            {t('button.search.item')}
                        </button>} */}
                    </>}
                    {<div className="">
                        <label className="d-flex align-items-center fw-bold">{t('text.chemical.name')}{hasChemInfo && '：' + (notEnglish ? firstName : firstEnName)}</label>
                        {!hasChemInfo && <input type="text" value={firstName} onChange={(e: any) => {
                            onChangeName(e, i18n.language);
                        }} className={`form-control`} />}
                        {/* {!hasChemInfo && showCtrlLocalName && <DisabledLabel content={firstName} />} */}
                    </div>}
                    {notEnglish && <div className="">
                        <label className="d-flex align-items-center fw-bold">{t('text.en_name')}{hasChemInfo && '：' + firstEnName}</label>
                        {!hasChemInfo && <input type="text" value={firstEnName} onChange={(e: any) => {
                            onChangeName(e, Language.ENUS);
                        }} className={`form-control`} />}
                        {/* {!hasChemInfo && showCtrlEnName && <DisabledLabel content={firstEnName} />} */}
                    </div>}
                    <div className="mt-1">
                        <label className="d-flex align-items-center fw-bold">{t('table.title.chemical.substance_phase_state')}{hasChemInfo && ('：' + (phaseStateOptions.find((item) => item.configId === phaseState)?.configName || ""))}</label>
                        {!hasChemInfo && phaseStateOptions.map((item) => {
                            const { configId, configName } = item;
                            const id = 'mix-phasestate-radio_' + configId + mixId;
                            return (<div className="form-check form-check-inline" key={id}>
                                <input className="form-check-input" type="radio" name="mix-phasestate" id={id} value={configId} data-parsley-mincheck="1"
                                    onChange={e => {
                                    }} />
                                <label className="form-check-label" htmlFor={id}>{configName}</label>
                            </div>)
                        })}
                    </div>
                    <div className="">
                        <label className="d-flex align-items-center fw-bold">
                            {t('text.chemical.substance_concen')}
                            {hasChemInfo ? (
                                <>
                                    {'：' + (`${concentration}%` || '')}
                                    {!concentrationMax ? '' : ` ~ ${concentrationMax}%`}
                                </>
                            ) : (
                                <span className="text-danger">*</span>
                            )}
                        </label>
                        {!hasChemInfo && <InputNumFloat className="form-control" minValue={CHEMICAL_CONCEN_MIN} maxValue={CHEMICAL_CONCEN_MAX} maxLength={3 + CHEMICAL_CONCEN_DECIMAL_PLACES} decimalPlaces={CHEMICAL_CONCEN_DECIMAL_PLACES} value={concentration || undefined} onChange={(e: any, num: number) => {// onChange("concentration", e.target.value);
                        }} onBlur={num => {
                            onChange({
                                ...mixInfo,
                                concentration: num || null
                            });
                        }} />}
                    </div>
                    <div className="">
                        <label className="d-flex align-items-center fw-bold">{t('text.chemical.substance_class')}</label>
                        {chemClassOptions.map((chemClass: EhsConfigParam) =>
                            <ChemClassifyEditItem
                                key={`chemclass-match-item-${chemClass.configId}`}
                                chemClass={chemClass}
                                isEdit
                                isAllDisabled
                                chemClassReadonly={getProtectChemClassReadonly}
                                selectedOptions={selectedChemClass}
                                handleChemClassChange={handleChemClassChange}
                                isCheckedOptionById={(options: EhsConfigParam[], configId: string) => {
                                    return options.some((item) => item.configId === configId);
                                }}
                            />
                        )}
                    </div>
                    <div className="">
                        <label className="d-flex align-items-center fw-bold">{t('text.note')}</label>
                        <textarea className="form-control" value={note} rows={2} maxLength={100} onChange={(e: any) => {
                            const value = e.target.value;
                            onChange({
                                ...mixInfo,
                                note: value,
                            });

                            // 檢查字數是否達到上限
                            if (value.length >= 100) {
                                showWarnToast(t('message.enter_word_limit', { limit: 100 }));
                            }
                        }} />
                    </div>
                </div>
            </div>
        </BlockUi>
    </StyledRow>;
}

const StyledRow = styled.div`
`;
export default MixChemical;