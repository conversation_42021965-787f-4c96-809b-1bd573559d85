import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';

interface LoadingSpinnerProps {
    className?: string;
    size?: 'sm' | 'md' | 'lg';
    color?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';
    text?: string;
}

export default function LoadingSpinner({
    className = '',
    size = 'md',
    color = 'primary',
    text  // 如果沒傳入 text，預設會使用 message.loading
}: LoadingSpinnerProps) {
    const { t } = useTranslation();
    const loadingText = text || t('message.loading');

    return (
        <StyledLoadingSpinner className={`text-center p-4 ${className}`}>
            <div className={`spinner-border text-${color} spinner-border-${size}`} role="status">
                <span className="visually-hidden">{loadingText}</span>
            </div>
            <div className={`mt-2 text-${color}`}>{loadingText}</div>
        </StyledLoadingSpinner>
    );
}

const StyledLoadingSpinner = styled.div`
    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }
    
    .spinner-border-md {
        width: 2rem;
        height: 2rem;
    }
    
    .spinner-border-lg {
        width: 3rem;
        height: 3rem;
    }
`;