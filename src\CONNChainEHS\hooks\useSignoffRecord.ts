import { useState, useEffect } from 'react';
import { SignoffAPI } from '../../api/signoffAPI';
import { SignoffFlowRecord } from 'ehs/models/SignoffFlowRecord';
import { isApiCallSuccess } from 'ehs/utils/resultUtil';
import { getBasicLoginUserInfo } from 'ehs/utils/authUtil';

interface UseSignoffRecordProps {
    loginUser: any | null;
    signStatusId: string;
    enabled?: boolean; // 控制是否要執行 fetch
    i18n?: { language: string }; // 可選參數
}

interface UseSignoffRecordReturn {
    signoffRecords: SignoffFlowRecord[];
    isLoading: boolean;
    error: Error | null;
    refetch: () => Promise<void>;
}

export const useSignoffRecord = ({
    loginUser,
    signStatusId,
    enabled = true,
    i18n
}: UseSignoffRecordProps): UseSignoffRecordReturn => {
    const [signoffRecords, setSignoffRecords] = useState<SignoffFlowRecord[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);

    const fetchSignoffRecord = async () => {
        if (!loginUser || !signStatusId || !enabled) return;

        setIsLoading(true);
        setError(null);

        try {
            const result = await SignoffAPI.getSignoffRecordList({
                ...getBasicLoginUserInfo(loginUser),
                signStatusId
            });

            if (isApiCallSuccess(result)) {
                setSignoffRecords(result.results);
            } else {
                throw new Error(result.message);
            }
        } catch (err) {
            setError(err instanceof Error ? err : new Error('Unknown error'));
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchSignoffRecord();
    }, [loginUser, signStatusId, enabled, i18n?.language]);

    return {
        signoffRecords,
        isLoading,
        error,
        refetch: fetchSignoffRecord
    };
}; 