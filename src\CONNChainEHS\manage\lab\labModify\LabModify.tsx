import BlockUi from "@availity/block-ui";
import React, { ChangeEvent, useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import styled from "styled-components";
import { CheckListAPI } from "../../../../api/checkListAPI";
import { FileAPI } from "../../../../api/fileAPI";
import { LabAPI } from "../../../../api/labAPI";
import { OrgAPI } from "../../../../api/orgAPI";
import { RoleAPI } from "../../../../api/roleAPI";
import { SelectListAPI } from "../../../../api/selectListAPI";
import { AppPaths } from "../../../../config/app-paths";
import BackButton from "../../../common/button/BackButton";
import BlockuiMsg from "../../../common/BlockuiMsg";
import Dialog from "../../../common/Dialog";
import FileUploader from "../../../common/FileUploader";
import NamesSplitFragment from "../../../common/NamesSplitFragment";
import { showSuccessToast, showWarnToast } from "../../../common/Toast";
import { CONFIG_SUB_TYPE_TOP, FILE_SUB_TYPE_LAB_FLOORPLAN, FILE_TYPE_LAB, LANGUAGE_MAPPING_TYPE_LAB, OPTION_LAB_STATUS_ROLE_LV_EDIT, ORG_SPLIT_FLAG, ROU_TYPE_LAB, UPLOAD_ACCEPT_TYPE_IMAGE_PDF } from "../../../constant/constants";
import { ActionMode } from "../../../enums/ActionMode";
import useLoginUser from "../../../hooks/useLoginUser";
import Breadcrumbs from "../../../layout/Breadcrumbs";
import Footer from "../../../layout/Footer";
import { EhsConfigParam } from "../../../models/EhsConfigParam";
import { EhsFile, initEhsFile } from "../../../models/EhsFile";
import { EhsLab, initEhsLab } from "../../../models/EhsLab";
import { EhsLanguage, initEhsLanguage } from "../../../models/EhsLanguage";
import { EhsOrg } from "../../../models/EhsOrg";
import { EhsOrgLevel } from "../../../models/EhsOrgLevel";
import { EhsRole } from "../../../models/EhsRole";
import { EhsRou, initEhsRou } from "../../../models/EhsRou";
import { SelectItem } from "../../../models/SelectItem";
import { isArrayEmpty } from "../../../utils/arrayUtil";
import { getBasicLoginUserInfo, getLoginUserRoleLevel, navigateToHome } from "../../../utils/authUtil";
import { convertToFiles } from "../../../utils/fileUtil";
import { getLabTextObj, getUserUnitTextObj, sortLanguagesListByCurrent } from "../../../utils/langUtil";
import { isApiCallSuccess } from "../../../utils/resultUtil";
import LabModifyAttr from "../labModify/LabModifyAttr";
import LabModifyUser from "../labModify/LabModifyUser";
import { OptionAPI } from "../../../../api/optionAPI";
import { EhsOptions, initEhsOptions } from "../../../models/EhsOptions";
import { EhsMultipleDetail, initEhsMultipleDetail } from "../../../models/EhsMultipleDetail";
import { errorMsg } from "ehs/common/SwalMsg";

const multiValType = {
  combustible: "combustible",
  otherHazardSubst: "otherHazardSubst",
}

function LabModify() {
  const { t, i18n } = useTranslation();
  const { loginUser, isAgent } = useLoginUser();
  const { userIdText } = getUserUnitTextObj(loginUser, t);
  const roleLevel = getLoginUserRoleLevel(loginUser);
  const { funcLabManageText, labAttribute, labFile, labFloorplan, labInformation, labPersonnel,
    msgNoChooseLabArea, msgNoChooseLabBuilding, msgNoChooseLabOrg, msgNoEnterLabName,
    msgNoEnterLabNumber, msgLabUserNoSet } = getLabTextObj(loginUser, t);
  const navigate = useNavigate();
  const [labInfo, setLabInfo] = useState<EhsLab>(initEhsLab);//實驗室資訊
  const [labAttributes, setLabAttributes] = useState<EhsConfigParam[]>([]);//實驗室屬性
  const [labUsers, setLabUsers] = useState<EhsRou[]>([]);//實驗室人員
  const [labUserRoles, setLabUserRoles] = useState<{ [key: string]: EhsRou[] }>({});//實驗室人員擁有的角色
  const [originalLabUserRoles, setOriginalLabUserRoles] = useState<{ [key: string]: EhsRou[] }>({});
  const [isLabUserRolesModified, setIsLabUserRolesModified] = useState(false);
  const [modifyLabUserNote, setModifyLabUserNote] = useState<string>('')
  const [checkLabAttributesMap, setCheckLabAttributesMap] = useState<{ [key: string]: string }>({});//已勾選實驗室屬性
  const [orgLevelList, setOrgLevelList] = useState<EhsOrgLevel[]>([]);
  const [orgList, setOrgList] = useState<EhsOrg[]>([]);
  const [labRoleList, setLabRoleList] = useState<EhsRole[]>([]);
  const [labFileList, setLabFileList] = useState<EhsFile[]>([]);
  const [labFileMap, setLabFileMap] = useState<{ [key: string]: EhsFile[] }>({});
  const [labStatusOption, setLabStatusOption] = useState<SelectItem[]>([]);
  const [areaOptionList, setAreaOptionList] = useState<SelectItem[]>([]);
  const [buildingOptionList, setBuildingOptionList] = useState<SelectItem[]>([]);
  const [buildingFloorOptionList, setBuildingFloorOptionList] = useState<SelectItem[]>([]);
  const [selectOrgItems, setSelectOrgItems] = useState<Record<string, EhsOrg[]>>({});
  const [selectParentOrgIds, setSelectParentOrgIds] = useState<{ [key: string]: string }>({});
  const [popupModeLabAttr, setPopupModeLabAttr] = useState<ActionMode | null>(null);
  const [popupModeLabUser, setPopupModeLabUser] = useState<ActionMode | null>(null);
  const [showOtherLangName, setShowOtherLangName] = useState(false);
  const [loadingBlock, setLoadingBlock] = useState(false);
  const [labNameList, setLabNameList] = useState<EhsLanguage[]>([]);
  const [editLabStatusRoleLvOption, setEditLabStatusRoleLvOption] = useState<EhsOptions>(initEhsOptions);
  const [multiInputs, setMultiInputs] = useState<{ [key: string]: string[] }>({
    [multiValType.combustible]: [],
    [multiValType.otherHazardSubst]: [],
  });
  const memoizedLabFileObj = useMemo(() => {
    const result: { [key: string]: File[] } = {};
    for (const key in labFileMap) {
      result[key] = convertToFiles(labFileMap[key]);
    }
    return result;
  }, [labFileMap]);

  const sortedLangList = loginUser ? sortLanguagesListByCurrent(loginUser!.langType) : [];
  const hasMultiLang = sortedLangList.length > 1;
  let firstLangValue = "";
  const { state } = useLocation();
  const { mode, initLabId, condition, fromPath }: ({ mode: ActionMode, initLabId: string, condition: object, fromPath: string }) = state || {};
  const isAddMode = mode === ActionMode.ADD;
  const funcName = isAddMode ? t('text.add') : t('text.edit')
  const { areaId, buildId, labNo, labPhone, labPhoneExt,
    labFloor, labHousenum, labNote, labStatus, labUse,
    labStructure, labOperTime, labFloorArea } = labInfo;
  const checkedTopLabAttr = labAttributes.filter((item: EhsConfigParam) => item.configSubType === CONFIG_SUB_TYPE_TOP && checkLabAttributesMap[item.configId]);
  const canEditLabStatus = roleLevel >= parseInt(editLabStatusRoleLvOption.optionValue);

  useEffect(() => {
    if (!isAddMode && !initLabId) {
      navigateToHome(navigate);
      return;
    }
    if (loginUser && initLabId) {
      fetchOption();
      fetchLabFile();
    }
  }, [loginUser])

  useEffect(() => {
    if (labInfo && labInfo.orgIds) {
      const orgIdsArray = labInfo.orgIds.split(ORG_SPLIT_FLAG);
      const updatedSelectParentOrgIds: { [key: string]: string } = {};
      orgIdsArray.forEach((orgId, index) => {
        updatedSelectParentOrgIds[index.toString()] = orgId;
      });
      setSelectParentOrgIds(updatedSelectParentOrgIds);
    }
  }, [loginUser, labInfo]);

  useEffect(() => {
    if (loginUser) {
      fetchData();
      SelectListAPI.getSelectArea({
        ...getBasicLoginUserInfo(loginUser),
      }).then(result => {
        if (isApiCallSuccess(result)) {
          setAreaOptionList(result.results)
        }
      })
    }
  }, [loginUser, i18n.language]);


  useEffect(() => {
    if (loginUser && areaId) {
      SelectListAPI.getSelectBuilding({
        ...getBasicLoginUserInfo(loginUser),
        areaId: areaId
      }).then((result) => {
        if (isApiCallSuccess(result)) {
          setBuildingOptionList(result.results);
        }
      });
    }
  }, [loginUser, areaId, i18n.language]);

  useEffect(() => {
    if (loginUser && buildId) {
      SelectListAPI.getSelectBuildingFloor({
        ...getBasicLoginUserInfo(loginUser),
        buildId: buildId
      }).then((result) => {
        if (isApiCallSuccess(result)) {
          setBuildingFloorOptionList(result.results);
        }
      });
    }
  }, [loginUser, buildId, i18n.language]);

  useEffect(() => {
    if (!isArrayEmpty(orgLevelList)) {
      OrgAPI.getOrgList({
        ...getBasicLoginUserInfo(loginUser),
      }).then(result => {
        if (isApiCallSuccess(result)) {
          const orgData = result.results;
          setOrgList(orgData)
        }
      })
    }
  }, [loginUser, orgLevelList, i18n.language])
  useEffect(() => {
    if (!isArrayEmpty(orgList)) {
      initSelectOption();
    }
  }, [orgList])

  const fetchData = () => {
    if (!isAddMode && !initLabId) {
      navigateToHome(navigate);
      return;
    }
    if (initLabId) {
      LabAPI.getLabDetail({
        ...getBasicLoginUserInfo(loginUser),
        labId: initLabId
      }).then((result) => {
        if (isApiCallSuccess(result)) {
          const labResults = result.results;
          const labBean = labResults.labBean;
          setLabInfo(labBean);
          setLabNameList(labBean.labNameList);
          const attrList = labResults.attributesBeanList;
          const labDetailMultiList = labResults.labDetailMultiList;

          if (labDetailMultiList && !isArrayEmpty(labDetailMultiList)) {
            const newMultiInputs: { [key: string]: string[] } = {};

            labDetailMultiList.forEach((item: EhsMultipleDetail) => {
              const { detailType, detailContent } = item;
              if (!newMultiInputs[detailType]) {
                newMultiInputs[detailType] = [];
              }
              newMultiInputs[detailType].push(detailContent);
            });

            setMultiInputs(newMultiInputs);
          }

          if (attrList && !isArrayEmpty(attrList)) {
            attrList.forEach((attr: EhsConfigParam) => {
              checkLabAttributesMap[attr.configId] = attr.configSubType;
              setCheckLabAttributesMap(checkLabAttributesMap);
            });
          }

          // 創建一個新的 labUsers 和 labUserRoles 對象
          const newLabUsers: { [key: string]: EhsRou } = {};
          const newLabUserRoles: { [key: string]: EhsRou[] } = {};

          labResults.rouBeanList.forEach((rouBean: EhsRou) => {
            const { userId } = rouBean;
            // 如果 labUserRoles 中不存在該 userId，則將對應的 EhsRou 放入 labUsers 和 labUserRoles 中
            if (!newLabUserRoles[userId]) {
              newLabUsers[userId] = rouBean;
              newLabUserRoles[userId] = [rouBean];
            } else {
              // 如果 labUserRoles 中存在該 userId，則只將對應的 EhsRou 放入 labUserRoles 中
              newLabUserRoles[userId].push(rouBean);
            }
          });

          // 更新狀態，只調用一次 setLabUsers 和 setLabUserRoles
          setLabUsers(Object.values(newLabUsers));
          setLabUserRoles(newLabUserRoles);
          setOriginalLabUserRoles(newLabUserRoles);//判斷是否修改人員
        }
      });
    } else {
      setLabInfo({ ...labInfo, labStatus: 1 });
    }

    SelectListAPI.getSelectLabStatus({
      ...getBasicLoginUserInfo(loginUser),
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        setLabStatusOption(result.results);
      }
    });
    SelectListAPI.getSelectArea({
      ...getBasicLoginUserInfo(loginUser),
    }).then(result => {
      if (isApiCallSuccess(result)) {
        setAreaOptionList(result.results)
      }
    })

    OrgAPI.getOrgLevelList({
      ...getBasicLoginUserInfo(loginUser),
    }).then(result => {
      if (isApiCallSuccess(result)) {
        setOrgLevelList(result.results)
      }
    })

    RoleAPI.getLabRoleList({
      ...getBasicLoginUserInfo(loginUser),
    }).then(result => {
      if (isApiCallSuccess(result)) {
        setLabRoleList(result.results);
      }
    })

    CheckListAPI.getLabAttributes({
      ...getBasicLoginUserInfo(loginUser),
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        setLabAttributes(result.results);
      }
    });
  };

  const fetchOption = () => {
    const optionIds = [OPTION_LAB_STATUS_ROLE_LV_EDIT];
    OptionAPI.getOptionListByIds({
      ...getBasicLoginUserInfo(loginUser),
      optionIdList: optionIds
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const options = result.results;
        const optionsMap: {
          [key: string]: EhsOptions;
        } = {}; // 建立一個空的 optionsMap
        options.forEach((item: any) => {
          optionsMap[item.optionId] = item;
        });
        setEditLabStatusRoleLvOption(optionsMap[OPTION_LAB_STATUS_ROLE_LV_EDIT]);
      }
    });
  };

  const fetchLabFile = () => {
    FileAPI.getFileList({
      ...getBasicLoginUserInfo(loginUser),
      fileTypeList: [FILE_TYPE_LAB],
      fileMappingIdList: [initLabId]
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const rs = result.results;
        const labFileMap = rs.reduce((acc: { [key: string]: EhsFile[] }, file: EhsFile) => {
          const subtype = file.fileSubtype;

          // 如果 map 中已經存在這個 subtype，則將該 file 加入對應的陣列
          if (!acc[subtype]) {
            acc[subtype] = [];
          }
          acc[subtype].push(file);

          return acc;
        }, {});
        setLabFileList(rs);
        setLabFileMap(labFileMap);
      }
    })
  }

  const initSelectOption = () => {
    const orgItem = orgList.reduce((itemMap: Record<string, EhsOrg[]>, org: EhsOrg) => {
      const orglvId = org.orglvId;
      itemMap[orglvId] = itemMap[orglvId] || [];
      itemMap[orglvId].push(org);
      return itemMap;
    }, {});

    setSelectOrgItems(orgItem);
  };

  const onChangeParentOrgSelect = (idx: number, orgId: string) => {
    // setSelectParentOrgIds(prevState => ({ ...prevState, [idx]: orgId }));
    setSelectParentOrgIds(prevState => {
      const updatedState = { ...prevState };
      Object.keys(updatedState).forEach(key => {
        if (parseInt(key) > idx) {
          delete updatedState[key];
        }
      });
      updatedState[idx] = orgId; // 设置新的值
      return updatedState;
    });

  }

  const orgSelect = selectOrgItems && orgLevelList && orgLevelList.map((data, idx) => {
    // 根據某條件篩選 selectOrgItems 的內容
    const orgOption = selectOrgItems[data.orglvId];
    const selectOrgPid = selectParentOrgIds[(idx - 1)];
    const selectOrgId = selectParentOrgIds[idx];
    const filteredOrgItems = selectOrgPid ? orgOption?.filter(item => selectOrgPid === item.orgPid) : orgOption;
    const isFirstOrgLv = idx === 0;

    return ((isFirstOrgLv || (!isFirstOrgLv && selectOrgPid && filteredOrgItems?.length > 0)) &&
      <div className="d-flex" key={data.orglvId}>
        <div className="col-md-4 mb-3 me-2">{data.orglvName}{isFirstOrgLv && <span className="text-danger me-1"> *</span>}</div>
        <div className="col-md-8 mb-3">
          <select className="form-select" value={selectOrgId} onChange={(e) => onChangeParentOrgSelect(idx, e.target.value)}>
            <option value=''>{t('text.all')}</option>
            {filteredOrgItems?.map((data, idx) => (
              <option key={'org-select-' + data.orgId} value={data.orgId}>{data.orgName}</option>
            ))}
          </select>
        </div>
      </div>
    );
  });

  const maxKeyValue = Object.keys(selectParentOrgIds).reduce((maxKey: string, currentKey: string) => {
    if (parseInt(currentKey) > parseInt(maxKey)) {
      if (selectParentOrgIds[currentKey] !== '') {
        return currentKey;
      }
    }
    return maxKey;
  }, "-1");

  const maxOrgValue = selectParentOrgIds[maxKeyValue];

  // 新增輸入框
  const addInput = (field: string) => {
    setMultiInputs({ ...multiInputs, [field]: [...multiInputs[field], ''] });
  };

  // 刪除指定索引的輸入框
  const removeInput = (field: keyof typeof multiInputs, index: number) => {
    const newInputs = [...multiInputs[field]];
    newInputs.splice(index, 1);
    setMultiInputs({ ...multiInputs, [field]: newInputs });
  };

  const handleInputChange = (field: keyof typeof multiInputs, index: number, value: string) => {
    const trimValue = value.trim(); // 移除前後空格
    setMultiInputs(prevState => {
      const newInputs = { ...prevState };
      newInputs[field][index] = trimValue;
      return newInputs;
    });
  };

  const handleLabUserRoleChecked = (e: ChangeEvent<HTMLInputElement>, userId: string, roleId: string) => {
    const newLabUserRoles = { ...labUserRoles };

    if (e.target.checked) {
      if (!newLabUserRoles[userId]) {
        newLabUserRoles[userId] = [];
      }
      newLabUserRoles[userId] = [
        ...(newLabUserRoles[userId] || []),
        { ...initEhsRou, roleId, userId }
      ];
    } else {
      newLabUserRoles[userId] = newLabUserRoles[userId].filter(
        (role) => role.roleId !== roleId
      );
    }

    // 比較新舊值是否有變化
    if (!isAddMode) {
      const hasChanges = Object.keys({ ...originalLabUserRoles, ...newLabUserRoles }).some(uid => {
        const originalRoles = originalLabUserRoles[uid] || [];
        const newRoles = newLabUserRoles[uid] || [];

        if (originalRoles.length !== newRoles.length) {
          return true;
        }

        const hasRoleChanges = originalRoles.some(origRole =>
          !newRoles.some(newRole => newRole.roleId === origRole.roleId)
        ) || newRoles.some(newRole =>
          !originalRoles.some(origRole => origRole.roleId === newRole.roleId)
        );

        return hasRoleChanges;
      });

      setIsLabUserRolesModified(hasChanges);
    }

    setLabUserRoles(newLabUserRoles);
  };

  const handleSaveLabUserRouList = (obj: { [key: string]: EhsRou[] }) => {
    // 合併所有角色 for 後端api處理
    const mergedArray = Object.values(labUserRoles).reduce((acc: EhsRou[], val: EhsRou[]) => acc.concat(val), []);
    const updatedArray = mergedArray.map((item: EhsRou) => ({ ...item, labId: labInfo.labId, orgId: labInfo.orgId, rouType: ROU_TYPE_LAB }));//設置為實驗室單位
    return updatedArray;
  };

  const setChangeLabFile = useCallback((file: File, status: boolean, id: string, fileType: string) => {
    // 添加防抖處理
    if (!file || !id || !fileType) return;

    // 使用函數式更新來確保狀態更新的正確性
    setLabFileList(prevList => {
      // 檢查是否已存在
      if (status && prevList.some(f => f.fileId === id)) {
        return prevList;
      }
      if (!status) {
        return prevList.filter(f => f.fileId !== id);
      }
      const newFile = {
        ...initEhsFile,
        fileId: id,
        fileName: file.name,
        fileType: FILE_TYPE_LAB,
        fileSubtype: fileType,
        fileObj: file
      };
      return [...prevList, newFile];
    });

    setLabFileMap(prevMap => {
      const existingFiles = prevMap[fileType] || [];
      if (status) {
        if (existingFiles.some(f => f.fileId === id)) {
          return prevMap;
        }
        return {
          ...prevMap,
          [fileType]: [...existingFiles, {
            ...initEhsFile,
            fileId: id,
            fileName: file.name,
            fileType: FILE_TYPE_LAB,
            fileSubtype: fileType,
            fileObj: file
          }]
        };
      }
      return {
        ...prevMap,
        [fileType]: existingFiles.filter(f => f.fileId !== id)
      };
    });
  }, []);

  // 1. 首先，將 FileUploader 抽出成一個獨立的 memo 組件
  const MemoizedFileUploader = React.memo(({
    fileInfo,
    accept,
    initStrKey,
    setFile,
    initialFiles,
    initialFileInfos
  }: {
    fileInfo: EhsFile;
    accept: string;
    initStrKey: string;
    setFile: (file: File, status: boolean, id: string) => void;
    initialFiles: File[];
    initialFileInfos: EhsFile[];
  }) => {
    return (
      <FileUploader
        fileInfo={fileInfo}
        accept={accept}
        initStrKey={initStrKey}
        setFile={(file, status, id) => setChangeLabFile(file, status, id, FILE_SUB_TYPE_LAB_FLOORPLAN)}
        initialFiles={initialFiles}
        initialFileInfos={initialFileInfos}
      />
    );
  }, (prevProps, nextProps) => {
    // 自定義比較函數，只在真正需要更新時才重新渲染
    return (
      prevProps.initStrKey === nextProps.initStrKey &&
      prevProps.initialFiles === nextProps.initialFiles &&
      prevProps.initialFileInfos === nextProps.initialFileInfos
    );
  });

  const validateLabInfo = () => {
    const hasLabUserRole = !isArrayEmpty(Object.keys(labUserRoles)) &&
      Object.values(labUserRoles).some(roles => !isArrayEmpty(roles));
    const validations = [
      { condition: !labInfo.labNo, message: msgNoEnterLabNumber },
      { condition: !labInfo.labName, message: msgNoEnterLabName },
      { condition: !labInfo.areaId, message: msgNoChooseLabArea },
      { condition: !labInfo.buildId, message: msgNoChooseLabBuilding },
      { condition: !maxOrgValue, message: msgNoChooseLabOrg },
      { condition: !hasLabUserRole, message: msgLabUserNoSet },
      // {
      //   condition: !isAddMode && isLabUserRolesModified && !modifyLabUserNote,
      //   message: t('message.enter') + ' ' + labUserModifyNoteText
      // },
    ];

    const error = validations.find(v => v.condition);
    if (error) {
      showWarnToast(error.message, { toastId: 'lab-modify-error' + error.message });
      return false;
    }
    return true;
  };

  const saveLabModify = async () => {
    if (!validateLabInfo()) {
      return;
    }
    setLoadingBlock(true);
    labInfo.orgId = maxOrgValue || "";
    const labAttrList = Object.keys(checkLabAttributesMap).map((configId) => ({
      labId: labInfo.labId,
      configId: configId,
    }));
    const rouList = handleSaveLabUserRouList(labUserRoles);

    let newLabNameList: EhsLanguage[] = [...labNameList].map(lab => ({
      ...lab,
      langValue: lab.langValue.trim()
    }));
    sortedLangList.forEach((item, idx) => {
      const isFirst = idx === 0;
      if (isFirst) {
        const firstLang = newLabNameList.find(lang => lang.langType === item.code);
        firstLangValue = firstLang ? firstLang.langValue : ""; // 確保 firstLang 存在，避免錯誤
      } else {
        const exists = newLabNameList.some((lang) => lang.langType === item.code);
        if (exists) {
          // 如果存在，且 langValue 為空，則將其設置為 firstLangValue
          const existingLangIndex = newLabNameList.findIndex(lang => lang.langType === item.code);
          if (existingLangIndex !== -1 && !newLabNameList[existingLangIndex].langValue) {
            newLabNameList[existingLangIndex].langValue = firstLangValue;
          }
        } else {
          newLabNameList = [
            ...newLabNameList,
            {
              ...initEhsLanguage,
              langType: item.code,
              langValue: firstLangValue,
              mappingType: LANGUAGE_MAPPING_TYPE_LAB
            }
          ];
        }
      }
    });
    const labDetailMultiList = Object.entries(multiInputs).flatMap(([key, values]) =>
      values
        .filter(content => content)
        .map(content => ({
          ...initEhsMultipleDetail,
          detailType: key,
          detailContent: content,
        })));

    const updateLabObj = {
      ...getBasicLoginUserInfo(loginUser),
      ehsLab: labInfo,
      labAttrList: labAttrList,
      rouList: rouList,
      labNameList: newLabNameList,
      fileList: labFileList,
      labDetailMultiList: labDetailMultiList,
      ...((!isAddMode) && {
        isLabUserRolesModified: isLabUserRolesModified,
        modifyLabUserNote: modifyLabUserNote
      })
    }

    try {
      const result = await (isAddMode
        ? LabAPI.addLab(updateLabObj)
        : LabAPI.editLab(updateLabObj));
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
        navigate("/" + AppPaths.manage.labList);
      }
    } catch (error) {
      console.error("save lab data error:", error);
      errorMsg(t('message.error'));
    } finally {
      setLoadingBlock(false);
    }
  }

  labUsers.sort((a, b) => a.userId.localeCompare(b.userId));//實驗室人員照ID 排序

  return (
    <StlyedLabModify >
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {
            <Dialog
              content={
                <LabModifyAttr onClose={() => setPopupModeLabAttr(null)} onActionSuccess={() => { setPopupModeLabAttr(null); }} mode={ActionMode.EDIT}
                  labAttributes={labAttributes} checkLabAttributesMap={checkLabAttributesMap} setCheckLabAttributesMap={setCheckLabAttributesMap} />
              }
              show={popupModeLabAttr !== null}
            />
          }
          {
            <Dialog
              content={
                <LabModifyUser onClose={() => setPopupModeLabUser(null)} onActionSuccess={() => { setPopupModeLabUser(null); }} mode={ActionMode.EDIT} modifyData={{}}
                  labUsers={labUsers} setLabUsers={setLabUsers} />
              }
              show={popupModeLabUser !== null}
            />
          }
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.system_manage_setting") },
                { label: funcLabManageText, path: AppPaths.manage.labList },
                { label: funcName },
              ]}
            />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{funcName} </h1>
            {/* END page-header */}
            <BackButton condition={condition} fromPath={fromPath} />
            <div className="card px-3 py-4 mt-3">
              <div className="card-body">
                <div className="row justify-content-between">
                  <div className="col-xxl-5 col-xl-12 ms-6">
                    {/* <!-- 實驗室場所基本資料 --> */}
                    <div className="row justify-content-center labBox h-100">
                      <div className="col-md-12">
                        <h4 className="mb-3 text-orange-600">{labInformation}</h4>
                      </div>
                      <div className="col-md-6">
                        <div className="row text-start align-items-center fs-5 mt-3">
                          <div className="col-md-4 mb-3">{t('text.no')}<span className="text-danger me-1"> *</span></div>
                          <div className="col-md-8 mb-3">
                            <input type="text" className="form-control" defaultValue={labNo} onChange={(e) => {
                              setLabInfo({ ...labInfo, labNo: e.target.value });
                            }} />
                          </div>
                          {loginUser && sortedLangList.map((item, idx) => {
                            const isFirst = idx === 0;
                            const labNameVal = labNameList.find((lang) => lang.langType === item.code)?.langValue || "";
                            return (
                              <div className={`row pe-0 me-0 ${!isFirst && !showOtherLangName && 'd-none'}`} key={"lab-name" + item.code}>
                                <div className="col-md-4 pe-0">{t('text.name')}：{isFirst && <span className="text-danger me-1">*</span>} <br />{hasMultiLang && item.language}</div>
                                <div className="col-md-8 pe-0">
                                  <input
                                    value={labNameVal}
                                    type="text"
                                    // className={`form-control ${isFirst && reqShowMsgValues.labname && 'is-invalid'}`}
                                    className={`form-control`}
                                    data-parsley-required="true"
                                    onChange={(e) => {
                                      const newVal = e.target.value;
                                      if (isFirst) {
                                        setLabInfo({ ...labInfo, labName: e.target.value });
                                      }
                                      setLabNameList((prevList) => {
                                        const index = prevList.findIndex((lang) => lang.langType === item.code);
                                        if (index !== -1) {
                                          // 找到匹配的項目，更新其 langValue
                                          return prevList.map((lang, idx) =>
                                            idx === index ? { ...lang, langValue: newVal } : lang
                                          );
                                        } else {
                                          // 未找到匹配的項目，新增一個新的項目
                                          return [
                                            ...prevList,
                                            {
                                              ...initEhsLanguage,
                                              langType: item.code,
                                              langValue: newVal,
                                              mappingType: LANGUAGE_MAPPING_TYPE_LAB
                                            }
                                          ];
                                        }
                                      });
                                    }}
                                  />
                                </div>
                                {isFirst && hasMultiLang && <button type="button" className="col-md-11 btn btn-primary my-2 mx-3" onClick={() => {
                                  setShowOtherLangName(!showOtherLangName);
                                }}>{t('button.enter_other_lang')} {t('text.name')}</button>}
                              </div>
                            )
                          })}
                          <div className="col-md-4 mb-3">{t('text.dedicated_line')}</div>
                          <div className="col-md-8 mb-3">
                            <input type="text" className="form-control" defaultValue={labPhone} onChange={(e) => {
                              setLabInfo({ ...labInfo, labPhone: e.target.value });
                            }} />
                          </div>
                          <div className="col-md-4 mb-3">{t('text.phone_ext')}</div>
                          <div className="col-md-8 mb-3">
                            <input type="text" className="form-control" defaultValue={labPhoneExt} onChange={(e) => {
                              setLabInfo({ ...labInfo, labPhoneExt: e.target.value });
                            }} />
                          </div>
                          <div className="col-md-4 mb-3">{t('text.lab.house_number')}</div>
                          <div className="col-md-8 mb-3">
                            <input type="text" className="form-control" defaultValue={labHousenum} onChange={(e) => {
                              setLabInfo({ ...labInfo, labHousenum: e.target.value });
                            }} />
                          </div>
                          <div className="col-md-4 mb-3">{t('text.lab.floor_space')}</div>
                          <div className="col-md-8 mb-3">
                            <input type="text" className="form-control" defaultValue={labFloorArea} onChange={(e) => {
                              setLabInfo({ ...labInfo, labFloorArea: e.target.value });
                            }} />
                          </div>
                          <div className="col-md-4 mb-3">{t('text.lab.use')}</div>
                          <div className="col-md-8 mb-3">
                            <input type="text" className="form-control" defaultValue={labUse} onChange={(e) => {
                              setLabInfo({ ...labInfo, labUse: e.target.value });
                            }} />
                          </div>
                          <div className="col-md-4 mb-3">{t('text.lab.structure')}</div>
                          <div className="col-md-8 mb-3">
                            <input type="text" className="form-control" defaultValue={labStructure} onChange={(e) => {
                              setLabInfo({ ...labInfo, labStructure: e.target.value });
                            }} />
                          </div>
                          <div className="col-md-4 mb-3">{t('text.lab.oper_time')}</div>
                          <div className="col-md-8 mb-3">
                            <input type="text" className="form-control" defaultValue={labOperTime} onChange={(e) => {
                              setLabInfo({ ...labInfo, labOperTime: e.target.value });
                            }} />
                          </div>
                          <div className="col-md-4 mb-3">{t('text.note')}</div>
                          <div className="col-md-8 mb-3">
                            <textarea className="form-control" defaultValue={labNote} onChange={(e) => {
                              setLabInfo({ ...labInfo, labNote: e.target.value });
                            }} />
                          </div>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="row text-start align-items-center fs-5 mt-3">
                          <div className="col-md-4 mb-3">{t('text.status')}</div>
                          <div className="col-md-8 mb-3">
                            <select className="form-select" value={isAddMode ? 1 : labStatus}
                              disabled={canEditLabStatus}
                              onChange={(e) => {
                                setLabInfo({
                                  ...labInfo,
                                  labStatus: parseInt(e.target.value),
                                })
                              }}>
                              {labStatusOption.filter(item =>
                                isAddMode
                                  ? item.value === 1  // 新增模式只顯示狀態 1
                                  : [0, 1, 4].includes(item.value)  // 編輯模式顯示狀態 0、1、4
                              ).map((item => {
                                return <option key={'labStatusOption-' + item.value} value={item.value} title={item.description}>{item.label}</option>
                              }))}
                            </select>
                          </div>
                          <div className="col-md-4 mb-3">{t('text.area.item')}<span className="text-danger me-1"> *</span></div>
                          <div className="col-md-8 mb-3">
                            <select className="form-select" value={areaId} onChange={(e) => {
                              setLabInfo({
                                ...labInfo,
                                areaId: e.target.value,
                                buildId: "",
                                labFloor: "",
                              })
                            }}>
                              <option value="">{t('text.select')}</option>
                              {areaOptionList.map((item) => {
                                return <option key={'area-select-' + item.value} value={item.value} title={item.description} >{item.label}</option>
                              })}
                            </select>
                          </div>
                          {areaId && <>
                            <div className="col-md-4 mb-3">{t('text.building.item')}<span className="text-danger me-1"> *</span></div>
                            <div className="col-md-8 mb-3">
                              <select className="form-select" value={buildId} onChange={(e) => {
                                setLabInfo({
                                  ...labInfo,
                                  buildId: e.target.value,
                                  labFloor: "",
                                })
                              }}>
                                <option value="">{t('text.select')}</option>
                                {buildingOptionList.map((item) => {
                                  return <option key={'building-select-' + item.value} value={item.value} title={item.description} >{item.label}</option>
                                })}
                              </select>
                            </div>
                          </>}
                          {areaId && buildId && <>
                            <div className="col-md-4 mb-3">{t('text.building.floor.item')}</div>
                            <div className="col-md-8 mb-3">
                              <select className="form-select" value={labFloor} onChange={(e) => {
                                setLabInfo({ ...labInfo, labFloor: e.target.value })
                              }}>
                                <option value="">{t('text.select')}</option>
                                {buildingFloorOptionList.map((item) => {
                                  return <option key={'building-floor-select-' + item.value} value={item.value} title={item.description} >{item.label}</option>
                                })}
                              </select>
                            </div>
                          </>}
                          {orgSelect}
                          <div className="col-12 mb-3">{t('text.combustible_material')}
                            <button className="btn btn-purple ms-3" onClick={() => { addInput(multiValType.combustible) }}><i className="fas fa-plus me-1" />{t('button.add')}</button>
                            <div className="row d-flex align-items-center">
                              {multiInputs.combustible.map((input, index) => {
                                return (
                                  <div key={'labCombustible' + index} className="col-12">
                                    <div className="d-flex align-items-center" >
                                      <span className="mx-3 multi-inputs">{index + 1 + '.'}</span>
                                      <input
                                        type="text"
                                        className={`form-control my-1 item-width-80`}
                                        data-parsley-required="true"
                                        value={input}
                                        onChange={(e) => {
                                          handleInputChange(multiValType.combustible, index, e.target.value)
                                        }}
                                      />
                                      {/* 刪除按鈕 */}
                                      {<i className="fas fa-trash fa-lg text-danger ps-2" title={t('button.delete')} onClick={() => {
                                        removeInput(multiValType.combustible, index)
                                      }} />}
                                    </div>
                                  </div>
                                )
                              })}
                            </div>
                          </div>
                          <div className="col-12 mb-3">{t('text.other_hazard_substance')}
                            <button className="btn btn-purple ms-3" onClick={() => { addInput(multiValType.otherHazardSubst) }}><i className="fas fa-plus me-1" />{t('button.add')}</button>
                            <div className="row d-flex align-items-center">
                              {multiInputs.otherHazardSubst.map((input, index) => {
                                return (
                                  <div key={'labCombustible' + index} className="col-12">
                                    <div className="d-flex align-items-center" >
                                      <span className="mx-3 multi-inputs">{index + 1 + '.'}</span>
                                      <input
                                        type="text"
                                        className={`form-control my-1 item-width-80`}
                                        data-parsley-required="true"
                                        value={input}
                                        onChange={(e) => {
                                          handleInputChange(multiValType.otherHazardSubst, index, e.target.value)
                                        }}
                                      />
                                      {/* 刪除按鈕 */}
                                      {<i className="fas fa-trash fa-lg text-danger ps-2" title={t('button.delete')} onClick={() => {
                                        removeInput(multiValType.otherHazardSubst, index)
                                      }} />}
                                    </div>
                                  </div>
                                )
                              })}</div>
                          </div>
                        </div>
                      </div>
                      {/* <!-- icon --> */}
                      <i className="col-2 fas-big-icon fas fa-building d-none d-xxl-block"></i>
                    </div>
                  </div>
                  <div className="col-xxl-5 col-xl-12 me-3 mb-2 pb-2">
                    {/* <!-- 實驗室場所屬性 --> */}
                    <div className="row labBox">
                      <div className="col-md-12">
                        <h4 className="text-orange-600 d-inline-block me-3">{labAttribute}</h4>
                        <button className="btn btn-warning " title={t('button.edit')} onClick={() => setPopupModeLabAttr(ActionMode.EDIT)}>
                          <i className="fas fa-pen me-1"></i>{t('button.edit')}</button>
                        {checkLabAttributesMap && !isArrayEmpty(Object.keys(checkLabAttributesMap)) &&
                          <div className="table-responsive">
                            <table className="table border-none fs-5 mt-3">
                              <tbody>
                                {checkedTopLabAttr.map((topConfig, index) => (
                                  <tr key={topConfig.configId}>
                                    <td>{topConfig.configName}</td>
                                    <td>
                                      {labAttributes
                                        .filter((item: EhsConfigParam) => item.configSubType === topConfig.configId && checkLabAttributesMap[item.configId])
                                        .map((subConfig, index) => (
                                          <React.Fragment key={'labAttr-' + topConfig.configId + '-' + subConfig.configId}>
                                            {/* 如果不是第一個子配置，則加上逗號 */}
                                            {index !== 0 && "、"}
                                            {/* 子配置的 configName */}
                                            {subConfig.configName}
                                          </React.Fragment>
                                        ))}
                                    </td>
                                  </tr>
                                ))
                                }
                              </tbody>
                            </table>
                          </div>
                        }
                      </div>
                      {/* <!-- icon --> */}
                      <i className="col-2 fas-big-icon fas fa-vial-virus d-none d-xxl-block"></i>
                    </div>
                    {/* <!-- 實驗室場所檔案 --> */}
                    <div className="row labBox">
                      <h4 className="text-orange-600">{labFile}</h4>
                      {/* <!-- icon --> */}
                      <i className="fas fas-big-icon fa-file d-none d-xxl-block"></i>
                      <div className="file-div">
                        <div className="col-12 mb-3">
                          <label className="h5">{labFloorplan}</label>
                          <MemoizedFileUploader key={'labfile-uploader' + FILE_SUB_TYPE_LAB_FLOORPLAN} fileInfo={{
                            ...initEhsFile,
                          }}
                            accept={UPLOAD_ACCEPT_TYPE_IMAGE_PDF} initStrKey={FILE_SUB_TYPE_LAB_FLOORPLAN}
                            setFile={(file, status, id) => setChangeLabFile(file, status, id, FILE_SUB_TYPE_LAB_FLOORPLAN)}
                            initialFiles={memoizedLabFileObj[FILE_SUB_TYPE_LAB_FLOORPLAN]} initialFileInfos={labFileMap[FILE_SUB_TYPE_LAB_FLOORPLAN]}
                          />
                        </div>
                        {checkedTopLabAttr.map((topConfig) => (
                          <div className="col-12 mb-3" key={'labfile-div-' + topConfig.configId}>
                            <label className="h5">{t('text.lab.attribute_file', { labAttr: topConfig.configName })}</label>
                            <MemoizedFileUploader key={'labfile-uploader-' + topConfig.configId} fileInfo={{
                              ...initEhsFile,
                            }}
                              accept={UPLOAD_ACCEPT_TYPE_IMAGE_PDF} initStrKey={topConfig.configId}
                              setFile={(file, status, id) => setChangeLabFile(file, status, id, topConfig.configValue)}
                              initialFiles={memoizedLabFileObj[topConfig.configValue]} initialFileInfos={labFileMap[topConfig.configValue]}
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* <!-- 實驗室場所人員清單 --> */}
            <div className="card px-3 py-4 mt-3">
              <div className="card-body">
                <div className="row mb-5">
                  <div className="col-xl-12">
                    <h3 className="text-orange-600 d-inline-block me-3"><i className="fas fas-big-icon fa-users-gear me-3"></i>
                      {labPersonnel}
                    </h3>
                    {!isAgent && <button className="btn btn-warning" onClick={() => setPopupModeLabUser(ActionMode.EDIT)} title={t('button.edit')}>
                      <i className="fas fa-user-plus"></i> {t('button.edit')} </button>}
                  </div>
                </div>
                {isArrayEmpty(labUsers) ? <div className="text-center"><label className="h4">{msgLabUserNoSet}</label> </div> : <div className="table-responsive">
                  <table id="data-table-default"
                    className="table table-hover align-middle dt-responsive">
                    <thead className="text-center fs-4 fw-bold">
                      <tr>
                        <th className="text-start">{t('table.title.lab.user_info')}</th>
                        {labRoleList && labRoleList.map((role: EhsRole) => {
                          return <th key={'labuserroleth' + role.roleId}>{role.roleName}</th>
                        })}
                      </tr>
                    </thead>
                    <tbody className="text-center fs-5">
                      {labUsers.map((user) => {
                        return (
                          <tr key={'labuser-' + user.userId}>
                            <td data-title={t('table.title.lab.user_info')} className="text-start">
                              {userIdText}：{user.userId}<br />
                              {t('table.title.lab.username_jobtitle')}：{user.userName}{user.jobTitleName && ' / '}{user.jobTitleName}<br />
                              {t('table.title.org.item')}：<NamesSplitFragment names={user.orgNames} separator={ORG_SPLIT_FLAG} lineBreak={<> / </>} />
                            </td>
                            {labRoleList && labRoleList.map((role: EhsRole) => {
                              const isChecked = labUserRoles[user.userId] && labUserRoles[user.userId].some((userRole) => userRole.roleId === role.roleId);
                              return <td key={'labuserroletd-' + user.userId + '-' + role.roleId}>
                                <div className="form-check">
                                  <input className="form-check-input" type="checkbox" checked={isChecked}
                                    disabled={(loginUser && loginUser?.loginRoleLevel >= role.roleLevel) || isAgent} onChange={(e) => handleLabUserRoleChecked(e, user.userId, role.roleId)} />
                                </div>
                              </td>
                            })}
                          </tr>);
                      })}
                    </tbody>
                  </table>
                </div>}
                <div className="row justify-content-center">
                  <div className="col-xl-3 my-3 d-flex justify-content-center">
                    <button className="btn btn-success d-block fs-4" title={t('button.save')} onClick={saveLabModify}><i className="fas fa-floppy-disk me-1"></i>
                      {t('button.save')}</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </BlockUi>
    </StlyedLabModify>
  );
}


const StlyedLabModify = styled.div`
  /* icon放大 */
  td .fas {
      font-size: 1.2rem;
      padding: .5rem;
  }
  
  /* 實驗室屬性表格 */
  table.border-none tr td {
      border-bottom-width: 0;
  }
  
  table.border-none tr td:nth-child(1) {
      font-size: 1rem;
      width: 15%;
  }
  
  thead {
    background:rgb(251, 205, 165);
  }
  .labBox {
      border: solid 1px #ededed;
      border-left: solid 3px #f59c1a;
      border-radius: 10px;
      padding: 1rem .5rem;
      margin-bottom: 1rem;
      position: relative;
  }
  
  .labBox .fas-big-icon {
      position: absolute;
      font-size: 4rem;
      color: #f59c1a;
      opacity: .5;
      left: -6rem;
      z-index: 1;
  }

  .file-div{
    overflow-y: auto;
    max-height: 600px;
  }

  .ms-6 {
      margin-left: 100px;
  }

  /* 人員表格的checkbox 置中 */
  #data-table-default input {
      float: none;
  }

  @media (max-width: 1400px) {
      .ms-6 {
          margin-left: 0;
      }
  }
  
  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    
    .lab-user-role-table table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 100px;
        text-align:left;
        min-height:100px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
`;

export default LabModify;
