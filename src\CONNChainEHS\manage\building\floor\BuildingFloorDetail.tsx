import DownloadButton from "ehs/common/button/DownloadButton";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { CONFIG_TYPE_FLOOR_DESC_TYPE, FILE_TYPE_BUILD_FLOOR_HAZARD_CARD, FILE_TYPE_BUILD_FLOOR_PLAN, FILE_TYPE_BUILD_FLOOR_RESCUE_IMAGE } from "../../../constant/constants";
import { EhsBuild } from "../../../models/EhsBuild";
import { EhsBuildingFloorInfo, initEhsBuildingFloorInfo } from "../../../models/EhsBuildingFloorInfo";
import { EhsConfigParam } from "../../../models/EhsConfigParam";
import { EhsFile } from "../../../models/EhsFile";
import { EhsMultipleDetail } from "../../../models/EhsMultipleDetail";
import { isArrayEmpty } from "../../../utils/arrayUtil";

type FormData = {
  disasterReliefPerson: { value: '' }[];
  combustible: { value: '' }[];
  otherHazardSubst: { value: '' }[];
};

export default function BuildingFloorDetail({ building, floorInfo = { ...initEhsBuildingFloorInfo }, floorFiles, floorMultiDetails, configData,
  onClose }: {
    building: EhsBuild;
    floorInfo: EhsBuildingFloorInfo;
    floorMultiDetails: Record<string, EhsMultipleDetail[]>;
    floorFiles: EhsFile[];
    configData: { [configType: string]: EhsConfigParam[] };
    onClose: () => void;
  }) {
  const { floorId, floorNo, floorArea, floorDesc, floorNote } = floorInfo
  const { t } = useTranslation();
  const floorDescOption = configData[CONFIG_TYPE_FLOOR_DESC_TYPE] || [];

  const getDefaultMultipleValue = (
    floorId: string,
    floorMultiDetails: Record<string, EhsMultipleDetail[]>
  ) => {
    const defaultFormData = floorMultiDetails[floorId] || [];

    return defaultFormData.reduce((acc: Record<string, { value: string }[]>, item: EhsMultipleDetail) => {
      const { detailType, detailContent } = item;
      const key = detailType as keyof FormData;

      // Type assertion to ensure `detailType` is a valid key of FormData
      if (acc[key]) {
        acc[key].push({ value: detailContent || '' });
      } else {
        acc[key] = [{ value: detailContent || '' }];
      }

      return acc;
    }, {} as Record<string, { value: string }[]>);
  };

  const defaultFormDataValues = useMemo(() => {
    return getDefaultMultipleValue(floorId, floorMultiDetails);
  }, [floorId, floorMultiDetails]);

  //file
  const fileMap: Map<string, EhsFile[]> = new Map([
    [FILE_TYPE_BUILD_FLOOR_PLAN, []],
    [FILE_TYPE_BUILD_FLOOR_RESCUE_IMAGE, []],
    [FILE_TYPE_BUILD_FLOOR_HAZARD_CARD, []]
  ]);

  floorFiles.forEach((file: EhsFile) => {
    const fileArray = fileMap.get(file.fileType);
    if (fileArray) {
      fileArray.push(file);
    }
  });
  const floorPlanFiles = fileMap.get(FILE_TYPE_BUILD_FLOOR_PLAN) || [];
  const rescueImageFiles = fileMap.get(FILE_TYPE_BUILD_FLOOR_RESCUE_IMAGE) || [];
  const hazardCardFiles = fileMap.get(FILE_TYPE_BUILD_FLOOR_HAZARD_CARD) || [];
  const getFirstExistFile = (files: EhsFile[]) => !isArrayEmpty(files) && files[0];
  const floorPlan = getFirstExistFile(floorPlanFiles);
  const rescueImage = getFirstExistFile(rescueImageFiles);
  const hazardCardImage = getFirstExistFile(hazardCardFiles);

  const doClose = () => {
    onClose();
  };

  return (
    <StyledBuildFloorDetail>
      <div className="modifyBuild">
        <div className="modifyBuild-header">
          <h4 className="modal-title">{t('func.detail')} {floorNo}</h4>
          <button
            type="button"
            className="btn-close"
            aria-hidden="true"
            onClick={doClose}
          ></button>
        </div>
        <div className="modifyBuild-body mx-3">
          <div className="row">
            <div className="col-xl-6 mb-3">
              <div className="col-md-12 mb-3">
                <label className="fw-bold mb-1">
                  {t('text.building.floor.description')}：{floorDesc && floorDescOption.find(option => option.configId === floorDesc)?.configName}
                </label>
              </div>
              <div className="col-md-12 mb-3">
                <label className="fw-bold mb-1">
                  {t('text.building.floor.area')}：{floorArea}
                </label>
              </div>
              <div className="col-md-12 mb-3">
                <label className="fw-bold mb-1">
                  {t('text.building.floor.note')}：{floorNote}
                </label>
              </div>
              <div className="col-md-12 mb-3">
                <label className="fw-bold mb-1">
                  {t('text.building.floor.floor_plan')}：
                  {floorPlan && <DownloadButton fileName={floorPlan.fileName} content={floorPlan.fileContentBase64} />}
                </label>
              </div>
              <div className="col-md-12 mb-3">
                <label className="fw-bold mb-1">
                  {t('text.building.rescue_image_class_b')}：
                  {rescueImage && <DownloadButton fileName={rescueImage.fileName} content={rescueImage.fileContentBase64} />}
                </label>
              </div>
              <div className="col-md-10 mb-3">
                <label className="fw-bold mb-1">
                  {t("text.building.hazard_identification_card")}：
                  {hazardCardImage && <DownloadButton fileName={hazardCardImage.fileName} content={hazardCardImage.fileContentBase64} />}
                </label>
              </div>
            </div>
            <div className="col-md-6 mb-3">
              <div className="col-md-12 mb-3">
                <label className="fw-bold mb-1">
                  {t('text.building.floor.dister_relief_person')}：
                  {defaultFormDataValues.disasterReliefPerson && defaultFormDataValues.disasterReliefPerson.map((item, index) => (
                    <span key={'labdetailOtherHazardSubstance' + index}>
                      {item.value}
                      {index < defaultFormDataValues.disasterReliefPerson.length - 1 && '、'}
                    </span>
                  ))}
                </label>
              </div>
              <div className="col-md-12 mb-3">
                <label className="fw-bold mb-1">
                  {t('text.building.floor.combustable_material')}：
                  {defaultFormDataValues.combustible && defaultFormDataValues.combustible.map((item, index) => (
                    <span key={'labdetailOtherHazardSubstance' + index}>
                      {item.value}
                      {index < defaultFormDataValues.combustible.length - 1 && '、'}
                    </span>
                  ))}
                </label>
              </div>
              <div className="col-md-12 mb-3">
                <label className="fw-bold mb-1">
                  {t('text.building.floor.other_hazard_substance')}：
                  {defaultFormDataValues.otherHazardSubst && defaultFormDataValues.otherHazardSubst.map((item, index) => (
                    <span key={'labdetailOtherHazardSubstance' + index}>
                      {item.value}
                      {index < defaultFormDataValues.otherHazardSubst.length - 1 && '、'}
                    </span>
                  ))}
                </label>
              </div>
            </div>
          </div>
        </div>
        <div className="modifyBuild-footer">
          <div className="btn btn-white" aria-hidden="true" onClick={doClose}>
            <i className="fas fa-times me-1" />
            {t("button.close")}
          </div>
        </div>
      </div>
    </StyledBuildFloorDetail>
  );
}

const StyledBuildFloorDetail = styled.div`
  background: white;
  width: 1000px;
  .modifyBuild-header {
    background:rgb(251, 205, 165);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  .modifyBuild-body {
    padding: 15px;
  }
  .modifyBuild-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-purple {
      margin-left: 10px;
    }
  }
  
    @media (max-width: 1024px) {
        width: 100%;  // 在小螢幕時使用 100% 寬度
        max-width: 100%;
        height: 100%;
        
        .modifyBuild-body {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 20px; // 添加底部間距，避免內容被 footer 遮擋
            max-height: 700px;
        }
        
        .modifyBuild-footer {
            position: sticky;
            bottom: 0;
            background: white;
            padding: 15px;
            border-top: 1px solid #ced4da;
            display: flex;
            justify-content: center;
        }
    }
`;
