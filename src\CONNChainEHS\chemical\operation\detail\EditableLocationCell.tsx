import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { EhsConfigParam } from '../../../models/EhsConfigParam';

interface EditableLocationCellProps {
    canEdit?: boolean;
    initialValue: string;
    storageLocationConfigs: EhsConfigParam[];
    updateLocation: (location: string) => void;
}

const EditableLocationCell: React.FC<EditableLocationCellProps> = ({ canEdit, initialValue, storageLocationConfigs, updateLocation }) => {
    const { t } = useTranslation();
    const [value, setValue] = useState(initialValue);
    const [isEditing, setIsEditing] = useState(false);
    const defaultOptionName = t('text.chemical.inventory_location_default');

    const handleEdit = (event: React.MouseEvent<HTMLButtonElement>) => {
        setIsEditing(true);
        stopPropagation(event);
    };

    const handleSave = (event: React.MouseEvent<HTMLButtonElement>) => {
        setIsEditing(false);
        updateLocation(value);
        setValue(value); // 更新狀態
        stopPropagation(event);
    };

    // 阻止事件冒泡 避免影響父元素的accordion收和
    const stopPropagation = (event: React.MouseEvent<HTMLButtonElement> | React.MouseEvent<HTMLTextAreaElement, MouseEvent>) => {
        event.stopPropagation();
    };

    return (
        <div>
            {isEditing ? (
                <select className="form-select form-select-lg" value={value} onClick={(event) => { event.stopPropagation(); }} onChange={(event: any) => setValue(event.target.value)}>
                    <option value="">{defaultOptionName}</option>
                    {storageLocationConfigs.map((item) => {
                        return <option value={item.configId} title={item.configName}>{item.configName}</option>
                    })}
                </select>
            ) : (
                <label>{storageLocationConfigs.find((item) => item.configId === value)?.configName || defaultOptionName}</label>
            )}
            {canEdit && <br />}
            {canEdit && <button
                type="button"
                className={`btn ${isEditing ? 'btn-success' : 'btn-warning'} mt-2`}
                onClick={isEditing ? (e) => handleSave(e) : (e) => handleEdit(e)}>
                {isEditing
                    ? <i className=" fas fa-floppy-disk me-1" />
                    : <i className="fas fa-pen me-1" />}
                {isEditing ? t('button.save') : t('button.edit')}
            </button>}
        </div>
    );
};

export default EditableLocationCell;