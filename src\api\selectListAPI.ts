import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const SelectListAPI = {
  getSelectConfigByType: async (
    parms: BaseParams & {
      configType: string;
      configSubType?: string;
    }
  ) => {
    return apiRequest(getApiAddress + "selectlist/config/type/subtype", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSelectArea: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "selectlist/areas", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSelectBuilding: async (
    parms: BaseParams & {
      areaId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "selectlist/building", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSelectBuildingFloor: async (
    parms: BaseParams & {
      buildId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "selectlist/building/floor", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSelectLabSearchView: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "selectlist/search", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSelectLabs: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "selectlist/labs", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  }, 
  getSelectLabStatus: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "selectlist/labstatus", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSelectLabAttributes: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "selectlist/labattr", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSelectChemicalCtrlnoList: async (
    parms: BaseParams & {}
  ) => {
    return apiRequest(getApiAddress + "selectlist/chemical/ctrlno", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSelectChemicalCtrlnoListByAreaId: async (
    parms: BaseParams & {
      areaId: string;
    }
  ) => {
    return apiRequest(getApiAddress + "selectlist/chemical/ctrlno/list/areaid", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSelectChemicalConcen: async (parms: BaseParams & {
    chemId: string
  }) => {
    return apiRequest(getApiAddress + "selectlist/chemical/concen", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSelectRoleLevelList: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "selectlist/level", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSelectOrgList: async (parms: BaseParams & {}) => {
    return apiRequest(getApiAddress + "selectlist/org", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getSelectOshms: async (parms: BaseParams & {
      configType: string;
  }) => {
    return fetch(getApiAddress + "selectlist/oshms", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};
