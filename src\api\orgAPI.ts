import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { EhsLanguage } from "../CONNChainEHS/models/EhsLanguage";
import { apiRequest, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";


export const OrgAPI = {
  getOrgList: async (parms: BaseParams) => {
    return apiRequest(getApiAddress + "organization/list",  createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  getOrgDetail: async (parms: BaseParams & {
    orgId: string;
  }) => {
    return apiRequest(getApiAddress + "organization/detail",  createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  addOrg: async (parms: BaseParams & {
    orgNameList: EhsLanguage[];
  }) => {
    return apiRequest(getApiAddress + "organization/add",  createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  editOrg: async (parms: BaseParams & {
    orgNameList: EhsLanguage[];
  }) => {
    return apiRequest(getApiAddress + "organization/edit",  createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  deleteOrg: async (parms: BaseParams & {
    orgId: string;
  }) => {
    return apiRequest(getApiAddress + "organization/del",  createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  getOrgLevelList: async (parms: BaseParams) => {
    return apiRequest(getApiAddress + "organization/level/list",  createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  getOrgLevelDetail: async (parms: BaseParams & {
    orglvId: string;
  }) => {
    return apiRequest(getApiAddress + "organization/level/detail",  createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  addOrgLevel: async (parms: BaseParams & {
    orglvNameList: EhsLanguage[];
  }) => {
    return apiRequest(getApiAddress + "organization/level/add",  createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  editOrgLevel: async (parms: BaseParams & {
    orglvId: string;
    orglvNameList: EhsLanguage[];
  }) => {
    return apiRequest(getApiAddress + "organization/level/edit",  createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
  deleteOrgLevel: async (parms: BaseParams & {
    orglvId: string;
  }) => {
    return apiRequest(getApiAddress + "organization/level/del",  createLoginPostJsonConfig(parms)).then( (res) => handleFetchResponse(res));
  },
};