// DisabledLabel.tsx
import React from 'react';
import styled from 'styled-components';

interface DisabledLabelProps {
    content: string;
}

const DisabledLabel: React.FC<DisabledLabelProps> = ({  content }) => {
    return (
        <StyledRow>
            <div className="form-control disabled-label">
                <label htmlFor="disabled-label">{content}</label>
            </div>
        </StyledRow>
    );
};

const StyledRow = styled.div`
    .disabled-label {
        border: 1px solid #ccc;
        padding: 8px;
        background-color: #f5f5f5;
        color: #888;
    }

    .disabled-label label {
        display: block;
        font-size: 14px;
        font-weight: normal;
        cursor: not-allowed;
    }
`;

export default DisabledLabel;