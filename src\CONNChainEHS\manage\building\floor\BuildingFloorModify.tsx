import { useEffect, useMemo, useState } from "react";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import { BuildAPI } from "../../../../api/buildAPI";
import InputFileUpload from "../../../common/InputFileUpload";
import { showSuccessToast } from "../../../common/Toast";
import { CONFIG_TYPE_FLOOR_DESC_TYPE, FILE_TYPE_BUILD_FLOOR_HAZARD_CARD, FILE_TYPE_BUILD_FLOOR_PLAN, FILE_TYPE_BUILD_FLOOR_RESCUE_IMAGE, UPLOAD_ACCEPT_TYPE_IMAGE } from "../../../constant/constants";
import useLoginUser from "../../../hooks/useLoginUser";
import { EhsBuild } from "../../../models/EhsBuild";
import { EhsBuildingFloorInfo, initEhsBuildingFloorInfo } from "../../../models/EhsBuildingFloorInfo";
import { EhsConfigParam } from "../../../models/EhsConfigParam";
import { EhsFile } from "../../../models/EhsFile";
import { EhsMultipleDetail, initEhsMultipleDetail } from "../../../models/EhsMultipleDetail";
import { isArrayEmpty } from "../../../utils/arrayUtil";
import { getBasicLoginUserInfo } from "../../../utils/authUtil";
import { isApiCallSuccess } from "../../../utils/resultUtil";

type FormData = {
  disasterReliefPerson: { value: '' }[];
  combustible: { value: '' }[];
  otherHazardSubst: { value: '' }[];
};

export default function BuildingFloorModify({ building, floorInfo = { ...initEhsBuildingFloorInfo }, floorFiles, floorMultiDetails, configData,
  onClose, setLoadingBlock, onActionSuccess, setFloorInfo, setFloorFiles }: {
    building: EhsBuild;
    floorInfo: EhsBuildingFloorInfo;
    floorMultiDetails: Record<string, EhsMultipleDetail[]>;
    floorFiles: EhsFile[];
    configData: { [configType: string]: EhsConfigParam[] };
    onClose: () => void;
    setLoadingBlock: (loading: boolean) => void;
    onActionSuccess: (newFloorInfo: EhsBuildingFloorInfo) => void;
    setFloorInfo: (floorInfo: EhsBuildingFloorInfo) => void;
    setFloorFiles: (floorFiles: EhsFile[]) => void;
  }) {
  const { floorId, floorNo, floorArea, floorDesc, floorNote } = floorInfo
  const { t } = useTranslation();
  const { loginUser } = useLoginUser();
  const initialDefaultValues: FormData = {
    disasterReliefPerson: [{ value: '' }],
    combustible: [{ value: '' }],
    otherHazardSubst: [{ value: '' }],
  };

  const getDefaultMultipleValue = (
    floorId: string,
    floorMultiDetails: Record<string, EhsMultipleDetail[]>
  ) => {
    const defaultFormData = floorMultiDetails[floorId] || [];

    return isArrayEmpty(defaultFormData) ? initialDefaultValues : defaultFormData.reduce((acc: Record<string, { value: string }[]>, item: EhsMultipleDetail) => {
      const { detailType, detailContent } = item;
      const key = detailType as keyof FormData;

      // Type assertion to ensure `detailType` is a valid key of FormData
      if (acc[key]) {
        acc[key].push({ value: detailContent || '' });
      } else {
        acc[key] = [{ value: detailContent || '' }];
      }

      return acc;
    }, {} as Record<string, { value: string }[]>);
  };

  const defaultFormDataValues = useMemo(() => {
    return getDefaultMultipleValue(floorId, floorMultiDetails);
  }, [floorId, floorMultiDetails]);

  const { control, handleSubmit, reset } = useForm<FormData>({
    defaultValues: defaultFormDataValues,
  });
  const { fields: disasterReliefPersonFields, append: addDisasterReliefPerson, remove: removeDisasterReliefPerson } = useFieldArray({
    control,
    name: 'disasterReliefPerson',  // 確保 name 對應到表單的欄位名稱
  });
  const { fields: combustibleFields, append: addCombustible, remove: removeCombustible } = useFieldArray({
    control,
    name: 'combustible',  // 確保 name 對應到表單的欄位名稱
  });

  const { fields: hazardFields, append: addHazard, remove: removeHazard } = useFieldArray({
    control,
    name: 'otherHazardSubst',  // 確保 name 對應到表單的欄位名稱
  });
  const [floorPlanFile, setFloorPlanFile] = useState<File | null>(null);
  const [rescueImage, setRescueImage] = useState<File | null>(null);
  const [hazardCardImage, setHazardCardImage] = useState<File | null>(null);
  const [delFileIdList, setDelFileIdList] = useState<string[]>([]);
  const floorDescOption = configData[CONFIG_TYPE_FLOOR_DESC_TYPE] || [];

  const fileMap: Map<string, EhsFile[]> = new Map([
    [FILE_TYPE_BUILD_FLOOR_PLAN, []],
    [FILE_TYPE_BUILD_FLOOR_RESCUE_IMAGE, []],
    [FILE_TYPE_BUILD_FLOOR_HAZARD_CARD, []]
  ]);
  floorFiles.forEach((file: EhsFile) => {
    const fileArray = fileMap.get(file.fileType);
    if (fileArray) {
      fileArray.push(file);
    }
  });
  const floorPlanFiles = fileMap.get(FILE_TYPE_BUILD_FLOOR_PLAN) || [];
  const rescueImageFiles = fileMap.get(FILE_TYPE_BUILD_FLOOR_RESCUE_IMAGE) || [];
  const hazardCardFiles = fileMap.get(FILE_TYPE_BUILD_FLOOR_HAZARD_CARD) || [];

  useEffect(() => {
    reset(defaultFormDataValues);
  }, [defaultFormDataValues])

  const onSubmit = (data: FormData) => {
    onAction(data);
  };

  const onAction = (data: FormData) => {
    setLoadingBlock(true);
    const floorDetailMultiList = transformDataToFloorMultiDetail(data);
    const updateObj = { ...floorInfo, buildId: building.buildId };
    setFloorInfo(updateObj);
    BuildAPI.modifyBuildFloorInfo({
      ...getBasicLoginUserInfo(loginUser),
      floorInfo: updateObj,
      floorPlan: floorPlanFile,
      rescueImageClassB: rescueImage,
      hazardCard: hazardCardImage,
      delFileIdList: delFileIdList,
      floorDetailMultiList
    }).then((res) => {
      if (isApiCallSuccess(res)) {
        showSuccessToast(t('message.success'));
        onActionSuccess(updateObj);
        doClose();
      }
      setLoadingBlock(false);
    }).catch((error) => {
      setLoadingBlock(false);
    })
  };

  const transformDataToFloorMultiDetail = (data: FormData): EhsMultipleDetail[] => {
    const result: EhsMultipleDetail[] = [];

    // 遍歷每個 FormData 的鍵
    Object.entries(data).forEach(([key, valueArray]) => {
      // 過濾掉 value 為空的項目
      const nonEmptyValues = valueArray.filter(item => item.value.trim() !== '');

      // 如果有不為空的項目，則轉換為 EhsMultipleDetail
      nonEmptyValues.forEach(item => {
        result.push({
          ...initEhsMultipleDetail,
          detailType: key,  // 使用 FormData 鍵值作為 detailType
          detailContent: item.value,  // 使用 value 作為 detailContent
        });
      });
    });

    return result;
  };

  const doClose = () => {
    onClose();
    resetState();
  };

  const resetState = () => {
    setFloorPlanFile(null);
    setRescueImage(null);
    setHazardCardImage(null);
    setDelFileIdList([]);
  };

  return (
    <StyledModifyBuildFloor>
      <div className="modifyBuild">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="modifyBuild-header">
            <h4 className="modal-title">{t('func.edit')} {floorNo}</h4>
            <button
              type="button"
              className="btn-close"
              aria-hidden="true"
              onClick={doClose}
            ></button>
          </div>
          <div className="modifyBuild-body mx-3">
            <div className="row">
              <div className="col-md-6 mb-3">
                <div className="col-md-12 mb-3">
                  <label className="fw-bold mb-1">
                    {t('text.building.floor.description')}：
                  </label>
                  <select className={`form-select w-100`} value={floorDesc}
                    onChange={(e) => {
                      setFloorInfo({ ...floorInfo, floorDesc: e.target.value })
                    }}>
                    <option value="">{t('text.select')}</option>
                    {floorDescOption.map((item) => {
                      return <option key={item.configId} value={item.configId} title={item.configName} >{item.configName}</option>
                    })}
                  </select>
                </div>
                <div className="col-md-12 mb-3">
                  <label className="fw-bold mb-1">
                    {t('text.building.floor.area')}：
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    value={floorArea}
                    onChange={(e) => {
                      setFloorInfo({ ...floorInfo, floorArea: e.target.value })
                    }}
                  />
                </div>
                <div className="col-md-12 mb-3">
                  <label className="fw-bold mb-1">
                    {t('text.building.floor.note')}：
                  </label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder={t('text.building.floor.note')}
                    value={floorNote}
                    onChange={(e) => {
                      setFloorInfo({ ...floorInfo, floorNote: e.target.value })
                    }}
                  ></textarea>
                </div>
                <div className="col-md-12 mb-3">
                  <label className="fw-bold mb-1">
                    {t('text.building.floor.floor_plan')}：
                  </label>
                  <InputFileUpload inputId={'floorPlan'}
                    defaultFile={isArrayEmpty(floorPlanFiles) ? undefined : floorPlanFiles[0]}
                    defaultFileInfo={isArrayEmpty(floorPlanFiles) ? undefined : floorPlanFiles[0]}
                    onFileChange={(file) => {
                      setFloorPlanFile(file);
                      if (file === null && !isArrayEmpty(floorPlanFiles)) {
                        const newDelFileId = floorPlanFiles[0].fileId;
                        setDelFileIdList([...delFileIdList, newDelFileId]);
                        setFloorFiles(floorFiles.filter(file => file.fileId !== newDelFileId));
                      }
                    }}
                    acceptedFileTypes={UPLOAD_ACCEPT_TYPE_IMAGE} />
                </div>
                <div className="col-md-12 mb-3">
                  <label className="fw-bold mb-1">
                    {t('text.building.rescue_image_class_b')}：
                  </label>
                  <InputFileUpload inputId={'rescueImage'}
                    defaultFile={isArrayEmpty(rescueImageFiles) ? undefined : rescueImageFiles[0]}
                    defaultFileInfo={isArrayEmpty(rescueImageFiles) ? undefined : rescueImageFiles[0]}
                    onFileChange={(file) => {
                      setRescueImage(file);
                      if (file === null && !isArrayEmpty(rescueImageFiles)) {
                        const newDelFileId = rescueImageFiles[0].fileId;
                        setDelFileIdList([...delFileIdList, newDelFileId]);
                        setFloorFiles(floorFiles.filter(file => file.fileId !== newDelFileId));
                      }
                    }}
                    acceptedFileTypes={UPLOAD_ACCEPT_TYPE_IMAGE} />
                </div>
                <div className="col-md-10 mb-3">
                  <label className="fw-bold mb-1">
                    {t("text.building.hazard_identification_card")}：
                  </label>
                  <InputFileUpload inputId={'hazardCard'}
                    defaultFile={isArrayEmpty(hazardCardFiles) ? undefined : hazardCardFiles[0]}
                    defaultFileInfo={isArrayEmpty(hazardCardFiles) ? undefined : hazardCardFiles[0]}
                    onFileChange={(file) => {
                      setHazardCardImage(file);
                      if (file === null && !isArrayEmpty(hazardCardFiles)) {
                        const newDelFileId = hazardCardFiles[0].fileId;
                        setDelFileIdList([...delFileIdList, newDelFileId]);
                        setFloorFiles(floorFiles.filter(file => file.fileId !== newDelFileId));
                      }
                    }}
                    acceptedFileTypes={UPLOAD_ACCEPT_TYPE_IMAGE} />
                </div>
              </div>
              <div className="col-md-6 mb-3">
                <div className="col-md-12 mb-3">
                  <label className="fw-bold mb-1">
                    {t('text.building.floor.dister_relief_person')}：
                  </label>
                  <button
                    type="button"
                    className="btn btn-purple ms-3"
                    onClick={() => addDisasterReliefPerson({ value: '' })}  // 新增 disasterReliefPerson 輸入框，傳入物件格式
                  >
                    <i className="fas fa-plus me-1" /> {t('button.add')}
                  </button>
                  <div className="row d-flex align-items-center">
                    {disasterReliefPersonFields.map((item, index) => (
                      <div key={item.id} className="col-12">
                        <div className="d-flex align-items-center">
                          <span className="mx-3 multi-inputs">{index + 1}.</span>
                          <Controller
                            name={`disasterReliefPerson.${index}.value`}
                            control={control}
                            render={({ field }) => (
                              <input
                                {...field}  // 傳遞 field 的值
                                className="form-control my-1 item-width-80"
                                data-parsley-required="true"
                              />
                            )}
                          />
                          {/* 刪除按鈕 */}
                          <i
                            className="fas fa-trash fa-lg text-danger ps-2"
                            title={t('button.delete')}
                            onClick={() => removeDisasterReliefPerson(index)}  // 刪除該項
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="col-md-12 mb-3">
                  <label className="fw-bold mb-1">
                    {t('text.building.floor.combustable_material')}：
                  </label>
                  <button
                    type="button"
                    className="btn btn-purple ms-3"
                    onClick={() => addCombustible({ value: '' })}  // 新增其他危害物質的輸入框，傳入物件格式
                  >
                    <i className="fas fa-plus me-1" /> {t('button.add')}
                  </button>
                  <div className="row d-flex align-items-center">
                    {combustibleFields.map((item, index) => (
                      <div key={item.id} className="col-12">
                        <div className="d-flex align-items-center">
                          <span className="mx-3 multi-inputs">{index + 1}.</span>
                          <Controller
                            name={`combustible.${index}.value`}
                            control={control}
                            render={({ field }) => (
                              <input
                                {...field}  // 傳遞 field 的值
                                className="form-control my-1 item-width-80"
                                data-parsley-required="true"
                              />
                            )}
                          />
                          {/* 刪除按鈕 */}
                          <i
                            className="fas fa-trash fa-lg text-danger ps-2"
                            title={t('button.delete')}
                            onClick={() => removeCombustible(index)}  // 刪除該項
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="col-md-12 mb-3">
                  <label className="fw-bold mb-1">
                    {t('text.building.floor.other_hazard_substance')}：
                  </label>
                  <button
                    type="button"
                    className="btn btn-purple ms-3"
                    onClick={() => addHazard({ value: '' })}  // 新增其他危害物質的輸入框，傳入物件格式
                  >
                    <i className="fas fa-plus me-1" /> {t('button.add')}
                  </button>
                  <div className="row d-flex align-items-center">
                    {hazardFields.map((item, index) => (
                      <div key={item.id} className="col-12">
                        <div className="d-flex align-items-center">
                          <span className="mx-3 multi-inputs">{index + 1}.</span>
                          <Controller
                            name={`otherHazardSubst.${index}.value`}
                            control={control}
                            render={({ field }) => (
                              <input
                                {...field}  // 傳遞 field 的值
                                className="form-control my-1 item-width-80"
                                data-parsley-required="true"
                              />
                            )}
                          />
                          {/* 刪除按鈕 */}
                          <i
                            className="fas fa-trash fa-lg text-danger ps-2"
                            title={t('button.delete')}
                            onClick={() => removeHazard(index)}  // 刪除該項
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="modifyBuild-footer">
            <div className="btn btn-white" aria-hidden="true" onClick={doClose}>
              <i className="fas fa-times me-1" />
              {t("button.close")}
            </div>
            <div className={`btn btn-warning ms-2`}
              onClick={() => handleSubmit(onSubmit)()} >
              <i className="fas fa-cubes" /> {t('func.edit')}
            </div>
          </div>
        </form>
      </div>
    </StyledModifyBuildFloor>
  );
}

const StyledModifyBuildFloor = styled.div`
  background: white;
  width: 1000px;
  .modifyBuild-header {
    background:rgb(251, 205, 165);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ced4da;
  }
  .modifyBuild-body {
    padding: 15px;
  }
  .modifyBuild-footer {
    border-top: 1px solid #ced4da;
    padding: 15px;
    display: flex;
    justify-content: end;
    .btn.btn-purple {
      margin-left: 10px;
    }
  }
  
    @media (max-width: 1024px) {
        width: 100%;  // 在小螢幕時使用 100% 寬度
        max-width: 100%;
        height: 100%;
        
        .modifyBuild-body {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 20px; // 添加底部間距，避免內容被 footer 遮擋
            max-height: 700px;
        }
        
        .modifyBuild-footer {
            position: sticky;
            bottom: 0;
            background: white;
            padding: 15px;
            border-top: 1px solid #ced4da;
            display: flex;
            justify-content: center;
        }
    }
`;
