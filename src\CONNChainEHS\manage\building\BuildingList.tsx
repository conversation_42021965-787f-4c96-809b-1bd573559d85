import BlockUi from "@availity/block-ui";
import ExpandableRwdTable from "ehs/common/ExpandableRwdTable";
import ExpandRwdButton from "ehs/common/ExpandRwdButton";
import { useExpandedRwdTableRow } from "ehs/hooks/useExpandedRwdTableRow";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import { AreaAPI } from "../../../api/areaAPI";
import { BuildAPI } from "../../../api/buildAPI";
import { ConfigAPI } from "../../../api/configAPI";
import { FileAPI } from "../../../api/fileAPI";
import { OptionAPI } from "../../../api/optionAPI";
import { AppPaths } from "../../../config/app-paths";
import BlockuiMsg from "../../common/BlockuiMsg";
import Dialog from "../../common/Dialog";
import ImageView from "../../common/ImageView";
import Loader from "../../common/Loader";
import NoDataRow from "../../common/NoDataRow";
import SortIcon from "../../common/SortIcon";
import { confirmMsg, errorMsg } from "../../common/SwalMsg";
import { showSuccessToast } from "../../common/Toast";
import { CONFIG_TYPE_BUILD_TYPE, FILE_TYPE_BUILD_RESCUE_IMAGE_CLASS_A, OPTION_SHOW_BUILDING_TYPE } from "../../constant/constants";
import { ActionMode } from "../../enums/ActionMode";
import { BtnType } from "../../enums/BtnType";
import useLoginUser from "../../hooks/useLoginUser";
import Breadcrumbs from "../../layout/Breadcrumbs";
import Footer from "../../layout/Footer";
import PageSizeSelector from "../../layout/PageSizeSelector";
import Pagination from "../../layout/Pagination";
import { EhsArea } from "../../models/EhsArea";
import { EhsBuild, initEhsBuild } from "../../models/EhsBuild";
import { EhsConfigParam } from "../../models/EhsConfigParam";
import { EhsFile, initEhsFile } from "../../models/EhsFile";
import { EhsOptions } from "../../models/EhsOptions";
import { PageInfo, initPageInfo } from "../../models/PageInfo";
import { isArrayEmpty } from "../../utils/arrayUtil";
import { checkBtnAuth, getBasicLoginUserInfo, getLoginUserRoleLevel } from "../../utils/authUtil";
import { getLabTextObj } from "../../utils/langUtil";
import { isApiCallSuccess } from "../../utils/resultUtil";
import BuildingModify from "./BuildingModify";

function BuildingList() {
  const { t, i18n } = useTranslation();
  const { loginUser } = useLoginUser();
  const { buildTypeText } = getLabTextObj(loginUser, t);
  const roleLevel = getLoginUserRoleLevel(loginUser);
  const areaSelect = useRef<HTMLSelectElement>(null)
  const buildTypeSelect = useRef<HTMLSelectElement>(null)
  const [popupImageView, setPopupImageView] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);
  const [loadingBlock, setLoadingBlock] = useState(false);
  const [showBuildType, setShowBuildType] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [localSearchKey, setLocalSearchKey] = useState("");
  const [popupMode, setPopupMode] = useState<ActionMode | null>(null);
  const [modifyData, setModifyData] = useState<EhsBuild>(initEhsBuild);
  const [areaList, setAreaList] = useState<EhsArea[]>([]);
  const [buildingList, setBuildingList] = useState<EhsBuild[]>([]);
  const [buildTypeList, setBuildTypeList] = useState<EhsConfigParam[]>([]);
  const [localSearchResult, setLocalSearchResult] = useState<EhsBuild[]>([]);
  const [buildRescueImageObj, setBuildRescueImageObj] = useState<{ [type: string]: EhsFile | null }>({});
  const [imageViewFile, setImageViewFile] = useState<EhsFile>(initEhsFile);
  const [condition, setCondition] = useState<{
    keyword: string;
    areaId: string;
    buildType: number | null;
    currentPage: number;
    pageSize: number;
  }>({
    keyword: "",
    areaId: "",
    buildType: null,
    currentPage: 1,
    pageSize: 50,
  });
  const [pageInfo, setPageInfo] = useState<PageInfo>(initPageInfo);
  const isAddBuildingRole = checkBtnAuth(BtnType.ADD_BUILDING);
  const isEditBuildingRole = checkBtnAuth(BtnType.EDIT_BUILDING);

  useEffect(() => {
    if (loginUser && isInitialLoad) {
      setIsInitialLoad(false);
      Promise.all([fetchData(), fetchOption(), fetchFiles()])
        .then(() => { });
    }
  }, [loginUser, isInitialLoad]);

  useEffect(() => {
    if (loginUser && !isInitialLoad) {
      fetchData();
    }
  }, [loginUser, condition, i18n.language]);

  useEffect(() => {
    if (localSearchKey) {
      let resultList = buildingList.filter((data: EhsBuild) => {
        return [data.areaName, data.buildNo, data.buildName].some(property => property && property.includes(localSearchKey));
      });
      setLocalSearchResult(resultList);
    }

  }, [localSearchKey])

  useEffect(() => {
    if (popupMode === null && loginUser) {
      if (modifyData.buildId) {
        fetchFiles(modifyData.buildId);
      }
      setModifyData(initEhsBuild);
    }
  }, [popupMode]);

  const fetchData = () => {
    setLoading(true)
    BuildAPI.getBuildList({
      ...getBasicLoginUserInfo(loginUser)!,
      areaId: condition.areaId,
      buildType: condition.buildType,
      currentPage: condition.currentPage,
      pageSize: condition.pageSize
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        setBuildingList(result.results);
        setPageInfo(result.pageinfo);
        setLoading(false)
      }
    });
    fetchAreaData();
    fetchConfigData();
  };

  const fetchOption = () => {
    const optionIds = [OPTION_SHOW_BUILDING_TYPE];
    OptionAPI.getOptionListByIds({
      ...getBasicLoginUserInfo(loginUser)!,
      optionIdList: optionIds
    }).then(result => {
      if (isApiCallSuccess(result)) {
        const options = result.results;
        const optionsMap: {
          [key: string]: EhsOptions;
        } = {};
        options.forEach((item: any) => {
          optionsMap[item.optionId] = item;
        });
        setShowBuildType(optionsMap[OPTION_SHOW_BUILDING_TYPE].optionIntValue === 1);
      }
    });
  };

  const fetchFiles = (fileMappingId: string | null | undefined = null) => {
    const params = {
      ...getBasicLoginUserInfo(loginUser),
      fileTypeList: [FILE_TYPE_BUILD_RESCUE_IMAGE_CLASS_A],
    };

    if (fileMappingId) {
      params.fileMappingIdList = [fileMappingId];
    }

    FileAPI.getFileList(params).then((result) => {
      if (isApiCallSuccess(result)) {
        const resultFileList: EhsFile[] = result.results;
        const rescurImgFiles: Record<string, EhsFile> = { ...buildRescueImageObj } as Record<string, EhsFile>;;
        if (fileMappingId) {
          // 使用 delete 操作，這會讓該項目變成 undefined，而不是 null
          delete rescurImgFiles[fileMappingId];
        }
        resultFileList.forEach((file: EhsFile) => {
          if (file.fileType === FILE_TYPE_BUILD_RESCUE_IMAGE_CLASS_A) {
            rescurImgFiles[file.fileMappingId] = file;
          }
        });
        setBuildRescueImageObj(rescurImgFiles);
      }
    });
  };

  const fetchAreaData = () => {
    AreaAPI.getAreaList({
      ...getBasicLoginUserInfo(loginUser)!,
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        const areaData = result.results;
        if (!isArrayEmpty(areaData)) {
          const areaOption = areaData.filter((area: EhsArea) => area.areaStatus === 1);
          setAreaList(areaOption);
        }
      }
    });
  };

  const fetchConfigData = () => {
    ConfigAPI.getConfigByType({
      ...getBasicLoginUserInfo(loginUser)!,
      configTypeList: [CONFIG_TYPE_BUILD_TYPE],
    }).then((result) => {
      if (isApiCallSuccess(result)) {
        setBuildTypeList(result.results);
      }
    });
  };

  const getShowList = () => {
    if (!localSearchKey) {
      return buildingList;
    }
    return localSearchResult;
  };
  // 添加你想要的特殊選項
  const defaultOption = <option key="defaultOption" value="">{t("text.all")}</option>;

  // 映射 areaList 並添加特殊選項
  const areaSelectOption = [defaultOption, ...areaList.map((data, index) => {
    return <option key={data.areaId} value={data.areaId}>{data.areaName}</option>
  })];

  const clickSearch = () => {
    setCondition({
      ...condition,
      areaId: areaSelect.current!.value,
      buildType: parseInt(buildTypeSelect.current!.value),
      currentPage: 1
    })
  }

  const editBuildStatus = (buildId: string, activated: boolean) => {
    const buildStatus = activated ? 1 : 0;
    BuildAPI.editBuildStatus({
      ...getBasicLoginUserInfo(loginUser)!,
      buildId: buildId,
      buildStatus: buildStatus
    }).then(result => {
      if (isApiCallSuccess(result)) {
        showSuccessToast(t('message.success'));
        let cloneList = [...buildingList];
        let updateData = cloneList.filter(updateData => updateData.buildId === buildId);
        if (updateData.length) {
          updateData[0].buildStatus = buildStatus;
          setBuildingList(cloneList);
        }
      } else {
        errorMsg(result.message);
      }
    }).catch(err => {
      console.error(err);
    })

  }

  const deleteSuccess = () => {
    fetchData();
  }

  return (
    <StlyedBuildingList $loading={loading}>
      <BlockUi tag="span" className="d-block" blocking={loadingBlock} message={<BlockuiMsg />}>
        <div className="d-flex flex-column p-0" id="content">
          {
            <Dialog
              content={
                <BuildingModify
                  onClose={() => {
                    setPopupMode(null);
                  }}
                  onActionSuccess={() => {
                    setPopupMode(null);
                    fetchData();
                    showSuccessToast(t('message.success'));
                  }}
                  setLoadingBlock={setLoadingBlock}
                  mode={popupMode}
                  modifyData={modifyData}
                  areaSelectOption={areaSelectOption}
                  buildTypeList={buildTypeList}
                  showBuildType={showBuildType}
                />
              }
              show={popupMode !== null}
            />
          }
          <Dialog
            content={
              <ImageView imageFile={imageViewFile} onClose={() => { setPopupImageView(false) }} />
            }
            show={popupImageView}
          />
          {/* BEGIN scrollbar */}
          <div className="app-content-padding flex-grow-1">
            {/* BEGIN breadcrumb */}
            <Breadcrumbs
              items={[
                { label: t("func.system_manage_setting") },
                { label: t("func.building_manage") },
              ]}
            />
            {/* END breadcrumb */}
            {/* BEGIN page-header */}
            <h1 className="page-header">{t("func.building_manage")} </h1>
            {/* END page-header */}

            <div className="card">
              {isAddBuildingRole && <div className="card-body">
                {/* 新增建築 button */}
                {<button
                  type="button"
                  className="btn btn-purple fs-5"
                  title={t("button.add")}
                  onClick={() => {
                    setModifyData(initEhsBuild)
                    setPopupMode(ActionMode.ADD);
                  }}
                >
                  <i className="fas fa-plus"></i> {t("button.add")}
                </button>}
              </div>}
              <div className="card-body p-4">
                <div className="row">
                  <div className="col-xl-3 d-flex align-items-center">
                    <label className="pe-3">{t('text.area.item')}</label>
                    <select className="form-select w-75" ref={areaSelect}>
                      {areaSelectOption}
                    </select>
                  </div>
                  {showBuildType && <div className="col-xl-3 d-flex align-items-center">
                    <label className="pe-3">{buildTypeText}</label>
                    <select className="form-select w-75" ref={buildTypeSelect}>
                      {defaultOption}
                      {buildTypeList.map((data) => {
                        return <option key={data.configId} value={data.configIvalue}>{data.configName}</option>
                      })}
                    </select>
                  </div>}
                  <div className="col-xl-3">
                    <button type="button" className="btn btn-primary" onClick={clickSearch}><i className="fas fa-magnifying-glass" title={t('button.search.item')}></i> {t('button.search.item')}</button>
                  </div>
                </div>
              </div>
            </div>
            <div className="card pt-3">
              <div className="row topFunctionRow">
                <div className="col-sm-12 col-md-6 left">
                  <PageSizeSelector pageSize={condition.pageSize} setCondition={setCondition} />
                </div>
                <div className="col-sm-12 col-md-6 right">
                  <div className="dataTables_filter d-flex">
                    search:
                    <input
                      value={localSearchKey}
                      onChange={(e) => {
                        setLocalSearchKey(e.target.value);
                      }}
                      type="search"
                      className="form-control form-control-sm"
                      placeholder=""
                      aria-controls="data-table-default"
                    />
                  </div>
                </div>
              </div>
              <div className="card-body">
                {loading && <Loader />}
                <ExpandableRwdTable>
                  <div className="table-container">
                    <table
                      id="data-table-default"
                      className={
                        "table table-hover align-middle dt-responsive nowrap"
                      }
                    >
                      <thead className="text-center fs-4 fw-bold">
                        <tr>
                          <th>{t("table.title.item")} </th>
                          <th>
                            {t("table.title.no")}{" "}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"buildNo"}
                              setFunction={setBuildingList}
                            />
                          </th>
                          <th className='text-start responsive-header'>
                            {t("table.title.area.item")}{" "}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"areaName"}
                              setFunction={setBuildingList}
                            />
                          </th>
                          <th className='text-start'>
                            {t("table.title.name")}{" "}
                            <SortIcon
                              dataList={buildingList}
                              dataField={"buildName"}
                              setFunction={setBuildingList}
                            />
                          </th>
                          <th className='text-end responsive-header'>
                            {t("table.title.building.floor.item")}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"buildFloors"}
                              setFunction={setBuildingList}
                            />
                          </th>
                          <th className='text-end responsive-header'>
                            {t("table.title.building.floor.basement")}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"buildUnfloors"}
                              setFunction={setBuildingList}
                            />
                          </th>
                          {showBuildType && <th className='text-center responsive-header'>
                            {buildTypeText}
                            <SortIcon
                              dataList={getShowList()}
                              dataField={"buildType"}
                              setFunction={setBuildingList}
                            />
                          </th>}
                          <th className='text-center responsive-header'>{t('text.building.rescue_image_class_a')}</th>
                          {isEditBuildingRole && <th data-orderable="false">{t("table.title.enable")}</th>}
                          <th data-orderable="false" className="text-start">{t("table.title.action")}</th>
                        </tr>
                      </thead>
                      <tbody className="text-center fs-5">
                        {!loading && getShowList() && !isArrayEmpty(getShowList()) ?
                          getShowList().map((data, idx) => {
                            return <Row key={data.buildId} index={idx + 1} building={data}
                              buildRescueImageObj={buildRescueImageObj} buildTypeList={buildTypeList}
                              showBuildType={showBuildType}
                              onEditStatus={editBuildStatus} onDeleteSuccess={deleteSuccess} setPopupMode={setPopupMode} setModifyData={setModifyData}
                              setLoadingBlock={setLoadingBlock} setImageViewFile={setImageViewFile} setPopupImageView={setPopupImageView} />;
                          }) : (!loading && <NoDataRow />)}
                      </tbody>
                    </table>
                  </div>
                </ExpandableRwdTable>
                <Pagination pageInfo={pageInfo} setCondition={setCondition} />
              </div>
            </div>
          </div>
          {/* BEGIN #footer */}
          <Footer />
          {/* END #footer */}
        </div>
      </BlockUi>
    </StlyedBuildingList>
  );
}

const Row = (props: {
  index: number; building: EhsBuild;
  buildRescueImageObj: Record<string, EhsFile | null>;
  showBuildType: boolean;
  buildTypeList: EhsConfigParam[];
  onEditStatus: (buildId: string, activated: boolean) => void; onDeleteSuccess: () => void;
  setLoadingBlock: (block: boolean) => void;
  setPopupMode: (mode: ActionMode | null) => void;
  setModifyData: React.Dispatch<React.SetStateAction<EhsBuild>>;
  setPopupImageView: (show: boolean) => void;
  setImageViewFile: (file: EhsFile) => void;
}) => {
  const { isExpanded, toggleExpanded } = useExpandedRwdTableRow();
  const navigate = useNavigate();
  const { loginUser } = useLoginUser();
  const { t } = useTranslation();
  const { buildTypeText } = getLabTextObj(loginUser, t);
  const { index, building, buildRescueImageObj, buildTypeList, showBuildType,
    onEditStatus, onDeleteSuccess, setPopupMode, setModifyData, setLoadingBlock,
    setImageViewFile, setPopupImageView } = props;
  const { buildId, buildNo, areaName, buildName, buildFloors, buildUnfloors, buildStatus, buildType } = building;
  const isEditBuildingRole = checkBtnAuth(BtnType.EDIT_BUILDING);
  const isDeleteBuildingRole = checkBtnAuth(BtnType.DELETE_BUILDING);
  const rescurImg = buildRescueImageObj[buildId];

  const clickEdit = () => {
    setModifyData({ ...building });
    setPopupMode(ActionMode.EDIT);
  };

  const clickDelete = () => {
    if (loginUser) {
      confirmMsg(t('message.confirm.delete'), t).then((value) => {
        if (value) {
          setLoadingBlock(true);
          BuildAPI.deleteBuild(
            {
              ...getBasicLoginUserInfo(loginUser)!,
              buildId: buildId
            }
          ).then(result => {
            if (isApiCallSuccess(result)) {
              showSuccessToast(t('message.success'));
              onDeleteSuccess()
              setLoadingBlock(false);
            } else {
              errorMsg(result.message);
              setLoadingBlock(false);
            }
          }).catch(err => {
            setLoadingBlock(false);
          })
        }
      });
    }
  };

  const onClickBuildFloor = () => {
    navigate("/" + AppPaths.manage.buildingFloorList, { state: { building, isEditBuildingRole } });
  }

  const buildTypeDisplay = buildTypeList.find((data) => data.configIvalue === buildType)?.configName;
  const rescueImageDisplay = rescurImg &&
    <div key={'rescueImage' + rescurImg.fileId} style={{ marginRight: '10px' }}>
      <img
        src={`data:image/png;base64,${rescurImg.fileContent}`}
        alt={rescurImg.fileName} title={rescurImg.fileName}
        className="my-1"
        style={{ width: '100px', height: '100px' }}
        onClick={() => {
          setImageViewFile(rescurImg);
          setPopupImageView(true);
        }}
      />
    </div>

  return (
    <>
      <tr>
        <td data-title={t("table.title.item")} className='item-width-3'>{index}</td>
        <td data-title={t("table.title.no")}>{buildNo}</td>
        <td data-title={t("table.title.area.item")} className='text-start responsive-cell'>{areaName}</td>
        <td data-title={t("table.title.name")} className='text-start'>{buildName}</td>
        <td data-title={t("table.title.building.floor.item")} className='text-end responsive-cell'>{buildFloors} </td>
        <td data-title={t("table.title.building.floor.basement")} className='text-end responsive-cell'>{buildUnfloors}</td>
        {showBuildType && <td data-title={buildTypeText} className='text-center responsive-cell'>{buildTypeDisplay}</td>}
        <td data-title={t('text.building.rescue_image_class_a')} className='text-center responsive-cell'>{rescueImageDisplay}</td>
        {isEditBuildingRole && <td data-title={t("table.title.enable")}>
          <div className="form-check form-switch d-flex align-items-center">
            <input
              className="form-check-input"
              type="checkbox"
              checked={buildStatus === 1}
              onChange={(e) => {
                onEditStatus(buildId, e.target.checked)
              }}
            />
          </div>
        </td>}
        {<td data-title={t("table.title.action")} className="text-start action-cell">
          {isEditBuildingRole && <button
            type="button"
            className="btn btn-warning me-3 fs-5 my-1 action-btn"
            title={t("button.edit")}
            onClick={clickEdit}
          >
            <i className="fas fa-pen"></i> {t("button.edit")}
          </button>}
          {isDeleteBuildingRole && <button
            type="button"
            className="btn btn-danger me-3 fs-5 my-1 action-btn"
            title={t("button.delete")}
            onClick={clickDelete}
          >
            <i className="fas fa-trash-can fa-lg"></i> {t("button.delete")}
          </button>}
          {(buildFloors > 0 || buildUnfloors > 0) && <button type="button" className="btn btn-secondary fs-5 my-1 action-btn" title={t("button.floor_manage")} onClick={onClickBuildFloor}>
            <i className="fas fa-file-alt"></i>  {t("button.floor_manage")}
          </button>}
          <ExpandRwdButton isExpanded={isExpanded} onClick={toggleExpanded} />
        </td>}
      </tr>
      {isExpanded &&
        <tr className="expanded-row">
          <td colSpan={5} className="p-0">
            <div className="expanded-content">
              <table className="expanded-table">
                <tbody>
                  <tr>
                    <td className="expanded-label">{t("table.title.area.item")}</td>
                    <td className="expanded-value"> {areaName} </td>
                  </tr>
                  <tr>
                    <td className="expanded-label">{t("table.title.building.floor.item")}</td>
                    <td className="expanded-value"> {buildFloors} </td>
                  </tr>
                  <tr>
                    <td className="expanded-label">{t("table.title.building.floor.basement")}</td>
                    <td className="expanded-value"> {buildUnfloors} </td>
                  </tr>
                  {showBuildType && <tr
                  ><td className="expanded-label">{buildTypeText}</td>
                    <td className="expanded-value">{buildTypeDisplay}</td>
                  </tr>}
                  <tr>
                    <td className="expanded-label">{t('text.building.rescue_image_class_a')}</td>
                    <td className="expanded-value"> {rescueImageDisplay}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </td>
        </tr>
      }
    </>
  );
};

const StlyedBuildingList = styled.div<{ $loading?: boolean }>`
  padding-bottom:150px;

  .buttonRow {
    width:100%;
    margin-top:10px;
    display:flex;
    justify-content:right;
  }
  .card-body {
    overflow-x:auto;
    font-size: 14px;
  }
  .topFunctionRow{
    padding:0 15px;
    .left {
      select {
        width:80px;
        margin:0 10px;
      }
    }
    .right {
      .dataTables_filter{
        justify-content: right;
        align-items: center;
        @media (max-width:767px){
          justify-content:left;
          margin-top:10px;
        }
        input {
          width:200px;
          margin-left:10px;
        }
      }
    }
  }
  table {
    position:relative;
    min-height:${props => props.$loading ? "200px" : "auto"};
    thead {
      background:rgb(251, 205, 165);
    }
    th {
      text-align: center;
      white-space:nowrap;
    }
    td {
      .form-check {
        justify-content:center;  
      }
    }
  }

  @media (max-width: 600px){
    label {
      width:200px;
    }
    .buttonPanel {
      margin-top:10px;
      display:flex;
      justify-content: flex-end;
    }
    table {
      thead {
        display:none;
      }
      tbody, td, tr {
        display:block;
        background: #fff !important;
        box-shadow: inset 0 0 0 9999px white;

      }
      tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        background: #fff !important;
      }
      td {
        background: #fff!important;
        position:relative;
        padding-left: 100px;
        text-align:left;
        min-height:55px;
        .form-check {
          justify-content:start;  
        }
      }
      td::before {
          content: attr(data-title);
          position: absolute;
          top: 6px;
          left: 6px;
          width: 30%;
          padding-right: 10px;
          white-space: break-word;
          text-align: left;
          font-weight: bold;
          color: #1a1a1a;
      }

    }
    
  }
  
  .action-cell { 
    
    @media (max-width: 992px) {
      min-width: 300px;
      
      .action-btn {
        width: 100% !important;  // 在平板時強制使用全寬
        margin-right: 0 !important;
      }
    }
  }
`;

export default BuildingList;
