import React, { useState } from 'react';
import styled from 'styled-components';
import { isArrayEmpty } from '../utils/arrayUtil';

interface Props {
    dataList: any[];
    id: string;
    fieldName: string;
}

const ShowMoreInfo: React.FC<Props> = ({ dataList = [], id, fieldName }) => {
    const [showMore, setShowMore] = useState(false);
    const filteredDataList = (dataList ?? []).filter(item => item[fieldName] !== null);

    return (
        <StyledShowMoreInfo>
            {filteredDataList && !isArrayEmpty(filteredDataList) && (
                <div>
                    {filteredDataList[0][fieldName]} {/* 显示第一项 */}
                    <br />
                    {filteredDataList.length > 1 && (
                        <span className="more-link" onClick={() => setShowMore(!showMore)}>
                            {showMore ? 'Less' : 'More'}
                        </span>
                    )}
                    {showMore && filteredDataList.slice(1).map(item => <div key={item[id]}>{item[fieldName]}</div>)}
                </div>
            )}
        </StyledShowMoreInfo>
    );
};

const StyledShowMoreInfo = styled.div`
  .more-link {
    color: blue; /* 设置链接颜色 */
    cursor: pointer; /* 设置鼠标悬停时的样式为手指 */
    user-select: none;
  }

`

export default ShowMoreInfo;
