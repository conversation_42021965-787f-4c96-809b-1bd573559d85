import React from 'react';
import { useTranslation } from 'react-i18next';

interface SignoffActionRadioProps {
    className?: string;
    recordStatus: number;
    handleSignoffActionChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const SignoffActionOption = ({ className = "", recordStatus, handleSignoffActionChange = () => { } }: SignoffActionRadioProps) => {
    const { t } = useTranslation();

    const signoffActionOptions = [
        { id: 'approve', value: 1, label: t('text.signoff.status.approve') },
        { id: 'reject', value: 0, label: t('text.signoff.status.reject') },
        { id: 'return', value: 2, label: t('text.signoff.status.return') }
    ];

    return (
        <div className={className}>
            {signoffActionOptions.map(option => (
                <div key={option.id} className="form-check form-check-inline mt-1">
                    <input
                        type="radio"
                        className="form-check-input"
                        id={option.id}
                        name="signoffActionOptions"
                        value={option.value}
                        checked={recordStatus === option.value}
                        onChange={handleSignoffActionChange}
                    />
                    <label htmlFor={option.id}>{option.label}</label>
                </div>
            ))}
        </div>
    );
};

export default SignoffActionOption;
