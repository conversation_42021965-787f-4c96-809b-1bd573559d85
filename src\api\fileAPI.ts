import { BaseParams } from "../CONNChainEHS/models/BaseParams";
import { EhsFile } from "../CONNChainEHS/models/EhsFile";
import { apiRequest, createLoginPostFormDataConfig, createLoginPostJsonConfig, getApiAddress, handleFetchResponse } from "./config";

export const FileAPI = {
  getFileList: async (parms: BaseParams & {
    fileTypeList: string[];
    fileMappingIdList?: string[];
    likeFileMappingId?: boolean;
  }) => {
    return apiRequest(getApiAddress + "file/list", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getBuildFloorFileList: async (parms: BaseParams & {
    buildId: string; 
  }) => {
    return apiRequest(getApiAddress + "file/list/buildid", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  getProductSdsFile: async (parms: BaseParams & {
    sdsId: string;
  }) => {
    return apiRequest(getApiAddress + "file/product/sds", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
  addFiles: async (parms: BaseParams & {
    fileList: EhsFile[];
    fileContentList: File[];
  }) => {
    const formData = new FormData();
    const { fileContentList } = parms;
    formData.append("loginUserId", parms.loginUserId);
    formData.append("loginRoleLevel", String(parms.loginRoleLevel));
    formData.append("loginRoleId", parms.loginRoleId);
    formData.append("langType", parms.langType);

    // 添加檔案資訊和檔案本身
    fileContentList.forEach((file, index) => {
      const fileInfo = parms.fileList[index];
      const fileListIndex = `fileList[${index}]`;
      formData.append(`${fileListIndex}.fileType`, fileInfo.fileType);
      formData.append(`${fileListIndex}.fileSubtype`, fileInfo.fileSubtype);
      formData.append(`${fileListIndex}.fileNote`, fileInfo.fileNote);
      formData.append(`${fileListIndex}.fileMappingId`, fileInfo.fileMappingId);
      formData.append(`${fileListIndex}.fileName`, fileInfo.fileName);
      formData.append(`fileContentList[${index}]`, file);
    });
    return apiRequest(getApiAddress + "file/add", createLoginPostFormDataConfig(parms, formData)).then((res) => handleFetchResponse(res));
  },
  deleteFile: async (parms: BaseParams & {
    fileId: string;
  }) => {
    return apiRequest(getApiAddress + "file/del", createLoginPostJsonConfig(parms)).then((res) => handleFetchResponse(res));
  },
};
