import { CHEMICAL_PHASE_LIQUID, CHEMICAL_PHASE_SOLID, CHEMICAL_TEMPERATURE_BOILING, CHEMICAL_TEMPERATURE_DECIMAL_PLACES, CHEMICAL_TEMPERATURE_MAX, CHEMICAL_TEMPERATURE_MELTING } from "ehs/constant/constants";
import { ChemicalTemperatureType } from "ehs/enums/ChemicalTempratureType";
import { EhsConfigParam } from "ehs/models/EhsConfigParam";
import { notChineseLang } from "ehs/utils/langUtil";
import React, { ChangeEvent } from "react";
import { useTranslation } from "react-i18next";
import InputNumFloat from "../input/InputNumFloat";
import { checkChemicalTemperature } from "ehs/utils/chemicalUtil";

interface TemperatureObjProps {
    tempratureTypeOptions: EhsConfigParam[];
    temperatureType: string;
    checked: number;
    // intervalCheck: boolean;
    checkedRadioOnChange: (event: ChangeEvent<HTMLInputElement>) => void;
    minValue: number | undefined;
    maxValue: number | undefined;
    minOnChange: (num: number | undefined) => void;
    maxOnChange: (num: number | undefined) => void;
    extClassName?: string;
    phaseStateValue?: string;
}

const TemperatureObj: React.FC<TemperatureObjProps> = ({
    tempratureTypeOptions,
    temperatureType,
    checked,
    // intervalCheck,
    checkedRadioOnChange,
    minValue,
    maxValue,
    minOnChange,
    maxOnChange,
    extClassName,
    phaseStateValue = "",
}) => {
    const { t, i18n } = useTranslation();
    const isRange = checked === ChemicalTemperatureType.Range;
    const temperatureTypeNameObj: { [key: string]: string } = {
        [CHEMICAL_TEMPERATURE_MELTING]: t('text.chemical.melt_point'),
        [CHEMICAL_TEMPERATURE_BOILING]: t('text.chemical.boil_point'),
    }
    const temperatureText = temperatureTypeNameObj[temperatureType];
    const isSolid = phaseStateValue === CHEMICAL_PHASE_SOLID;
    const isLiquid = phaseStateValue === CHEMICAL_PHASE_LIQUID;
    const hasInvalidTemperatureMsg = checkChemicalTemperature(isRange, isLiquid, minValue, maxValue, temperatureType, t);

    return (!isSolid &&
        <div className={"row " + extClassName}>
            <div className="col-12 mb-2  ">
                <label className="d-md-flex align-items-center fw-bold me-3">
                    {temperatureText}{notChineseLang(i18n.language) && ' '}{t('text.type')}
                    {isLiquid && <span className="text-danger ms-1">*</span>}
                </label>
                <div className="d-md-flex align-items-center">
                    {tempratureTypeOptions.map((item) => {
                        const id = `${temperatureType}${item.configValue}`;
                        return (
                            <div className="form-check me-3" key={id}>
                                <input
                                    id={id}
                                    type="radio"
                                    checked={checked === item.configIvalue}
                                    onChange={(e) => checkedRadioOnChange(e)}
                                    className={"form-check-input"}
                                    value={item.configIvalue}
                                />
                                <label className="form-check-label" htmlFor={id}>
                                    {item.configName}
                                </label>
                            </div>
                        );
                    })}
                </div>
            </div>
            <div className="col-12 d-md-flex align-items-center mb-2">
                <div className="col-sm-12 col-md-6 col-xl-4 d-flex align-items-center">
                    <InputNumFloat className="form-control d-inline item-width-100 me-1" value={minValue} maxValue={CHEMICAL_TEMPERATURE_MAX} allowNegative
                        maxLength={2 + CHEMICAL_TEMPERATURE_DECIMAL_PLACES} decimalPlaces={CHEMICAL_TEMPERATURE_DECIMAL_PLACES}
                        allowEmptyUndefine
                        onBlur={(num) => {
                            minOnChange(num);
                        }} />
                    <label>°C</label>
                </div>
                {isRange && (
                    <>
                        <span className="mx-1">~</span>
                        <div className="col-sm-12 col-md-6 col-xl-4 d-flex align-items-center">
                            <InputNumFloat className="form-control d-inline item-width-100 me-1" value={maxValue} maxValue={CHEMICAL_TEMPERATURE_MAX} allowNegative
                                maxLength={2 + CHEMICAL_TEMPERATURE_DECIMAL_PLACES} decimalPlaces={CHEMICAL_TEMPERATURE_DECIMAL_PLACES}
                                allowEmptyUndefine
                                onBlur={(num) => {
                                    maxOnChange(num);
                                }} />
                            <label>°C</label>
                        </div>
                    </>
                )}
            </div>
            <div className="col-12 d-flex">
                <span className={`text-danger ${hasInvalidTemperatureMsg ? "error-field" : ""}`}>
                    {hasInvalidTemperatureMsg}
                </span>
            </div>
        </div>
    );
};

export default TemperatureObj;